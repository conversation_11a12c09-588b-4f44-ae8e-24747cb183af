import express from "express";
import { PayloadRequest } from "payload/types";
import { parse } from "url";
import { nextApp } from "./server/next-utils";

const cartRouter = express.Router();

cartRouter.get("/", (req, res) => {
  const request = req as PayloadRequest;

  if (!request.user) return res.redirect("/sign-in?origin=cart");

  const parsedUrl = parse(req.url, true);
  const { query } = parsedUrl;

  return nextApp.render(req, res, "/cart", query);
});

export default cartRouter;
