import Head from "next/head";
interface Metadata {
    slug?:string;
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
}

interface PageHeadProps {
  metadata: Metadata;
}

const PageHead: React.FC<PageHeadProps> = ( { metadata }:PageHeadProps ) => {
  return (
    <>
      <Head>
        <title>{metadata?.title ?? "Rentprompt"}</title>
        <meta name="keywords" content={metadata?.keywords ?? ""} />
        <meta name="description" content={metadata?.description ?? ""} />
        <meta name="yandex-verification" content="0e9611f8418c7183" />
        <meta
          name="google-translate-customization"
          content="a648e4b2b9009c0f-ca96432aba1106ac-gb7380bc44e8f6cea-10"
        />
      </Head>
    </>
  );
};
export default PageHead;
