import Container from "@/components/ui/container";
import ProductReel from "@/components/ProductReel";
import WhoWeAre from "../components/home/<USER>";
import JoinOurCommunity from "@/components/ui/ourcommunity";
import { Metadata } from "next";
import Loader from "@/components/Loader";
import { cookies } from "next/headers";
import { getServerSideUser } from "../lib/payload-utils";
import RappReel from "@/components/Rappreel";
import SVGComponent from "@/components/home/<USER>";
import Bo<PERSON> from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import Slider from "@/components/home/<USER>";
import HeroSlider from "@/components/home/<USER>";
import HeaderItems from "@/components/ui/header/headeritems";
import Footer from "@/components/Footer";
import dynamic from "next/dynamic";
import { redirect } from "next/navigation";
import { toast } from "sonner";

const Walkthrough = dynamic(() => import("@/components/ui/walkthrough"), {
  ssr: false, // Disable server-side rendering for this component
});
export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: {
          absolute:
            "One Stop Generative AI Marketplace | Rentprompts",
        },
        keywords: [
          "Rentprompts",
          "lease prompt",
          "lease AI prompt",
          "prompts",
          "sell prompts",
          "buy prompts",
          "AI prompts",
          "buy prompt",
          "sell prompt",
          "sell Al assets",
          "generate content",
          "generate images",
          "generate audio",
          "generate video",
          "generate music",
          "AI prompts",
          "monetize AI apps",
          "create AI apps",
          "learn and earn",
          "AI Bounties",
          "bounty rewards",
          "All AI Models",
          "All in one",
          "gpt-4",
          "gpt",
          "flux",
          "llama",
          "llama 3.70B",
          "sdxl",
          "pulid",
          "codeformer",
          "stable audio open",
          "stable audio",
          "stable diffusion",
          "llama vision model",
          "pay as you go",
          "relavent AI",
          "responsible AI",,
          "gpt-4o",
          "gemini",
          "groq models",
          "replicate models",
          "copilot",
          "AI apps",
          "Free AI Apps",
          "Free Prompts",
          "Free AI Courses",
          "Free AI Learning Materials",
          "create content with AI",
          "Branding and Marketing with AI",
          "generate lead with AI",
          "AI in finance",
          "AI in healthcare",
          "AI in awareness",
          "AI for content creation",
          "AI for automations",
          "Rent prompts",
          "lease prompts",
          "no code AI App builder",
          "AI Agents",
          "generate audio",
          "generate music",
          "generate images",
          "edit images",
          "enhance images",
          "generate vedio",
          "generate 3D",
          "generate text",
          "text to image",
          "text to audio",
          "text to music",
          "no subscription AI tools",
          "easy AI tools",
          "AI use cases",
          "fast AI prototyping",
          "fast POCs",
        ],
        alternates: {
          canonical: "/",
        },
        description:
          "Create, Explore and Monetize AI Apps. Generate and Sell Assets, Images, Audio, Video and Prompts. Learn with Community and Earn with AI Bounties ",
        openGraph: {
          title:
            "One Stop Generative AI Marketplace | Rentprompts",
          description:
            "Create, Explore and Monetize AI Apps. Generate and Sell Assets, Images, Audio, Video and Prompts. Learn with Community and Earn with AI Bounties.",
          url: "/",
          siteName: "Rentprompts",
          images: [
            {
              url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
            },
          ],
        },
        twitter: {
          card: "summary_large_image",
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
        robots: {
          index: true,
          follow: true,
        },
      }
    : null;

const Home = async () => {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  // if (response.status === 401) {
  //   toast.error('Session expired. Please log in again.'); 
  //   redirect('/sign-in'); 
  //   return <div>Loading...</div>;
  // }

  const { user } = response;

  return (
    <>
      <HeaderItems user={user} />
      <div className="relative isolate bg-dash md:pt-[calc(60px+40px)]">
        <HeroSlider />
      </div>
      <Container>
        <Loader />
        <div className="space-y-10 px-4">
          {/* <div className="relative ">
          <CustomTools />
        </div> */}
        <div className=" relative gap-y-20 md:gap-y-0   sm:px-4 lg:px-8">
          {/* <RappReel
            query={{ limit: 8 }}
            href="/marketplace"
            title="Brand new AI Apps"
            subtitle="Explore cutting-edge RentPrompts AI apps (Rapps), built in seconds with live previews and no coding required."
            user={user}
          /> */}

            {/* <ProductReel
            query={{ limit: 4 }}
            href="/marketplace"
            title="Brand new Products"
            user={user}
          /> */}

            <RappReel
              query={{ limit: 8, sort: "likes" }}
              href="/marketplace"
              title="Trending AI Apps"
              subtitle="Save time and costs with optimized AI apps designed for impactful content generation."
              user={user}
            />

            <ProductReel
              query={{ sort: "-likes", limit: 8 }}
              href="/marketplace"
              title="Trending Products"
            subtitle = "Explore cutting-edge bundle of images, audio, video, and prompts with no copyright."
              user={user}
            />
          </div>
          {/* <div>
          <h3 className="text-2xl font-bold sm:text-3xl md:px-8 py-8">
            A new Kind of WorkBench
            <DragHomeWiget />
          </h3>
        </div> */}
          <WhoWeAre />
        </div>
        <Footer />
        {/* <Walkthrough steps={steps} /> */}
      </Container>
    </>
  );
};

export default Home;
