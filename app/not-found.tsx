import Link from 'next/link'
import React from 'react'
import { Button } from '@/components/ui/button'

export default function NotFound() {
  return (
    <div style={styles.container}>
      <div style={styles.content}>
        <h1 style={styles.heading}>404 - Page Not Found</h1>
        <p style={styles.message}>Oops! The page you're looking for doesn't exist.</p>
      </div>
      {/* <Button asChild variant="default">
        <Link href={`${process.env.PAYLOAD_PUBLIC_SERVER_URL}`}>Go home</Link>
      </Button> */}
    </div>
  )
}

// Internal CSS Styles
const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    backgroundColor: '#3730a3',
    textAlign: 'center',
    fontFamily: 'Arial, sans-serif',
    color: 'whtie',
  },
  content: {
    maxWidth: '600px',
  },
  heading: {
    fontSize: '33px',
    fontWeight: '700',
    marginBottom: '0px',
    color: '#fff',
    textAlign: 'start',

    // fontFamily: " 'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace"

  },
  message: {
    marginTop: '0.5rem',
    fontSize: '18px',
    color: '#fff',
  },
}
