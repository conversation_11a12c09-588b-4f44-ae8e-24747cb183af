import AssistantBubble from "@/components/AssistantBubble"; 
import "./globals.css";
// import Header from "@/components/Header";
import type { Metadata } from "next";
import { Cabin } from "next/font/google";
import { Providers } from "@/components/Providers";
const cabin = Cabin({ subsets: ["latin"] });
import { toast, Toaster } from "sonner";
import Footer from "@/components/Footer";
// import SecondaryNav from "@/components/SecondaryNav";
import Script from "next/script";
import { cookies } from "next/headers";
import HeaderItems from "@/components/ui/header/headeritems";
import { getServerSideUser } from "@/lib/payload-utils";
import FooterItems from "@/components/ui/header/footeritems";
import { redirect } from "next/navigation";



export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: {
          default: "One Stop Generative AI Marketplace | Rentprompts",
          template: "Rentprompts - %s",
        },
        metadataBase: new URL("https://rentprompts.com"),
        alternates: {
          canonical: "/",
        },
        description:
          "Create, Explore and Monetize AI Apps. Generate and Sell Assets, Images, Audio, Video and Prompts. Learn with Community and Earn with AI Bounties.",
        twitter: {
          card: "summary_large_image",
          title: "One Stop Generative AI Marketplace | Rentprompts",
          description:
            "Create, Explore and Monetize AI Apps. Generate and Sell Assets, Images, Audio, Video and Prompts. Learn with Community and Earn with AI Bounties.",
          creator: "https://rentprompts.com/",
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
        robots: {
          index: true,
          follow: true,
        },
      }
    : null;

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  // if (response.status === 401) {
  //   toast.error("Session expired. Please log in again.");
  //   redirect("/sign-in");
  //   return <html lang="en" className="no-scrollbar"><body>Loading...</body></html>;
  // }

  const { user } = response;

  return (
    <html lang="en" className="no-scrollbar">
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=G-S8TFD37YL7`}
        strategy="afterInteractive"
      />
      <Script id="G-S8TFD37YL7" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-S8TFD37YL7');

        `}
      </Script>
      <body
        className={cabin.className}
        style={{ margin: "0px", padding: "0px" }}
      >
        <Providers>{children}</Providers>
        <AssistantBubble />
        <Toaster position="top-center" richColors />
      </body>
    </html>
  );
}
