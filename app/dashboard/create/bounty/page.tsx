import CreateBountyForm from "@/components/bountysteps/createform";
import { getServerSideUser } from "@/lib/payload-utils";
import { cookies } from "next/headers";
import React from "react";
import { redirect } from "next/navigation";
// import { toast } from "sonner";

const CreateBounty = async () => {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  if (response.status === 401) {
    // toast.error("Session expired. Please log in again.");
    redirect('/sign-in');
    return <div>Loading...</div>;
  }

  const { user } = response;

  return (
    <div>
      <CreateBountyForm user={user} />
    </div>
  );
};

export default CreateBounty;
