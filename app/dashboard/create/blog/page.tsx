import BlogMultiStepForm from "@/components/Blogs/createBlogForm";
import CreateBountyForm from "@/components/bountysteps/createform";
import { getServerSideUser } from "@/lib/payload-utils";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";
import { toast } from "sonner";


const CreateBlog = async () => {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  if (response.status === 401) {
    // toast.error("Session expired. Please log in again.");
    redirect('/sign-in');
    return <div>Loading...</div>;
  }

  const { user } = response;
  return <div>
    <BlogMultiStepForm user={user}/>
  </div>;
};

export default CreateBlog;
