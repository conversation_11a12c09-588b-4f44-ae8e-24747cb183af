"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import TopHeader from "@/components/ui/topheader";
import StepNavigation from "../../../../components/ui/dashboard/StepNavigation";
import { Step1 } from "@/components/productsteps/stepfirst";
import { Step2 } from "@/components/productsteps/steptwo";
import { Step3 } from "@/components/productsteps/stepthree";
import { Step4 } from "@/components/productsteps/stepfour";
import { toast } from "sonner";

export default function MultiStepForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("Id");

  const [isClient, setIsClient] = useState(false);
  const [productType, setProductType] = useState("");

  useEffect(() => {
    setIsClient(true);
  }, []);

  const STORAGE_KEY_FORM = "productFormData";
  const STORAGE_KEY_STEP = "productFormStep";

  const getStoredFormData = () => {
    if (typeof window === "undefined") return null;
    const savedData = localStorage.getItem(STORAGE_KEY_FORM);
    return savedData ? JSON.parse(savedData) : null;
  };

  // Function to get stored step
  const getStoredStep = () => {
    if (typeof window === "undefined") return 1;
    const savedStep = localStorage.getItem(STORAGE_KEY_STEP);
    return savedStep ? parseInt(savedStep, 10) : 1;
  };

  const [step, setStep] = useState(1);
  const [user, setUser] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    generationType: "",
    generationModel: null,
    price: 0,
    category: "",
    images: [],
    affiliated_with: "",
    needsApproval: false,
    listingCategory: "",
    product_files: null,
    productCategory: null,
    slug: "",
  });

  useEffect(() => {
    if (isClient) {
      const savedFormData = getStoredFormData();
      const savedStep = getStoredStep();

      if (savedFormData && !id) setFormData(savedFormData);
      if (savedStep && !id) setStep(savedStep);
    }
  }, [isClient]);

  useEffect(() => {
    if (isClient  && !id) {
      localStorage.setItem(STORAGE_KEY_FORM, JSON.stringify(formData));
      localStorage.setItem(STORAGE_KEY_STEP, step.toString());
    }
  }, [formData, step, isClient]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`
        );
        if (!response.ok) throw new Error("Network response was not ok");
        const data = await response.json();
        setUser(data.data);
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    fetchUserData();
  }, []);

  // Fetch product details if ID is present
  useEffect(() => {
    if (id) {
      const fetchProduct = async () => {
        try {
          const response = await fetch(`/api/products/product-detail/${id}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            credentials: "include",
          });

          if (response.ok) {
            const productData = await response.json();

            const product = productData.data;
            const productStatus =
              product.approvedForSale === "denied"
                ? "pending"
                : product.approvedForSale;

            setFormData((prev) => ({
              ...prev,
              listingCategory: product.listingCategory || "",
              slug: product.slug || "",
              name: product.name || "",
              generationType: product.generationType,
              generationModel: product.generationModel,
              description: product.description || "",
              price: product.price || 0,
              category: product.category || "",
              images: product.images || [],
              affiliated_with: product.affiliated_with || "",
              productCategory: product.productCategory || "",
              needsApproval: product.needsApproval || false,
              product_files: product.product_files || null,
              approvedForSale: productStatus,
            }));
            setProductType(product.listingCategory)
          } else {
            toast.error("Failed to fetch product data.");
          }
        } catch (error) {
          toast.error("Error fetching product:", error);
        }
      };

      fetchProduct();
    } else {
      // Creating a new product
      const savedFormData = getStoredFormData();
      if (savedFormData) setFormData(savedFormData);

      // Clear stored step to start from step 1
      localStorage.removeItem(STORAGE_KEY_STEP);
      setStep(1);
    }
  }, [id]);

  const totalSteps = 4;

  const nextStep = () => {
    if (step < totalSteps) setStep((prev) => prev + 1);
  };

  const prevStep = () => {
    if (step > 1) setStep((prev) => prev - 1);
  };

  if (!isClient) return null;

  return (
    <>
      <TopHeader user={user} title="Create Product" buttonName="Products" />

      <div className="flex-col flex items-center py-2 bg-dash-foreground rounded-lg md:m-6">
        <StepNavigation step={step} totalSteps={totalSteps} />
        <div className="max-w-3xl w-full">
          {step === 1 && (
            <Step1
              nextStep={nextStep}
              formData={formData}
              setFormData={setFormData}
            />
          )}
          {step === 2 && (
            <Step2
              nextStep={nextStep}
              prevStep={prevStep}
              formData={formData}
              setFormData={setFormData}
            />
          )}
          {step === 3 && (
            <Step3
              nextStep={nextStep}
              prevStep={prevStep}
              formData={formData}
              setFormData={setFormData}
            />
          )}
          {step === 4 && (
            <Step4
              prevStep={prevStep}
              formData={formData}
              setFormData={setFormData}
            />
          )}

        </div>
      </div>
    </>
  );
}
