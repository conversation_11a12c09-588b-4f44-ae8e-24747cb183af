import { Each } from "@/components/each";
import { DashboardCard } from "@/components/ui/dashboardcard";
import EaringCard from "@/components/ui/earingcard";
import TopHeader from "@/components/ui/topheader";
import { cookies } from "next/headers";
import { getServerSideUser } from "@/lib/payload-utils";
import DataCards from "@/components/ui/dashboard/datatable";
import { redirect } from "next/navigation";
import { Target } from "lucide-react";

const CardData = [
  {
    title: "Create AI Apps",
    description:
      "Create an AI App to generate anything from text, to image, to video.",
    Icon: "Speaker",
    // href: "sell/collections/rapps?limit=10&sort=-name",
    href: "https://rentprompts.ai/",
    Target: "_blank",

    disabled: false,
  },
  {
    title: "Create Product",
    description: "Easily create and manage your products.",
    Icon: "Speaker",
    href: "/dashboard/create/product",
    disabled: false,
  },
  {
    title: "Create Bounty",
    description: "Set up bounties and reward users for contributions.",
    Icon: "Speaker",
    href: "/dashboard/create/bounty",
    disabled: false,
  },
  {
    title: "Create Blog",
    description: "Share your thoughts by creating engaging blogs.",
    Icon: "Speaker",
    href: "/dashboard/create/blog",
    disabled: false,
  },
];

const DashBoard = async () => {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  if (response.status === 401) {
    redirect("/sign-in"); // Replace "/login" with your login route
    return null;
  }

  const { user } = response;
 if (!user) {
      redirect("/sign-in"); // Redirect to login page
      return null; // Ensure no further rendering
    }
  const earningsData = [
    {
      title: "Balance",
      value: user?.coinBalance.toFixed(2),
      tooltipMessage: "Your Currect Balance",
    },
    {
      title: "Last 7 Days",
      value: "0",
      tooltipMessage: "Your Last 7 Days Earing",
    },
    {
      title: "Last 28 Days",
      value: "0",
      tooltipMessage: "Your Last 28 Days Earing",
    },
    {
      title: "Total Earnings",
      value: "0",
      tooltipMessage: "Your Total Earning",
    },
  ];

  return (
    <div className="items-center">
      <TopHeader user={user} title={"Dashboard"} />
      <div>
        <div>
          <div className="h-full px-4 py-2 md:px-6">
            <div className="grid grid-cols-1 w-full md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Each
                of={earningsData}
                render={(item, index) => (
                  <EaringCard
                    key={index}
                    title={item.title}
                    value={item.value}
                    tooltipMessage={item.tooltipMessage}
                  />
                )}
              />
            </div>
            <div className="mt-10">
              <h3 className="text-base md:text-3xl font-bold text-white">
                Getting started
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-5">
                <Each
                  of={CardData}
                  render={(item, index) => (
                    <DashboardCard
                      key={index}
                      items={item}
                      // key={index}
                      // title={item.title}
                      // description={item.description}
                      // Icon={item.Icon}
                      // href={item.href}
                      // disabled={item.disabled}
                    />
                  )}
                />
              </div>
            </div>
          </div>
          <div className="py-4 px-4 md:px-6">
            <DataCards userId={user?.id} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashBoard;
