import AIAppList from "@/components/ui/dashboard/applist";
import BlogList from "@/components/ui/dashboard/blogslist";
import TopHeader from "@/components/ui/topheader";
import { getServerSideUser } from "@/lib/payload-utils";
import { getPayloadClient } from "@/server/get-payload";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import React from "react";
import { toast } from "sonner";

async function AllBlogs() {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  if (response.status === 401) {
    toast.error("Session expired. Please log in again.");
    redirect('/sign-in');
    return <div>Loading...</div>;
  }

  const { user } = response;
    if (!user) {
      redirect("/sign-in"); // Redirect to login page
      return null; // Ensure no further rendering
    }
    const payload = await getPayloadClient();
  const userId = user?.id;
  const limit = 10; // Items per page
  const page = 1; // Initial page

  // Fetch products during SSR
  const { docs: initialRapps, totalDocs }: any = await payload.find({
    collection: "rapps",
    where: {
      creator: { equals: userId },
    },
    limit,
    page,
  });
  return (
    <>
      <TopHeader
        user={user}
        title="All AI Apps"
        buttonName="Add AI App"
        buttonLink="https://rentprompts.ai/dashboard"
      />
      <div className="px-6">
        <AIAppList initialRapps={initialRapps}
          totalDocs={totalDocs}
          userId={userId}
          //  title={title}
          limit={limit} />
      </div>
    </>
  );
}

export default AllBlogs;
