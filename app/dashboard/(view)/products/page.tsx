import ProductsList from "@/components/ui/dashboard/productslist";
import TopHeader from "@/components/ui/topheader";
import { getServerSideUser } from "@/lib/payload-utils";
import { getPayloadClient } from "@/server/get-payload";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";
import { toast } from "sonner";

async function AllProducts() {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  if (response.status === 401) {
    // toast.error("Session expired. Please log in again.");
    redirect('/sign-in');
    return <div>Loading...</div>;
  }

  const { user } = response;
  // console.log("user inside create product", user);

  if (!user) {
    redirect("/sign-in"); // Redirect to login page
     // Ensure no further rendering
  }

  const payload = await getPayloadClient();
  const userId = user?.id;
  const limit = 10; // Items per page
  const page = 1; // Initial page

  // Fetch products during SSR
  const { docs: initialProducts, totalDocs }: any = await payload.find({
    collection: "products",
    where: {
      user: { equals: userId },
    },
    limit,
    page,
  });
  return (
    <>
      <TopHeader
        user={user}
        title="All Products"
        buttonName="Add Products"
        buttonLink="/dashboard/create/product"
      />
      <div className="">
        <ProductsList
          initialProducts={initialProducts}
          totalDocs={totalDocs}
          userId={userId}
          //  title={title}
          limit={limit}
        />
      </div>
    </>
  );
}

export default AllProducts;
