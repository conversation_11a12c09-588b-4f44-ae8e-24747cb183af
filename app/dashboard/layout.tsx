
import type { Metada<PERSON> } from 'next'
import React from 'react'
import '../globals.css'
import { Toaster } from 'sonner'
import { SidebarComponent } from '@/components/ui/sidebar'
import {Providers} from '@/components/Providers'
import DashFooter from '@/components/ui/dashboard/dashFooter'


export default function DashboardLayout({ children }: { children: React.ReactNode }) {

  return (
      <div className='fixed h-screen w-full bg-dash no-scrollbar overflow-hidden' >
        <div
          className=
            "flex flex-col lg:flex-row w-full overflow-hidden"
        >

          <SidebarComponent/>
          <Providers>
            <div className="flex w-full h-[calc(100vh-64px)] lg:h-screen justify-between no-scrollbar flex-col overflow-y-auto whitespace-nowrap">
                <div>
                {children}
                </div>
              <DashFooter/>

            </div>
          </Providers>

          {/* <Toaster position="top-center" richColors /> */}
        </div>
      </div>
  )
}


