import { toast } from "sonner";

const downloadFile = async (fileName: string, displayFileName: string) => {
  toast.loading("Downloading...");

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api2/downloadUploadedFile/${fileName}`
    );

    if (!response.ok) {
      // Attempt to extract error message from response
      let errorMessage = `Download failed with status ${response.status}`;
      try {
        const errorText = await response.text();
        if (errorText) {
          errorMessage += `: ${errorText}`;
        }
      } catch (e) {
        // Ignore errors while extracting error message
      }
      throw new Error(errorMessage);
    }

    const blob = await response.blob();

    if (!blob || blob.size === 0) {
      throw new Error("Downloaded file is empty.");
    }

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = displayFileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    window.URL.revokeObjectURL(url);

    toast.dismiss();
    toast.success("Download Successful");
  } catch (error: any) {
    console.error("Download error:", error);
    toast.dismiss();
    toast.error(error.message || "Download failed");
  }
};

export default downloadFile;
