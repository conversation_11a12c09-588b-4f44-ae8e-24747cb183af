type UploadFileParams = {
    event: React.ChangeEvent<HTMLInputElement>;
    updateFileState: (file: any) => void;
    setLoading: (loading: boolean) => void;
    updateFormData: (data: any) => void;
    updateErrors: (errors: any) => void;
    allowedFileTypes?: string[]; // Pass file types dynamically (optional)
    maxFileSize?: number; // Pass max file size dynamically (optional, in bytes)
  };
  
  export const handleUploadFile = async ({
    event,
    updateFileState,
    setLoading,
    updateFormData,
    updateErrors,
    allowedFileTypes = [], // Default: No restriction
    maxFileSize, // Default: No size limit
  }: UploadFileParams) => {
    const file = event.target.files?.[0];
  
    if (!file) {
      updateErrors((prev) => ({ ...prev, product_files: "No file selected." }));
      return;
    }
  
    // Check file type (if allowedFileTypes is provided)
    if (allowedFileTypes.length > 0 && !allowedFileTypes.includes(file.type)) {
      updateErrors((prev) => ({
        ...prev,
        product_files: `Invalid file type. Allowed types: ${allowedFileTypes.join(", ")}`,
      }));
      return;
    }
  
    // Check file size (if maxFileSize is provided)
    if (maxFileSize && file.size > maxFileSize) {
      updateErrors((prev) => ({
        ...prev,
        product_files: `File size exceeds the ${maxFileSize / (1024 * 1024)}MB limit.`,
      }));
      return;
    }
  
    setLoading(true);
  
    try {
      const fileFormData = new FormData();
      fileFormData.append("file", file);
  
      const mediaRes = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files?depth=0`,
        {
          method: "POST",
          credentials: "include",
          body: fileFormData,
        }
      );
  
      if (!mediaRes.ok) {
        throw new Error(`File upload failed: ${mediaRes.statusText}`);
      }
  
      const mediaData = await mediaRes.json();
  
      if (!mediaData?.doc?.id || !mediaData?.doc?.url) {
        throw new Error("Invalid response from server.");
      }
  
      const uploadedFile = {
        id: mediaData.doc.id,
        filename: mediaData.doc.filename,
        url: mediaData.doc.url,
      };
  
      updateFileState(uploadedFile);
      updateFormData((prevData) => ({
        ...prevData,
        product_files: uploadedFile,
      }));
      updateErrors((prev) => ({ ...prev, product_files: "" }));
    } catch (error: any) {
      updateErrors((prev) => ({
        ...prev,
        product_files: error.message || "File upload failed.",
      }));
    } finally {
      setLoading(false);
    }
  };
  
  type RemoveFileParams = {
    updateFileState: (file: any) => void;
    updateFormData: (data: any) => void;
  };
  
  export const handleRemoveFile = ({
    updateFileState,
    updateFormData,
  }: RemoveFileParams) => {
    updateFileState(null);
    updateFormData((prevData) => ({ ...prevData, product_files: null }));
  };
  