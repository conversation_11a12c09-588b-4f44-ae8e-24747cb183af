export const INITIAL_QUESTIONS = [
  {
    content: "What are the benefits of meditation for mental health?",
  },
  {
    content: "How can I improve my public speaking skills?",
  },
  {
    content: "Can you explain the concept of blockchain technology?",
  },
  {
    content: "What are some effective strategies for time management?",
  },
  {
    content: "What are the latest trends in artificial intelligence?",
  },
  {
    content: "How does climate change impact global ecosystems?",
  },
  {
    content: "What is the importance of renewable energy sources?",
  },
  {
    content: "What are the key principles of effective communication?",
  },
  {
    content: "How can I develop a growth mindset?",
  },
  {
    content: "What are some tips for improving work-life balance?",
  },
  ];

  // export const IMAGE_GENERATION_QUESTIONS = [
  //   {
  //     content: "Can you show me a diagram illustrating the water cycle?",
  //   },
  //   {
  //     content: "Provide an image that represents climate change.",
  //   },
  //   {
  //     content: "Show an example of sustainable architecture.",
  //   },
  //   {
  //     content: "Share an image of a famous painting and describe it.",
  //   },
  //   {
  //     content: "Post an infographic about healthy eating habits.",
  //   },
  //   {
  //     content: "Can you display a map highlighting world biodiversity hotspots?",
  //   },
  //   {
  //     content: "Provide a visual representation of the solar system.",
  //   },
  //   {
  //     content: "Show me an image of a historical landmark and its significance.",
  //   },
  //   {
  //     content: "Share an image of a sports event and describe the atmosphere.",
  //   },
  //   {
  //     content: "Can you provide an image that illustrates the effects of pollution?",
  //   },
  //   {
  //     content: "Create an artwork of a tranquil Zen garden, featuring cherry blossoms and a koi pond.",
  //   },
  //   {
  //     content: "Visualize a grand castle on a hill, surrounded by mist and a mysterious forest.",
  //   },
  //   {
  //     content: "Generate an image of a robot playing chess against a human in a futuristic setting.",
  //   },
  //   {
  //     content: "Design an enchanted library filled with floating books and magical creatures.",
  //   },
  //   {
  //     content: "Create a whimsical scene of animals having a tea party in a colorful meadow.",
  //   },
  // ];
  