// file name - search.ts

export interface SearchResult {
  id: string;
  title: string;
  slug: string;
  type: string; // Yeh URL path aur general category ke liye use hoga (e.g., 'generate', 'ai-apps')
  url: string;
  image?: string;
  description?: string;
  specificType?: string; // Model ka actual type (e.g., 'text', 'image') - raw data
  uiDisplayType?: string; // NAYA: UI mein suggestions mein dikhane wala type label
}

// globalSearch function waisa hi rahega...
export const globalSearch = async (query: string): Promise<SearchResult[]> => {
  try {
    const res = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
    if (!res.ok) {
      let errorMsg = 'Search failed';
      try {
        const errorResult = await res.json();
        errorMsg = errorResult.error || errorMsg;
      } catch (e) { /* Ignore */ }
      throw new Error(errorMsg);
    }
    const { results } = await res.json();
    return results || [];
  } catch (err) {
    console.error('Search error in globalSearch:', err);
    return [];
  }
};
