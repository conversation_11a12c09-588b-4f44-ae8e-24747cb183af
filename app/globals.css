@tailwind base;
@tailwind components;
@tailwind utilities;
 

@layer base {
  :root {
    /* --border: 228 96.5% 88.8%;
    --input: 228 96.5% 88.8%;

    --radius: 0.3rem;
    --ring: 234.5 89.5% 73.9%;

    --background: 228 96.5% 88.8%;
    --foreground: 243.8 47.1% 20%;

    --muted: 226.5 100% 93.9%;
    --muted-foreground: 238.7 83.5% 66.7%;

    --popover: 0 0% 100%;
    --popover-foreground: 243.8 47.1% 20%;

    --card: 0 0% 100%;
    --card-foreground: 243.8 47.1% 20%;

    --primary: 242.2 47.4% 34.3%;
    --primary-foreground: 225.9 100% 96.7%;

    --secondary: 226.5 100% 93.9%;
    --secondary-foreground: 242.2 47.4% 34.3%;

    --accent: 226.5 100% 93.9%;
    --accent-foreground: 242.2 47.4% 34.3%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 225.9 100% 96.7%; */
    --border: 243.7 54.5% 41.4%;
    --input:  225.9 100% 96.7%;

    --radius: 0.3rem;
    --ring: 243.7 54.5% 41.4%;

    --background: 243.7 54.5% 41.4%;
    --foreground: 225.9 100% 96.7%;

    --muted: 243.7 60.5% 41.4%;
    --muted-foreground: 234.5 89.5% 73.9%;

    --popover: 243.8 47.1% 20%;
    --popover-foreground: 225.9 100% 96.7%;

    --card: 243.8 47.1% 20%;
    --card-foreground: 225.9 100% 96.7%;

    --primary: 225.9 100% 96.7%;
    --primary-foreground: 242.2 47.4% 34.3%;

  
    --secondary-foreground: 225.9 100% 96.7%;     
    --secondary-foreground: 250 92% 66%;                      

    --neo: 250 92% 66%;
    --neo-foreground: 127 64% 86%;

    --accent: 243.7 54.5% 41.4%;
    --accent-foreground: 225.9 100% 96.7%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 225.9 100% 96.7%;

    --dash: 228 67% 13%;
    --dash-foreground: 227, 60%, 17%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loader {
  width: 30px;
  height: 30px;
  border: 8px solid #3490dc;
  border-top-color: #e4e8ea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dark {
    --border: 243.7 54.5% 41.4%;
    --input:  225.9 100% 96.7%;

    --ring: 243.7 54.5% 41.4%;

    --background: 243.7 54.5% 41.4%;
    --foreground: 225.9 100% 96.7%;

    --muted: 243.7 60.5% 41.4%;
    --muted-foreground: 234.5 89.5% 73.9%;

    --popover: 243.8 47.1% 20%;
    --popover-foreground: 225.9 100% 96.7%;

    --card: 243.8 47.1% 20%;
    --card-foreground: 225.9 100% 96.7%;

    --primary: 225.9 100% 96.7%;
    --primary-foreground: 242.2 47.4% 34.3%;

    --secondary: 243.7 60.5% 41.4%;
    --secondary-foreground: 225.9 100% 96.7%;

    --neo: 250 92% 66%;
    --neo-foreground: 127 64% 86%;

    --accent: 243.7 54.5% 41.4%;
    --accent-foreground: 225.9 100% 96.7%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 225.9 100% 96.7%;

    --dash: 228 67% 13%;
    --dash-foreground: 227, 60%, 17%;
}
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

/* width */
::-webkit-scrollbar {
    width: 6px; 
}

/* Track */
::-webkit-scrollbar-track {
    background: #ccc;
    border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #4F46E5;
    border-radius: 24px;
}
/* To handle height for vertical scrollbar, you can control the thumb height */
::-webkit-scrollbar-thumb {
    max-height: 200px; /* Minimum height of the thumb handle */
}

/* Webkit Browsers (Chrome, Edge, Safari) */
.thin-scrollbar::-webkit-scrollbar {
  height: 6px; /* Thin scrollbar height */
}

@keyframes slide-glow {
  0% {
    transform: translateX(-100%) skewX(-20deg);
  }
  100% {
    transform: translateX(100%) skewX(-12deg);
  }
}


.animate-slide-glow {
  animation: slide-glow 2s linear infinite;
}

.hide-scrollbar {
  /* Hide scrollbar in Internet Explorer and Edge */
  -ms-overflow-style: none;
  /* Hide scrollbar in Firefox */
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  /* Hide scrollbar in Chrome, Safari, and WebKit-based browsers */
  display: none;
}

.spin-animation {
  animation: spins 1s linear infinite;
}

/* Custom CSS */
@keyframes spins {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Webkit (Chrome, Safari) */
input[type='range']::-webkit-slider-thumb {
  appearance: none;
  height: 20px;  /* Taller height */
  width: 20px;   /* Slim width for a rectangular look */
  background-color: #ffffff; /* White thumb */
  border-radius: 2px;  /* Minimal rounding */
  border: 2px solid #e5e7eb; /* Light gray border */
  cursor: pointer;
  transition: transform 0.2s ease;
}

input[type='range']:hover::-webkit-slider-thumb {
  transform: scale(1.1); /* Subtle hover effect */
}

/* Firefox */
input[type='range']::-moz-range-thumb {
  height: 28px;
  width: 12px;
  background-color: #ffffff;
  border-radius: 2px;
  border: 2px solid #e5e7eb;
  cursor: pointer;
  transition: transform 0.2s ease;
}

input[type='range']:hover::-moz-range-thumb {
  transform: scale(1.1); /* Subtle hover effect */
}

@keyframes rapidBounce {
  0%, 100% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-20px);
  }
  40% {
    transform: translateY(10px);
  }
  60% {
    transform: translateY(-15px);
  }
  80% {
    transform: translateY(5px);
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes colorChange {
  0% {
    color: #f06e46; /* Bright Red */
  }
  25% {
    color: #f4c53a; /* Bright Orange */
  }
  50% {
    color: #8cc34e; /* Bright Green */
  }
  75% {
    color: #44a4f1; /* Bright Blue */
  }
  100% {
    color: #f26e46; /* Back to Bright Red */
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(50px); /* Start from the right */
  }
  to {
    opacity: 1;
    transform: translateX(0); /* End at the original position */
  }
}

.fade-in-right {
  animation: fade-in-right 1s ease-out 1; /* Run the animation once */

}

@layer demo {
  /* Global Custom Properties */
  :root {
    --speed: 3.5s; /* Animation speed */
    --hue-start: 350; /* Starting hue value */
    --hue-end: 40; /* Ending hue value */
    --span-count: 10; /* Number of spans */
    --loader-size: 100%; /* Base size of the loader */
  }

  /* Loader Container */
  .loaders {
    width: var(--loader-size); /* Make size dynamic */
    aspect-ratio: 1 / 1; /* Keep it square */
    position: relative;
    scale: 1 -1; /* Flip vertically */
    transform-style: preserve-3d;
  }

  /* Loader Span Styles */
  .loaders span {
    width: 90%; /* Relative to the loader's width */
    aspect-ratio: 10 / 2.25; /* Maintain the aspect ratio */
    position: absolute;
    top: 0%;
    left: 50%;
    transform: translate(-50%, 0%) scale(0.25); /* Initial transform state */
    border-radius: 50%;
    border: calc(1% * var(--loader-size)) solid hsl(0 0% 50%); /* Scaled border */

    /* Background and Border Colors */
    --bg: hsl(var(--hue) 85% 58% / 0.85);
    background: radial-gradient(hsl(0 0% 100% / 0.25), #0000), var(--bg);
    border-color: color-mix(in hsl, var(--bg), white 20%);

    /* Animations */
    /* animation:
      hue var(--speed) calc((-1 * var(--speed)) * (var(--idx) / var(--span-count))) infinite ease-in-out reverse,
      travel var(--speed) calc((-1 * var(--speed)) * (var(--idx) / var(--span-count))) infinite linear,
      scale calc(var(--speed) * 0.5) calc((-1 * var(--speed)) * (var(--idx) / var(--span-count))) infinite alternate; */
      animation:
      hue var(--speed) calc((-1 * var(--speed)) * (var(--idx) / var(--span-count))) infinite ease-in-out reverse,
      transform-travel-scale var(--speed) calc((-1 * var(--speed)) * (var(--idx) / var(--span-count))) infinite linear; 
  }

  /* Define Animatable Property */
  @property --hue {
    syntax: '<number>';
    initial-value: 0;
    inherits: true;
  }

  /* Animations */
  @keyframes hue {
    0% {
      --hue: var(--hue-start);
    }
    100% {
      --hue: var(--hue-end);
    }
  }
  @keyframes transform-travel-scale {
    0% {
      opacity: 0;
      transform: translate3d(-50%, -5%, 0) scale(0); /* Start small and slightly above */
    }
    5% {
      opacity: 1;
      transform: translate3d(-50%, -5%, 0) scale(0.25); /* Appear at the initial position */
    }
    25% {
      opacity: 1;
      transform: translate3d(-50%, 25%, 2px) scale(0.5); /* Midway position */
    }
    50% {
      opacity: 1;
      transform: translate3d(-50%, 100%, 5px) scale(0.85); /* Larger, mid-point */
    }
    75% {
      opacity: 1;
      transform: translate3d(-50%, 200%, 8px) scale(1); /* Almost at the bottom */
    }
    95% {
      opacity: 0.75;
      transform: translate3d(-50%, calc(300%), 10px) scale(0.85); /* Near bottom */
    }
    100% {
      opacity: 0.2;
      transform: translate3d(-50%, calc(320%), 5px) scale(0.25); /* Full size and bottom */
    }
  } 
}


#xscroll::-webkit-scrollbar {
  display: none;
}

#dashboardScroll::-webkit-scrollbar {
  display: none;
}