import React from "react";
import { BackgroundGradientAnimation } from "@/components/ui/background-gradient-animation";
import { FlipWords } from "@/components/ui/flip-words";
import Container from "@/components/ui/container";
import Loader from "@/components/Loader";
import { cookies } from "next/headers";
import { getServerSideUser } from "../../../lib/payload-utils";
import AcademyChild from "./components/AcademyChild";
import { Metadata } from "next";
import Carousel from "@/components/ui/academy/imageslider";


const words = ["Better", "Strong", "Advance", "Modern"];

// const ListItem = React.forwardRef<
//   React.ElementRef<"a">,
//   React.ComponentPropsWithoutRef<"a">
// >(({ className, title, children, ...props }, ref) => {
//   return (
//     <li>
//       <NavigationMenuLink asChild>
//         <a
//           ref={ref}
//           className={cn(
//             "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
//             className
//           )}
//           {...props}
//         >
//           <div className="text-sm font-medium leading-none">{title}</div>
//           <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
//             {children}
//           </p>
//         </a>
//       </NavigationMenuLink>
//     </li>
//   );
// });
// ListItem.displayName = "ListItem";

export const metadata: Metadata = process.env.SERVER_ENV === 'prod'
  ? { 
      title: {
        absolute: "Academy - Learn & Advance Your Skills | Rentprompts",
      },
      keywords: [
        "Online Learning Academy", 
        "Generative AI Courses", 
        "AI Education", 
        "Rentprompts Academy", 
        "RentPrompts Academy", 
        "Learn AI Online", 
        "AI Courses", 
        "Learn Generative AI"
      ],
      alternates: {
        canonical: "/academy",
      },
      description: "Join RentPrompts Academy to explore a variety of courses on Generative AI, machine learning, and more. Learn from industry experts and advance your skills.",
      openGraph: {
        title: "Academy - Learn & Advance Your Skills | Rentprompts",
        description: "Explore cutting-edge courses on AI and Generative AI at RentPrompts Academy. Start learning today and advance your career.",
        url: "/academy",
        siteName: "Rentprompts Academy",
        images: [
          {
            url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
            alt: "RentPrompts Academy Banner",
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title: "Academy - Learn & Advance Your Skills | Rentprompts",
        description: "Unlock your potential with expert-led AI courses at RentPrompts Academy. Explore now.",
        images: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
      },
      verification: {
        google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
      },
      robots: {
        index: true,
        follow: true,
      },
    } 
  : null;

const AcademyPage = async () => {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);

 

  return (
    <>
        <Carousel/>

      <Container>
        <Loader />
        {/* <BackgroundGradientAnimation>
          <div className="absolute mt-32 inset-0 items-center justify-center font-bold px-4 pointer-events-none text-3xl text-center md:text-4xl lg:text-7xl">
            <h1 className="sm:text-5xl text-weight lg:text-6xl mx-auto font-normal text-white dark:text-neutral-400">
              Learn & Become
              <FlipWords words={words} /> <br />
            </h1>
            <h2 className="text-xl md:text-3xl py-4 text-cyan-300 md:w-10/12 mx-auto">
              Explore the possibilities and start learning today!
            </h2>
          </div>
        </BackgroundGradientAnimation> */}

        <AcademyChild user={user} />
      </Container>
    </>
  );
};

export default AcademyPage;
