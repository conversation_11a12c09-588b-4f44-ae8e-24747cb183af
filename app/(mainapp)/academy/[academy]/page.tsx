import Link from "next/link";
import { getPayloadClient } from "@/server/get-payload";
import { Button } from "@/components/ui/button";

interface PageProps {
  searchParams: {
    id: string;
  };
}

const CourseDetails = async ({ searchParams: { id } }: PageProps) => {
  const payload = await getPayloadClient();
  const course: any = await payload.findByID({
    collection: "courses",
    id: id,
  });
  if (!course) {
    return <p>Blog not found</p>;
  }

  return (
    <>
      <div className="container w-11/12 md:w-3/4 justify-center mt-28 text-center border border-white">
        <h1 className="text-4xl font-bold mb-8">{course.title}</h1>
        <h3 className="text-2xl font-bold">{course.description}</h3>

        <div className="flex flex-wrap gap-4 mx-auto py-6">
          <Button>Buy Now</Button>
          <Button>Download </Button>
        </div>

        <div></div>
      </div>
    </>
  );
};

export default CourseDetails;
