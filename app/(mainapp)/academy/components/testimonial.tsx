
'use client';

import Image from 'next/image';

interface Testimonial {
  name: string;
  imgUrl: string;
  message: string;
}

const testimonials: Testimonial[] = [
  {
    name: '<PERSON><PERSON><PERSON>',
    imgUrl: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Dipti.jpeg`,
    message: 'RentPrompts provided me with hands-on experience in AI, enhancing my skills in prompt engineering and AI research. The supportive team and real-world projects helped me grow in problem-solving and teamwork. This internship boosted my confidence and prepared me for future opportunities in AI. I highly recommend it to anyone passionate about AI and technology!',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    imgUrl: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Kratika.jpeg`,
    message: 'RentPrompts provided the perfect platform to explore, innovate, and master generative AI in a real-world setting. The mentorship and hands-on projects enhanced my skills in prompt engineering, AI-driven applications, and problem-solving. The supportive environment helped me grow both technically and creatively. I highly recommend this internship to anyone passionate about AI and innovation!',
  },
  {
    name: '<PERSON><PERSON>',
    imgUrl: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Priya.jpeg`,
    message: 'RentPrompts has empowered me with cutting-edge AI skills, paving the way for my future in AI innovation! The hands-on experience in prompt engineering, research, and AI content optimization expanded my technical expertise. Collaborating with industry experts in a dynamic environment strengthened my problem-solving and research capabilities. I highly recommend this internship to anyone eager to dive into AI and innovation!',
  },
  {
    name: 'Purvi Chelawat',
    imgUrl: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Purvi.jpg`,
    message: 'RentPrompts provided an incredible learning journey, blending creativity, problem-solving, and hands-on AI experience. Working on real-world projects like the Email Generator RAP and Stanza Studio sharpened my skills in prompt engineering and AI research. The supportive mentorship and innovative environment boosted my confidence and career growth. I highly recommend this internship to anyone eager to explore AI-driven solutions!',
  },
  {
    name: 'Ayushi Gurjar',
    imgUrl: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/ayushi.jpeg`,
    message: 'RentPrompts has been an enriching journey where I gained hands-on experience with NestJS, Payload CMS, and AI model integration. The supportive and collaborative culture empowered my leadership and technical skills. Real-world challenges enhanced my problem-solving abilities and career growth. I felt truly valued as part of the team. "a RARE space where ideas ignite — RentPrompts fuels innovation and growth.',
  },
  {
    name: 'Shrajik Patil',
    imgUrl: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Shrajik.jpeg`,
    message: 'I joined RentPrompts in May 2024 to explore AI-driven solutions and cutting-edge prompt engineering. The opportunity to work with LangChain and contribute to impactful projects in a supportive environment was truly exciting. Collaborating with talented individuals helped me grow both technically and professionally. RentPrompts has been a game-changer in my AI journey!',
  },
];

const Testimonials = () => {
  return (
    <div className="min-w-screen min-h-screen flex items-center justify-center py-5">
      <div className="w-full px-5 py-16 md:py-24 text-gray-800">
        <div className="w-full max-w-6xl mx-auto">
          <div className="text-center  mx-auto">
          <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text mb-4">Our happy community members say about us</h2>
            {/* <h3 className="text-xl mb-5 font-light">
              Lorem ipsum dolor sit amet consectetur adipisicing elit.
            </h3> */}
            <div className="text-center mb-10">
              <span className="inline-block w-1 h-1 rounded-full bg-indigo-500 ml-1"></span>
              <span className="inline-block w-3 h-1 rounded-full bg-indigo-500 ml-1"></span>
              <span className="inline-block w-40 h-1 rounded-full bg-indigo-500"></span>
              <span className="inline-block w-3 h-1 rounded-full bg-indigo-500 ml-1"></span>
              <span className="inline-block w-1 h-1 rounded-full bg-indigo-500 ml-1"></span>
            </div>
          </div>

          <div className="-mx-3 md:flex flex-wrap">
            {testimonials.map((testimonial, i) => (
              <div key={i} className="px-3 md:w-1/3">
                <div className="w-full mx-auto rounded-lg bg-indigo-700 border border-indigo-600 p-5 text-gray-800 font-light mb-6">
                  <div className="w-full flex mb-4 items-center">
                    <div className="overflow-hidden rounded-full w-10 h-10 bg-gray-50 border border-gray-200">
                      <Image
                        src={testimonial.imgUrl}
                        alt={testimonial.name}
                        width={40}
                        height={40}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div className="flex-grow pl-3">
                      <h6 className="font-bold text-sm uppercase text-gray-100">
                        {testimonial.name}
                      </h6>
                    </div>
                  </div>
                  <div className="w-full">
                    <p className="text-sm leading-tight">
                      <span className="text-lg leading-none italic font-bold text-gray-300 mr-1">
                        &quot; {" "}
                      </span>
                      <span className='text-gray-300'>
                      {testimonial.message}
                      </span>
                      <span className="text-lg leading-none italic font-bold text-gray-300 ml-1">
                      &quot;
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Floating "Buy me a beer" */}
      {/* <div className="flex items-end justify-end fixed bottom-0 right-0 mb-4 mr-4 z-10">
        <div>
          <a
            title="Buy me a beer"
            href="https://www.buymeacoffee.com/scottwindon"
            target="_blank"
            rel="noopener noreferrer"
            className="block w-16 h-16 rounded-full transition-all shadow hover:shadow-lg transform hover:scale-110 hover:rotate-12"
          >
            <Image
              src="https://i.pinimg.com/originals/60/fd/e8/60fde811b6be57094e0abc69d9c2622a.jpg"
              alt="Buy me a beer"
              width={64}
              height={64}
              className="object-cover object-center w-full h-full rounded-full"
            />
          </a>
        </div>
      </div> */}
    </div>
  );
};

export default Testimonials;
