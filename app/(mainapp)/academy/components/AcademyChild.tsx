"use client";
import React, { useEffect, useRef, useState } from "react";
import coinImage from "../../../../public/img/coin-png.png";
import { Course, User } from "@/server/payload-types";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SpaceModal } from "@/components/SpaceModal";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import ReactMarkdown from "react-markdown";
import TestimonialsCarousel from "./testimonial";
import Vision from "@/components/ui/academy/Vision";
import { GlowingEffectDemo } from "@/components/ui/academy/acdemyProgram";

interface AcademyChildProps {
    user: User | null;
}

const AcademyChild = ({user}:AcademyChildProps) => {
  const [course, setcourse] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [showAlldescription, setShowAllDescription] = useState<Course | null>(
    null
  );
  const [hoveredCardIndex, setHoveredCardIndex] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [selectedLevel, setSelectedLevel] = useState<string>("");
  const [selectedPriceFilter, setSelectedPriceFilter] = useState<string>("");
  const [selectedTypeFilter, setSelectedTypeFilter] = useState("");
  const cardRef = useRef(null);
  const [position, setPosition] = useState("left-full"); // Default to right side

  useEffect(() => {
    if (hoveredCardIndex && cardRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect();
      const screenWidth = window.innerWidth;

      if (cardRect.right > screenWidth) {
        setPosition("right-full"); // Move left if it overflows
      } else {
        setPosition("left-full"); // Keep right if it fits
      }
    }
  }, [hoveredCardIndex]);

  const courses = [
    {
      title: "React for Beginners",
      instructor: "John Doe",
      price: "49.99",
      image: "https://via.placeholder.com/150"
    },
    {
      title: "Advanced JavaScript",
      instructor: "Jane Smith",
      price: "69.99",
      image: "https://via.placeholder.com/150"
    },
    {
      title: "Tailwind CSS Mastery",
      instructor: "Mike Johnson",
      price: "39.99",
      image: "https://via.placeholder.com/150"
    }
  ];

  

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const res = await fetch(`/api2/courses`);
        const getdata = await res.json();
        setcourse(getdata?.data);
        setFilteredCourses(getdata?.data);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    fetchCourses();
  }, []);

  const handleBuyNow = (course: any, event: React.MouseEvent) => {
    event.stopPropagation();
    if (user) {
      if (user?.coinBalance >= course.cost) {
        setSelectedCourse(course);
        setIsModalOpen(true);
      } else {
        toast.error("Insufficient Credits to buy the course");
      }
    } else {
      toast.error("Please login to Buy Course");
    }
  };

  const handleConfirmPurchase = async (course: any) => {
    try {
      toast.loading("Downloading...");
      setIsModalOpen(false);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/courses/purchaseCourse`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            courseId: course.id,
          }),
        }
      );
      const result = await response.json();
      if (response.ok) {
        setLoading(true);

        const pdfId =
          typeof course.pdf === "string" ? course.pdf : course.pdf?.id;
        // let fileName = course.pdf.filename;
        let fileName = course.pdf;
        const id = course.id;
        const type = "course";

        if (fileName === "undefined" || fileName === null) {
          try {
            const req = await fetch(
              `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files/${pdfId}`
            );
            const data = await req.json();
            fileName = data.filename;
          } catch (err) {
            console.log(err);
            toast.dismiss();
          }
        }

        if (
          !fileName ||
          fileName === "undefined" ||
          (fileName === null && !id) ||
          id === "undefined"
        ) {
          toast.dismiss();
          toast.error("Unable to downlaod file due to some reason");
          return;
        }

        fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api2/downloadFile/${type}/${id}/${fileName}/${user?.id}`
        )
          .then((response) => response.blob())
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            toast.dismiss();
            toast.success(
              "Course Purchase Successful and Credits have been deducted."
            );
            setLoading(false);
          })
          .catch(() => {
            toast.dismiss();
            toast("Failed to download file"), setLoading(false);
          });
      } else {
        toast.dismiss();
        toast.error(result.message);

        // throw new Error(result.message);
      }
    } catch (error) {
      toast.dismiss();
      toast.error(`An error occurred while purchasing the Course`);
    }
  };

  const handleCardClick = (course: Course) => {
    setShowAllDescription(course);
  };

  useEffect(() => {
    const filtered = course?.filter((course: Course) => {
      const matchesSearch = course.title
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const matchesLevel = selectedLevel
        ? course.level === selectedLevel
        : true;
      const matchesPrice =
        selectedPriceFilter === "free"
          ? course.cost === 0
          : selectedPriceFilter === "paid"
            ? course.cost > 0
            : true;
      const matchesType =
        selectedTypeFilter === "" ||
        (Array.isArray(course.type) &&
          course.type.includes(
            selectedTypeFilter as
              | "AI Assistants / Agents"
              | "Basics of DL"
              | "Agent Graph"
              | "Advance Prompt Eng"
              | "RAG"
              | "Basics of Prompt Eng"
          ));
      return matchesSearch && matchesLevel && matchesPrice && matchesType;
    });
    setFilteredCourses(filtered);
  }, [
    searchQuery,
    selectedLevel,
    selectedPriceFilter,
    course,
    selectedTypeFilter,
  ]);
  return (
    <>
    
    <GlowingEffectDemo/>
    <Vision/> 
<TestimonialsCarousel />
    <section className="rounded-2xl p-6 md:p-10 max-w-5xl mx-auto text-center space-y-10">
      <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text ">At RentPrompts - We’re RARE Academy</h2>
      <h3 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text mb-4">We celebrate the amazing success of our Next-GenAI Talent!</h3>


      <ul className="text-gray-200 text-base md:text-lg text-center grid grid-cols-1 md:grid-cols-3 gap-8">
        <li className="bg-indigo-700 border border-indigo-600 p-4 rounded-lg">
          <strong className="bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent">Skill-up Program Wins:</strong> Our champions are making an impact in top companies through immersive roles.
        </li>
        <li className="bg-indigo-700 border border-indigo-600 p-4 rounded-lg">
          <strong className="bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent">Career Growth:</strong>We’ve helped our learners transition into GenAI careers.
        </li>
        <li className="bg-indigo-700 border border-indigo-600 p-4 rounded-lg">
          <strong className="bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent">Real Impact:</strong>Our rising stars are using GenAI to make the world smarter, one solution at a time.
        </li>
      </ul>

      <p className="text-lg md:text-xl font-medium text-gray-100">
        Your success is our story! Let’s write the next chapter together.
      </p>
    </section>



      {/* <div>
      <div className="w-full flex items-center justify-center">
      <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text max-w-2xl text-center">
          Explore our free courses
        </h2>
        </div>
        <div className="mt-10 mb-5">
          <div className="flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4">

            <input
              type="text"
              className="sm:w-9/12 w-11/12 mx-auto md:mx-0 md:w-2/6 px-4 py-2 border rounded-lg text-black"
              placeholder="Search courses..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />


            <div className="flex flex-row sm:flex-row md:p-0 p-4 gap-2 md:gap-3 md:w-auto mx-auto">
              <select
                className="w-full sm:w-auto px-1 py-2 rounded-lg font-bold bg-indigo-700"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                <option value="">All Levels</option>
                <option value="advance">Advance</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
              </select>

              <select
                className="w-full sm:w-auto px-1 py-2 rounded-lg font-bold bg-indigo-700"
                value={selectedPriceFilter}
                onChange={(e) => setSelectedPriceFilter(e.target.value)}
              >
                <option value="">All Courses</option>
                <option value="free">Free Courses</option>
                <option value="paid">Paid Courses</option>
              </select>

              <select
                className="w-full sm:w-auto px-2 py-2 rounded-lg font-bold bg-indigo-700"
                value={selectedTypeFilter}
                onChange={(e) => setSelectedTypeFilter(e.target.value)}
              >
                <option value="">All Types</option>

                <option value="Agent Graph">Agent Graph</option>
                <option value="Advance Prompt Eng">
                  Advance Prompt Engineering
                </option>
                <option value="AI Assistants / Agents">
                  AI Assistants / Agents
                </option>
                <option value="Basics of DL">Basics of DL</option>
                <option value="Basics of Prompt Eng">
                  Basics of Prompt Eng
                </option>
                <option value="RAG">RAG</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="w-11/12 mx-auto md:max-w-3xl lg:max-w-6xl grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 justify-center">
        {filteredCourses?.length > 0 ? (
          filteredCourses.map((course: any, index: any) => (
            <div
            key={index}
      className="relative h-full"
      onMouseEnter={() => setHoveredCardIndex(index)}
          onMouseLeave={() => setHoveredCardIndex(null)}
      onClick={() => handleCardClick(course)}
    >
      <CardContainer className="p-2 w-full">
        <CardBody className="relative border shadow-sm rounded-xl overflow-hidden dark:border-neutral-700 cursor-pointer flex flex-col h-auto w-full">

          <div className="relative">
            <Image
              src={
                course?.image ||
                "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSU_deW0kRi7dabGk6lVHIdhzKsgnHgad5MOt6A8FZkiVtiZx9UTbSyh0gmCQ3plai6jVw&usqp=CAU"
              }
              alt={course.name}
              width={100}
              height={80}
              objectFit="cover"
              className="w-full h-60 sm:h-52 md:h-44 object-cover aspect-video"
            />
            {course?.isFeatured && (
              <div className="absolute top-0 -left-2 px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                  FEATURED
                </div>
              </div>
            )}
          </div>


          <div className="relative z-10 flex flex-col p-4 bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 h-48">
            <CardItem translateZ="40" className="text-lg font-bold text-white line-clamp-1">
              {course.organization}{course.title}
            </CardItem>

            <CardItem
              as="p"
              translateZ="40"
              className="text-gray-300 text-sm mt-2 line-clamp-3"
            >
              {course.description || "Effortlessly generate stunning images using our AI model."}
            </CardItem>

            <CardItem
              as="p"
              translateZ="40"
              className="text-gray-300 absolute bottom-2 text-sm flex items-center bg-white/20 px-2 py-1 rounded-lg"
            >

              {course.cost === 0 ? (
                <span className="ml-1">Free</span>
              ) : (
                <span className="ml-1 flex items-center">
                  {course.cost}
                  <img
                    src="/coin.png"
                    alt="Coin"
                    className="ml-1 w-4 h-4"
                    loading="lazy"
                  />
                </span>
              )}
            </CardItem>

            <CardItem
              as="p"
              translateZ="40"
              className="text-gray-300 absolute bottom-10 text-sm flex items-center bg-white/20 px-2 py-1 rounded-lg"
            >
              {course.level}
            </CardItem>


            <div className="mt-auto absolute bottom-0 right-0 p-2 flex justify-end">
              <CardItem
                translateZ={40}
                className="flex items-center justify-center px-4 py-2 rounded-lg bg-indigo-100 text-indigo-700 hover:text-white text-xs font-bold hover:bg-indigo-600"
              >
                <ArrowRight className="w-4 h-4" />
              </CardItem>
            </div>
          </div>
        </CardBody>
      </CardContainer>


      {hoveredCardIndex === index && (
          <div
          ref={cardRef}
          className={`absolute max-md:hidden ${position} z-40 top-0 bg-white text-black p-6 rounded-2xl shadow-lg w-96 border border-gray-200`}
        >

          <h2 className="text-xl font-bold">
            The Complete AI-Powered Copywriting Course & ChatGPT Course
          </h2>
  

          <p className="text-gray-600 text-sm mt-2">
            26.5 total hours · All Levels · Subtitles
          </p>
  

          <p className="text-gray-700 text-sm mt-3">
            Become a Pro Copywriter with the Complete Copywriting and Content Marketing Course.
            Use ChatGPT. Get 70+ Pro Templates.
          </p>
  

          <ul className="mt-3 space-y-2 text-gray-700 text-sm">
            <li className="flex items-start">
              ✅ <span className="ml-2">Course Fully Updated: Dive into animated videos, 50 writing assignments, and 60 interactive quizzes.</span>
            </li>
            <li className="flex items-start">
              ✅ <span className="ml-2">Mastering ChatGPT for Content Creation: Learn to generate creative content, SEO, social media, and blogs.</span>
            </li>
            <li className="flex items-start">
              ✅ <span className="ml-2">Advanced Copywriting Skills: Convert website visitors into buyers using persuasive techniques.</span>
            </li>
          </ul>
  

          <button className="bg-purple-600 text-white w-full py-3 rounded-lg mt-4 font-semibold hover:bg-purple-700 transition">
            Add to cart
          </button>
        </div>
      )}
    </div>
          ))
        ) : (
          <div className="text-center text-white text-2xl font-bold">
            No courses available.
          </div>
        )}

      </div> */}



      {isModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">
              {selectedCourse.cost === 0 ? (
                "This course is free and No credits will be deducted for this course. Confirm your purchase"
              ) : (
                <div>
                  The Course Price {selectedCourse.cost}
                  <img
                    src={coinImage.src}
                    alt="Coin"
                    style={{
                      width: "25px",
                      height: "27px",
                      display: "inline",
                      margin: "0px 4px 4px 2px",
                    }}
                     loading="lazy"
                  />
                  Credits will be deducted from your account for this purchase.
                </div>
              )}
            </p>
            {errorMessage && <p className="text-red-500">{errorMessage}</p>}
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Close
              </Button>
              <Button
                variant="outline"
                onClick={() => handleConfirmPurchase(selectedCourse)}
              >
                Confirm
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}

      {showAlldescription && (
        <SpaceModal
          title=""
          description=""
          isOpen={true}
          onClose={() => {
            setShowAllDescription(null); // Close the modal by setting the state to null
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold capitalize">{showAlldescription?.title}</p>
            <div className="text-lg max-h-72 overflow-y-auto">
              <ReactMarkdown
                components={{
                  a: ({ href, children }) => (
                    <a
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline "
                    >
                      {children}
                    </a>
                  ),
                }}
                className="first-letter:uppercase"
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {showAlldescription?.description}
              </ReactMarkdown>
            </div>
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAllDescription(null)}
              >
                Close
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}
    </>
  );
};

export default AcademyChild;
