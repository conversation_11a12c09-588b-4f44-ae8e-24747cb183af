'use client';

import { useEffect, useRef } from 'react';

interface Client {
  name: string;
  logoUrl: string;
}
const clients: Client[] = [
  {
    name: 'Alphanexis LLC',
    logoUrl: 'https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/alphanexis_logo.jpg',
  },
  {
    name: 'Popm Consulting',
    logoUrl: 'https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/Popm%20new%20Logo-04.png',
  },
  {
    name: 'Truly Digitally LLP',
    logoUrl: 'https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/d7080b7a-b80d-4fd4-9223-a44a11033abd.png',
  },
  {
    name: 'Quantic Cloud Technologies',
    logoUrl: 'https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/1630604292031.jpg',
  },
  {
    name: 'Adwallz Outdoor Advertising',
    logoUrl: 'https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/cropped-adwallz-logo-99x66.png',
  },
  {
    name: 'Learning Curve Technologies',
    logoUrl: 'https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/learning_curve_technologies_cover.jpeg.jpg',
  },
];

const AboutUsClients = () => {
  const marqueeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!marqueeRef.current) return;

    const marquee = marqueeRef.current;
    const marqueeContent = marquee.firstElementChild as HTMLDivElement;

    if (!marqueeContent) return;

    marqueeContent.innerHTML = '';
    const clientsDuplicated = [...clients, ...clients];
    
    clientsDuplicated.forEach((client, i) => {
      const item = document.createElement('div');
      item.className = 'flex-shrink-0 px-6 flex items-center justify-center';
      
      const isLearningCurve = client.name === 'Learning Curve Technologies';
      
      item.innerHTML = `
        <div class="flex flex-col items-center justify-center w-[200px]">
          <div class="w-24 h-24 rounded-full bg-white flex items-center justify-center p-1 mb-4 relative overflow-hidden border-2 border-indigo-400/20 hover:border-indigo-400/40 transition-all">
            <div class="absolute inset-0 flex items-center justify-center p-0 bg-white">
              <img 
                src="${client.logoUrl}" 
                alt="${client.name}" 
                class="w-full h-full ${isLearningCurve ? 'object-contain scale-110' : 'object-contain'}" 
                style="
                  filter: drop-shadow(0 0 4px rgba(0,0,0,0.1));
                  ${isLearningCurve ? 'transform: scale(1.1);' : ''}
                "
                onload="
                  if(${isLearningCurve ? 'true' : 'false'}) {
                    this.style.padding = '0';
                    this.style.objectFit = 'contain';
                    this.style.transform = 'scale(1.1)';
                  } else {
                    this.parentElement.style.padding = this.naturalWidth > this.naturalHeight ? '0 15%' : this.naturalHeight > this.naturalWidth ? '15% 0' : '0';
                  }
                "
              />
            </div>
          </div>
          <span class="text-white text-center font-medium">${client.name}</span>
        </div>
      `;
      marqueeContent.appendChild(item);
    });

    let animationFrameId: number;
    let lastTimestamp = 0;
    const scrollSpeed = 1;

    const animate = (timestamp: number) => {
      if (!lastTimestamp) lastTimestamp = timestamp;
      const delta = timestamp - lastTimestamp;
      lastTimestamp = timestamp;

      marquee.scrollLeft += scrollSpeed * (delta / 16);

      if (marquee.scrollLeft >= marqueeContent.scrollWidth / 2) {
        marquee.scrollLeft = 0;
      }

      animationFrameId = requestAnimationFrame(animate);
    };

    animationFrameId = requestAnimationFrame(animate);

    const handleMouseEnter = () => {
      cancelAnimationFrame(animationFrameId);
    };
    const handleMouseLeave = () => {
      lastTimestamp = 0;
      animationFrameId = requestAnimationFrame(animate);
    };

    marquee.addEventListener('mouseenter', handleMouseEnter);
    marquee.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      cancelAnimationFrame(animationFrameId);
      marquee.removeEventListener('mouseenter', handleMouseEnter);
      marquee.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <div className="min-w-screen min-h-screen flex items-center justify-center py-5 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-950 via-indigo-900 to-gray-950">
      <div className="absolute inset-0 bg-[size:70px] opacity-10" />
      
      <div className="w-full px-2 py-16 md:py-24 text-gray-800 relative z-10">
        <div className="w-full max-w-full mx-auto">
          <div className="text-center mx-auto">
            <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text pb-2">
              Our Happy Clients, Partners, and Communities
            </h2>
            <div className="text-center mt-6 mb-10">
              <span className="inline-block w-1 h-1 rounded-full bg-indigo-400 ml-1"></span>
              <span className="inline-block w-3 h-1 rounded-full bg-indigo-400 ml-1"></span>
              <span className="inline-block w-40 h-1 rounded-full bg-indigo-400"></span>
              <span className="inline-block w-3 h-1 rounded-full bg-indigo-400 ml-1"></span>
              <span className="inline-block w-1 h-1 rounded-full bg-indigo-400 ml-1"></span>
            </div>
          </div>

          <div
            ref={marqueeRef}
            className="w-full overflow-hidden relative py-6"
            style={{
              maskImage: 'linear-gradient(90deg, transparent 0%, black 20px, black calc(100% - 20px), transparent 100%)',
            }}
          >
            <div className="flex w-max items-center">
            </div>
          </div>
        </div>
      </div>

      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -bottom-20 -left-20 w-64 h-64 rounded-full bg-indigo-800/10 blur-3xl" />
        <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-purple-800/10 blur-3xl" />
      </div>
    </div>
  );
};

export default AboutUsClients;