import { DemoHeroGeometric } from "@/components/ui/aboutus/aboutBg/demo";
import { HeroGeometric } from "@/components/ui/aboutus/aboutBg/shape-landing-hero";
import { SplineSceneBasic } from "@/components/ui/aboutus/aboutbot/demo";
import AboutUsQueries from "@/components/ui/aboutus/aboutusqueries";
import TimelineDemo from "@/components/ui/aboutus/horizontal";
import AboutUsClients from "./components/testimonial";

// import HorizontalScrollGrid from "@/components/ui/aboutus/horizontal";
import React from "react";

const About = () => {

  return (
    <>
      <SplineSceneBasic />
      <HeroGeometric 
            title1 = "Our Vision"
            content= "Our vision Is to enable human in AI collaboration in order to democratize generative AI, making it responsive, affordable, relevant, and easy for all. We aim to revolutionize content creation and automation for individuals and businesses."/>
      <DemoHeroGeometric />
      {/* <section className="px-40 justify-center align-middle ">

      </section> */}

      {/* <section className="container my-[50px]">
        <OurProgress />
      </section> */}
      <section className="mt-[80px] mb-[50px]">
        <TimelineDemo />
      </section>
      <section>
      <AboutUsClients/>
      <section id="write-to-us">
     <AboutUsQueries showFields/>        

      </section>

      </section>
    </>
  );
};

export default About;