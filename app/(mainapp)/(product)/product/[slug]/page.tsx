import ImageSlider from "@/components/ImageSlider";
import ProductReel from "@/components/ProductReel";
import { PRODUCT_CATEGORIES } from "@/constants";
import { getPayloadClient } from "@/server/get-payload";
import { Check } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import Image from "next/image";
import BuyNowProductWrapper from "@/components/BuyNowProductWrapper";

import { Product, User } from "@/server/payload-types";
import { cookies } from "next/headers";
import { getServerSideUser } from "../../../../../lib/payload-utils";
import Loader from "@/components/Loader";
import Container from "@/components/ui/container";
import { Metadata, ResolvingMetadata } from "next";
import ProductLikeButton from "@/components/ProductLikeButton";
import DescriptionToggle from "../../../../../components/DescriptionToggle";
import StarRating from "@/components/RatingStar";
import { log } from "node:console";
import { AnimatedModelProduct } from "../../../../../components/AnimatedModelProduct";
import { Icons } from "@/components/Icons";

interface PageProps {
  params: {
    slug: string;
  };
}

const BREADCRUMBS = [
  { id: 1, name: "Home", href: "/" },
  { id: 2, name: "Products", href: "/products" },
];

export async function generateMetadata(
  { params: { slug } }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const payload = await getPayloadClient();

  const products: any = await payload.find({
    collection: "products",
    // limit: 1,
    where: {
      slug: {
        equals: slug,
      },
      approvedForSale: {
        equals: "approved",
      },
    },
  });
  const ProductData = products?.docs[0] || "";

  const producturl = `${process.env.PAYLOAD_PUBLIC_SITE_URL}/product/${ProductData.slug}`;
  // const validUrls = ProductData.images?.image?.url;
  const validUrls = ProductData.images
    ? (ProductData.images
        .map((img: any) => img.image?.url) // Access only `image.url`
        .filter(Boolean)[0] as string[]) // Filter out any undefined or null URLs
    : [];
  // console.log("validUrls", validUrls);

  const metadata =
    process.env.SERVER_ENV === "prod"
      ? {
          title: ProductData?.name,
          description: ProductData?.description,
          alternates: {
            canonical: `/product/${ProductData.slug}`,
          },
          openGraph: {
            title: ProductData.title,
            url: producturl,
            siteName: "Rentprompt.com",
            images: validUrls,
          },
          verification: {
            google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
          },
        }
      : null;

  return metadata;
}

const Page = async ({ params: { slug } }: PageProps) => {
  // const { productId } = params;

  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  // if (response.status === 401) {
  //   toast.error("Session expired. Please log in again.");
  //   redirect('/sign-in');
  //   return <div>Loading...</div>;
  // }

  const { user } = response;
  // if (!user) {
  //   redirect("/sign-in"); // Redirect to login page
  //   return null; // Ensure no further rendering
  // }
  const payload = await getPayloadClient();

  const { docs: products } = await payload.find({
    collection: "products",
    limit: 1,
    where: {
      slug: {
        equals: slug,
      },
      // approvedForSale: {
      //   equals: "approved",
      // },
    },
  });

  const [product]: any = products;

  if (!product) return notFound();

  const label = PRODUCT_CATEGORIES.find(
    ({ value }) => value === product.category
  )?.label;

  const validUrls = product.images
    .map(({ image }: any) => (typeof image === "string" ? image : image?.url))
    .filter(Boolean) as string[];
  const productId = product.id;
  const userName = (product?.user as User)?.user_name;
  const userEmailName = (product?.user as User)?.email?.split("@")[0];
  const userId = (product?.user as User)?.id;
  const userImage = (product?.user as any)?.profileImage?.url;
  const likes = (product.likes as string[]).length;
  const isInitiallyLiked = product.likes.some(
    (like: any) => like.user?.id === user?.id
  );

  const getRelativeTime = (dateString: string): string => {
    const createdAt = new Date(dateString).getTime(); // Convert to timestamp (number)
    const now = new Date().getTime(); // Convert to timestamp (number)
    const diffInMs = now - createdAt; // Now both are numbers

    const diffInSec = Math.floor(diffInMs / 1000);
    const diffInMin = Math.floor(diffInSec / 60);
    const diffInHours = Math.floor(diffInMin / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInMonths / 12);

    if (diffInYears > 0)
      return ` ${diffInYears} Year${diffInYears > 1 ? "s" : ""} ago`;
    if (diffInMonths > 0)
      return ` ${diffInMonths} Month${diffInMonths > 1 ? "s" : ""} ago`;
    if (diffInDays > 0)
      return ` ${diffInDays} Day${diffInDays > 1 ? "s" : ""} ago`;
    if (diffInHours > 0)
      return ` ${diffInHours} Hour${diffInHours > 1 ? "s" : ""} ago`;
    if (diffInMin > 0)
      return ` ${diffInMin} Minute${diffInMin > 1 ? "s" : ""} ago`;
    return "Created just now";
  };

  return (
    <>
      <Loader />
      <Container>
        <div className="px-4">
          <div className="bg-background pt-4 md:mt-10 mb-24">
            <div className="lg:p-14 p-4 md:flex  gap-3 lg:gap-7 lg:mx-20 shadow-input rounded-xl mt-8 md:mt-16 shadow-2xl bg-gradient-to-r from-indigo-900 to-indigo-600">
              <div className="w-full md:w-[60%] mx-auto">
                {/* Product images */}
                {/* {products[0]?.approvedForSale === "denied" && (
                  <div className="bg-red-100 text-red-700 px-4 py-2 rounded-lg border border-red-400">
                    <strong>Notice:</strong> Your Product has been denied by the
                    admin. <strong>Reason:</strong>{" "}
                    {products[0]?.adminReview as string}
                  </div>
                )} */}
                <div className="rounded-lg overflow-hidden">
                  <ImageSlider urls={validUrls} label={label} />
                </div>

                {/* Product Details */}
                <div className="border-x border-b border-muted mt-6">
                  <div className="lg:px-4 px-2 pt-1">
                    <div className="mt-4 flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                      <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-50 capitalize">
                        {product?.name}
                      </h1>
                      {products[0]?.approvedForSale === "approved" && (
                        <ProductLikeButton
                          productId={productId}
                          initialLikes={likes}
                          isInitiallyLiked={isInitiallyLiked}
                          userId={user?.id ?? null}
                        />
                      )}
                    </div>
                    {/* {JSON.stringify(product?.user?.id)} */}

                    <div className="flex mt-4 justify-between items-center">
                      <div className="flex items-center">
                        <div className="rounded-full w-10 h-10 overflow-hidden">
                          <Image
                            className="h-auto w-10 rounded-full"
                            src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/2.png`}
                            alt=""
                            width={40}
                            height={40}
                          />
                        </div>
                        <Link
                          className="text-white ml-2 text-base md:text-xl first-letter:uppercase"
                          href={`/users/${userName}`}
                        >
                          {/* {(userName ?? userEmailName == undefined)
                            ? "Anonymous"
                            : (userEmailName ?? userName)} */}
                          {userName ? userName : "Anonymous"}
                        </Link>
                      </div>
                      <div className="flex items-center flex-col">
                        <div className="flex gap-[2px] items-center">
                          {products[0]?.approvedForSale === "approved" ? (
                            <>
                              <Check
                                aria-hidden="true"
                                className="h-5 w-5 md:h-7 md:w-5 flex-shrink-0 text-green-500 font-semibold"
                                style={{ strokeWidth: "5" }}
                              />
                              <p className="text-sm md:text-lg text-white">
                                Verified
                              </p>
                            </>
                          ) : (
                            <>
                              {products[0]?.approvedForSale === "pending" && (
                                <Icons.pending className="h-5 w-5 md:h-7 md:w-5" />
                              )}
                              {products[0]?.approvedForSale === "denied" && (
                                <Icons.denied />
                              )}

                              <p className="text-sm md:text-lg text-white first-letter:uppercase">
                                {products[0]?.approvedForSale as string}
                              </p>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <section className="mt-4">
                      <DescriptionToggle description={product.description} />
                    </section>
                    {products[0]?.approvedForSale === "approved" && (
                      <div className="mt-2 flex gap-3">
                        <div>
                          <StarRating user={user} productId={product.id} />
                        </div>
                      </div>
                    )}
                    <div className="flex justify-between mt-4">
                      <div>
                        {product.affiliated_with && (
                          <div>
                            Affiliated With :{" "}
                            <a
                              href={product.affiliated_with}
                              className="text-green-500 underline"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {product.affiliated_with.substring(0, 30) + "..."}
                            </a>
                          </div>
                        )}
                      </div>
                      <div className="text-sm  bg-white/30 text-white px-2 py-1 rounded">
                        {" "}
                        {getRelativeTime(product.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-transparent via-neutral-300 dark:via-neutral-700 to-transparent my-6 h-[1px] w-full" />
                  {/* Add to cart part */}
                  <div className="px-4 pb-6">
                    <div className="flex justify-between items-center pb-4">
                      <div className="font-bold text-white text-3xl flex items-center gap-1">
                        {product.price === 0 ? (
                          <div className="px-4 py-1 rounded-full bg-gradient-to-r  text-white text-2xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                            FREE
                          </div>
                        ) : (
                          product.price
                        )}
                        {product.price === 0 ? (
                          ""
                        ) : (
                          <img
                            src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                            alt="Coin"
                            style={{ width: "30px", height: "31px" }}
                            className="mt-[2px]"
                            loading="lazy"
                          />
                        )}
                      </div>
                      <div className="ml-4 rounded bg-pink-500 text-white px-2 pb-[3px] md:px-2 text-base md:text-md uppercase">
                        {product.product_type}
                      </div>
                    </div>
                    {product?.user.id !== user?.id && (
                      <BuyNowProductWrapper product={product} user={user} />
                    )}
                  </div>
                  {products[0]?.approvedForSale === "approved" && (
                    <div className="">
                      <AnimatedModelProduct
                        user={user}
                        productId={product.id}
                        product={product}
                      />
                    </div>
                  )}
                </div>
              </div>
              <ScrollArea className="hidden md:block md:w-[40%] h-[528px] md:h-[828px]">
                <div className="flex flex-col gap-4">
                  {validUrls.map((url, i) => (
                    <div key={i} className="rounded-lg overflow-hidden">
                      <img
                        className="rounded-lg aspect-square object-cover"
                        src={url}
                        alt="Product image"
                        // width={410}
                        // height={410}
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
                <ScrollBar orientation="vertical" />
              </ScrollArea>
            </div>
          </div>

          {product?.user?.id !== user?.id && (
            <ProductReel
              href="/products"
              query={{ category: product.category, limit: 4 }}
              title={`Similar products`}
              subtitle={`Browse similar products just like '${product.name}'`}
              user={user}
            />
          )}
        </div>
      </Container>
    </>
  );
};

export default Page;
