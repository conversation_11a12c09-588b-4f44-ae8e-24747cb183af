"use client";
import React, { useState } from "react";
import { useSearchParams } from "next/navigation";


import { toast } from "sonner";
import { useRouter } from "next/navigation";

const Checkout = () => {
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState<boolean>(true);
  const router = useRouter();

  const packageId = searchParams.get("packageId");
  const packageName = searchParams.get("packageName");
  const packageAmount = searchParams.get("packageAmount");
  const packageTax = searchParams.get("packageTax");
  const totalPaidAmount = searchParams.get("totalPaidAmount");
  const receivedCoins = searchParams.get("receivedCoins");
  const userName = searchParams.get("userName");
  const userEmail = searchParams.get("userEmail");


  const formatIndianNumber = (number) => {
    const numStr = number.toString();
    const parts = numStr.split('.');
    let wholePart = parts[0];
    const decimalPart = parts[1] || '';
  
    // Add commas in Indian style
    let lastThree = wholePart.slice(-3);
    let otherDigits = wholePart.slice(0, -3);
    if (otherDigits !== '') {
      lastThree = ',' + lastThree;
    }
    let result = otherDigits.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
  
    // Add decimal part if it exists
    if (decimalPart) {
      result += '.' + decimalPart;
    }
  
    return result;
  };
  // -------------------------- Razorpay Handler ----------------------------------

  const loadRazorpayScript = () => {
    console.log("script loaded")
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        reject(new Error('Failed to load Razorpay script'));
      };
      document.body.appendChild(script);
    });
  };
  const displayRazorpay = async ({
    totalCoins,
    pricePackageId,
  }: {
    totalCoins: string;
    pricePackageId: string;
  }) => {
    try {
      await loadRazorpayScript();
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/orders/buy-coins`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
          body: JSON.stringify({
            paymentMethod: "RAZORPAY",
            totalCoins: totalCoins,
            pricePackageId: pricePackageId,
          }),
        }
      );

      if (!result) {
        console.log("Server error. Are you online?");
        return;
      }
      const body = await result.json();

      if (body.success) {
        const { id, receipt, amount, currency, status } = body.data;
        const options = {
          key: process.env.RAZORPAY_KEY, // need to remove hardcoded keys
          amount: new String(amount),
          currency: currency ? currency : "INR",
          name: "RentPrompts",
          description: "User purchase of credits for generative AI and Cloud Infra services",
          order_id: id ? id : body.data.id,
          handler: async function (response: any) {
            const data = {
              orderCreationId: receipt,
              razorpayPaymentId: response.razorpay_payment_id,
              razorpayOrderId: response.razorpay_order_id,
              razorpaySignature: response.razorpay_signature,
            };

            // Console log for debugging (optional)
            // console.log('data sent to razor pay ::', JSON.stringify(data));

            toast.loading("Verifying Your Payment...");

            try {
              const result = await fetch(
                `${process.env.NEXT_PUBLIC_SERVER_URL}/api/verify-razorpay`,
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    "Access-Control-Allow-Origin": "*",
                    "x-razorpay-signature": data.razorpaySignature,
                  },
                  credentials: "include",
                  body: JSON.stringify(data),
                }
              );

              const body = await result.json();
              // console.log("final response: ", body);

              if (!result.ok) {
                toast.dismiss();
                toast.warning("Your Payment is Pending...")
                return router.push(
                  `${process.env.NEXT_PUBLIC_SERVER_URL}/payment-success?orderId=${body.data?.OrderId}`
                );
              }

              if (
                body.data.status === "completed" &&
                body.data.isPaid === true
              ) {
                toast.dismiss();
                return router.push(
                  `${process.env.NEXT_PUBLIC_SERVER_URL}/payment-success?orderId=${body.data?.OrderId}`
                );
              }

              if (body.data.status === "pending") {
                toast.loading("Your Payment is Pending...");
                return router.push(
                  `${process.env.NEXT_PUBLIC_SERVER_URL}/payment-success?orderId=${body.data?.OrderId}`
                );
              }

              if (body.data.status === "rejected") {
                toast.error("Your Payment has been Failed, Please try again later");
              }
            } catch (error) {
              console.error("Error verifying payment:", error);
            }
          },
          prefill: {
            name: userName,
            email:userEmail,
            // contact: "9131233767",
          },
          notes: {
            address: "Razorpay Corporate Office",
          },
          theme: {
            color: "#3399cc",
          },
        };
        
        const paymentObject = new window.Razorpay(options);
        paymentObject.open();
      } else {
        // console.log("some error loading razor pay");
        toast.error(
          "Something went wrong while processing your request, Please reach out to support team. "
        );
      }
    } catch (err: any) {
      console.log(err);
      toast.error(
        "Something went wrong while processing your request, Please reach out to support team."
      );
    } finally {
      //console.log("inside final");
      setLoading(false);
    }
  };

  // -------------------------- PhonePe Handler ----------------------------------
  const handlePhonePe = async ({
    totalCoins,
    pricePackageId,
  }: {
    totalCoins: string;
    pricePackageId: string;
  }) => {

    if (packageName === "Enterprice") {
      router.push("/aboutus#about-us");
    } else {
      try {
        const result = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/orders/buy-coins`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            credentials: "include",
            body: JSON.stringify({
              paymentMethod: "PHONEPE",
              totalCoins: totalCoins,
              pricePackageId: pricePackageId,
            }),
          }
        );

        if (!result) {
          console.log("Server error. Are you online?");
          return;
        }
        const body = await result.json();
        if (body.success) {
          const { OrderId, Url } = body.data;

          return router.push(Url); // redirect to phonepe modal
        } else {
          toast.error(
            "Error initiating payment, Please reach out to support team."
          );
        }
        setLoading(false);
      } catch (error) {
        toast.error(
          "Error initiating payment, Please reach out to support team."
        );
        return;
      }
    }
  };

  const paymentGatewayCharge = (parseInt(packageAmount) * 0.02).toFixed(2);

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-white shadow-lg rounded-lg max-w-md w-full p-6 space-y-6">
        <h1 className="text-2xl font-bold text-gray-800 text-center">
          Checkout
        </h1>

        <div className="space-y-4 text-lg">
          <div className="flex justify-between text-black font-bold">
            <span>Order Summary:</span>
          </div>
          <div className="flex justify-between text-gray-700">
            <span>Package Name:</span>
            <span className="font-semibold">{packageName}</span>
          </div>
          <div className="flex justify-between text-gray-700">
            <span>Package Amount:</span>
            <span className="font-semibold">₹{formatIndianNumber(packageAmount)}</span>
          </div>
          <div className="flex justify-between text-gray-700">
            <span>Number of Credits:</span>
            <span className="font-semibold flex items-center">
              {formatIndianNumber(receivedCoins)}
              <img
                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                alt="Coin"
                style={{ width: "20px", height: "20px" }}
                loading="lazy"
              />
            </span>
          </div>
          <div className="flex justify-between text-gray-700">
            <span>GST:</span>
            <span className="font-semibold">₹{packageTax}</span>
          </div>
          <div className="flex justify-between text-gray-700">
            <span>Payment Gateway Charge:</span>
            <span className="font-semibold">₹{(formatIndianNumber(packageAmount) * 0.02).toFixed(2)}</span>
          </div>

          <div className="border-t pt-4 flex justify-between text-gray-700">
            <span className="font-bold text-lg">Total Paid:</span>
            <span className="font-bold text-lg">
  ₹{formatIndianNumber(Number(totalPaidAmount) + Number(paymentGatewayCharge))}
</span>
          </div>
        </div>

        <div className="space-y-4">
          <button
            className="w-full bg-purple-600 text-white py-3 rounded-lg flex items-center justify-center space-x-2 hover:bg-purple-700 transition"
            onClick={() =>
              handlePhonePe({
                pricePackageId: packageId,
                totalCoins: packageAmount,
              })
            }
          >
            <img
              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/phonepe%20logo.png`}
              alt="PhonePe"
              className="w-6 h-6"
              loading="lazy"
            />
            <span>Pay with PhonePe</span>
          </button>
          <button
            className="w-full bg-blue-600 text-white py-3 rounded-lg flex items-center justify-center space-x-2 hover:bg-blue-700 transition"
            onClick={() =>
              displayRazorpay({
                pricePackageId: packageId,
                totalCoins: packageAmount,
              })
            }
          >
            <img
              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/razorpay%20logo.png`}
              alt="Razorpay"
              className="w-6 h-6"
              loading="lazy"
            />
            <span>Pay with Razorpay</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
