"use client";
import { useNotifications } from "@/app/hooks/useNotifications";
import NotificationList from "@/components/notification/notificationList";
import Container from "@/components/ui/container";
import { BellRing, CheckCheck, ChevronLeft, ChevronRight } from "lucide-react";
import React, { useState } from "react";

export default function Notification() {
  const {
    notifications,
    markAsRead,
    unreadCount,
    totalPages,
    currentPage,
    filter,
    changePage,
    changeFilter,
    readCount
  } = useNotifications();

  const markAllAsRead = () => {
    notifications.forEach((notif) => {
      if (!notif.read) {
        markAsRead(notif.id);
      }
    });
  };

  return (
    <div className="min-h-screen">
      <Container>
        <div className="max-w-5xl mx-auto pt-20 px-4">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-3">
              <BellRing className="h-7 w-7 text-white" strokeWidth={1.5} />
              <h1 className="text-2xl font-bold text-gray-100">
                Notifications
              </h1>
            </div>

            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="flex items-center gap-1 text-sm font-medium text-white hover:text-gray-100 transition-colors"
              >
                <CheckCheck size={16} />
                Mark all as read
              </button>
            )}
          </div>

          <div className="bg-indigo-700 rounded-xl shadow-lg overflow-hidden border border-indigo-100 min-h-[80vh] md:min-h-[50vh]">
            <div className="flex border-b border-indigo-100">
            
              {["all", "unread", "read"].map((tab) => (
                <button
                  key={tab}
                  onClick={() => changeFilter(tab as "all" | "unread" | "read")}
                  className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
                    filter === tab
                      ? "bg-indigo-600 text-white"
                      : "text-white hover:bg-indigo-600"
                  }`}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)} (
                  {tab === "all"
                    ? unreadCount + readCount
                    : tab === "unread"
                      ? unreadCount
                      : readCount}
                  )
                </button>
              ))}
            </div>

            <div className="divide-y divide-indigo-100">
              {notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <BellRing
                    className="h-12 w-12 text-indigo-200 mx-auto mb-3"
                    strokeWidth={1.5}
                  />
                  <p className="text-lg font-medium text-indigo-900 mb-1">
                    No {filter !== "all" ? filter : ""} notifications
                  </p>
                  <p className="text-sm text-indigo-500">
                    {filter === "all"
                      ? "You don't have any notifications yet"
                      : filter === "unread"
                        ? "You've read all your notifications"
                        : "You don't have any read notifications"}
                  </p>
                </div>
              ) : (
                <>
                  {notifications.map((notif) => (
                    <NotificationList
                      key={notif.id}
                      notif={notif}
                      onClick={() => markAsRead(notif.id)}
                    />
                  ))}

                  {/* Pagination Controls */}
                 
                  {totalPages > 1 && (
                    <div className="flex justify-between items-center p-4 border-t border-indigo-600">
                      <button
                        onClick={() => changePage(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`flex items-center gap-1 text-sm font-medium ${
                          currentPage === 1
                            ? "text-indigo-400 cursor-not-allowed"
                            : "text-white hover:text-indigo-200 cursor-pointer"
                        }`}
                      >
                        <ChevronLeft size={16} />
                        Previous
                      </button>

                      <div className="text-white text-sm">
                        Page {currentPage} of {totalPages}
                      </div>

                      <button
                        onClick={() => changePage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`flex items-center gap-1 text-sm font-medium ${
                          currentPage === totalPages
                            ? "text-indigo-400 cursor-not-allowed"
                            : "text-white hover:text-indigo-200 cursor-pointer"
                        }`}
                      >
                        Next
                        <ChevronRight size={16} />
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {notifications.length > 0 && (
            <p className="text-xs text-center text-indigo-400 mt-4">
              Notifications are automatically cleared after 30 days
            </p>
          )}
        </div>
      </Container>
    </div>
  );
}
