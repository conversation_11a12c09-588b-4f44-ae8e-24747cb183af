"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

const UserJourney = () => {
  const [step, setStep] = useState(1);
  const [userType, setUserType] = useState("");
  const [username, setUsername] = useState("");
  const [role, setRole] = useState("");
  const [hobbies, setHobbies] = useState([]);
  const [is18, setIs18] = useState(false);
  const router = useRouter();

  const handleHobbyChange = (hobby) => {
    setHobbies((prev) =>
      prev.includes(hobby) ? prev.filter((i) => i !== hobby) : [...prev, hobby]
    );
  };

  const handleNext = () => {
    if (step === 1 && !userType) {
      alert("Please select if you are an Enterprise or Individual.");
    } else if (step === 2 && !role) {
      alert("Please select a role.");
    } else if (step === 3 && (!is18 || hobbies.length === 0)) {
      alert("Please confirm you are 18 and select at least one interest.");
    } else {
      setStep(step + 1);
    }
  };

  const handleSkip = () => {
    router.push("/");
  };

  const handleSubmit = () => {
    window.location.href = "/";
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-r from-blue-500 to-purple-500 relative">
      <div className="absolute inset-0 bg-black bg-opacity-20 backdrop-blur-lg"></div>

      <div className="relative z-10 max-w-lg w-full mx-4 md:mx-0">
        {step === 1 && (
          <motion.div
            className="bg-gray-900/30 text-white p-8 rounded-lg shadow-lg transition-transform duration-300"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <h2 className="text-3xl font-bold mb-6 text-purple-400 text-center">
              🤔 Are you an Enterprise or Individual?
            </h2>

            <div className="mb-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {["Enterprise", "Individual"].map((type) => (
                  <label
                    key={type}
                    className="flex items-center p-4 border border-gray-700 rounded-lg transition-all duration-300 hover:bg-gray-800 cursor-pointer"
                  >
                    <input
                      type="radio"
                      value={type}
                      checked={userType === type}
                      onChange={() => setUserType(type)}
                      className="mr-2"
                    />
                    <span className="font-medium">{type}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={handleSkip}
                className="px-4 py-2 border border-gray-600 rounded hover:border-gray-700/50 transition duration-300 transform hover:scale-105 text-white"
              >
                Skip
              </button>
              <button
                onClick={handleNext}
                className="px-4 py-2 border border-purple-600 text-white rounded transition duration-300 transform hover:border-purple-400/50 hover:scale-105 shadow-lg"
              >
                Next
              </button>
            </div>
          </motion.div>
        )}

        {step === 2 && (
          <motion.div
            className="bg-gray-900/30 text-white p-8 rounded-lg shadow-lg transition-transform duration-300"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <h2 className="text-3xl font-bold mb-6 text-center text-purple-400">
              📝 What is your role?
            </h2>

            <div className="mb-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {["Developer", "Student", "Creator", "Entrepreneur"].map(
                  (r) => (
                    <label
                      key={r}
                      className="flex items-center p-4 border border-gray-700 rounded-lg transition-all duration-300 hover:bg-gray-800 cursor-pointer"
                    >
                      <input
                        type="radio"
                        value={r}
                        checked={role === r}
                        onChange={() => setRole(r)}
                        className="mr-2"
                      />
                      <span className="font-medium">{r}</span>
                    </label>
                  )
                )}
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={handlePrevious}
                className="px-4 py-2 border border-gray-600 rounded hover:border-gray-700/50 transition duration-300 transform hover:scale-105 text-white"
              >
                Prev
              </button>
              <button
                onClick={handleNext}
                className="px-4 py-2 border border-purple-600 text-white rounded transition duration-300 transform hover:border-purple-400/50 hover:scale-105 shadow-lg"
              >
                Next
              </button>
            </div>
          </motion.div>
        )}

        {step === 3 && (
          <motion.div
            className="bg-gray-900/30 text-white p-8 rounded-lg shadow-lg transition-transform duration-300"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <h2 className="text-3xl font-bold mb-6 text-center text-purple-400">
              🎨 What are your interests?
            </h2>

            <div className="mb-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {[
                  "Marketer",
                  "Education",
                  "Content Creator",
                  "Advertising",
                  "Product Design",
                ].map((hobby) => (
                  <label
                    key={hobby}
                    className="flex items-center p-4 border border-gray-700 rounded-lg transition-all duration-300 hover:bg-gray-800 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      value={hobby}
                      checked={hobbies.includes(hobby)}
                      onChange={() => handleHobbyChange(hobby)}
                      className="mr-2"
                    />
                    <span className="font-medium">{hobby}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={is18}
                  onChange={() => setIs18(!is18)}
                  className="mr-2"
                />
                I confirm that I am 18 years or older
              </label>
            </div>

            <div className="flex justify-between mt-6">
              <button
                onClick={handlePrevious}
                className="px-4 py-2 border border-gray-600 rounded hover:border-gray-700/50 transition duration-300 transform hover:scale-105 text-white"
              >
                Prev
              </button>
              <button
                onClick={handleSubmit}
                className="px-4 py-2 border border-green-600 bg-transparent rounded transition-all duration-300 transform hover:scale-105 text-white shadow-lg hover:border-green-800"
              >
                Submit
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default UserJourney;
