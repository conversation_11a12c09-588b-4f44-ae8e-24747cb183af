"use client";
import coinImg from "../../../../public/img/coin-png.png";
import Link from "next/link";
import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { MessageSquarePlus, Rocket, Dot, Star, Newspaper } from "lucide-react";
import { Media, Purchase } from "@/server/payload-types";
import { CardBody, CardContainer, CardItem } from "@/components/ui/3d-card";
import Image from "next/image";
import placeholderImg from "@/public/png/blog-placeholder-img.png"
import dynamic from "next/dynamic";

const bgColors = [
  "bg-custom-bg4",
  "bg-custom-bg5",
  "bg-custom-bg6",
  "bg-custom-bg1",
  "bg-custom-bg2",
  "bg-custom-bg3",
];

const Blog = ({ blogs, user }) => {

  const [isExpanded, setIsExpanded] = useState(false);
  const blogsToShow = isExpanded ? blogs : blogs.slice(0, 3);

  const renderImage = (image: string | Media | undefined) => {
    if (typeof image === "string") {
     return <img src={image ?? undefined} alt="Blog Image" />;
      //return <Image src={image ?? placeholderImg }  alt="Blog Image" layout="fill" objectFit="cover" loading="lazy" className="px-6" />;
    } else if (image && image.sizes && image.sizes.thumbnail) {
      return <img src={image.sizes.thumbnail.url ?? undefined} alt={image.sizes.thumbnail.url ?? undefined}/>;
    }
    return null;
  };

  return (
    <>
      <div className="flex gap-2 items-center mt-5 text-neo-foreground">
        {/* <Rocket className="w-5 h-5 sm:w-6 sm:h-6" /> */}
        <Newspaper className="w-5 h-5 sm:w-6 sm:h-6" />
        <div className="sm:text-3xl text-lg font-medium">Blogs</div>
      </div>
      {blogs?.length !== 0 ? (
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-10 sm:gap-x-6 md:grid-cols-3 lg:grid-cols-3 md:gap-y-10 lg:gap-x-8">
          {blogsToShow.map((blog, index) => (
            <CardContainer key={blog.id} className="inter-var">
              <CardBody>
                <article
                  className={`flex flex-col justify-between border border-white hover:border-violet-500 rounded-2xl md:max-w-xl sm:w-sm ${
                    bgColors[index % bgColors.length]
                  }`}
                >
                  <Link href={`/blog/${blog.slug}`}>
                    <CardItem>
                      <div className="flex flex-wrap px-6 py-4 justify-between md:space-x-2 sm:justify-between">
                        <h1 className="text-sm text-indigo-800 font-bold">
                          {" "}
                          {new Date(blog.createdAt).toLocaleDateString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            }
                          )}{" "}
                        </h1>
                        <Badge className="bg-indigo-300 rounded-lg ">
                          <span className="first-letter:uppercase">{blog.tags}{" "}</span>
                        </Badge>
                        {blog.isFeatured === true && (
                          <div className="absolute top-[-10px] -left-5 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                            <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                              FEATURED
                              <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                            </div>
                          </div>
                        )}
                      </div>
                    </CardItem>
                    <CardItem>
                      <div
                        className="relative w-full rounded-lg aspect-h-10 overflow-hidden flex flex-col justify-center
                          sm:justify-between px-6 xl:aspect-h-10"
                      >
                        {blog.images &&
                          blog.images.length > 0 &&
                          renderImage(blog.images[0].image)}
                      </div>
                    </CardItem>
                    <CardItem>
                      <div className="font-bold text-lg bg-transparent md:text-sm lg:text-lg text-indigo-900 px-6 mt-4 capitalize">
                        {`${blog.title.substring(0, 25)}...`}
                      </div>
                    </CardItem>
                    <CardItem>
                      <div className="font-semibold px-6 text-indigo-400 py-2">
                        {/* </Button> */}
                        <h2 className="font-normal text-sm text-indigo-600 first-letter:uppercase">{`${blog.content.substring(
                          0,
                          100
                        )}...`}</h2>
                      </div>
                    </CardItem>
                    <CardItem>
                      <div className="py-4 text-right px-6">
                        <p className="text-indigo-900 text-sm font-bold">
                          Reading time: {blog.time} mins
                        </p>
                      </div>
                    </CardItem>
                  </Link>
                </article>
              </CardBody>
            </CardContainer>
          ))}
        </div>
      ) : (
        <p className="text-gray-200">No Blogs yet.</p>
      )}

      {blogs.length > 3 && (
        <div className="text-center mt-6 flex items-center justify-center">
          <div className="flex-grow border-t border-gray-300"></div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-white font-bold cursor-pointer mx-4"
          >
            {isExpanded ? "▲ View Less" : "▼ View More"}
          </button>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>
      )}
    </>
  );
};

export default Blog;
