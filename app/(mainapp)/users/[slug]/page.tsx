import Image from "next/image";

import { getPayloadClient } from "@/server/get-payload";
import { notFound } from "next/navigation";
import { MessageSquarePlus, Rocket, Dot, Info } from "lucide-react";
import FollowButton from "@/components/FollowButton";
import LikeAndThumbButton from "@/components/LikeAndThumbButton";
import Loader from "@/components/Loader";
import RentListing from "@/components/ui/rentListing";
import { getServerSideUser } from "@/lib/payload-utils";
import { cookies } from "next/headers";
import ProductListing from "@/components/ProductListing";
import { Heart, User, Wrench } from "lucide-react";

import Link from "next/link";
import React from "react";
import Blog from "./Blog";
import Course from "./Course";
import Bounty from "./Bounty";
import ExpandableList from "@/components/ui/ExpandableList";
import ProductExpandableList from "@/components/ui/productExpandableList";

interface PageProps {
  params: {
    slug: string;
  };
}

const page = async ({ params }: PageProps) => {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);

  const { slug } = params;

  const decodedSlug = decodeURIComponent(slug);

  const payload = await getPayloadClient();
  const { docs: portfolioUsers }: any = await payload.find({
    collection: "users",
    limit: 1,
    where: {
      user_name: {
        equals: decodedSlug,
      },
    },
  });
  const [portfolioUser] = portfolioUsers;
  

  const userId = portfolioUser?.id;

  if (!portfolioUser) return notFound();

  //fetching rapps for rapp count
  const { docs: rapps }: any = await payload.find({
    collection: "rapps",
    where: {
      creator: {
        equals: userId,
      },
      status: {
        equals: "approved",
      },
      // private: {
      //   equals: false,
      // },
    },
  });

  //fetching products
  const { docs: products }: any = await payload.find({
    collection: "products",
    where: {
      user: {
        equals: userId,
      },
      approvedForSale: {
        equals: "approved",
      },
    },
  });

  const { docs: blogs }: any = await payload.find({
    collection: "blogs",
    where: {
      user: {
        equals: userId,
      },
      status: {
        equals: "approved",
      },
    },
  });

  const { docs: courses }: any = await payload.find({
    collection: "courses",
    where: {
      user: {
        equals: userId,
      },
      status: {
        equals: "approved",
      },
    },
  });

  const { docs: bounties }: any = await payload.find({
    collection: "bounties",
    where: {
      user: {
        equals: userId,
      },
      status: {
        equals: "approved",
      },
    },
  });

  const userImage = portfolioUser?.profileImage?.url;
  const userCoverImage = portfolioUser?.coverImage?.url;
  const followers = (portfolioUser?.followers as string[])?.length ?? 0;
  const following = (portfolioUser?.following as string[])?.length ?? 0;
  const likes = (portfolioUser?.likes as string[])?.length ?? 0;
  const interests =
    typeof portfolioUser?.genInfo?.interests === "string"
      ? portfolioUser.genInfo.interests.split(",")
      : Array.isArray(portfolioUser?.genInfo?.interests)
        ? portfolioUser.genInfo.interests
        : [];
  const skills = portfolioUser?.genInfo?.skills
    ? portfolioUser?.genInfo.skills.split(",")
    : [];
  const profession = portfolioUser?.genInfo?.profession ?? "";
  const experience = portfolioUser?.genInfo?.workExperience ?? "";

  return (
    <>
      <Loader />
      <div className="relative h-[21rem] sm:h-56 bg-indigo-900/55 mt-10 md:mt-20 flex flex-col sm:flex-row gap-4 sm:gap-10 justify-center sm:justify-start items-center lg:px-12 sm:px-6 px-4 py-4 sm:py-0">
        <div className="relative">
          <Image
            className="w-40 sm:w-40 h-40 sm:h-40 rounded-full object-cover"
            src={userImage ?? `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/2.png`}
            alt={""}
            width={100}
            height={100}
          />
          <LikeAndThumbButton
            userPortfolio={portfolioUser}
            loginUser={user}
            likes={likes}
          />
        </div>
        <FollowButton
          userPortfolio={portfolioUser}
          loginUser={user}
          followers={followers}
          following={following}
        />
      </div>

      <div className="sm:flex sm:gap-5 mt-12 md:mt-16 px-4 sm:px-6 lg:px-12 ">
        <div className="flex flex-col gap-8 w-full sm:w-[30%]">
          <div className="bg-indigo-700 border hover:border hover:border-gray-400 transition-all duration-200 rounded-xl shadow-md p-5">
            <div className="flex items-center gap-2 mb-4">
              <Heart className="text-white w-6 h-6" />
              <div className="text-white text-xl sm:text-2xl font-semibold">
                Interests
              </div>
            </div>
            <div className="text-white text-base font-light">
              {interests.length === 0 ? (
                <p className="italic">None yet.</p>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {interests.map((interest, i) => (
                    <div
                      key={i}
                      className="bg-white/20 text-white px-2 py-1 rounded-full text-sm first-letter:uppercase"
                    >
                      {interest.trim()}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="bg-indigo-700 border hover:border hover:border-gray-400 transition-all duration-200 rounded-xl shadow-md p-5">
            <div className="flex items-center gap-2 mb-4">
              <Wrench className="text-white w-6 h-6" />
              <div className="text-white text-xl sm:text-2xl font-semibold">
                Skills
              </div>
            </div>
            <div className="text-white text-base font-light">
              {skills.length === 0 ? (
                <p className="italic">None yet.</p>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill, i) => (
                    <div
                      key={i}
                      className="bg-white/20 text-white px-2 py-1 rounded-full text-sm first-letter:uppercase"
                    >
                      {skill}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="bg-indigo-700 border hover:border hover:border-gray-400 transition-all duration-200 rounded-xl shadow-md p-5">
            <div className="flex items-center gap-2 mb-4">
              <User className="text-white w-6 h-6" />
              <div className="text-white text-xl sm:text-2xl font-semibold">
                About
              </div>
            </div>
            <div className="text-white text-base font-light">
              {profession === "" && experience === "" ? (
                <p className="italic">None yet.</p>
              ) : (
                <div>
                  {profession && (
                    <p>
                      Working as <strong>{profession}</strong>
                    </p>
                  )}
                  {experience && <p>{experience} Years experienced</p>}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-t from-transparent via-neutral-300 dark:via-neutral-700 to-transparent md:mx-2 sm:mx-2 w-[1px]" />

        <div className="flex flex-col gap-6 mt-10 sm:mt-0 md:w-[75%]">
          {rapps.length > 0 && (
            <>
              <div className="flex gap-2 items-center">
                <Rocket className="w-5 h-5 sm:w-6 sm:h-6" />
                <div className="sm:text-3xl text-2xl font-medium">Rapps</div>
              </div>
              {rapps.length !== 0 ? (
                <ExpandableList items={rapps} user={user} />
              ) : (
                <p className="text-gray-200">No Spaces yet.</p>
              )}
            </>
          )}

          {products.length > 0 && (
            <>
              <div className="flex gap-2 items-center">
                <Rocket className="w-5 h-5 sm:w-6 sm:h-6" />
                <div className="sm:text-3xl text-2xl font-medium">Products</div>
              </div>
              {products.length !== 0 ? (
                <ProductExpandableList items={products} user={user} />
              ) : (
                <p className="text-gray-200">No Spaces yet.</p>
              )}
            </>
          )}

          {bounties.length > 0 && <Bounty bounties={bounties} user={user} />}

          {blogs.length > 0 && <Blog blogs={blogs} user={user} />}

          {courses.length > 0 && <Course courses={courses} user={user} />}
        </div>
      </div>

      <div></div>
    </>
  );
};

export default page;
