"use client";

import Link from "next/link";
import React, { useState } from "react";
import { <PERSON>, Star } from "lucide-react";

const Bounty = ({ bounties, user }) => {

  const [isExpanded, setIsExpanded] = useState(false);
  const bountyToShow = isExpanded ? bounties : bounties.slice(0, 4);

  return (
    <>
      <div className="flex gap-2 items-center mt-5 text-neo-foreground">
        <Rocket className="w-6 h-6 sm:w-8 sm:h-8 " />
        <div className="sm:text-4xl text-2xl font-medium ">Bounties</div>
      </div>

      {bounties?.length > 0 ? (
        <div className="w-full grid grid-cols-1 gap-6 md:grid-cols-2">
          {bountyToShow?.map((bounty, i) => (
            <React.Fragment key={i}>
              {bounty.status === "approved" && (
                <Link href={`/bounties/${bounty.slug}`}>
                  <div className="flex w-full">
                    <div className="bg-indigo-700 w-full p-4 rounded-xl shadow-md border border-slate-300 hover:shadow-lg hover:border-indigo-400 transition-all transform hover:-translate-y-1">
                      <div className="flex justify-between items-center mb-4">
                        {/* Price Section */}
                        {bounty.isFeatured === true && (
                          <div className="absolute top-[-10px] -left-5 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                            <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                              FEATURED
                              <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                            </div>
                          </div>
                        )}

                        <div className="text-white text-4xl flex items-center font-bold">
                          {bounty.estimatedPrice === 0 ? (
                            <div className="px-3 py-1 rounded-full bg-gradient-to-r  text-white text-xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                              FREE
                            </div>
                          ) : (
                            bounty.estimatedPrice
                          )}
                          {bounty.estimatedPrice === 0 ? (
                            ""
                          ) : (
                            <img
                              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                              style={{ width: "35px", height: "39px" }}
                              alt="coin"
                              loading="lazy"
                            />
                          )}
                        </div>
                        <button className="bg-gray-800 text-white text-sm rounded-full px-3 py-1 hover:bg-gray-600">
                          Open
                        </button>
                      </div>

                      {/* Bounty Title */}
                      <h3 className="text-lg sm:text-xl font-semibold text-white mb-2 leading-tight line-clamp-2 capitalize">
                        {bounty.title.length > 25
                          ? `${bounty.title.substring(0, 30)}...`
                          : bounty.title}
                      </h3>

                      {/* Content Preview */}
                      <p className="text-gray-300 mb-4 line-clamp-2 text-md sm:text-lg first-letter:uppercase">
                        {bounty.content.length > 30
                          ? `${bounty.content.substring(0, 50)}...`
                          : bounty.content}
                      </p>

                      {/* Bounty Type and Applicants */}
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-indigo-200 ">
                          Type: {bounty.bountyType}
                        </p>
                        <p className="text-white text-sm">
                          {bounty?.applicants?.length} Applicants
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              )}
            </React.Fragment>
          ))}
        </div>
      ) : (
        <p className="text-gray-200">No Bounties yet.</p>
      )}

      {bounties?.length > 4 && (
        <div className="text-center mt-6 flex items-center justify-center">
          <div className="flex-grow border-t border-gray-300"></div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-white font-bold cursor-pointer mx-4"
          >
            {isExpanded ? "▲ View Less" : "▼ View More"}
          </button>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>
      )}
    </>
  );
};

export default Bounty;
