"use client";

import coinImg from "../../../public/img/coin-png.png";
import Link from "next/link";
import React, { useState } from "react";
import { MessageSquarePlus, Rocket, Dot, Star } from "lucide-react";

import { Clock4, SignalMedium } from "lucide-react";
import coinImage from "../../../../public/img/coin-png.png";

const Course = ({ courses, user }) => {

  const [isExpanded, setIsExpanded] = useState(false);
  const coursesToShow = isExpanded ? courses : courses.slice(0, 4);

  return (
    <>
      <div className="flex gap-2 items-center mt-5 text-neo-foreground">
        <Rocket className="w-5 h-5 sm:w-6 sm:h-6" />
        <div className="sm:text-3xl text-2xl font-medium">Courses</div>
      </div>
      {courses?.length !== 0 ? (
        <div className="w-full grid grid-cols-1 sm:grid-cols-1  sm:gap-x-6 md:grid-cols-2 lg:grid-cols-2 md:gap-y-6 lg:gap-x-8">
          {coursesToShow.map((course: any, index: any) => (
            <div key={index}>
              <Link href="/academy">
                <div
                  className="px-0 mx-auto min-w-full py-4 md:py-0 lg:py-0 rounded-lg pt-2 cursor-pointer"
                  key={index}
                  // onClick={() => handleCardClick(course)}
                >
                  <div
                    className="  from-indigo-600 to-indigo-700 text-white rounded-xl  
              hover:shadow-lg transition duration-200 shadow-input bg-gradient-to-br  border border-transparent hover:scale-[1.06]
              "
                  >
                    <div className="relative">
                      {course.isFeatured === true && (
                        <div className="absolute top-[-10px] -left-5 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                          <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                            FEATURED
                            <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                          </div>
                        </div>
                      )}
                    </div>

                    <div className=" flex flex-col justify-between p-4 md:p-6 gap-1">
                      <div className="flex justify-between">
                        <div className="flex items-center">
                          <p className="text-4xl font-bold">
                            {course.cost === 0 ? (
                              <div className="px-3 py-1 rounded-full bg-gradient-to-r  text-white text-xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                                FREE
                              </div>
                            ) : (
                              course.cost
                            )}
                          </p>
                          {course.cost === 0 ? (
                            ""
                          ) : (
                            <img
                              src={coinImage.src}
                              alt="Coin"
                              style={{ width: "35px", height: "39px" }}
                              className=""
                               loading="lazy"
                            />
                          )}
                        </div>
                        <div className="flex justify-end gap-5 items-center">
                          <div className="flex items-center gap-1">
                            <Clock4 size={16} />
                            <p className="text-sm">{course.time}</p>
                          </div>

                          <p className="flex gap-1 items-center  first-letter:uppercase">
                            <SignalMedium size={28} className="mb-2 -mr-2" />
                            {course.level}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-1 "></div>
                      <h4 className="text-3xl capitalize">{`${
                        course.title.length > 15
                          ? course.title.substring(0, 15) + "..."
                          : course.title
                      }`}</h4>
                      <div className="flex justify-between items-center gap-3 ">
                        <h4 className="text-md text-gray-300 line-clamp-2 first-letter:uppercase">
                          {`${
                            course.description.length > 50
                              ? course.description.substring(0, 60) + "..."
                              : course.description
                          }`}
                        </h4>
                        {/* <div className=" flex justify-end gap-5 z-10"> */}
                        {/* <button
                          className="bg-indigo-900 hover:bg-indigo-800 cursor-pointer px-4 rounded-lg py-1 items-center gap-1 hidden md:flex"
                          onClick={(e) => handleBuyNow(course, e)}
                        >
                          <Download /> Download
                        </button> */}
                        {/* </div> */}
                      </div>
                      <div>
                        {/* <button
                        className="bg-indigo-900 hover:bg-indigo-800 cursor-pointer px-4 rounded-lg py-1 flex items-center gap-1
                  md:hidden mt-2"
                        onClick={(e) => handleBuyNow(course, e)}
                      >
                        <Download /> Download
                      </button> */}
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-200">No Courses yet.</p>
      )}

      {courses.length > 4 && (
        <div className="text-center mt-6 flex items-center justify-center">
          <div className="flex-grow border-t border-gray-300"></div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-white font-bold cursor-pointer mx-4"
          >
            {isExpanded ? "▲ View Less" : "▼ View More"}
          </button>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>
      )}
    </>
  );
};

export default Course;
