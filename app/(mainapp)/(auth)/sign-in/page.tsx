"use client";

import { Icons } from "@/components/Icons";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight, Loader2 } from "lucide-react";
import Link from "next/link";
import { useForm } from "react-hook-form";
import {
  AuthCredentialsValidator,
  TAuthCredentialsValidator,
} from "@/lib/validators/account-credentials-validator.ts";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import Container from "@/components/ui/container";
import UserJourney from "@/components/UserJourney";
import { User } from "@/server/payload-types";
import { verify } from "crypto";
import { signIn, signOut, useSession } from 'next-auth/react';
import { ZodString } from "zod";
import { motion } from "framer-motion";
import { FcGoogle } from "react-icons/fc";
import { FaGithub } from "react-icons/fa";

const Page = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const isSeller = searchParams.get("as") === "seller";
  const origin = searchParams.get("service");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const redirectTo = searchParams.get("from") || "/";
  // const searchParams = new URLSearchParams(window.location.search);
  const encodedCredentials = searchParams.get('credentials');
  // const userJourney = searchParams.get('UserJourney');
  const hasRun = useRef(false); 
  const [authProvider, setAuthProvider] = useState<string | null>(null);
  const [passwordValue, setPasswordValue] = useState("");
  

  const {
    register,
    handleSubmit,
    formState: { errors , touchedFields  },
    setValue
  } = useForm<TAuthCredentialsValidator>({
    resolver: zodResolver(AuthCredentialsValidator),
  });

  useEffect(() => {
    if (encodedCredentials) {
      // Decode the string and parse it back to an object
      const credentials = JSON.parse(decodeURIComponent(encodedCredentials));
      
      const email = credentials?.email;
      const password = credentials?.InputCred;
      const provider = credentials?.authProvider;

      if (provider) {
        setAuthProvider(provider);
      }

      if (!hasRun.current && email && password) {
        setValue("email", email);  // Set form values
        setValue("password", password);
        handleSubmit(onSubmit)();  // Trigger form submission
        hasRun.current = true;  // Mark that the effect has run
      }
    }
  }, [encodedCredentials, setValue, handleSubmit, searchParams]);

  const onSubmit = async ({ email, password }: TAuthCredentialsValidator) => {
    setIsLoading(true);
    try {
      const { data }: { data: CustomEndPointResponse } = await axios.post(
        `/api2/user/signin`,
        {
          email,
          password,
        }
      );
      setIsLoading(false);
      if (data.success === false) {
        //toast.error("Invalid email or password.");
        toast.error(data.message);
        return;
      }
      router.refresh();
      const credentials = JSON.parse(decodeURIComponent(encodedCredentials));
      const userJourney = credentials?.UserJourney;

      // if (data.success === false && ) {
      //   toast.error("Invali");
      //   return;
      // }
      

      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`
        );
        
        const data = await res.json();
        localStorage.setItem("token", data.token);
        setUser(data?.user);
        
      } catch (error) {
        console.error("Error fetching user data:", error);
      }

      if(userJourney || redirectTo.startsWith("/verify-email")) {
        setIsModalOpen(true);
      } 
      else {
          toast.success("Signed in Successfully");
          window.location.replace(redirectTo);
        }
      
    } catch (e) {
      setIsLoading(false);
      toast.error(e.response.data.message);
    }
  };


  const BottomGradient = () => {
    return (
      <>
        <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
        <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
      </>
    );
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3, // Delays between children elements' animations
      },
    },
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 100 } },
  };
  
  const buttonVariants = {
    hover: {
      scale: 1.05,
      transition: { type: "spring", stiffness: 200 },
    },
  };
  

  return (
    <React.Fragment>
    <Container>
      <motion.div
        className="container md:w-11/12 mx-auto relative flex pt-20 md:mt-10 flex-col gap-10 md:gap-2  md:flex-row items-center justify-start lg:px-0"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Left section: Logo and Heading */}
        <motion.div
          className="mx-auto w-full md:w-1/2 flex flex-col justify-start space-y-6 sm:w-[350px] md:text-left md:items-start"
          variants={itemVariants}
        >
          <motion.div className="flex flex-col items-center md:items-center md:justify-start space-y-2 text-center">
            <motion.div
              className="h-20 md:h-32 w-20 md:w-32"
              initial={{ scale: 0 }}
              animate={{ scale: 1, transition: { duration: 0.5 } }}
            >
              <Icons.logo className="fill-muted-foreground" />
            </motion.div>
            <motion.h1 className="text-2xl md:text-5xl font-semibold tracking-tight pt-8 pb-5" variants={itemVariants}>
              Sign in to your {isSeller ? "seller" : ""} account
            </motion.h1>
            <motion.div variants={itemVariants}>
            Don&apos;t have an account?
            <ArrowRight className="h-4 w-4 inline" />
              <Link href="/sign-up" className="border border-gray-300 px-2 md:px-3 py-[6px] md:py-2 rounded-lg ml-2 hover:bg-indigo-700 transition-all duration-200">
                Sign Up
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
  
        {/* Right section: Form */}
        <motion.div className="mx-auto md:w-1/2 flex flex-col justify-center space-y-6 w-full sm:w-[350px]" variants={itemVariants}>
          <motion.div variants={itemVariants}>
            <Button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                signIn("google");
              }}
              className=" text-indigo-700 border border-indigo-700 w-full rounded-md font-semibold flex items-center justify-center space-x-2"
              
            >
              <FcGoogle className="w-6 h-6 " />
              <span>Continue with Google</span>
            </Button>
          </motion.div>
  
          <motion.div variants={itemVariants}>
            <Button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                signIn("github");
              }}
              className="  border border-indigo-700 w-full rounded-md font-semibold flex items-center justify-center space-x-2"
              
            >
              <FaGithub className="w-6 h-6 text-black" />
              <span className="text-indigo-700">Continue with Github</span>
            </Button>
          </motion.div>
  
          <motion.div className="relative flex justify-center text-xs uppercase" variants={itemVariants}>
            <span className="bg-background px-2 text-muted-foreground">or Login with</span>
          </motion.div>
  
          <motion.div className="grid gap-6" variants={itemVariants}>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit(onSubmit)();
                }}
              >
              <motion.div className="grid gap-2" variants={itemVariants}>
                <motion.div className="grid gap-1 pb-2" variants={itemVariants}>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    {...register("email")}
                    className={cn({ "focus-visible:ring-red-500": errors?.email })}
                    placeholder="<EMAIL>"
                  />
                  {errors?.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
                </motion.div>
  
                <motion.div className="grid gap-1 py-2 relative" variants={itemVariants}>
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      {...register("password")}
                      type={showPassword ? "text" : "password"}
                      className={cn({ "focus-visible:ring-red-500": errors?.password })}
                      placeholder="Password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 top-1 right-0 pr-3 flex items-center text-sm leading-5"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5 text-indigo-400" /> : <Eye className="h-5 w-5 text-indigo-400" />}
                    </button>
                  </div>
                  {touchedFields.password && errors?.password && <p className="text-sm text-red-500">{errors.password.message}</p>}  
                </motion.div>
                <div className="text-right">
                  <Link href="/forgot-password" className="text-sm text-indigo-400 hover:underline">
                    Forgot Password?
                  </Link>
                </div>
              </motion.div>
  
              <Button
                className={cn("flex bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 w-full text-white rounded-md h-10 font-medium")}
                disabled={isLoading}
                
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 spin-animation" />}
                Sign in
                <BottomGradient />
              </Button>
            </form>
          </motion.div>
  
          {/* Rest of your form code... */}
        </motion.div>
      </motion.div>
      <UserJourney isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} user={user} />
    </Container>
  </React.Fragment>
  );
};

export default Page;
