 "use client";

import { Icons } from "@/components/Icons";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { useForm } from "react-hook-form";
import {
  AuthCredentialsValidator,
  TAuthCredentialsValidator,
} from "@/lib/validators/account-credentials-validator.ts";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import { useEffect, useRef, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import Container from "@/components/ui/container";
import coinImage from "../../../../public/img/coin-png.png";
import { signIn, signOut, useSession } from 'next-auth/react';
import { FcGoogle } from "react-icons/fc";
import { FaGithub } from "react-icons/fa";
import { motion } from "framer-motion";


const Page = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TAuthCredentialsValidator>({
    resolver: zodResolver(AuthCredentialsValidator),
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();

  const checkSession = async () => {
    try {
      const response = await fetch("/api/auth/session");
      const sessionData = await response.json();
    } catch (error) {
      console.error("Failed to fetch session:", error);
    }
  };

  const onSubmit = async ({ email, password }: TAuthCredentialsValidator) => {
    try {
      const { data }: { data: CustomEndPointResponse } = await axios.post(
        `/api2/user/create`,
        {
          email,
          password,
        }
      );
      if (data.success === false) {
        toast.error(data.message);
        return;
      }

      toast.success(`Verification email sent to ${email}.`);
      router.push("/verify-email?to=" + email);
      
    } catch (e) {
      toast.error("Something went wrong. Please try again.");
    }
  };

  const BottomGradient = () => {
    return (
      <>
        <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
        <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
      </>
    );
  };
  useEffect(() => {
    setTimeout(() => setIsVisible(true), 500);
  }, []);

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 100 } },
  };


  return (
    <>
      <Container>
      <div
        className={`text-xl md:text-4xl text-center w-11/12 mx-auto  font-bold mt-[100px] md:mt-28 ${
          isVisible ? 'animate-[fadeInUp_1s_ease-in-out]' : 'opacity-0'
        }`}
      >
        <div className="inline-flex items-center flex-col">
          <div className="animate-[rapidBounce_2s_ease-in-out_infinite] mb-2">
            <span className="animate-blink animate-[colorChange_1.5s_ease-in-out_infinite]">
              Sign Up Now and Get 25
            </span>
            <img
              src={coinImage.src}
              alt="Coin"
              style={{ width: "35px", height: "36px" }}
              className="inline mb-1  animate-bounce"
               loading="lazy"
            />
            <span className="animate-blink animate-[colorChange_2s_ease-in-out_infinite]">
              Credits Free
            </span>
          </div>
        </div>
      </div>
      <div className="container md:w-10/12 mx-auto relative flex flex-col gap-5 lg:flex-row pt-10 items-center justify-between lg:px-0">
        {/* Left Side: Animation and Logo */}

        <motion.div className="flex md:mt-[-30px] flex-col items-center md:items-center md:justify-start space-y-2 text-center">
            <motion.div
              className="h-20 md:h-32 w-20 md:w-32"
              initial={{ scale: 0 }}
              animate={{ scale: 1, transition: { duration: 0.5 } }}
            >
              <Icons.logo className="fill-muted-foreground" />
            </motion.div>
            <motion.h1 className="text-2xl md:text-5xl font-semibold tracking-tight pt-8 pb-5" variants={itemVariants}>
            Create an account
            </motion.h1>
            <motion.div variants={itemVariants}>
            Already have an account?
            <ArrowRight className="h-4 w-4 inline" />
              <Link href="/sign-in" className="border border-gray-300 px-2 md:px-3 py-[6px] md:py-2 rounded-lg ml-2 hover:bg-indigo-700 transition-all duration-200">
                Sign In
              </Link>
            </motion.div>
          </motion.div>

        {/* Right Side: Form */}
        <div className="w-full sm:w-[350px] md:w-1/2">

        <Button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default action if needed
                      signIn('google'); // Call sign-in function
                    }}
                    className="bg-white text-indigo-700 border mb-4 border-indigo-700 w-full rounded-md font-semibold flex items-center justify-center space-x-2"
                  >
                     <FcGoogle className="w-6 h-6 " />
                    <span>Continue with Google</span>
                  </Button>

        <Button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default action if needed
                      signIn('github'); // Call sign-in function
                    }}
                    className="bg-white text-indigo-700 mb-4 border border-indigo-700 w-full rounded-md font-semibold flex items-center justify-center space-x-2"
                  >
                    <FaGithub className="w-6 h-6 text-black" />
                    <span>Continue with Github</span>
                  </Button>

                  <div className="relative flex justify-center text-xs uppercase">
                   <span className="bg-background px-2 text-muted-foreground">
                     or SignUp with
                   </span>
                 </div>
          <div className="grid gap-6">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid gap-2">
                <div className="grid gap-1 py-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    {...register("email")}
                    className={cn({
                      "focus-visible:ring-red-500": errors?.email,
                    })}
                    placeholder="<EMAIL>"
                  />
                  {errors?.email && (
                    <p className="text-sm text-red-500">{errors.email.message}</p>
                  )}
                </div>

                <div className="grid gap-1 py-2 relative">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      {...register("password")}
                      type={showPassword ? "text" : "password"}
                      className={cn({
                        "focus-visible:ring-red-500": errors?.password,
                      })}
                      placeholder="Password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 top-1 pr-3 flex items-center text-sm leading-5"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-indigo-500" />
                      ) : (
                        <Eye className="h-5 w-5 text-indigo-500" />
                      )}
                    </button>
                  </div>
                  {errors?.password && (
                    <p className="text-sm text-red-500">{errors.password.message}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2 mt-1">
                  <input
                    type="checkbox"
                    id="terms"
                    onChange={(e) => setIsTermsAccepted(e.target.checked)}
                    className="mt-[3px]"
                  />
                  <Link href={`/privacypolicy`}>
                    <Label
                      htmlFor="terms"
                      className="text-sm font-medium  leading-none cursor-pointer hover:text-blue-500"
                    >
                      Accept Terms and Conditions
                    </Label>
                  </Link>
                </div>

                <Button
                  className="bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 block w-full text-white rounded-md h-10 font-medium"
                  disabled={!isTermsAccepted}
                >
                  Sign up
                  <BottomGradient />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Container>
    </>
  );
};

export default Page;
