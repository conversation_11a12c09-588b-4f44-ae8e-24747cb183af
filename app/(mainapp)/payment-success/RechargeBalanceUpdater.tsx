"use client";

import { useEffect } from "react";
import { useCoinBalanceContext } from "@/components/coin-balance-initializer";

interface RechargeBalanceUpdaterProps {
  newBalance: number;
  rechargeAmount: number;
}

export default function RechargeBalanceUpdater({ newBalance, rechargeAmount }: RechargeBalanceUpdaterProps) {
  const { setBalance } = useCoinBalanceContext();

  useEffect(() => {
    // Recharge के बाद balance को immediately update करें
    setBalance(newBalance);
    console.log('💳 Recharge completed - Amount:', rechargeAmount, 'New balance:', newBalance);
  }, [newBalance, rechargeAmount, setBalance]);

  return null; // यह component कुछ render नहीं करता
}
