import { getServerSideUser } from '@/lib/payload-utils'
import { cookies } from 'next/headers'
import { getPayloadClient } from '@/server/get-payload'
import { notFound, redirect } from 'next/navigation'
import Link from 'next/link'
import coinImage from '../../../public/img/coin-png.png'
import Loader from '@/components/Loader'

interface PageProps {
  searchParams: {
    [key: string]: string | string[] | undefined
  }
}

const Page = async ({ searchParams }: PageProps) => {
  const orderId = searchParams.orderId;
  const nextCookies = cookies()

  const response = await getServerSideUser(nextCookies);

  if (response.status === 401) {
    redirect('/sign-in');
    return <div>Loading...</div>;
  }

  const { user } = response;
  // if (!user) {
  //   redirect("/sign-in"); // Redirect to login page
  //   return null; // Ensure no further rendering
  // }
  const payload = await getPayloadClient()

  const { docs: orders }: any = await payload.find({
    collection: 'orders',
    depth: 2,
    where: {
      id: {
        equals: orderId,
      },
    },
  })

  const order = orders[0]

  if (!order) return notFound()

  if (!user) {
    return redirect(`/sign-in?origin=payment-success?orderId=${order.id}?orderStatus=${order.status}`)
  }

  return (
    <>
    <Loader />
    <main className="relative lg:min-h-full text-white py-16 md:mt-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <p className={`text-lg font-medium ${order.status === 'completed' ? 'text-green-400' : order.status === 'pending' ? 'text-yellow-200' : 'text-yellow-200' }  animate-pulse`}>
          {
            order.status === 'completed' 
              ? "Payment Successful" 
              : order.status === 'pending'
                ? "Payment Pending"
                : "Payment Initiated"
          }
          </p>
          {
            order.status === 'completed' ? <h1 className="mt-4 text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight text-gray-100">
            Thanks for Purchasing Joules
          </h1> : order.status === 'pending' ? "" : ""
          }
          
          <p className="mt-2 text-base sm:text-lg text-gray-300">
          {order.status === 'completed' ? "Your order was processed, and the purchased Joules have been added to your Joule Balance. You can view your order in the dashboard." : order.status === 'pending' ? <>Your order is pending, please reach out to the support team.  <a href="https://discordapp.com/channels/1179720072370065428/1280188340108525661" target='_blank' className='border-gray-300 font-bold underline'>Click Here</a></> : "Your order has initiated"}
          </p>
        </div>

        <div className="mt-12 text-base sm:text-lg lg:text-xl font-medium">
          <div className="bg-indigo-700 p-4 sm:p-6 lg:p-8 rounded-lg shadow-lg mx-auto max-w-lg">
            <div className="space-y-4">
              <p className="text-lg font-semibold text-yellow-300">
                Payment of ₹{order.totalBasePrice} to RentPrompts {order.status === 'completed' ? "Successful" : order.status === 
                'pending' ? "Pending" : "Initiated"}
              </p>
              {order.status === 'completed' ? <p className="text-lg font-semibold text-yellow-300 flex items-center gap-1">
                You Got {order.totalCoins} <img src={coinImage.src} alt="Coin" width={20} height={20} loading="lazy" />
              </p> : ""}
              
              <div className="border-t border-indigo-500 mt-2 pt-2">
                {[
                  { label: 'Order ID:', value: order.id },
                  { label: 'Payment Status:', value: order.status },
                  { label: 'Payment Method:', value: order.paymentMethod },
                  { label: 'Base Price:', value: `₹${order.totalBasePrice}` },
                  { label: 'Tax:', value: `₹${(order.tax).toFixed(2)}` },
                  { label: 'Total Price:', value: `₹${(order.total).toFixed(2)}` },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center border-b border-indigo-600 py-2"
                  >
                    <span className="text-md font-semibold text-gray-100">{item.label}</span>
                    <span className="text-md text-gray-300">{item.value}</span>
                  </div>
                ))}
              </div>
              <div className="pt-4 flex items-center justify-between mt-3">
                <div className="flex items-center gap-1">
                  <p className="text-lg font-semibold text-gray-100">JOULE BALANCE:</p>
                  <p className="text-lg font-semibold text-yellow-300">{user?.coinBalance.toFixed(2)}</p>
                  <img src={coinImage.src} alt="Coin" width={22} height={22}  loading="lazy" />
                </div>
              </div>
            </div>

            <div className="mt-8 lg:mt-12 border-t border-gray-600 pt-4 text-right">
              <Link href="/" className="text-sm font-medium text-blue-400 hover:text-blue-500 transition">
                Continue shopping →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
    </>
  )
}

export default Page
