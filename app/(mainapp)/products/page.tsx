// (explore)/explore/page.tsx

import fetchUser from "./fetchUser";
import { Metadata } from "next";
import DynamicSquareBackground from "@/components/landingWiget";
import RentPlaceProduct from "@/components/ui/rentplace-product";

export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: "Gen AI Marketplace - Discover Insights on Generative AI | Rentprompts",
        description:
          "Explore the latest Generative AI tools, AI innovations, trends on RentPrompt's Marketplace.",
        keywords: [
          "Generative AI Insights",
          "AI Innovations",
          "Exploring AI Tools",
          "Emerging AI Trends",
          "AI Marketplace Discoveries",
          "Creative AI Solutions",
          "AI-driven Technology",
          "Next-Gen AI Tools",
          "AI-powered Platforms",
        ],
        alternates: {
          canonical: `${process.env.PAYLOAD_PUBLIC_SITE_URL}/marketplace`,
        },
        openGraph: {
          title: "Gen AI Marketplace - Discover Insights on Generative AI | Rentprompts",
          description:
            "Stay updated with insights on Generative AI tools, marketplace trends, and AI innovations on RentPrompts.",
          url: `${process.env.PAYLOAD_PUBLIC_SITE_URL}/marketplace`,
          siteName: "Rentprompts",
          images: [
            {
              url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
              alt: "RentPrompts Explore Banner",
              width: 1200,
              height: 630,
            },
          ],
        },
        twitter: {
          card: "summary_large_image",
          title: "Explore - Discover Insights on Generative AI | Rentprompts",
          description:
            "Discover the latest insights and updates on AI tools, Generative AI, and marketplace trends.",
          images: [
            {
              url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
              alt: "RentPrompts Twitter Banner",
            },
          ],
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
        robots: {
          index: true,
          follow: true,
        },
      }
    : null;

export default async function ExplorePage() {
  const user = await fetchUser();

  return (
    <>
      <RentPlaceProduct user={user} />
    </>
  );
}
