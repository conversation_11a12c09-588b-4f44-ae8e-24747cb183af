
import { cookies } from "next/headers";
import { getServerSideUser } from "@/lib/payload-utils";
import { toast } from "sonner";
import { redirect } from "next/navigation";

export default async function fetchUser() {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  // if (response.status === 401) {
  //   toast.error("Session expired. Please log in again.");
  //   redirect('/sign-in');
  // }

  const { user } = response;
  // if (!user) {
  //   redirect("/sign-in");
  // }
  return user;
}
