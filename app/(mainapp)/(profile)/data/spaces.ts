export interface Space {
    name: string
    artist: string
    cover: string
  }
  
  export const YourProduct: Space[] = [
    {
      name: "React Rendezvous",
      artist: "<PERSON> By<PERSON>",
      cover:
        "/img/1.png",
    },
    {
      name: "Async Awakenings",
      artist: "Nina Netcode",
      cover:
        "/img/2.png",
    },
    {
      name: "The Art of Reusability",
      artist: "Lena Logic",
      cover:
        "/img/3.png",
    },
    {
      name: "Stateful Symphony",
      artist: "Beth Binary",
      cover:
        "/img/4.png",
    },
  ]
  
  export const madeForSpaces: Space[] = [
    {
      name: "Thinking Components",
      artist: "<PERSON> Logic",
      cover:
        "/img/5.png",
    },
    {
      name: "Functional Fury",
      artist: "<PERSON>",
      cover:
        "/img/6.png",
    },
    {
      name: "React Rendezvous",
      artist: "<PERSON>",
      cover:
        "/img/3.png",
    },
    {
      name: "Stateful Symphony",
      artist: "<PERSON> Binary",
      cover:
        "/img/4.png",
    },
    {
      name: "Async Awakenings",
      artist: "<PERSON> Netcode",
      cover:
        "/img/1.png",
    },
    {
      name: "The Art of Reusability",
      artist: "Lena Logic",
      cover:
        "/img/2.png",
    },
  ]