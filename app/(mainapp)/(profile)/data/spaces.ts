export interface Space {
    name: string
    artist: string
    cover: string
  }
  
  export const YourProduct: Space[] = [
    {
      name: "React Rendezvous",
      artist: "<PERSON> Byte",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/1.png`,
    },
    {
      name: "Async Awakenings",
      artist: "Nina Netcode",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/2.png`,
    },
    {
      name: "The Art of Reusability",
      artist: "<PERSON> Logic",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/3.png`,
    },
    {
      name: "Stateful Symphony",
      artist: "<PERSON>",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/4.png`,
    },
  ]
  
  export const madeForSpaces: Space[] = [
    {
      name: "Thinking Components",
      artist: "<PERSON> Logic",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/5.png`,
    },
    {
      name: "Functional Fury",
      artist: "<PERSON> Binary",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/6.png`
    },
    {
      name: "React Rendezvous",
      artist: "Ethan Byte",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/3.png`,
    },
    {
      name: "Stateful Symphony",
      artist: "Beth Binary",
      cover:
          `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/4.png`,
    },
    {
      name: "Async Awakenings",
      artist: "Nina Netcode",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/1.png`,
    },
    {
      name: "The Art of Reusability",
      artist: "Lena Logic",
      cover:
        `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/2.png`,
    },
  ]