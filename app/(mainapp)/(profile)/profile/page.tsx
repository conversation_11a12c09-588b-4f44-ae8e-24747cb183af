"use client";
import { useState, useEffect } from "react";
import ProfileSidebar from "@/components/ProfileSidebar";
import { spacelists } from "../data/spacelists";
import UserProfile from "@/components/ui/userprofile/userprofile";
import GeneralInfo from "@/components/ui/userprofile/generalinfo";
import AllProject from "@/components/ui/userprofile/allproject";
import SocialMediaIcon from "@/components/ui/userprofile/socialmedia";
import { User } from "@/server/payload-types";
import {Icons} from "@/components/Icons"
import Container from "@/components/ui/container";

export default function ProfilePage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,               //calling custom me api endpoint to prevent data leak
        {
          method: "GET",
        }
      );
      if (!response.ok) {
         throw new Error("Network response was not ok");
      }
      const data = await response.json();                                      //getting logged in  user details in the data 
      setUser(data.data);
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading)
    return (
      <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50 ">
        <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
      </div>
    );
  if (!user) return <div className='mt-28 text-2xl text-center font-semibold'>Can&apos;t Access Without Login</div>

  return (
    <>
      <Container>
      <div className="block md:block md:mt-14">
        <div className="border-t">
          <div className="bg-background">
            <div className="grid lg:grid-cols-5 ">
              <div className="hidden lg:block">
                <ProfileSidebar playlists={spacelists} />
              </div>
              <div className="col-span-3 lg:col-span-4 lg:border-l">
                <div className="h-full px-4 py-6 lg:px-8">
                  <UserProfile user={user} /> 
                  <GeneralInfo user={user} />
                  <SocialMediaIcon user={user} />
                  {/* AllProject component */}
                  {/* <AllProject user={user} /> */}
                  {/* <FileUpload user={user} /> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </Container>
    </>
  );
}
