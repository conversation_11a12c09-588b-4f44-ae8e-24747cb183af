import { notFound } from "next/navigation";
import { fetchPage } from "../../../api/live-preview/fetchPage";
import { cookies } from "next/headers";
import { getServerSideUser } from "@/lib/payload-utils";
import { RappTemplate, ModelTemplate, BlogsTemplate } from "./page.client";
import { fetchRapp } from "@/app/api/live-preview/fetchRapp";
import { fetchModel } from "@/app/api/live-preview/fetchModel";
import { fetchBlog } from "@/app/api/live-preview/fetchBlog";

interface PageParams {
  params: { slug: string };
  searchParams: { id: string };
}

export default async function Page({
  params: { slug },
  searchParams: { id },
}: PageParams) {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);

  if (slug === "rapps") {
    const rapp = await fetchRapp(id);
    return <RappTemplate page={rapp} user={user} id={id} />;
  }

  if (slug === "models") {
    const page = await fetchModel(id);
    return <ModelTemplate page={page} user={user} id={id} />;
  }
  if (slug === "blogs") {
    const page = await fetchBlog(id);
    return <BlogsTemplate page={page} id={id} />;
  }
}
