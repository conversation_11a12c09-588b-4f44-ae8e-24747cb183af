import { useLivePreview } from "@payloadcms/live-preview-react";
import {
  RenderModel,
  RenderRapp,
  RenderBlog,
} from "../../../../components/Render";
import { Blog, Model, Rapp } from "@/server/payload-types";
import Loader from "@/components/Loader";

export const RappTemplate: React.FC<{
  page: Rapp | null | undefined;
  user: any;
  id: string;
}> = ({ page, user, id }) => {
  const { data } = useLivePreview({
    serverURL: process.env.NEXT_PUBLIC_SERVER_URL || "",
    depth: 2,
    initialData: page,
  });

  return (
    <main>
      <Loader />
      <RenderRapp content={data} user={user} id={id} />
    </main>
  );
};

export const ModelTemplate: React.FC<{
  page: Model | null | undefined;
  user: any;
  id: string;
}> = ({ page,user, id }) => {
  const { data } = useLivePreview({
    serverURL: process.env.NEXT_PUBLIC_SERVER_URL || "",
    depth: 2,
    initialData: page,
  });

  return (
    <main>
      <Loader />
      <RenderModel content={data } user={user} id={id} />
    </main>
  );
};

export const BlogsTemplate: React.FC<{
  page: Blog | null | undefined;
  id: string;
}> = ({ page, id }) => {
  const { data } = useLivePreview({
    serverURL: process.env.NEXT_PUBLIC_SERVER_URL || "",
    depth: 3,
    initialData: page,
  });

  return (
    <main>
      <Loader />
      <RenderBlog content={data} id={id} />
    </main>
  );
};
