import { getServerSideUser } from "@/lib/payload-utils";
import { cookies } from "next/headers";
import { getPayloadClient } from "@/server/get-payload";
import Loader from "@/components/Loader";
import PurchaseCourse from "@/components/PurchaseCourse";
import PurchaseProduct from "@/components/PurchaseProduct";
import { toast } from "sonner";
import { redirect } from "next/navigation";

interface PageProps {
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}

const PurchasesPage = async ({ searchParams }: PageProps) => {
  const nextCookies = cookies();
  const response = await getServerSideUser(nextCookies);

  // if (response.status === 401 || !response.user) {
  //   toast.error("Session Expired, Please Login Again");
  //   redirect("/sign-in")
  //   return (
  //     <div className="mt-28 text-2xl text-center font-semibold">
  //       Can&apos;t Access Without Login
  //     </div>
  //   );
  // }

  const { user } = response;
  const payload = await getPayloadClient();

  const { docs: purchasesProduct }: any = await payload.find({
    collection: "purchases",
    depth: 2,
    where: {
      user: {
        equals: user.id,
      },
      purchaseType: {
        equals: "assets",
      },
    },
  });

  const { docs: purchasesCourse }: any = await payload.find({
    collection: "purchases",
    where: {
      user: {
        equals: user.id,
      },
      purchaseType: {
        equals: "course",
      },
    },
  });

  return (
    <>
      <Loader />
        <div className="md:container pt-12">
          {/* product purchase component*/}
          <PurchaseProduct user={user} purchases={purchasesProduct} />
          {/* course purchase component*/}
          <PurchaseCourse user={user} purchases={purchasesCourse} />
        </div>
    </>
  );
};

export default PurchasesPage;
