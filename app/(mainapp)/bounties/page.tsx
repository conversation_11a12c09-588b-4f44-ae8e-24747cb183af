import React from "react";
import Image from "next/image";
import { cookies } from "next/headers";
import { getServerSideUser } from "../../../lib/payload-utils";
import Container from "@/components/ui/container";
import CreateBountyForm from "./components/CreateBountyForm";
import BountyFiltersCards from "./components/BountyFiltersCards";
import { Metadata } from "next";
import { BackgroundBeams } from "@/components/ui/background-beams";
// import ChatTestPage from "@/components/ChatTestPage";


export const metadata: Metadata = process.env.SERVER_ENV === 'prod'
  ? { 
      title: {
        absolute: "Accelerate AI Innovation: Post Bounties & Collaborate with Top AI Engineers | Rentprompts",
      },
      keywords: [
        "AI Bounties", 
        "Generative AI Bounties", 
        "AI Projects", 
        "Generative AI Projects", 
        "RentPrompts Bounty", 
        "RentPrompts Bounties", 
        "AI Job Listings", 
        "AI Marketplace Jobs", 
        "AI Freelance Opportunities",
        "AI Gig work",
        "AI Gig works",
        "Earn Money with AI Projects",
        "Submit AI Work",
        "Submit AI Bounties",
        "Submit AI Bounties and Earn",
        "Create AI Bounty",
        "List AI Bounty",
        "Create AI Works",
        "Create AI Projects",
        "Rewarding AI Bounties",
        "Rewarding AI Projects",
        "AI Heckathon",
        "AI Freelancing",
        "Remote AI Work",
        "Remote AI Jobs",
        "Innovate with AI",
        "Automate with AI",
        "Cheapest Platform for AI Projects",
        "Cheapest Platform for AI Bounties",
        "Collaborate with AI Projects",
        "Collaborate with AI Bounties",
        "Hire AI Engineers",
        "Hire AI Engineers for AI Projects",
        "Hire AI Engineers for AI Bounties",
        "AI Usecases",
        "Chatbot Development",
        "Agent Development",
        "Quick AI Development",
        "Innovative AI Platform",
        "AI Problem Solving",
        "Crowdsource AI Solutions",
        "Remote AI Jobs",
        "Freelance AI Projects",
        "Collaborative AI Engineering",
        "AI Innovation Platform",
        "Monetize AI Skills",
        "Enterprise AI Challenges",
        "Generative AI Projects",
        "AI and Human Collaboration",
        "Automate AI Development",
      ],
      alternates: {
        canonical: "/bounties",
      },
      description: "Unlock AI-driven innovation with RentPrompt's Bounty Platform. Post challenges, set rewards, and crowdsource solutions from a global community of top AI engineers to turn ideas into reality—fast, affordable, and collaborative",
      openGraph: {
        title: "Accelerate AI Innovation: Post Bounties & Collaborate with Top AI Engineers | Rentprompts",
        description: "Unlock AI-driven innovation with RentPrompt's Bounty Platform. Post challenges, set rewards, and crowdsource solutions from a global community of top AI engineers to turn ideas into reality—fast, affordable, and collaborative",
        url: "/bounties",
        siteName: "Rentprompts",
        images: [
          {
            url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
            alt: "RentPrompts Bounties Banner",
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title: "Accelerate AI Innovation: Post Bounties & Collaborate with Top AI Engineers | Rentprompts",
        description: "Unlock AI-driven innovation with RentPrompt's Bounty Platform. Post challenges, set rewards, and crowdsource solutions from a global community of top AI engineers to turn ideas into reality—fast, affordable, and collaborative",
        images: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
      },
      verification: {
        google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
      },
      robots: {
        index: true,
        follow: true,
      },
    } 
  : null;


const Page = async () => {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);
  const myBounties = user?.bounties || [];

  return (
    <>
    

    <Container>
    <CreateBountyForm user={user} />
    {/* <BackgroundBeams className="flex items-center flex-col justify-center px-2 md:px-10 w-full h-full"></BackgroundBeams> */}
      {/* ---------------------Search Bar and Filters ----------------*/}

      <BountyFiltersCards user={user} myBounties={myBounties} />

      {/* <div className="relative w-11/12 md:w-9/12 mx-auto md:self-center h-full z-10 flex flex-col justify-center items-center bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-3 dark:focus:ring-offset-gray-800 transition-all ">
        <div className="absolute inset-0 flex justify-center items-center rounded">
          <Image
            height={400}
            width={1100}
            src="https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/Image.png"
            alt=""
            className="object-cover w-full h-full rounded opacity-80 blur-md"
          />
        </div>
        <div className="mx-3 my-7 md:m-7 flex flex-col py-6 md:px-8 text-center gap-1.5 justify-center px-4 w-10/12 h-[80%] items-center rounded bg-[#FFFFFF1F] backdrop-blur-sm z-10">
          <p className="text-white md:text-lg lg:max-w-[70%] mt-6 text-center ">
            Welcome to our Generative AI Bounty Program, where clients with
            challenging AI problems meet skilled engineers ready to provide
            innovative solutions. Submit your AI problem statements and tap into
            our talented community for groundbreaking results. For engineers,
            this is your chance to tackle real-world AI issues, showcase your
            expertise, and earn rewards. Join us to transform challenges into
            opportunities and drive the future of generative AI.
          </p>
        </div>
      </div> */}
      {/* <ChatTestPage/> */}

    </Container>
    </>
  );
};

export default Page;
