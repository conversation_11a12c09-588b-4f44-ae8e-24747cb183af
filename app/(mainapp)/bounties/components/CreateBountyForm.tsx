"use client";
import React, { useState } from "react";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { SpaceModal } from "@/components/SpaceModal";
import { useForm, SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { User } from "@/server/payload-types";
import DynamicSquareBackground from "@/components/landingWiget";

interface CreateBountyFormProps {
  user: User | null;
}

const tagOptions = [
  { value: "ai/ml", label: "AI/ML" },
  { value: "assets", label: "Assets" },
  { value: "backend", label: "Backend" },
  { value: "bug", label: "Bug" },
  { value: "blockchain", label: "Blockchain" },
  { value: "chatbot", label: "Chatbot" },
  { value: "design", label: "Design" },
  { value: "finetuning", label: "Finetuning" },
  { value: "frontend", label: "Frontend" },
  { value: "fullstack", label: "Fullstack" },
  { value: "prompt", label: "Prompt" },
  { value: "testing", label: "Testing" },
  { value: "ui/ux", label: "UI/UX" },
];

const CreateBountyForm = ({ user }: CreateBountyFormProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rupees, setRupees] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  // const [currentBounty, setCurrentBounty] = useState<Bounty | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const handleTermsClick = () => {
    setTermsAccepted((prevState) => !prevState);
  };

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
    watch,
  } = useForm<FormData>();

  const today = new Date().toISOString().split("T")[0];

  const onSubmit: SubmitHandler<FormData> = async (formData, event) => {
    formData.user = user?.id;
    setLoading(true);

    try {
      const userCoinBalance = user?.coinBalance ?? 0;
      const estimatedPrice = parseFloat(formData.estimatedPrice);

      if (userCoinBalance < estimatedPrice) {
        toast.error("Insufficient balance. Please recharge your account.");
        return;
      }

      const formDataToSend = new FormData();
      formDataToSend.append("title", formData.title);
      formDataToSend.append("content", formData.content);
      formDataToSend.append("completionDate", formData.completionDate);
      formDataToSend.append("estimatedPrice", formData.estimatedPrice);
      formDataToSend.append("bountyType", formData.bountyType);
      formDataToSend.append("user", formData.user);
      formDataToSend.append("needsApproval", (formData as any).needsApproval);

      formData.tags.forEach((tag) => {
        formDataToSend.append("tags", tag);
      });
      //console.log("formDataToSend", formDataToSend);
      // Check if the form contains 'product_files'
      if (formData.product_files && formData.product_files.length > 0) {
        const fileFormData = new FormData();

        // Convert FileList to Array
        Array.from(formData.product_files).forEach((file) => {
          fileFormData.append("file", file);
        });

        try {
          const mediaRes = await fetch(
            `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files?depth=0`,
            {
              method: "POST",
              credentials: "include",
              body: fileFormData,
            }
          );

          if (mediaRes.ok) {
            const mediaData = await mediaRes.json();
            const uploadedFilesIds = mediaData.doc.id;
            formDataToSend.append("product_files", uploadedFilesIds);
          } else {
            console.error("Error uploading media:");
            toast.error("Media upload failed. Please try again.");
          }
        } catch (mediaError) {
          console.error("Media upload error:", mediaError);
          toast.error("Media upload failed. Please try again.");
        }
      }
     // console.log("formdata",formData)
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties?depth=0`,
          {
            method: "POST",
            credentials: "include",
            body: formDataToSend,
          }
        );

        const data = await res.json();

        if (res.ok) {
          setIsModalOpen(false);
          {
            (formData as any).needsApproval === true
              ? toast.success(
                  "Bounty Created Successfully and Set for Approval"
                )
              : toast.success("Bounty Created Successfully");
          }
          reset();
          setRupees("");
          window.location.reload();
        } else {
          console.error("Error:", data);
          toast.error(data.errors[0].message);
        }
      } catch (error) {
        console.error("Error creating bounty:", error);
        toast.error("Failed to create bounty. Please try again.");
      } finally {
        setLoading(false);
      }
    } catch (err) {
      console.log(err);
      toast.error("Bounty Creation Failed, Please try again");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (event: any) => {
    const value = event.target.value;
    setRupees(value);
  };

  return (
    <>
    <div className="w-full mx-auto">
          <DynamicSquareBackground
            buttonHref="https://discord.gg/kPkYbzMvN3"
            buttonHref1="/dashboard/create/bounty"
            buttonText="JOIN DISCORD"
            buttonText1="CREATE BOUNTY"
            description="Post Your Challenges and Needs, Set Bounties, and Crowdsource AI-Driven Solutions From Global Community of AI Experts!"
            tag="Bounty"
            title="Unlock AI Innovation with RentPrompt's Bounty program"
            image={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/bounty.png`}
            image1=""
          />
        </div>
      {/* <div className="w-11/12 mx-auto md:pt-10 rounded-md overflow-hidden flex flex-col items-center justify-center"> */}
        
        {/* <h1 className="text-white text-4xl md:text-6xl lg:w-[80%] font-bold text-center pt-7 md:pt-0">
          Generative AI Bounties: Where Creativity Meets Challenges
        </h1>
        <p className="text-white md:text-lg lg:max-w-[80%] mt-6 text-center ">
          Be part of a vibrant Bounty Program where every challenge is a chance
          for innovation. Let&apos;s advance generative AI together!
        </p> */}
        
        {/* <div className="flex flex-row items-center gap-4 mt-8">
          <Button
            size="lg"
            className="text-sm lg:text-xl font-semibold text-primary-muted bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent text-white rounded z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-2 md:py-3 px-4 md:px-6 dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
            onClick={() => {
              if (user) {
                setIsModalOpen(true);
                // setCurrentBounty(null);
              } else {
                toast.error("Please login to create a bounty");
              }
            }}
          >
            Create a Bounty
            <Plus size={20} />
          </Button>

          <a
            className="py-2 md:py-2 px-4 md:px-6 text-sm lg:text-xl font-extrabold text-primary-muted bg-white text-indigo-600 rounded z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none hover:cursor-pointer"
            href="https://discord.gg/kPkYbzMvN3"
            target="_blank"
            rel="noopener noreferrer"
          >
            Join Discord →
          </a>
        </div> */}
      {/* </div> */}
      <SpaceModal
        title="New Bounty"
        description="Create Your Bounty Here"
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      >
        <div className="space-y-4 py-2 pb-4">
          <form onSubmit={handleSubmit(onSubmit)} encType="multipart/form-data">
            <div className="space-y-1 h-[400px] overflow-auto mb-2">
              <div className="pr-2">
                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>
                    Bounty Title :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <input
                    className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                    placeholder="Name your bounty"
                    type="text"
                    {...register("title", {
                      required: "Title is required",
                      minLength: {
                        value: 5,
                        message: "Title must be at least 5 characters long",
                      },
                    })}
                  />
                  {errors.title && (
                    <p className="text-yellow-300 text-sm">
                      {errors.title.message}
                    </p>
                  )}
                </div>
                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>
                    Bounty Description :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <textarea
                    className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                    placeholder="Your bounty description"
                    rows={5}
                    {...register("content", {
                      required: "Description is required",
                      minLength: {
                        value: 10,
                        message:
                          "Description must be at least 10 characters long",
                      },
                    })}
                  />
                  {errors.content && (
                    <p className="text-yellow-300 text-sm">
                      {errors.content.message}
                    </p>
                  )}
                </div>
                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>
                    Bounty Completion Date :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <input
                    className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                    placeholder="Bounty completion date"
                    type="date"
                    min={today}
                    {...register("completionDate", {
                      required: "Completion date is required",
                    })}
                  />
                  {errors.completionDate && (
                    <p className="text-yellow-300 text-sm">
                      {errors.completionDate.message}
                    </p>
                  )}
                </div>
                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>
                    Bounty Estimate Credits :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <input
                    className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                    placeholder="Bounty amount in Coins"
                    type="number"
                    min={0}
                    {...register("estimatedPrice", {
                      required: "Estimated price is required",
                      validate: (value) =>
                        Number(value) > 0 || "Price must be greater than zero",
                    })}
                    onChange={handleInputChange}
                  />

                  {errors.estimatedPrice && (
                    <p className="text-yellow-300 text-sm">
                      {errors.estimatedPrice.message}
                    </p>
                  )}

                  <p className="text-sm text-green-400">
                    Estimated price in INR :{" "}
                  </p>
                </div>

                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>
                    Bounty Related to:{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <Select
                    {...register("bountyType", {
                      required: "Bounty type is required",
                    })}
                    onValueChange={(value) => {
                      setValue("bountyType", value);
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Bounty Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ai">
                        Artificial Intelligence (AI)
                      </SelectItem>
                      <SelectItem value="blockchain">Blockchain</SelectItem>
                      <SelectItem value="ml">Machine Learning (ML)</SelectItem>
                      <SelectItem value="mobile">Mobile Application</SelectItem>
                      <SelectItem value="web">Web Application</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.bountyType && (
                    <p className="text-yellow-300">
                      {errors.bountyType.message}
                    </p>
                  )}
                </div>

                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>Related Files :</label>
                  <input
                    className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                    placeholder="Name your bounty"
                    type="file"
                    multiple
                    {...register("product_files", {
                      validate: (value) => {
                        if (value && value.length > 5) {
                          return "You can upload up to 5 files";
                        }
                        return true;
                      },
                    })}
                  />
                  {errors.product_files && (
                    <p className="text-yellow-300">
                      {errors.product_files.message}
                    </p>
                  )}
                </div>

                <div className="py-2 col-span-12 lg:col-span-10">
                  <label>Tags:</label>
                  <select
                    className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                    {...register("tags", {})}
                    multiple
                  >
                    {/* Map through tagOptions to generate option elements */}
                    {tagOptions.map((option, index) => (
                      <option key={index} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {errors.tags && (
                    <p className="text-yellow-300">{errors.tags.message}</p>
                  )}
                </div>

                <div className="flex items-center gap-1">
                  <input
                    type="checkbox"
                    {...register("needsApproval" as any)}
                    className="h-4 w-4"
                  />
                  <label>Send for Approval</label>
                </div>

                <div className=" py-2">
                  <label
                    htmlFor="termsAccepted"
                    className="flex items-center gap-2 font-normal"
                  >
                    <input
                      type="checkbox"
                      id="termsAccepted"
                      name="termsAccepted"
                      {...register("termsAccepted", {
                        required: "You must accept the terms and conditions",
                      })}
                      className="h-4 w-4"
                    />
                    <label onClick={handleTermsClick}>
                      <Link
                        href={"/bountytandc"}
                        className=" hover:text-blue-500"
                        target="_blank"
                      >
                        Accept Terms and Conditions
                      </Link>
                    </label>
                  </label>
                  {errors.termsAccepted && (
                    <p className="text-yellow-300">
                      {errors.termsAccepted.message}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button disabled={loading} type="submit">
                {loading ? "Creating..." : "Create"}
              </Button>
            </div>
          </form>
        </div>
      </SpaceModal>
    </>
  );
};

export default CreateBountyForm;
