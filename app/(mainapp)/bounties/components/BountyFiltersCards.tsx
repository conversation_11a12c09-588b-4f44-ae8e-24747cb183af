"use client";
import React, { useState, useEffect, useRef } from "react";
import SearchBar from "@/components/ui/searchbar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Icons } from "@/components/Icons";
import BountyCard from "@/components/BountyCard";
import { User } from "@/server/payload-types";
import { Loader2 } from "lucide-react";

interface BountyFiltersCardsProps {
  user: User | null;
  myBounties: any;
}

const BountyFiltersCards = ({ user, myBounties }: BountyFiltersCardsProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [sortBy, setSortBy] = useState("");
  const [bountyStatus, setBountyStatus] = useState("");
  const [filteredBounties, setFilteredBounties] = useState<Bounty[]>([]);
  const [data, setData] = useState<Bounty[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [noMoreData, setNoMoreData] = useState(false);
  const lastBountyRef = useRef<HTMLDivElement>(null);






  useEffect(() => {
    const fetchBounties = async () => {
      if (!hasMore) return;
  
      try {
        setIsLoadingMore(true);
        const res = await fetch(`/api2/bounties?page=${page}&limit=${limit}`);
        const getdata = await res.json();
        const newData = getdata?.data || [];
  
        if (newData.length > 0) {
          setData((prevData) => {
            // Ensure no duplicate entries by filtering out already existing IDs
            const existingIds = new Set(prevData.map((bounty) => bounty.id));
            const uniqueNewData = newData.filter((bounty) => !existingIds.has(bounty.id));
  
            return [...prevData, ...uniqueNewData];
          });
          setHasMore(getdata.hasMore);
        } else {
          setHasMore(false);
        }
      } catch (error) {
        console.error("Error fetching bounties:", error);
      } finally {
        setIsLoadingMore(false);
      }
    };
  
    fetchBounties();
  }, [page]);


  const updateFilteredBounties = () => {
    setNoMoreData(false);
    const filtered =
      bountyStatus === "myBounty"
        ? myBounties
        : data
          .filter((bounty) => {
            if (
              selectedCategory !== "all" &&
              !bounty?.title
                .toLowerCase()
                .includes(selectedCategory.toLowerCase())
            ) {
              return false;
            }
            if (
              bountyStatus !== "all" &&
              bountyStatus &&
              bounty.status !== bountyStatus
            ) {
              return false;
            }
            if (
              searchQuery &&
              !bounty.title.toLowerCase().includes(searchQuery)
            ) {
              return false;
            }
            return true;
          })
          .sort((a, b) => {
            if (sortBy === "reward") {
              const priceA =
                typeof a.estimatedPrice === "string"
                  ? parseFloat(a.estimatedPrice.replace("$", ""))
                  : a.estimatedPrice;
              const priceB =
                typeof b.estimatedPrice === "string"
                  ? parseFloat(b.estimatedPrice.replace("$", ""))
                  : b.estimatedPrice;
              return priceB - priceA;
            }
            if (sortBy === "posting_date") {
              const dateA = new Date(a.createdAt);
              const dateB = new Date(b.createdAt);
              return dateB.getTime() - dateA.getTime();
            }
            return 0;


          });
    setFilteredBounties(filtered);

    if (filtered.length === 0 && data.length > 0) {
      setNoMoreData(true);
    } else {
      setNoMoreData(false);
    }
  };

  

  useEffect(() => {
    updateFilteredBounties();
  }, [data, selectedCategory, bountyStatus, searchQuery, sortBy, myBounties]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          setIsLoadingMore(true);
          setPage((prevPage) => prevPage + 1);
        }
      },
      { threshold: 1 }
    );

    if (lastBountyRef.current) {
      observer.observe(lastBountyRef.current);
    }

    return () => {
      if (lastBountyRef.current) {
        observer.unobserve(lastBountyRef.current);
      }
    };
  }, [hasMore, isLoadingMore]);

  const handleValueChange = (value: string) => {
    setBountyStatus(value);
    if (value === "myBounty") {
      setFilteredBounties(myBounties);
    } else {
      setFilteredBounties(data);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query.toLowerCase());
  };




  return (
    <div className="px-4 md:px-0 md:w-9/12 mx-auto py-2">
      <div className="flex flex-col md:flex-row items-center pb-5 justify-between gap-3 md:gap-8 w-full">
        <div className="w-full md:w-[50%] md:-mt-6">
          <SearchBar mainheader={false} onSearch={handleSearch} />
        </div>

        <div>
          <form className="flex gap-3 md:justify-end">
            <div className="py-2 col-span-12 lg:col-span-10">
              <Select onValueChange={(value) => setSelectedCategory(value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Sort By Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="AI">AI</SelectItem>
                  <SelectItem value="Blockchain">Blockchain</SelectItem>
                  <SelectItem value="Mobile">Mobile</SelectItem>
                  <SelectItem value="ML">ML</SelectItem>
                  <SelectItem value="Web">Web</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="py-2 col-span-12 lg:col-span-10">
              <Select onValueChange={(value) => setSortBy(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="posting_date">Posting Date</SelectItem>
                  <SelectItem value="recommended">Recommended</SelectItem>
                  <SelectItem value="reward">Reward</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {user ? (
              <div className="py-2 col-span-12 lg:col-span-10">
                <Select onValueChange={handleValueChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Bounties" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Bounties</SelectItem>
                    <SelectItem value="myBounty">My Bounties</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            ) : (
              ""
            )}
          </form>
        </div>
      </div>
     <div className="grid gap-5 mt-5">
  {/* Loader Only on First Page Load */}
  {loading && page === 1 ? (
    <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50 ">
      <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
    </div>
  ) : filteredBounties.length > 0 ? (
    filteredBounties.map((bounty, index) => (
      <div key={index}>
        <BountyCard
          key={index}
          bounty={bounty}
          index={index}
          user={user}
          bountyStatus={bountyStatus}
        />
      </div>
    ))
  ) : (
    // Show "No bounties present" only if loading is false and no data available
    !loading && !isLoadingMore && <p className="text-white text-center">No bounties present</p>
  )}

  {/* Show Infinite Scroll Loader */}
  {isLoadingMore && (
    <div className="flex justify-center items-center py-4">
      <Loader2 className="h-8 w-8 spin-animation font-normal" />
    </div>
  )}

  {/* No More Bounties Message */}
  {!hasMore && !isLoadingMore && filteredBounties.length > 0 && (
    <div className="flex justify-center items-center py-4">
      <p className="text-white">No more bounties to fetch.</p>
    </div>
  )}

  <div ref={lastBountyRef} />
</div>
    </div>
  );
};

export default BountyFiltersCards;