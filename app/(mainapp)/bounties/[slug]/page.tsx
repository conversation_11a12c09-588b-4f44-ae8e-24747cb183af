import React, { ReactNode } from "react";
import Link from "next/link";
import { getPayloadClient } from "@/server/get-payload";
import { User } from "@/server/payload-types";
import {
  ArrowLeft,
  Users,
  UserCircleIcon,
  Calendar,
  Clock,
  UsersIcon,
} from "lucide-react";
import ApplyBounty from "@/components/ApplyBounty";
import coinImage from "../../../../public/img/coin-png.png";
import BountyShareButton from "@/components/BountyShareButton";
import Loader from "@/components/Loader";
import { cookies } from "next/headers";
import { getServerSideUser } from "../../../../lib/payload-utils";
import { Metadata } from "next";
import ApplicationTabs from "@/components/bountysteps/bountyChat";
import Image from "next/image";
import Container from "@/components/ui/container";
import ApplicationStatusMessage from "@/components/bountysteps/ApplicantStatus";

interface PageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }): Promise<Metadata> {
  const payload = await getPayloadClient();
  // const previousImage = (await parent).openGraph?.images || [];

  const bountie: any = await payload.find({
    collection: "bounties",
    where: {
      slug: {
        equals: params.slug,
      },
    },
  });
  // console.log("Bountie Data:", bountie);
  const bountieData = bountie?.docs[0] as any;

  const bountieTitle = bountieData?.title ?? "Default Blog Title"; // Fallback title
  const bountieContent =
    bountieData?.content?.slice(0, 150) ?? "Default description";
  // const img = bountie?.docs[0]?.images[0]?.image?.url;
  const bountieUrl = `${process.env.PAYLOAD_PUBLIC_SITE_URL}/bounties/${bountie.slug}`;
  const bountieimg =
    "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp";

  const metadata: Metadata =
    process.env.SERVER_ENV === "prod"
      ? {
          title: bountieTitle,
          description: bountieContent,
          alternates: {
            canonical: `/bounties/${bountieData?.slug}`,
          },
          openGraph: {
            title: bountieTitle,
            description: bountieContent,
            url: bountieUrl,
            siteName: "Rentprompt.com",
            images: [bountieimg],
          },
          verification: {
            google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
          },
        }
      : null;

  return metadata;
}

const Page = async ({ params: { slug } }: PageProps) => {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);

  const payload = await getPayloadClient();

  const bountyData = await payload.find({
    collection: "bounties",
    where: {
      slug: {
        equals: slug,
      },
    },
  });

  const bounty = bountyData.docs[0];
  if (!bounty) {
    return <div>Bounty not found</div>;
  }
  //opn/close bounty code
  const createdAtDate = new Date(bounty.createdAt as string);
  const currentDate = new Date();

  const calculateDaysLeft = () => {
    const completionDate = new Date(bounty.completionDate as string);
    const currentDate = new Date();
    const timeDiff = completionDate.getTime() - currentDate.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    return daysLeft;
  };

  // const applyExpireDate = new Date(bounty.applyExpireDate as string);
  // const isApplyExpired = currentDate > applyExpireDate;

  // const { createdAt, completionDate } = bounty;
  const daysLeft = calculateDaysLeft();
  // const isExpired = daysLeft < 0;

  const bountyUser = bounty.user as User;
  const userName =
    bountyUser && typeof bountyUser !== "string"
      ? bountyUser.email.split("@")[0]
      : "Anonymous";

  const userImage = (bounty?.user as any)?.profileImage?.url;

  // const isCompleted = bounty.status === "completed";

  const applicant = bounty.applicants as any[];
  const applicantsCount = applicant.length;

  const isApplicantsLimitReached =
    applicantsCount >= (bounty.applicantsLimit as number);




// const currentDate = new Date();

// Helper to remove time from date
const getDateOnly = (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate());

// Parse bounty dates
const completionDate = new Date(bounty.completionDate as string);
const applyExpireDate = new Date(bounty.applyExpireDate as string);

// Get date-only for accurate comparisons
const today = getDateOnly(currentDate);
const applyExpireDay = getDateOnly(applyExpireDate);
const completionDay = getDateOnly(completionDate);

// Condition: Can still apply?
const isApplyExpired = today > applyExpireDay;

// Condition: Show "Completed" only AFTER completion date
const isCompleted = bounty.status === "completed" && today > completionDay;

// Condition: Expired based on completionDate
const isExpired = today > completionDay;
  return (
    <>
      <Loader />

      <Container>
        {(bounty?.applicants as any)?.map((applicant: any) => {
          if (applicant?.userId === user?.id) {
            return <ApplicationStatusMessage applicant={applicant} />;
          }
          return null;
        })}
        <div className="flex flex-col md:flex-row mx-auto justify-center gap-8 w-11/12 py-6 mt-20 md:mt-12">
          <Link
            href="/bounties"
            className="fixed max-md:top-14 md:bottom-4 left-2 z-50"
          >
            <button className="text-white px-3 bg-indigo-700 py-2 my-4 rounded-lg shadow-md hover:bg-indigo-600 transition-all duration-200">
              <div className="flex items-center text-md font-bold">
                <ArrowLeft size={18} />
              </div>
            </button>
          </Link>

          <div className="w-full md:w-1/3 mx-auto">
            <div className="bg-gradient-to-br from-indigo-800 to-indigo-700 rounded-xl shadow-xl p-4 mb-6">
              <div className="gap-6">
                <h1 className="text-2xl md:text-3xl font-bold capitalize break-words whitespace-normal">
                  {bounty.title as string}
                </h1>
                <div className="text-3xl md:text-4xl font-extrabold flex items-center mt-4">
                  {bounty.estimatedPrice === 0 ? (
                    <div className="px-4 py-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white text-xl font-bold uppercase tracking-wider shadow-md transition-all duration-300 ease-in-out hover:scale-105">
                      FREE
                    </div>
                  ) : (
                    <>
                      <h3 className="text-3xl">
                        Earn {bounty.estimatedPrice as ReactNode}
                      </h3>
                      <Image
                        src={coinImage.src}
                        alt="Coin"
                        width="100"
                        height="100"
                        className="w-7 h-8 md:w-9 md:h-10 ml-2"
                        loading="lazy"
                      />
                    </>
                  )}
                </div>
              </div>

              <div className="flex flex-wrap gap-2 my-4">
                <span className="px-3 py-1 rounded-lg text-white bg-indigo-600 uppercase text-sm font-medium">
                  {bounty.bountyType as string}
                </span>
                {Array.isArray((bounty as any)?.tags) &&
                  (bounty as any).tags.map((item: string, index: number) => (
                    <span
                      key={item}
                      className="px-3 py-1 rounded-lg text-white bg-indigo-600 uppercase text-sm font-medium"
                    >
                      {item}
                    </span>
                  ))}
              </div>

              <div className="my-2">
                <p className="text-gray-300 text-sm flex items-center">
                  <Clock size={14} className="mr-1" />
                  {(() => {
                    const createdAtDate = new Date(bounty.createdAt as any);
                    const currentDate = new Date();
                    const timeDifference =
                      currentDate.getTime() - createdAtDate.getTime();
                    const daysDifference = Math.floor(
                      timeDifference / (1000 * 3600 * 24)
                    );
                    if (daysDifference >= 7) {
                      const weeksDifference = Math.floor(daysDifference / 7);
                      return `Posted ${weeksDifference} week${
                        weeksDifference > 1 ? "s" : ""
                      } ago`;
                    } else {
                      return `Posted ${daysDifference} day${
                        daysDifference > 1 ? "s" : ""
                      } ago`;
                    }
                  })()}
                </p>
              </div>

              <div className="flex items-center gap-3 border-indigo-600">
                {userImage ? (
                  <Image
                    src={userImage}
                    width="100"
                    height="100"
                    alt={`${userName}'s Profile`}
                    className="w-10 h-10 md:w-12 md:h-12 rounded-full object-cover border-2 border-indigo-500"
                    loading="lazy"
                  />
                ) : (
                  <Image
                    src={
                      "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp"
                    }
                    width="100"
                    height="100"
                    alt={`${userName}'s Profile`}
                    className="w-10 h-10 md:w-12 md:h-12 rounded-full border-2 border-indigo-500"
                    loading="lazy"
                  />
                )}

                <Link
                  href={`/users/${(bounty?.user as { user_name: string })?.user_name}`}
                >
                  {(bounty?.user as { user_name: string })?.user_name ? (
                    <p className="text-lg md:text-lg text-gray-300 font-bold">
                      {" "}
                      {(bounty?.user as { user_name: string })?.user_name}
                    </p>
                  ) : (
                    <p className="text-lg md:text-lg text-gray-300 font-bold">
                      {" "}
                      {userName}
                    </p>
                  )}
                </Link>
              </div>
            </div>
            {/* <BountyShareButton />
            <div className="text-lg text-indigo-200">
              {isExpired ? " " : ``}
            </div>
            {!isCompleted && !isExpired ? (
              <ApplyBounty
                bounty={bounty}
                index={0}
                user={user}
                bountyStatus={""}
                
              />
            ) : (
              ``
            )} */}

            <div className="bg-gradient-to-br from-indigo-800 to-indigo-700 rounded-xl shadow-xl p-4 mb-6">
              <div className="space-y-1">
                <div className="flex justify-between items-center py-2 border-b border-indigo-600/50">
                  <h3 className="text-base font-semibold flex items-center">
                    <UsersIcon size={16} className="mr-2" />
                    Total Applicants:
                  </h3>
                  <p className="text-base text-indigo-200 font-medium">
                    {(applicant as any[]).length !== 0
                      ? `${(applicant as any[]).length as any} Applicants`
                      : "N/A"}
                  </p>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-indigo-600/50">
                  <h3 className="text-base font-semibold flex items-center">
                    <UsersIcon size={16} className="mr-2" />
                    {/* Accept Approval: */}
                    Approval Limit:
                  </h3>
                  <p className="text-base text-indigo-200 font-medium">
                    {bounty?.applicantsLimit as number}
                  </p>
                </div>

                <div className="flex justify-between items-center py-2 border-b border-indigo-600/50">
                  <h3 className="text-base font-semibold flex items-center">
                    <Calendar size={16} className="mr-2" />
                    {/* Expiry Date: */}
                    Application Deadline:
                  </h3>
                  <p className="text-base text-indigo-200 font-medium">
                    {bounty?.applyExpireDate
                      ? new Date(
                          bounty?.applyExpireDate as string
                        ).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>

                <div className="flex justify-between items-center py-2 border-b border-indigo-600/50">
                  <h3 className="text-base font-semibold flex items-center">
                    <Calendar size={16} className="mr-2" />
                    {/* Completion Date: */}
                    Completion Deadline:
                  </h3>
                  <p className="text-base text-indigo-200 font-medium">
                    {bounty?.completionDate
                      ? new Date(
                          bounty?.completionDate as string
                        ).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>
              </div>

              <div className="flex justify-between items-center mt-6">
                <div className="flex gap-2">
                  <button className="bg-white text-indigo-600 p-2 hover:bg-indigo-500 hover:text-white rounded-lg">
                    <Link
                      href={"https://discord.gg/kPkYbzMvN3"}
                      target="_blank"
                      rel="noreferrer"
                    >
                      <span className="sr-only">Discord</span>
                      <Users size={20} />
                    </Link>
                  </button>
                  <BountyShareButton />
                </div>
               
                  <ApplyBounty
                    bounty={bounty}
                    index={0}
                    user={user}
                    bountyStatus={""}
                    isApplicantsLimitReached={isApplicantsLimitReached}
                  />
              </div>
              <div className="text-lg text-indigo-200">
                {isExpired ? " " : ``}
              </div>
            </div>
          </div>

          <div className="w-full md:w-2/3">
            <ApplicationTabs bounty={bounty} user={user} />
          </div>
        </div>
      </Container>
    </>
  );
};

export default Page;
