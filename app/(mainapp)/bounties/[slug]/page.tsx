import React, { ReactNode } from "react";
import Link from "next/link";
import { getPayloadClient } from "@/server/get-payload";
import { User } from "@/server/payload-types";
import { ArrowLeft, Users, UserCircleIcon } from "lucide-react";
import ApplyBounty from "@/components/ApplyBounty";

import BountyShareButton from "@/components/BountyShareButton";
import Loader from "@/components/Loader";
import { cookies } from "next/headers";
import { getServerSideUser } from "../../../../lib/payload-utils";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import { Metadata } from "next";

interface PageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata(
  { params }
): Promise<Metadata> {
  const payload = await getPayloadClient();
  // const previousImage = (await parent).openGraph?.images || [];

  const bountie: any = await payload.find({
    collection: "bounties",
    where: {
      slug: {
        equals: params.slug,
      },
    },
  });
const bountieData= bountie?.docs[0] as any;

// console.log("bountieData===>>",bountieData);

const bountieTitle = bountieData?.title ?? "Default Blog Title"; // Fallback title
  const bountieContent = bountieData?.content?.slice(0, 150) ?? "Default description";
  // const img = bountie?.docs[0]?.images[0]?.image?.url;
  const bountieUrl = `${process.env.PAYLOAD_PUBLIC_SITE_URL}/bounties/${bountie.slug}`;
  const bountieimg = `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Group%2048095914.png`;

  const metadata:Metadata = process.env.SERVER_ENV === 'prod' 
  ? {
    title: bountieTitle,
    description: bountieContent,
    alternates: {
      canonical: `/bounties/${bountieData?.slug}`,
    },
    openGraph: {
      title: bountieTitle,
      description: bountieContent,
      url: bountieUrl,
      siteName: "Rentprompt.com",
      images: [bountieimg],
    },
    verification: {
      google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
    },
  } : null;

  return metadata;
}

const Page = async ({ params: { slug } }: PageProps) => {
  // const {
  //   searchParams: { id },
  //   onEdit,
  // } = args;

  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);

  const payload = await getPayloadClient();
  // const bounty: any = await payload.findByID({
  //   collection: "bounties",
  //   id: id,
  // });
  const bountyData = await payload.find({
    collection: "bounties",
    where: {
      slug: {
        equals: slug,
      },
    },
  });
  const bounty = bountyData.docs[0];
  // console.log(">>>>>bountybountybountybounty");
  // console.log("bounty", bounty);

  if (!bounty) {
    return <div>Bounty not found</div>;
  }

  //opn/close bounty code
  const createdAtDate = new Date(bounty.createdAt as string);
  const currentDate = new Date();

  const calculateDaysLeft = () => {
    const completionDate = new Date(bounty.completionDate as string);
    const currentDate = new Date();
    const timeDiff = completionDate.getTime() - currentDate.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    return daysLeft;
  };

  const { createdAt, completionDate } = bounty;
  const daysLeft = calculateDaysLeft();
  const isExpired = daysLeft < 0;

  const bountyUser = bounty.user as User;
  const userName =
    bountyUser && typeof bountyUser !== "string"
      ? bountyUser.email.split("@")[0]
      : "Anonymous";

  const userImage = (bounty?.user as any)?.profileImage?.url;

  // const Expired = (date) => {
  //   return currentDate > completionDate;
  // };
  const applicant = bounty.applicants;

  const isCompleted=bounty.status==="completed"
  
  return (
    <>
      <Loader />
      <div className="w-11/12 md:w-9/12 md:mt-20  mx-auto justify-center">
        <Link href="/bounties">
          <button className="text-indigo-200 px-2 bg-indigo-700 py-2 my-4 rounded-lg shadow shadow-indigo-900">
            <div className="flex items-center text-md font-bold">
              <ArrowLeft />
              Back to All Bounties
            </div>
          </button>
        </Link>

        <div className="pt-8 md:pt-12">
          <span className="text-4xl md:text-5xl font-extrabold mt-8 flex items-center">
            {bounty.estimatedPrice === 0 ? (
              <div className="px-4 py-2 rounded-full bg-gradient-to-r from-indigo-600 to-indigo-700  text-white text-2xl font-bold uppercase tracking-wider shadow-xl  transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                FREE
              </div>
            ) : (
              <>
                {bounty.estimatedPrice as ReactNode}
                <img
                  src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                  alt="Coin"
                  className="w-8 h-9 md:w-11 md:h-12"
                   loading="lazy"
                />
              </>
            )}
          </span>
          <h1 className="text-3xl py-2 mt-2 md:text-4xl">
            {bounty.title as string}
          </h1>
        </div>
        <div className="my-2 mb-2 space-x-2">
          <span className="px-3 py-1 rounded-lg text-indigo-700 bg-white">
            {bounty.bountyType as string}
          </span>
          <span className="space-x-2">
            {Array.isArray(bounty?.tags) &&
              bounty.tags.map((item, index) => (
                <span
                  key={item}
                  className="px-3 py-1 rounded-lg text-indigo-700 bg-white space-x-2"
                >
                  {item}
                  {index < (bounty.tags as string[]).length - 1}
                </span>
              ))}
          </span>
        </div>
        <div className="flex flex-col md:flex-row md:justify-between md:items-center py-4 gap-1 md:space-y-0 md:space-x-4">
          <div className="flex gap-1 items-center">
            {userImage ? (
              <img
                src={userImage}
                alt={`${userName}'s Profile`}
                className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover"
                 loading="lazy"
              />
            ) : (
              <img
                src={
                  `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}Group%2048095914.webp`
                }
                alt={`${userName}'s Profile`}
                className="w-8 h-8 md:w-10 md:h-10 rounded-full"
                 loading="lazy"
              />
            )}

            <Link href={`/users/${(bounty?.user as { user_name: string })?.user_name}`}>
              {(bounty?.user as { user_name: string })?.user_name ? (
                <p className="text-lg md:text-lg text-gray-300 font-bold">
                  {" "}
                  {(bounty?.user as { user_name: string })?.user_name}
                </p>
              ) : (
                <p className="text-lg md:text-lg text-gray-300 font-bold">
                  {" "}
                  {userName}
                </p>
              )}
            </Link>
          </div>
          <ul>
            <li className="text-gray-300 text-sm md:text-lg md:font-bold md:list-disc">
              {(() => {
                const createdAtDate = new Date(bounty.createdAt);
                const currentDate = new Date();
                const timeDifference =
                  currentDate.getTime() - createdAtDate.getTime();
                const daysDifference = Math.floor(
                  timeDifference / (1000 * 3600 * 24)
                );
                if (daysDifference >= 7) {
                  const weeksDifference = Math.floor(daysDifference / 7);
                  return `Posted ${weeksDifference} week${
                    weeksDifference > 1 ? "s" : ""
                  } ago`;
                } else {
                  return `Posted ${daysDifference} day${
                    daysDifference > 1 ? "s" : ""
                  } ago`;
                }
              })()}
            </li>
          </ul>
          <div className="flex gap-2 mt-3 md:0">
            <button className=" bg-indigo-700 text-white px-2 py-2 hover:bg-white hover:text-indigo-900 rounded">
              <Link
                href={"https://discord.gg/kPkYbzMvN3"}
                target="_blank"
                rel="noreferrer"
              >
                <span className="sr-only"> Discord </span>
                <Users size={20} />
              </Link>
            </button>

            <BountyShareButton />
            <div className="text-lg text-indigo-200">
              {isExpired ? " " : ``}
            </div>
            {!isCompleted && !isExpired ? (
              <ApplyBounty
                bounty={bounty}
                index={0}
                user={user}
                bountyStatus={""}
              />
            ) : (
              ``
            )}
          </div>
        </div>
        <div className="py-4">
          <h2 className="text-xl font-bold mb-1">Information :</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 justify-center gap-4">
            {/* APPLICANTS */}
            <div className="text-center bg-gradient-to-r from-indigo-900 to-indigo-600 border border-cyan-200 rounded-lg shadow shadow-indigo-900">
              <div className="py-2">
                <h3 className="text-lg font-bold pb-1 ">APPLICANTS</h3>
                <p className="text-lg text-indigo-200 ">
                  {applicant.length !== 0
                    ? `${applicant.length} Applied`
                    : "No applicants"}
                </p>
              </div>
            </div>
            {/* TIME */}
            <div className="text-center bg-gradient-to-l from-indigo-900 to-indigo-600 border border-cyan-200 rounded-lg shadow shadow-indigo-900">
              <div className="py-2">
                <h3 className="text-lg font-bold pb-1">TIME</h3>
                <div className="flex flex-wrap justify-center ">
                  <div className="text-lg text-indigo-200">
                    {isExpired ? "Expired" : ``}
                  </div>
                  {!isExpired && (
                    <div className="text-lg text-indigo-200">
                      {" "}
                      {daysLeft} Days Left
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          {/* BOUNTY DESCRIPTION */}
          <div className="border border-cyan-200 rounded-lg p-3 md:p-6 flex flex-col gap-2 md:gap-4  w-full my-4 bg-indigo-900 to-indigo-700">
            <h2 className="text-xl font-bold ">BOUNTY DESCRIPTION</h2>
            <h3 className=" font-bold">Requirements:</h3>
            <div
              className="text-indigo-200 md:-mt-2"
              style={{
                wordBreak: "break-word",
                overflowWrap: "break-word",
                whiteSpace: "pre-wrap",
              }}
            >
              <ReactMarkdown
                components={{
                  a: ({
                    href,
                    children,
                  }: {
                    href?: string;
                    children?: React.ReactNode;
                  }) => (
                    <a
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      {children as ReactNode}
                    </a>
                  ),
                }}
                // rehypePlugins={[rehypeSanitize]}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {typeof bounty?.content === "string"
                  ? bounty.content
                  : "No content available"}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Page;