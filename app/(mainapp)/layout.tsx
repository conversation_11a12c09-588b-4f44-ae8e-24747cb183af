  
  import type { Metada<PERSON> } from 'next'
  import React from 'react'
  import '../globals.css'
  import { toast, Toaster } from 'sonner'
  import { SidebarComponent } from '@/components/ui/sidebar'
  import {Providers} from '@/components/Providers'
  import HeaderItems from '@/components/ui/header/headeritems'
  import Footer from '@/components/Footer'
  import { cookies } from 'next/headers'
  import { getServerSideUser } from '@/lib/payload-utils'
  import { redirect } from 'next/navigation'


  export default async function DashboardLayout({ children }: { children: React.ReactNode }) {

    const nextCookies = cookies();
    const response = await getServerSideUser(nextCookies);

    // if (response.status === 401) {
    //   toast.error('Session expired. Please log in again.'); 
    //   redirect('/sign-in');
    //   return <div>Loading...</div>;
    // }

    const { user } = response;

    
    return (
      <>
      {/* <Header nextCookies={nextCookies} /> */}
        {/* <SecondaryNav /> */}
        <HeaderItems user={user} />
        {/* <MainMenu sidebarnav={false} /> */}

        <div className="md:pt-[calc(60px+40px)]">
          {children}
        </div>

        <Footer />
        
    </>
    )
  }