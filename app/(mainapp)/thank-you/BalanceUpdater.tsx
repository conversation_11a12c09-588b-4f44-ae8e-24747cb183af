"use client";

import { useEffect } from "react";
import { useCoinBalanceContext } from "@/components/coin-balance-initializer";

interface BalanceUpdaterProps {
  newBalance: number;
}

export default function BalanceUpdater({ newBalance }: BalanceUpdaterProps) {
  const { setBalance } = useCoinBalanceContext();

  useEffect(() => {
    // Purchase के बाद balance को immediately update करें
    setBalance(newBalance);
    console.log('🛒 Purchase completed - Balance updated to:', newBalance);
  }, [newBalance, setBalance]);

  return null; // यह component कुछ render नहीं करता
}
