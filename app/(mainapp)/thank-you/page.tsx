

import { getServerSideUser } from '@/lib/payload-utils'
import Image from 'next/image'
import { cookies } from 'next/headers'
import { getPayloadClient } from '@/server/get-payload'
import { notFound, redirect } from 'next/navigation'
import Link from 'next/link'
import DownloadButton from './DownloadButton' // Adjust the import path

import Loader from '@/components/Loader'
import { toast } from 'sonner'

interface PageProps {
  searchParams: {
    [key: string]: string | string[] | undefined
  }
}

const ThankYouPage = async ({ searchParams }: PageProps) => {
  const purchaseId = searchParams.purchaseId
  const nextCookies = cookies()

  const response = await getServerSideUser(nextCookies);

  // if (response.status === 401) {
  //   toast.error("Session expired. Please log in again.");
  //   redirect('/sign-in');
  //   return <div>Loading...</div>;
  // }

  const { user } = response;
  // if (!user) {
  //   redirect("/sign-in"); // Redirect to login page
  //   return null; // Ensure no further rendering
  // }
  const payload = await getPayloadClient()


  const { docs: purchases }: any = await payload.find({
    collection: 'purchases',
    depth: 2,
    where: {
      id: {
        equals: purchaseId,
      },
    },
  })

  if (!purchases || purchases.length === 0) {
    return notFound()
  }

  const purchase = purchases[0]

  const { docs: products }: any = await payload.find({
    collection: 'products',
    depth: 1,
    where: {
      id: {
        equals: purchase.product.id,
      },
    },
  })

  const product = products[0];

  if (!product) return notFound()

  const purchaseUserId = purchase.user.id

  if (!user) {
    return redirect(`/sign-in?origin=thank-you?productId=${product.id}`)
  }

  return (
    <>
    <Loader />
    <main className='relative lg:min-h-full  text-white py-16 md:mt-16'>
      <div className='container mx-auto px-6'>
        <div className='text-center'>
          <p className='text-lg font-medium text-green-400 animate-pulse'>
            Purchase Successful
          </p>
          <h1 className='mt-4 text-4xl font-bold tracking-tight sm:text-5xl'>
            Thank You for Your Purchase!
          </h1>
          <p className='mt-2 text-base text-gray-400'>
            Your purchase was successful and your assets are ready for download.
          </p>
        </div>

        <div className='mt-12 text-xl font-medium'>
          <div className='bg-indigo-700 p-6 rounded-lg shadow-lg'>
            <div className='space-y-3'>
              <p>Purchase ID: <span className='text-gray-300'>{purchase.id}</span></p>
              <p>Product Name: <span className='text-gray-300'>{purchase.product.name}</span></p>
              <p className='flex items-center gap-1'>
                Remaining Credits: <span className='text-gray-300'>{purchase.user.coinBalance.toFixed(2)}</span> 
                <Image src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`} alt="Coin" width={23} height={23} />
              </p>

              <DownloadButton productFiles={product.product_files} product={product} userId={user.id} type="product"/>

              <div className='mt-12 text-right'>
                <Link href='/' className='text-sm font-medium text-blue-400 hover:text-blue-500 transition'>
                  Continue Shopping →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    </>
  )
}

export default ThankYouPage
