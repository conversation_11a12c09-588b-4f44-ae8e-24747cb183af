'use client'
import React, { useState } from 'react';
import { toast } from 'sonner';
import { Download } from 'lucide-react';

const DownloadButton = ({ productFiles, product, userId , type}:any) => {
  const [loading, setLoading] = useState<boolean>(false);

  const downloadFile = async (url: string, filename: string) => {
    if (loading) return; // Prevent multiple downloads at once
    toast.loading("Downloading...")
    setLoading(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api2/downloadFile/${type}/${product.id}/${filename}/${userId}`);

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
  
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      toast.dismiss();
      toast.success("Download Successfully")
      setLoading(false);
    } catch (error) {
      toast.dismiss();
      toast.error('Failed to download file');
    } finally {
      toast.dismiss();
      setLoading(false);
    }
  };
  

  return (
    <div>
      <h2 className="text-lg font-bold text-gray-200">Download Files:</h2>
      <div className="mt-4 flex flex-wrap gap-4">
        {productFiles && (
          <button
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center gap-2"
            onClick={() => downloadFile(productFiles.url, productFiles.filename)}
            disabled={loading}
          >
            <Download/>
            {loading ? 'Downloading...' : 'Download File'}
          </button>
        )}
      </div>
    </div>
  );
};

export default DownloadButton;
