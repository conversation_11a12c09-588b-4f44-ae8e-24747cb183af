"use client";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useRouter, useSearchParams } from "next/navigation";
import { Icons } from "@/components/Icons";
import Container from "@/components/ui/container";
import TiltCard from "@/components/TiltCard";
import { User } from "@/server/payload-types";



import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Description } from "@radix-ui/react-dialog";
import TopUpSlider from "@/components/ui/Topupslider";
import { Label } from "@/components/ui/label";

interface Plan {
  id: string;
  packageName: string;
  numberOfCoins: number;
  discount: number;
  benefits: string;
  priceInDollars: number;
  tax: number;
}
interface TopUp {
  id: string;
  packageName: string;
  numberOfCoins: number;
  discount: number;
  benefits: string;
  priceInDollars: number;
  tax: number;
}
declare global {
  interface Window {
    Razorpay: any; // Or a more specific type if available
  }
}

const Pricing: React.FC = () => {
  const [user, setUser] = useState<any>();
  const [loading, setLoading] = useState<boolean>(true);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [topUp, setTopUp] = useState<TopUp[]>([]);
  const router = useRouter();
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [customAmount, setCustomAmount] = useState("");
  const conversionRate = 0.012;

  const searchParams = useSearchParams();

  useEffect(() => {
    const openModal = searchParams.get("openModal");
    if (openModal === "true") {
      setIsPopupOpen(true); 
    }
  }, [searchParams]);

  // ------------------- Fetching the plans and top-up details --------------
  useEffect(() => {
    const fetchPrices = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/prices?limit=20`
        );
        const data = await response.json();

        if (!data) {
          setLoading(false);
        }

        const isPlans = data.docs.filter((card: any) => card.isTopUp === false);
        setPlans(isPlans);

        const isTopUp = data.docs.filter((card: any) => card.isTopUp === true);
        setTopUp(isTopUp);

        setLoading(false);
      } catch (error) {
        console.error("Error fetching prices:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrices();
  }, []);

  const plansDummyData = [
    {
      id: "6769498eee4aaad54fc1fcd4",
      packageName: "Free",
      numberOfCoins: 25,
      priceInDollars: "",
      isTopUp: false,
      rate: 0,
      tax: 0,
      discount: 0,
      active: false,
      button: "Sign-up Now",
      benefits: [
        "Unlimited access to all features",
        "List prompts and assets",
        "List public AI apps",
        "Apply unlimited bounties",
        "Ad-free experience",
        "Community support on discord",
        // "Secure and private access",
        // "No credit card required",
        // "Available on all devices",
        // "Access to community forums",
      ],
    },
    {
      id: "7678698abc8edf123fc1fcd5",
      packageName: "Professionals",
      numberOfCoins: 0,
      priceInDollars: "0",
      Description:
        "Designed for AI enthusiasts, developers and creators scaling AI projects",
      isTopUp: false,
      rate: 0,
      tax: 0,
      discount: 0,
      active: true,
      button: "Choose Plan",
      benefits: [
        "Access to premium models",
        "List unlimited prompts and assets",
        "Create unlimited private AI apps",
        "Create and apply unlimited bounties",
        "Collaborate on your use cases",
        "Team support on discord",
        // "No expiry date for the plan",
        // "Supports multiple platforms and devices",
        // "Simple and hassle-free setup process",
        // "24/7 access to recharge services",
      ],
    },
    {
      id: "6672b7713e006fe4c6411256",
      packageName: "Enterprises",
      numberOfCoins: 0,
      Description:
        "Perfect for businesses and organizations seeking secure, scalable AI solutions.",
      isTopUp: false,
      active: true,
      button: "Contact Sales",
      benefits: [
        "Access to secure models",
        "Custom apps, agents and workflow automation",
        "Collaborate, onboard team and share",
        "Branding and lead generation",
        "AI Consulting",
        "Dedicated support",
        "Easy API's to integrate",
        // "Dedicated account manager for premium users",
        // "Integration with third-party tools",
        // "Regular updates and new features",
        // "Comprehensive training and onboarding support",
      ],
      priceInDollars: "0",
    },
  ];

  //----------------- Fetching Logged inUser with custom endpoint -----------------
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,
          {
            method: "GET",
          }
        );
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setUser(data.data);
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };
    fetchUserData();
  }, []);

  if (loading)
    return (
      <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50 ">
        <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
      </div>
    );

  const handlePayClick = (topUp) => {
    console.log("handlePayClick topUp", topUp);
    if (!user) {
      toast.error("Please login to make a payment");
    } else {
      const totalCoins = Math.round(
        topUp.numberOfCoins + (topUp.numberOfCoins * topUp.discount) / 100
      );
      const totalBasePrice = topUp.numberOfCoins;
      const totalTax = ((topUp?.tax ? topUp?.tax : 0) / 100) * totalBasePrice; 
      const total = totalBasePrice + totalTax;
      const receivedCoins = Math.round(
        topUp.numberOfCoins + (topUp.numberOfCoins * topUp.discount) / 100
      );

      const params = new URLSearchParams({
        packageId: topUp.id,
        packageName: topUp?.packageName,
        packageAmount: totalBasePrice.toString(),
        packageTax: totalTax.toFixed(2).toString(),
        totalPaidAmount: total.toFixed(2).toString(),
        receivedCoins: receivedCoins.toString(),
        userName: user.name,
        userEmail: user.email,
      });
      router.push(`/checkout?${params.toString()}`);
    }
  };

  const handleButtonClick = (plan: any) => {
    if (!user) {
      toast.error("Please login to continue.");
      router.push("/sign-in");
      return;
    }

    // Handle button actions based on the plan's button type
    if (plan.button === "Catch Now") {
      router.push("/");
    } else if (plan.button === "Choose Plan") {
      setIsPopupOpen(true);
    } else if (plan.button === "Contact Sales") {
      router.push("/aboutus#write-to-us");
    }
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  const generateId = () => {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  };
  const handlePay = () => {
    const amountInINR = parseInt(customAmount, 10) || 0; // Default to 0 if blank
    if (amountInINR < 10) {
      toast.error("Please enter an amount of ₹10 or more", {
        position: "top-center",
        //autoClose: 2000,
      });
      return;
    }
    
    const amountInDollars = amountInINR * conversionRate; // Convert INR to USD
    const id = generateId(); // Generate a unique ID

    const updatedPackage = {
      id, // Include the unique ID
      discount: 0,
      tax: 0,
      packageName: `Top-up (${customAmount} Rs)`,
      numberOfCoins: amountInINR, // Update number of coins with entered amount
      amountInDollars: amountInDollars.toFixed(2), // Add converted amount
    };

    handlePayClick(updatedPackage); // Pass the updated package to the handler
  };

  const handleCustomPrice = (e: React.ChangeEvent<HTMLInputElement>) => {
    let { id, value } = e.target;

    // Allow only positive numbers and up to one decimal place
    if (id === "rechargeAmount") {
      // Ensure the input doesn't allow negative values or more than one decimal point
      if (!/^\d*\.?\d{0,1}$/.test(value)) {
        return; // Prevent invalid input
      }

      // Prevent negative values programmatically
      if (parseFloat(value) < 0) {
        return;
      }
    }

    setCustomAmount(value); // Update state with valid input
  };

  const formatIndianNumber = (number) => {
    const numStr = number.toString();
  
    const parts = numStr.split('.');
    let wholePart = parts[0];
    const decimalPart = parts[1] || '';
  
    // Add commas in Indian style
    let lastThree = wholePart.slice(-3);
    let otherDigits = wholePart.slice(0, -3);
    if (otherDigits !== '') {
      lastThree = ',' + lastThree;
    }
    let result = otherDigits.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
  
    // Add decimal part if it exists
    if (decimalPart) {
      result += '.' + decimalPart;
    }
  
    return result;
  };

  return (
    <>
      <Container>
        <div className="flex flex-col  lg:flex-row gap-4 items-center mt-20 mx-4 md:mx-8">
          <div className="flex lg:w-[40%] gap-4 w-full items-center lg:text-left lg:justify-end justify-center">
            <div className="flex justify-center relative h-fit">
              <div className="absolute w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 blur-3xl opacity-90"></div>
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Winner.png`}
                alt="pricing logo"
                width={100}
                height={50}
                className="relative z-10"
              />
            </div>
            <h1 className="md:text-4xl h-fit text-3xl font-bold ">
              {/* Pay-as-you go */}
              Use-and-Pay
              <br /> Pricing
            </h1>
          </div>
          <p className="lg:ml-10 lg:w-[60%] w-full md:text-lg mx-auto text-center lg:text-left lg:pt-3">
            Pay only for the AI models you use, directly at vendor rates. No
            contracts,
            <br className="max-lg:hidden" /> no extra charges, no
            complexity—just straightforward pricing.
          </p>
        </div>

        <div defaultValue="account" className="w-11/12 mx-auto md:w-full ">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full md:w-11/12 mx-auto place-items-center bg-gradient-to-br px-0 md:px-4 py-6 text-slate-900">
            {plansDummyData.map((plan, index) => (
              <div
                key={plan.id}
                className={`p-6 rounded-xl shadow-lg h-full flex flex-col justify-between ${
                  index === Math.floor(plansDummyData.length / 2)
                    ? "bg-gradient-to-r from-black/[0.3] via-black/[0.1] to-black/[0.4] text-white border-2 border-white"
                    : "bg-white text-black border-2 border-black"
                } w-full max-w-sm  transform hover:scale-105 transition-transform duration-300`}
              >
                <div>
                  <h2 className={`text-3xl font-bold mb-4 `}>
                    {plan.packageName}
                  </h2>
                  {plan.numberOfCoins !== 0 ? (
                    <>
                      <p
                        className={`text-lg font-bold mb-4 ${
                          index === Math.floor(plansDummyData.length / 2)
                            ? "text-white"
                            : "text-gray-800"
                        }`}
                      >
                        {/* Get {plan.numberOfCoins} Joules */}
                        {/* "Sign up today and claim your {plan.numberOfCoins}  */}
                        <span
                          className={`flex font-bold gap-[2px] items-center`}
                        >
                          {/* ₹{plan.priceInDollars} */} Sign up today and claim
                          your {plan.numberOfCoins}
                          <div className=" -rotate-45">
                            <div className=" motion-preset-stretch motion-duration-2000">
                              <Image
                                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                                width={30}
                                height={30}
                                alt="Coin"
                              />
                            </div>
                          </div>
                        </span>
                        credits absolutely free!
                      </p>
                    </>
                  ) : (
                    <span
                      className={`flex font-bold leading-7 items-center mb-4`}
                    >
                      {plan.Description}
                    </span>
                  )}

                  {Array.isArray(plan.benefits) ? (
                    <ul
                      className={`text-sm  mb-6 ${
                        index === Math.floor(plansDummyData.length / 2)
                          ? "  text-white "
                          : " text-gray-800"
                      }`}
                    >
                      {plan.benefits.map((benefit, index) => (
                        <li key={index} className="flex items-center mb-1">
                          <span className="text-green-500 mr-2 text-xl">✔</span>{" "}
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p
                      className={`text-sm  mb-6 ${
                        index === Math.floor(plansDummyData.length / 2)
                          ? "  text-white "
                          : " text-gray-800"
                      }`}
                    >
                      {plan.benefits}
                    </p>
                  )}
                </div>

                <button
                  onClick={() => handleButtonClick(plan)} // Function to open the popup
                  className="bg-gradient-to-r from-amber-500 to-pink-500 hover:bg-gradient-to-4 hover:from-amber-600 hover:to-pink-600 hover:bg-purple-700
                   text-white text-sm font-medium py-2 px-4 rounded-md w-full"
                >
                  {plan.button}
                </button>
              </div>
            ))}
          </div>

          <div>
            {isPopupOpen && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-indigo-800 p-4 rounded-lg max-w-4xl w-[400px] md:w-[500px] relative">
                  <h3 className="text-lg font-bold">Recharge:</h3>
                  <button
                    onClick={handleClosePopup} // Close the popup
                    className="absolute top-0 right-2 text-4xl font-bold text-white"
                  >
                    ×
                  </button>

                  <div className=" mt-3 overflow-y-auto max-md:no-scrollbar md:w-full mx-auto border-white flex flex-col gap-4 no-scrollbar">
                    <div className="relative p-6 rounded-xl bg-dash-foreground text-white w-full shadow-lg">
                      <TopUpSlider topUp={topUp} onPayClick={handlePayClick} />

                      <div className="flex items-center w-full justify-center border-t border-indigo-600 pt-6">
                        <div className="text-center text-xl font-extrabold mb-4 bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">
                          CUSTOM RECHARGE
                        </div>
                      </div>

                      <Label
                        htmlFor="rechargeAmount"
                        className="block text-lg font-semibold mb-2"
                      >
                        Enter amount of Joules
                      </Label>
                      <div className="flex items-center gap-3 w-full">
                        <div className="flex-1">
                          <Input
                            type="number"
                            id="rechargeAmount"
                            value={customAmount}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (/^\d*$/.test(value) && value.length <= 7) {
                                setCustomAmount(value);
                              }
                            }}
                            onKeyPress={(e) => {
                              if (e.key === '.' || e.key === '-' || e.key === 'e') {
                                e.preventDefault();
                              }
                            }}
                            placeholder="Enter amount"
                            className="w-full p-3 text-white font-bold text-xl rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                          />
                          {customAmount.length > 7 && (
                            <p className="text-red-500 text-sm mt-1">Maximum 7 digits allowed.</p>
                          )}
                        </div>
                      </div>

                      {customAmount && !isNaN(parseInt(customAmount, 10)) && (
                        <div className="text-center flex justify-center mt-4">
                          <div className="flex gap-2 items-center">
                            <Image
                              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                              width={40}
                              height={40}
                              alt="Coin"
                              className="rotate-45"
                            />
                            <p className="text-2xl font-bold">
                              Get {formatIndianNumber(customAmount)} Joules
                            </p>
                          </div>
                        </div>
                      )}

                      {/* {customAmount && !isNaN(parseInt(customAmount, 10)) && (
                        <p className="mt-2 text-lg font-semibold">
                          Amount in USD:{" "}
                          <span className="text-yellow-400">
                            $
                            {(
                              parseInt(customAmount, 10) * conversionRate
                            ).toFixed(2)}
                          </span>
                        </p>
                      )} */}

<button
              onClick={handlePay}
              disabled={!customAmount || isNaN(parseInt(customAmount, 10))}
              className="w-full bg-gradient-to-r from-amber-500 to-pink-500 hover:bg-gradient-to-4 hover:from-amber-600 hover:to-pink-600 hover:bg-purple-700 text-white md:text-xl font-black py-2 px-6 mt-6 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed disabled:hidden"
            >
              Pay Now<span className="mx-1 text-xl font-bold"></span>
          <span className="md:text-xl font-bold">
            ₹{formatIndianNumber(customAmount)}
          </span>
          <span className="mx-1 text-xl font-bold">/</span>
          <span className="text-sm font-bold justify-self-end">
            ${(
                              parseInt(customAmount, 10) * conversionRate
                            ).toFixed(2)}
          </span>
            </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Container>
    </>
  );
};

export default Pricing;
