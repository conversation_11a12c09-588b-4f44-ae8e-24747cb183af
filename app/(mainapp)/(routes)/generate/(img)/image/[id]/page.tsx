"use client";

import React, { useEffect, useState } from "react";
import axios from "axios";
import ReactMarkdown from "react-markdown";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import { toast } from "sonner";
import { ArrowLeft, Copy, Download, Zap } from "lucide-react";
import { Label } from "@radix-ui/react-dropdown-menu";
import { Input } from "@/components/ui/input";
import { useParams, useRouter } from "next/navigation";
import coinImage from "../../../../../../../public/img/coin-png.png";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import ModelSettings from "@/components/ModelSettings"; // Import your ModelSettings component
import ImageUploader from "@/components/ui/ImageUploader";
import ImageWithAspectRatio from "@/components/ImageWithAspectRatio";
import { render } from "@react-email/components";
import { Textarea } from "@/components/ui/textarea";

export interface Model {
  id: string;
  name: string;
  description: string;
  modelType: "text" | "audio" | "image" | "video"; // Ensure all definitions match!
  organization: string;
  cost: number;
  commission: number;
  enablePrompt: boolean;
  imageinput: boolean;
  imageinputopt: boolean;
  provider: string;
  examples: { example: string; id: string }[];
  settings: any; // Ensure the settings type is consistent
}

const ModelDetails = () => {
  const [model, setModel] = useState<Model | null>(null);
  const [output, setOutput] = useState<string | undefined>();
  const [spinLoading, setSpinLoading] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const type = "image";
  const { id } = useParams();
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [prompt, setPrompt] = React.useState("");
  const [settingsValues, setSettingsValues] = useState({});
  const router = useRouter();
  const toggleAdvancedSettings = () => setShowAdvanced(!showAdvanced);

  useEffect(() => {
    if (!id) return;
    const fetchModel = async () => {
      setLoading(true);
      try {
        const res = await fetch(`/api2/models/${id}?type=${type}`);
        if (!res.ok) {
          throw new Error("Model not found");
        }
        const data = await res.json();
        setModel(data.data);
      } catch (error) {
        console.error("Error fetching model:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchModel();
  }, [id, type]);

  const handleOnSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      setSpinLoading(true);
      toast.loading("Running...");

      const formData = new FormData(e.currentTarget);

      const formObject = Object.fromEntries(formData.entries());
      
      const selectedFields = {
        image: formObject.image || base64Image, // Use base64Image if `image` is missing
        prompt: formObject.prompt || "", // Default to empty string if `prompt` is missing
      };

      const modelSettingsWithSeed = modelSettings
        ? {
            ...modelSettings,
            seed: modelSettings.seed
              ? parseInt(modelSettings.seed as string, 10)
              : undefined, // Convert seed to integer if it exists
          }
        : model.settings.reduce(
            (acc, setting) => {
              acc[setting.name] = setting.type === "integer" ? 0 : ""; // Default values based on type
              return acc;
            },
            {} as Record<string, any>
          );

      // Run the model and get the response
      const { data: runResponse } = await axios.post(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/runModel/${id}`,
        {
          obj: selectedFields,
          settings: modelSettingsWithSeed,
          url: base64Image,
        }
      );

      if (runResponse.success === false) {
        toast.error(runResponse.message);
        return;
      }

      const { output } = runResponse;
      if(output.error || output === null) {
        toast.error("Currently Model are not available, Please try after some time.");
        const emailResponse  = await fetch("/api/model-error-email-sent", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ errorMessage: output.error }),
        });
        return;
      }

      // Fetch the image from the URL provided in the response
      const imageUrl = runResponse.output.imageUrl || runResponse.output;
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Image = reader.result as string;
        setOutput(base64Image);
      };
      reader.readAsDataURL(blob);  
      
      const purchaseResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/purchaseGenerateText`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            modelId: id,
          }),
        }
      );

      const purchaseData = await purchaseResponse.json();

      if (purchaseData.status === "success" && model.cost > 0) {
        toast.success("Credits have been deducted for this run");
      }
      if (purchaseData.status === "success" && model.cost === 0) {
        toast.success("Successfully run");
      }

      toast.dismiss();
    } catch (error) {
      toast.dismiss();
      if (error.response) {
        toast.error(error.response.data.message);
      } else {
        toast.error("An error occurred. Please try again.");
      }
    } finally {
      setSpinLoading(false);
    }
  };

  const handleCopy = () => {
    if (output) {
      navigator.clipboard.writeText(output).then(
        () => {
          toast.success("Output copied to clipboard");
        },
        (err) => {
          toast.error("Failed to copy output");
          console.error("Could not copy text: ", err);
        }
      );
    }
  };

  const handleDownload = async () => {
    if (output) {
      try {
        const response = await axios.get(output, { responseType: "blob" });
        const blob = new Blob([response.data], {
          type: response.headers["content-type"] || "image/png",
        });

        const url = window.URL.createObjectURL(blob);

        const link = document.createElement("a");

        const format = modelSettings.output_format || "png";
        link.href = url;
        link.download = `output.${format}`;
        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        toast.error("Download failed: ", error);
      }
    } else {
      toast.error("No output URL provided for download.");
    }
  };

  const [modelSettings, setModelSettings] = useState<
    Record<string, number | string>
  >({});

  const handleSettingsChange = (
    updatedSettings: Record<string, number | string>
  ) => {
    setModelSettings(updatedSettings);
  };

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageFile(e.target.files?.[0] || null);
  };

  if (loading) return <p className="text-center mt-20 md:mt-32">Loading...</p>;
  if (!model) return <p>No model found.</p>;


  return (
    <div className="min-h-screen py-10 ">
      <div className="flex justify-start">
        <button
          onClick={() => router.push("/generate/image")}
          className="flex ml-3 lg:ml-20 md:mt-12 items-center justify-center w-10 h-10 rounded-full bg-indigo-600 text-white shadow-md hover:bg-indigo-700 transition ease-in-out duration-200"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
      </div>
      <div className="max-w-6xl mx-auto grid gap-8 p-3 md:p-6">
        {/* Full-width Model Information */}
        {/* Main Container with Background */}

        {/* Model Information Section */}
        <div className="border border-indigo-500 rounded-lg p-3 md:p-6 shadow-xl bg-indigo-700">
          <div className="flex flex-col md:flex-row justify-between items-start gap-2 mb-4">
            <h2 className="text-3xl font-extrabold text-white capitalize"> {model.organization}/{model.name}</h2>
            <div className="text-base bg-indigo-600 px-3 py-2 rounded-md shadow-md">
              <span className="mb-1 flex items-center">
                Cost :
                {model.cost + model.commission === 0 ? (
                  <span className="font-bold text-yellow-300 text-xl ml-1">
                    Free
                  </span>
                ) : (
                  <>
                    <span className="font-bold text-yellow-300 text-xl ml-1 flex items-center">
                      {model.cost + model.commission}
                      <img
                        src={coinImage.src}
                        alt="Coin"
                        style={{ width: "17px", height: "20px" }}
                        className="inline ml-[1px] mt-[3px]"
                        loading="lazy"
                      />
                    </span>
                    <span className="text-lg ml-1 mt-[3px]">credits</span>
                  </>
                )}
              </span>
            </div>
          </div>
          <p className="text-lg text-gray-300 mb-4 first-letter:uppercase">{model.description}</p>
          <p className="text-base mb-2">
            <strong>Model Type:</strong>
            <div className=" px-2 py-[2px] rounded-md w-fit inline ml-2 bg-indigo-600 uppercase">
              {" "}
              {model.modelType}
            </div>
          </p>
          <p className="text-base">
            <strong>Model Provider:</strong>
            <div className=" px-2 py-[2px]  rounded-md w-fit inline ml-2 bg-indigo-600 uppercase">
              {model.provider}
            </div>
          </p>

          {/* Highlight Examples Section */}
          {model.examples.length > 0 && (
            <div className="mt-6">
              <h4 className="text-lg font-semibold text-yellow-400">
                Examples:
              </h4>
              <div className="flex flex-col md:flex-row gap-1 md:gap-2">
                {model.examples.map((example, index) => (
                  <div
                    key={example.id}
                    className="bg-indigo-600 text-white p-4 mt-3 rounded-md border border-indigo-500 w-fit"
                  >
                    <p>
                      <strong className="text-yellow-400">
                        Example {index + 1}:
                      </strong>{" "}
                      {example.example}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Form and Output Section in Two-Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Section */}
          <form
            className="border border-indigo-500 rounded-lg p-3 md:p-6 shadow-xl bg-indigo-700 relative"
            onSubmit={handleOnSubmit}
          >
            {model.enablePrompt && (
              <div className="mb-4">
                <label
                  className="block text-lg font-semibold mb-2 text-yellow-300"
                  htmlFor="prompt"
                >
                  Enter Prompt:
                </label>
                {/* <textarea
                  rows={4}
                  name="prompt"
                  id="prompt"
                  className="w-full p-3 bg-indigo-600 border border-indigo-400 rounded-lg text-lg text-white focus:outline-none focus:ring-2"
                  placeholder="Type your prompt here..."
                  required
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = "auto";
                    target.style.height = `${target.scrollHeight}px`;
                  }}
                /> */}

                <Textarea
                  name="prompt"
                  id="prompt"
                  className=" text-white bg-indigo-600 border-indigo-500 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  // className="w-full p-3  border border-indigo-400 rounded-lg text-lg text-white focus:outline-none focus:ring-2"
                  placeholder="Type your prompt here..."
                  required
                  value={prompt} // Bind state
                  onChange={(e) => {
                    setPrompt(e.target.value); // Update state
                  }}
                  style={{ overflow: "hidden", resize: "none" }}
                  rows={5}
                />
              </div>
            )}

            {(model.imageinputopt || model.imageinput) && (
              <ImageUploader model={model} setBase64Image={setBase64Image} />
            )}

            {/* Advanced Settings */}
            {
              model?.settings?.length > 0 ? 
              <div className="mb-4">
              <button
                type="button"
                onClick={toggleAdvancedSettings}
                className="w-full py-2 px-4 bg-yellow-400 text-indigo-700 font-semibold rounded-lg"
              >
                {showAdvanced
                  ? "Hide Advanced Settings"
                  : "Show Advanced Settings"}
              </button>
            </div> :
            <div className='py-2'>▶ This model does not support settings.</div>
            }
            

            {/* Dynamically render the provider-specific settings */}
            {showAdvanced && model.settings.length > 0 && (
              <ModelSettings
                settings={model?.settings}
                model={model}
                onSettingsChange={handleSettingsChange}
                showAdvancedSettings={showAdvanced}
              />
            )}

            <button
              type="submit"
              disabled={spinLoading}
              className="w-full py-3 mt-4 rounded-lg text-lg font-semibold text-white 
              bg-gradient-to-r from-amber-500 to-pink-500 hover:bg-gradient-to-4 hover:from-amber-600 hover:to-pink-600 hover:bg-purple-700
               transition-all ease-in-out shadow-lg"
            >
              {spinLoading ? (
                "Generating..."
              ) : (
                <div className="flex items-center justify-center text-center space-x-2">
                  <span>Generate</span>
                  <Zap className="fill-yellow-500 stroke-2 w-6 h-6" />
                </div>
              )}
            </button>
          </form>

          {/* Output Section */}
          <div className="border border-indigo-500 rounded-lg p-3 md:p-6 shadow-xl bg-indigo-700 flex flex-col justify-between h-full">
            {output ? (
              <div className="relative flex flex-col justify-between h-full">
                {model?.modelType === "text" ? (
                  <div className="relative flex-1">
                    <div
                      className="absolute top-0 right-0 m-2 cursor-pointer p-2 bg-indigo-600 hover:bg-yellow-400 rounded-full"
                      onClick={handleCopy}
                    >
                      {spinLoading ? <div className="loader"></div> : <Copy />}
                    </div>
                    <h2 className="text-lg font-semibold text-yellow-400 mb-4">
                      Output:
                    </h2>
                    <div className="text-sm overflow-y-scroll max-h-80 custom-scrollbar p-4 bg-indigo-600 rounded-lg">
                      <ReactMarkdown
                        rehypePlugins={[rehypeSanitize]}
                        remarkPlugins={[remarkGfm]}
                      >
                        {output}
                      </ReactMarkdown>
                    </div>
                  </div>
                ) : (
                  <div className="relative flex-1">
                    <div
                      className="absolute top-0 right-0 m-2 cursor-pointer p-2 bg-indigo-600 hover:bg-yellow-400 rounded-full"
                      onClick={handleDownload}
                    >
                      <Download />
                    </div>

                    {/* Image Component */}
                    <ImageWithAspectRatio imageSrc={output} />
                  </div>
                )}
              </div>
            ) : (
              <p className="text-center text-lg text-yellow-400 bg-indigo-700 flex items-center justify-center h-full">
                No output generated yet.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
    // </div>
  );
};

export default ModelDetails;
