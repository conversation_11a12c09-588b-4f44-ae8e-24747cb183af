import { ArrowRight } from "lucide-react";
// import { useRouter } from "next/navigation";

import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  CardBody,
  CardContainer,
  CardItem,
} from "../../../../components/ui/3d-card";
import Link from "next/link";
import { tools } from "@/constants";
import Image from "next/image";
import MaxWidthWrapper from "@/components/MaxWidthWrapper";
import Loader from "@/components/Loader";
import Container from "@/components/ui/container";
import { Metadata } from "next";
import SolidBento from "@/components/ui/bentogrid";
import HeroBoxes from "@/components/ui/userprofile/heroBox";
import DynamicSquareBackground from "@/components/landingWiget";

export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: {absolute:"Generate Text, Images, Audio & Video with Top AI Models | AI Content Generator | Rentprompts"},
        description:
          "Discover AI-powered generation AI labs at RentPrompts. Generate text, images, videos, music, and 3D models effortlessly with our advanced generation tools. We’re RARE.",
        alternates: {
          canonical: "/generate",
        },
        keywords: [
          "AI content generation",
          "Automated creative tools",
          "AI-driven media creation",
          "Generative AI labs",
          "Text and image generation",
          "AI video production",
          "Music generation AI",
          "3D model generation",
          "AI-powered creation",
          "Innovative content tools",
          "Next-gen AI tools",
          "Creative AI solutions",
          "AI lab for creators",
          "Effortless AI content",
          "Advanced generative tools",
        ],
        openGraph: {
          title: {absolute:"Generate Text, Images, Audio & Video with Top AI Models | AI Content Generator | Rentprompts"},
          description:
            "Discover AI-powered generation AI labs at RentPrompts. Generate text, images, videos, music, and 3D models effortlessly with our advanced generation tools. We’re RARE.",
          url: "/generate",
          siteName: "Rentprompt.com",
          images: [
            {
              url:
                `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Group%2048095915.jpg`,
            },
          ],
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
      }
    : null;

export default function GeneratePage() {
  return (
    <>
      <Loader />
      <Container>
        <div className="w-full mx-auto rounded-md overflow-hidden">
                <DynamicSquareBackground
                buttonHref="#"
                buttonHref1="#"
                buttonText=""
                buttonText1=""
                description="Your all-in-one AI playground—create text, images, audio, and videos effortlessly with top AI Models, no limits, no subscriptions!"
                tag="AI playground"
                title="Generate"
                image={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/generate_banner.png`}
                image1=""
                />
                </div>
        {/* <div className="mb-8 md:mt-20 mt-8 space-y-4">
          <h1 className="text-3xl md:text-5xl font-bold text-center">
            Explore the power of AI
          </h1>
          <p className="text-white md:text-lg mt-6 text-center">
            Ours featured Models
          </p>
        </div> */}
        <p className="mt-8">
        <SolidBento/>
        </p>

        <HeroBoxes/>


        {/* <div className="mb-16">
          <div className=" p-8">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <Card className="md:col-span-3 bg-indigo-900 p-2 rounded-lg relative overflow-hidden border-gray-300 hover:border-gray-200">
                <div className="space-y-4">
                  <div className="h-[300px] sm:h-[400px] relative">
                    {" "}

                    <Image
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSoab11Lk3qVh1Hriq58N5MljAb_RhkXg1t9Q&s"
                      alt="Goal tracking visual"
                      layout="fill"
                      objectFit="cover"
                      className="rounded-md"
                    />

                    <div className="absolute bottom-0 w-full bg-gradient-to-t from-indigo-900/90 to-transparent p-2 text-white text-sm font-medium text-center">
                      Empowering AI for Smarter Solutions
                    </div>
                  </div>
                  <p className="text-gray-400 text-center">
                    RentPrompt leverages cutting-edge AI generative technology
                    to help individuals and businesses streamline their rental
                    processes. Stay focused, organized, and efficient with
                    intelligent tools tailored for your needs.
                  </p>
                </div>
              </Card>
              <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4 md:grid">
                {[
                  {
                    src:
                      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSSTeIAh3vpfNhGXnZI3iFO_49DDWX3N69-qg&s",
                    alt: "AI Generate",
                    title: "Streamlined Rental Solutions",
                    description:
                      "Simplify your rental processes with intelligent automation powered by AI.",
                  },
                  {
                    src:
                      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSU_deW0kRi7dabGk6lVHIdhzKsgnHgad5MOt6A8FZkiVtiZx9UTbSyh0gmCQ3plai6jVw&usqp=CAU",
                    alt: "AI Feature 2",
                    title: "Personalized Assistance",
                    description:
                      "Leverage AI for tailored recommendations and support in decision-making.",
                  },
                  {
                    src:
                      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQjGUfQDlWslL9bK4CBn2TQm1yJDA7W4Hmffg&s",
                    alt: "AI Feature 3",
                    title: "Ai Analytics",
                    description:
                      "Gain valuable insights with data-driven analytics and reporting.",
                  },
                  {
                    src:
                      "asset.rentprompts.com/output-4.webp",
                    alt: "AI Feature 4",
                    title: "Seamless Integration",
                    description:
                      "Integrate with your existing tools and platforms effortlessly.",
                  },
                ].map((card, index) => (
                  <Card
                    key={index}
                    className="bg-indigo-900 p-4 rounded-lg relative overflow-hidden h-[200px] sm:h-[250px] flex flex-col border-l-2 border-r-2 border-gray-300"
                  >
                    <div className="relative h-full">
                      <Image
                        src={card.src}
                        alt={card.alt}
                        layout="fill"
                        objectFit="cover"
                        className="rounded-md"
                      />

                      <div className="absolute bottom-0 w-full bg-gradient-to-t from-indigo-900/90 to-transparent p-2 text-white text-sm font-medium text-center">
                        {card.title}
                      </div>
                    </div>
                    <p className="text-gray-400 text-center mt-1">
                      {card.description}
                    </p>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div> */}

        {/* <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 px-4  lg:grid-cols-3 items-center justify-center">
          {tools.map((tool) => (
            <Link href={tool.href} key={tool.href} className="max-w-fit">
              <CardContainer className="inter-var px-2" key={tool.href}>
                <CardBody className="bg-indigo-100 relative group/card border-black/[0.1] rounded-xl p-6 border w-[400px]">
                  <CardItem
                    translateZ="50"
                    className="text-xl font-bold text-neutral-600"
                  >
                    {tool.label}
                  </CardItem>

                  <CardItem
                    as="p"
                    translateZ="60"
                    className="text-neutral-500 text-sm max-w-sm mt-2 h-[60px] overflow-hidden"
                  >
                    {tool.disc}
                  </CardItem>

                  <CardItem translateZ="100" className="w-full mt-10">
                    <tool.icon className={cn("h-40 w-full", tool.color)} />
                  </CardItem>

                  <div className="flex justify-end items-right mt-10">
                    {tool.available ? (
                      <CardItem
                        translateZ={20}
                        className="px-4 py-2 rounded-lg bg-background text-white text-xs font-bold"
                      >
                        <ArrowRight className="w-5 h-5" />
                      </CardItem>
                    ) : (
                      <CardItem
                        translateZ={20}
                        className="px-4 py-2 rounded-lg bg-background text-white text-xs font-bold"
                      >
                        <p>Coming Soon</p>
                      </CardItem>
                    )}
                  </div>
                </CardBody>
              </CardContainer>
            </Link>
          ))}
        </div> */}

      </Container>
    </>
  );
}
