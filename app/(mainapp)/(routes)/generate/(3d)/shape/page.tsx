"use client";

import * as z from "zod";
//import { useState, useEffect } from 'react';
import { Box,} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useEffect, useRef, useState } from 'react'

import { formSchema } from "./constants";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { SpacesHeading } from "@/components/SpacesHeading";
import Container from "@/components/ui/container";
import Loader from "@/components/Loader";

import { cn } from "@/lib/utils";


const Shape = () => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeHeight, setIframeHeight] = useState(0);
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      if (mutations.some(m => m.target === iframeRef.current?.contentDocument?.body)) {
        setIframeHeight(iframeRef.current?.contentDocument?.body.scrollHeight || 1200);
      }
    });
    if (iframeRef.current) {
      const targetNode = iframeRef.current.contentDocument?.body;
      if (targetNode) {
        observer.observe(targetNode, { childList: true, subtree: true });
      }
    }
    return () => observer.disconnect();
  }, []);
  return (
    <>
      <Loader />
      <Container>
        <div className="flex md:mt-16  flex-col w-11/12 mx-auto">
          <SpacesHeading
            title="OpenAI shap-e"
            description="Our most advanced 3D generating model."
            icon={Box}
            iconColor="text-violet-500"
          />
          {/* Dynamically update height using cn */}
          <div className={cn('h-[1200px]', iframeHeight > 0 && `h-[${iframeHeight}px]`)}> 
            <iframe
              ref={iframeRef}
              src="https://hysts-shap-e.hf.space/?__theme=dark"
              className="container resize-y h-full space-iframe flex-grow overflow-hidden outline-none bg-transparent"
              scrolling="no"
            />
          </div>
        </div>
      </Container>
    </>
  );
};
export default Shape
