"use client";

import * as z from "zod";
import axios from "axios";
import { ArrowLeft, MessageSquare } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useChat } from '@ai-sdk/react'
import React, { useEffect, useRef, useState } from "react";
import { ChatOllama } from "@langchain/community/chat_models/ollama";
import { ChatLayout } from "@/components/chat/chat-layout";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { SpacesHeading } from "@/components/SpacesHeading";
import {
  ChatCompletionMessage,
  ChatCompletionUserMessageParam,
} from "openai/resources/index.mjs";
import Loader from "@/components/Loader";
import { toast } from "sonner";
import { getSelectedModel } from "@/lib/model-helper";
import { v4 as uuidv4 } from "uuid";
import { AIMessage, HumanMessage } from "@langchain/core/messages";
import { BytesOutputParser } from "@langchain/core/output_parsers";
import { ChatRequestOptions } from "ai";
import Container from "@/components/ui/container";

const Chatgpt = () => {
  const router = useRouter();

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit, // We are not using this handleSubmit from useChat directly for form submission
    error,
    stop,
    setMessages,
    setInput,
  } = useChat({
    onResponse: (response) => {
      if (response) {
        setLoadingSubmit(false);
      }
    },
    onError: (error) => {
      setLoadingSubmit(false);
      toast.error("An error occurred with the chat service. Please try again.");
    },
  });
  const [chatId, setChatId] = React.useState<string>("");
  const [selectedModel, setSelectedModel] =
    React.useState<string>(getSelectedModel());
  const [open, setOpen] = React.useState(false);
  const [ollama, setOllama] = useState<ChatOllama>();
  const env = process.env.NODE_ENV;
  const [loadingSubmit, setLoadingSubmit] = React.useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [isLoading, setIsLoading] = useState(false); // This seems to be from useChat, ensure it's used correctly or removed if not needed for submit logic
  const [modelId, setModelId] = useState('');


  useEffect(() => {
    if (messages.length < 1) {
      const id = uuidv4();
      setChatId(id);
    }
  }, [messages]);

  React.useEffect(() => {
    // Ensure loadingSubmit is also considered if it reflects the overall submission process
    if (!loadingSubmit && !error && chatId && messages.length > 0) {
      localStorage.setItem(`chat_${chatId}`, JSON.stringify(messages));
      window.dispatchEvent(new Event("storage"));
    }
  }, [chatId, loadingSubmit, error, messages]); // Added messages to dependency array as well

  const addMessage = (Message: any) => {
    setMessages((prevMessages) => [...prevMessages, Message]); // Use functional update for setMessages
    // window.dispatchEvent(new Event("storage")); // This might be redundant if the useEffect above handles it
  };

  const handleImageUrlChange = (url: string) => {
    setBase64Image(url);
  };

  // Function to handle chatting with Ollama in production (client side)
  // This function seems unused in the provided onSubmit logic. If it's meant to be an alternative,
  // it should also be updated with the correct API call order.
  const handleSubmitProduction = async (
    e: React.FormEvent<HTMLFormElement>
  ) => {
    e.preventDefault();
    // This is a placeholder, ensure it aligns with your intended logic
    // For example, it doesn't call the credit deduction API
    addMessage({ role: "user", content: input, id: chatId }); // Consider unique ID for user message
    const currentInput = input; // Capture input before clearing
    setInput("");

    // Placeholder for Ollama specific logic
    toast.info("Ollama production submit is not fully implemented with credit deduction.");
    // Example:
    // try {
    //   // ... Ollama specific API call ...
    //   // ... then credit deduction ...
    //   // ... then add assistant message ...
    // } catch (error) {
    //   toast.error("Error with Ollama production model.");
    // }
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim()) { // Prevent empty submissions
        toast.info("Please enter a message.");
        return;
    }
    setLoadingSubmit(true);
    const userInput = input; // Capture input before clearing
    const userMessageId = `user-${uuidv4()}`; // Use uuid for uniqueness

    // Add user message to UI immediately
    addMessage({ role: "user", content: userInput, id: userMessageId });
    setInput(""); // Clear input field after submission

    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    let modelApiResponse;
    let purchaseApiResponse;
    let modelOutput;

    try {
      // --- 1. Call the model API ---
      modelApiResponse = await axios.post(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/runModel/${selectedModel}`,
        {
          obj: { prompt: userInput }, // Use the captured userInput
          url: base64Image,
        },
        { signal }
      );

      if (axios.isCancel(modelApiResponse)) { // Check for cancellation early
        console.log("Model request canceled by axios");
        toast.info("Request to model was canceled.");
        setLoadingSubmit(false);
        // Optionally remove the user message if the request is canceled before sending
        // setMessages(prev => prev.filter(msg => msg.id !== userMessageId));
        return;
      }

      if (!modelApiResponse.data.success) {
        toast.error(modelApiResponse.data.message || "Failed to get response from model.");
        setLoadingSubmit(false);
        return;
      }

      modelOutput = modelApiResponse.data.output;

      if (modelOutput.error) {
        toast.error("Model is currently unavailable. Please try again later.");
        // Send error email
        await fetch("/api/model-error-email-sent", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ errorMessage: modelOutput.error }),
        });
        setLoadingSubmit(false);
        return;
      }

      // --- 2. Call the credit deduction API ---
      purchaseApiResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/purchaseGenerateText`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ modelId: selectedModel }),
          // Add signal here if this API call also needs to be abortable
        }
      );

      const purchaseData = await purchaseApiResponse.json();

      if (!purchaseApiResponse.ok || purchaseData.status !== "success") {
        toast.error(purchaseData.message || "Credit deduction failed. Please try again.");
        // Potentially, you might want to refund or not proceed if credit deduction fails
        // For now, we stop and don't show the model output
        setLoadingSubmit(false);
        return;
      }

      toast.success("Credits have been successfully deducted for this run.");

      // --- 3. Process and display output ONLY if both APIs were successful ---
      let finalOutputContent;
      if (process.env.NEXT_PUBLIC_SERVER_URL === 'https://rentprompts.com' ? selectedModel === '67c960c61312cc2db5915a34' : selectedModel === '67c959068748b9f360130523') {
        finalOutputContent = modelOutput?.result?.replace(/<\/?think>/g, '') || 'No valid response from model.';
        finalOutputContent = finalOutputContent.trim();
      } else if (modelId === selectedModel) { // Assuming modelId is the ID for the llama-4-maverick-instruct
        finalOutputContent = Array.isArray(modelOutput) // Check if modelOutput is an array
          ? modelOutput.filter(Boolean).join(' ')
          : (typeof modelOutput === 'string' ? modelOutput : 'No response from model'); // Handle if it's already a string
        if (!finalOutputContent) finalOutputContent = 'No response from model';
      } else {
        finalOutputContent = modelOutput?.result || 'No valid response from model.';
      }

      addMessage({
        role: "assistant",
        content: finalOutputContent,
        id: `assistant-${uuidv4()}`, // Use uuid for uniqueness
      });

      // Store messages in local storage (if needed)
      // This will now include the assistant's message
      // The useEffect for localStorage saving will trigger due to messages update
      // localStorage.setItem(`chat_${chatId}`, JSON.stringify(messages));
      // window.dispatchEvent(new Event("storage")); // This might be redundant

    } catch (error) {
      if (axios.isCancel(error)) {
        console.log("Request canceled:", error.message);
        toast.info("Request has been stopped by the user.");
      } else if (error.response) { // Axios error
        toast.error(
          error.response.data.message || "An error occurred with the server. Please try again."
        );
      } else if (error instanceof Error) { // General JS error (e.g., network error for fetch)
         toast.error(error.message || "An unexpected error occurred. Please try again.");
      } else {
        toast.error("An unknown error occurred. Please try again.");
      }
    } finally {
      setLoadingSubmit(false);
      abortControllerRef.current = null; // Clear the abort controller
    }
  };

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      // toast.success("Request processing has been stopped."); // Toast is handled in the catch block for cancellation
    }
    if (typeof stop === 'function') { // Ensure stop from useChat is a function
        stop(); // Call the stop function from the chat context if it exists
    }
    setLoadingSubmit(false); // Ensure loading state is reset
  };

  const onOpenChange = (isOpen: boolean) => {
    const username = localStorage.getItem("ollama_user");
    if (username) return setOpen(isOpen);

    localStorage.setItem("ollama_user", "Anonymous");
    window.dispatchEvent(new Event("storage"));
    setOpen(isOpen);
  };

  const handleChildData = (childData: Array<{ name: string, id: string }>) => { // Added type for childData
    const filterModel = childData.find((item) => item.name === 'meta/llama-4-maverick-instruct'); // Used find for single object
    if (filterModel) {
      setModelId(filterModel.id);
    }
  };


  return (
    <>
      <Loader /> {/* Assuming Loader visibility is handled internally or via a global state */}
      <Container>
        <main className="flex h-[calc(90vh)] mx-4 flex-col items-center relative">
          <ChatLayout
            chatId={chatId} // Pass chatId
            setSelectedModel={setSelectedModel}
            onImageUrlChange={handleImageUrlChange}
            messages={messages}
            generateType="both"
            input={input}
            handleInputChange={handleInputChange}
            handleSubmit={onSubmit} // Use your custom onSubmit
            isLoading={isLoading} // This is from useChat context, may not represent your custom loadingSubmit
            loadingSubmit={loadingSubmit} // Pass your custom loading state
            selectedModel={selectedModel}
            error={error} // This is from useChat context
            sendData={handleChildData}
            stop={handleStop}
            navCollapsedSize={10}
            defaultLayout={[30, 160]}
            formRef={formRef}
            setMessages={setMessages}
            setInput={setInput}
          />
        </main>
      </Container>
    </>
  );
};

export default Chatgpt;