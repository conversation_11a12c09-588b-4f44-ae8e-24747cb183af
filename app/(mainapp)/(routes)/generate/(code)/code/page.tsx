"use client";

import * as z from "zod";
//import { useState, useEffect } from 'react';
import { Box,} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import React from 'react'

import { formSchema } from "./constants";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { SpacesHeading } from "@/components/SpacesHeading";
import Container from "@/components/ui/container";
import Loader from "@/components/Loader";

const Shape = () => {

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
          prompt: ""
        }
      })

    const isLoading = form.formState.isSubmitting;
    
    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        // console.log("sumitted")
      }

// const [name, setName] = useState("")
// const [description, setDescription] = useState("")

// const submitHandler = async (e) => {
//     e.preventDefault()

//     if (name === "" || description === "") {
//       window.alert("Please provide a name and description")
//       return
//     }

//     return create3D()
// }

// const create3D = async () =>{
//     const URL = `https://813531256da59a8d9d.gradio.live`
//     const response = await fetch(URL,{
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//       }
//     })
//     const data = response.data
//     console.log(data);
// }

  return (
   <>
   <Loader />
    <div className='form'>
        <Form {...form}>
            <SpacesHeading
                title="OpenAI shap-e"
                description="Our most advanced 3D generating model."
                icon={Box}
                iconColor="text-violet-500"
            />
            <div className="h-screen">
              <iframe src="https://hysts-shap-e.hf.space/?__theme=dark" className="container h-full space-iframe flex-grow overflow-hidden px-8 outline-none bg-transparent" scrolling="no">
              </iframe>
            </div>  
            {/* <form 
              onSubmit={form.handleSubmit(onSubmit)} 
              className="
                rounded-lg 
                border 
                w-full 
                p-4 
                px-3 
                md:px-6 
                focus-within:shadow-sm
                grid
                grid-cols-12
                gap-2
              "
            >
                <FormField
                    name="prompt"
                    render={({ field }) => (
                        <FormItem className="col-span-12 lg:col-span-10">
                            <FormControl className="m-0 p-0">
                                <Input
                                    className="border-2 border-gray-200 px-2 focus-visible:ring-0 focus-visible:ring-transparent"
                                    disabled={isLoading} 
                                    placeholder="Create burger with fries" 
                                {...field}
                                />
                            </FormControl>
                        </FormItem>
                    )}
                />
              <Button className="col-span-12 lg:col-span-2 w-full" type="submit" disabled={isLoading} size="icon">
                Generate
              </Button>
            </form> */}
        </Form>
      </div>
   </>
  )
}


export default Shape
