"use client";

import * as z from "zod";
import { <PERSON><PERSON>eft, ArrowRight, VideoIcon} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useEffect, useRef, useState } from 'react'
import { formSchema } from "./constants";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { SpacesHeading } from "@/components/SpacesHeading";
import Container from "@/components/ui/container";
import Loader from "@/components/Loader";
import { cn } from "@/lib/utils";
import { CardBody, CardContainer, CardItem } from "@/components/ui/3d-card";
import coinImage from "../../../../../../public/img/coin-png.png";
import router from "next/router";
import { usePathname, useRouter } from "next/navigation";
import Link from 'next/link';
import Image from 'next/image';

const Video = () =>  {
  const [model, setModel] = useState<any>([]);
  const router = useRouter();

  const pathname = usePathname();
  useEffect(() => {
    window.scrollTo(0, 0); // Scroll to top when the path changes
  }, [pathname]);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const res = await fetch(`/api2/models?type=video`);
        const getdata = await res.json();
        setModel(getdata.data);
      } catch (err) {
        console.error(err);
      }
    };
    fetchModels();
  }, []);

  return (
    <>
      <Loader />
  <Container>
        <div className="mb-8 md:mt-8 py-8 my-4 px-4 md:px-0 space-y-4">
          <div className="flex justify-start">
            <button
              onClick={() => router.push("/generate")}
              className="flex ml-3 md:ml-10 items-center justify-center w-10 h-10 rounded-full bg-indigo-600 text-white shadow-md hover:bg-indigo-700 transition ease-in-out duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
          </div>
          <h1 className="text-3xl md:text-5xl font-bold text-center">
            Explore AI Models
          </h1>
          <p className="text-white md:text-lg mt-6 text-center">
            Generate content using advanced AI models with ease.
          </p>
        </div>

        {/* Adjusted grid gap and layout */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 px-4 md:grid-cols-3 lg:grid-cols-4 items-center justify-center">

          {model?.map((m: any) => (
             <Link href={`/generate/video/${m.id}`} key={m.id} className="h-full">
             <CardContainer className=" p-2 w-full">
               <CardBody className="relative border shadow-sm rounded-xl overflow-hidden dark:border-neutral-700 cursor-pointer flex flex-col h-auto w-full">
                 {/* Background Image */}
                 <div className="relative">
                   <Image
                     src={m?.modelImage[0]?.image?.url || "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSU_deW0kRi7dabGk6lVHIdhzKsgnHgad5MOt6A8FZkiVtiZx9UTbSyh0gmCQ3plai6jVw&usqp=CAU"} // Use m.image or a placeholder
                     alt={m.name}
                     width="100"
                     height="80"
                     objectFit="cover"
                     className="w-full h-60 sm:h-52 md:h-44 object-cover aspect-video"
                   />
                   {m?.isFeatured === true && (
                      <div className="absolute top-0 -left-2 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                        <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                          FEATURED
                          <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                        </div>
                      </div>
                    )}
                   {/* Overlay */}
                   {/* <div className="absolute inset-0 bg-gradient-to-b from-gray-900/80 to-transparent" /> */}
                 </div>
           
                 {/* Card Content (Below Image) */}
                 <div className="relative z-10 flex flex-col p-4 bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 h-40">
                   {/* Card Title */}
                   <CardItem translateZ="40" className="text-lg font-bold text-white line-clamp-1 capitalize">
                     {m.organization}/{m.name}
                   </CardItem>
           
                   {/* Card Description */}
                   <CardItem
                     as="p"
                     translateZ="40"
                     className="text-gray-300 text-sm mt-2 line-clamp-3 first-letter:uppercase"
                   >
                     {m.description || "Effortlessly generate stunning images using our AI model."}
                   </CardItem>
           
                   {/* Cost Section */}
                   <CardItem
                     as="p"
                     translateZ="40"
                     className="text-gray-300 absolute bottom-2 text-sm flex items-center bg-white/20 px-2 py-1 rounded-lg"
                   >
                     <span className="font-bold">Cost: </span>
                     {m.cost === 0 ? (
                       <span className="ml-1">Free</span>
                     ) : (
                       <span className="ml-1 flex items-center">
                         {m.cost}
                         <img
                           src={coinImage.src}
                           alt="Coin"
                           className="ml-1 w-4 h-4"
                           loading="lazy"
                         />
                       </span>
                     )}
                   </CardItem>

                   {/* Arrow Button */}
                 <div className="mt-auto absolute bottom-0 right-0 p-2 flex justify-end">
                   <CardItem
                     translateZ={40}
                     className="flex items-center justify-center px-4 py-2 rounded-lg bg-indigo-100 text-indigo-700 hover:text-white text-xs font-bold hover:bg-indigo-600"
                   >
                     <ArrowRight className="w-4 h-4" />
                   </CardItem>
                 </div>
                 </div>
           
                 
               </CardBody>
             </CardContainer>
           </Link>
           
          ))}
        </div>
      </Container>




      
    </>
  );
};

export default Video