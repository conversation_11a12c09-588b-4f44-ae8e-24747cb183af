import React from "react";
import Container from "@/components/ui/container";
import BlogChild from "./components/BlogChild";
import HeroSec from "./components/HeroSec";
import { Metadata } from "next";
import DynamicSquareBackground from "@/components/landingWiget";

export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: {
          absolute: "Blogs - Insights on Generative AI | Rentprompts",
        },
        keywords: [
          "Generative AI Blogs",
          "AI Insights",
          "RentPrompts Blogs",
          "AI News",
          "AI Tools Reviews",
          "AI Marketplace Updates",
          "Generative AI Articles",
        ],
        alternates: {
          canonical: "/blogs",
        },
        description:
          "Explore our latest blogs on Generative AI, AI tools, marketplace updates, and more on RentPrompts.",
        openGraph: {
          title: "Blogs - Insights on Generative AI | Rentprompts",
          description:
            "Stay informed with our latest blogs on Generative AI tools, AI marketplace trends, and creative AI innovations.",
          url: "/blogs",
          siteName: "Rentprompts",
          images: [
            {
              url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
              alt: "RentPrompts Blog Banner",
            },
          ],
        },
        twitter: {
          card: "summary_large_image",
          title: "Blogs - Insights on Generative AI | Rentprompts",
          description:
            "Discover our latest insights and updates on AI tools and the Generative AI marketplace.",
          images:
            "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
        robots: {
          index: true,
          follow: true,
        },
      }
    : null;

const App = () => {
  return (
    <>
      {/* <HeroSec /> */}
      <Container>
        <div className="w-full mx-auto rounded-md overflow-hidden">
          <DynamicSquareBackground
            buttonHref="https://discord.gg/kPkYbzMvN3"
            buttonHref1="/dashboard/create/blog"
            buttonText="JOIN DISCORD"
            buttonText1="CREATE BLOG"
            description="Explore Blogs for expert insights on AI applications, prompt optimization, and the latest trends in Artificial Intelligence!"
            tag="Blogs"
            title="Learn, grow and share with RentPrompts Blog and Stay ahead in the AI revolution"
            image="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/generate_banner.png"
            image1=""
          />
        </div>
        <BlogChild />
      </Container>
    </>
  );
};

export default App;
