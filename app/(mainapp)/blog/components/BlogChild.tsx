"use client";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Icons } from "@/components/Icons";
import { constant, debounce } from "lodash";
import Image from "next/image";
import placeholderImg from "@/public/png/blog-placeholder-img.png";
import dynamic from "next/dynamic";
import { CardContainer } from "@/components/ui/3d-card";
import { Loader2 } from "lucide-react";
import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";

const BlogSidebar = dynamic(() => import("@/components/BlogSidebar"), {
  ssr: false,
});
const SearchBar = dynamic(() => import("@/components/ui/searchbar"), {
  ssr: false,
});

type Tags =
  | "Research"
  | "Casestudy"
  | "Learning"
  | "Development"
  | "All Blogs"
  | "";

const BlogChild = () => {
  const [blogs, setBlogs] = useState<any[]>([]);
  const [filteredBlogs, setFilteredBlogs] = useState<any[]>([]);
  const [selectedTags, setSelectedTags] = useState<Tags>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const fetchBlogs = async () => {
    if (isFetching || !hasMore) return; // Prevent unnecessary calls

    setIsFetching(true);

    try {
      const response = await fetch(
        `/api/blogs/getAllBlogs?page=${page}&limit=10`
      );
      if (!response.ok) throw new Error("Failed to load blogs");

      const data = await response.json();

      if (data.data.length === 0) {
        setHasMore(false); // Stop fetching when no more blogs are returned
      } else {
        setBlogs((prev) => [...prev, ...data.data]);
        setFilteredBlogs((prev) => [...prev, ...data.data]);

        if (data.data.length < 10) {
          setHasMore(false); // No more pages if less than 10 blogs are returned
        }
      }
    } catch (err) {
      setError("Failed to load blogs");
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  useEffect(() => {
    fetchBlogs();
  }, [page]);

  useEffect(() => {
    let filtered = blogs;
    if (selectedTags && selectedTags !== "All Blogs") {
      filtered = blogs.filter((blog) => blog.tags.includes(selectedTags));
    }
    if (searchQuery) {
      const lowerCaseQuery = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (blog) =>
          blog.title.toLowerCase().includes(lowerCaseQuery) ||
          blog.content.toLowerCase().includes(lowerCaseQuery)
      );
    }
    setFilteredBlogs(filtered);
  }, [selectedTags, searchQuery, blogs]);

  const handleFilter = (tag: Tags) => {
    setSelectedTags(tag);
  };

  const handleSearch = debounce((query: string) => {
    setSearchQuery(query);
  }, 300);

  useEffect(() => {
    if (!hasMore || isFetching) return; // Stop observing if no more blogs

    if (observerRef.current) observerRef.current.disconnect();

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isFetching) {
          setPage((prev) => prev + 1);
        }
      },
      { threshold: 1.0 }
    );

    const target = document.querySelector("#load-more-trigger");
    if (target) observerRef.current.observe(target);

    return () => observerRef.current?.disconnect();
  }, [hasMore, isFetching]);

  if (loading) {
    return (
      <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50">
        <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
      </div>
    );
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="px-4 md:flex lg:space-x-12 sm:space-x-2 mt-3 md:mt-0">
      <div className="mt-3 hidden md:block">
        <BlogSidebar onFilterChange={handleFilter} />
      </div>
      <div className="">
        <div className="flex flex-row gap-2">
          <div className="w-full md:w-[50%]">
            <SearchBar mainheader={false} onSearch={handleSearch} />
          </div>
          <div className="">
            <BlogSidebar onFilterChange={handleFilter} className="md:hidden" />
          </div>
        </div>

        <hr className="max-md:hidden block my-4" />

        {filteredBlogs.length > 0 ? (
          <div className="grid grid-cols-1 gap-8 lg:mx-0 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1 h-fit w-full">
            {filteredBlogs.map((blog, index) => (
              <CardContainer key={blog.id} className="inter-var h-full w-full">
                <Link
                  href={`/blog/${(blog as any)?.slug}`}
                  className="h-full w-full"
                >
                  <div className="bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 shadow-lg rounded-lg h-full w-full">
                    <div className="relative w-full">
                      <Image
                        className="rounded-t-lg object-cover w-full aspect-video md:aspect-3/2"
                        src={
                          blog.images && blog.images.length > 0
                            ? (typeof blog.images[0].image === "string"
                                ? blog.images[0].image
                                : blog.images[0].image?.sizes?.thumbnail
                                    ?.url) || placeholderImg
                            : placeholderImg
                        }
                        alt={blog.title}
                        width={400}
                        height={200}
                        objectFit="cover"
                        loading="lazy"
                      />
                      {blog?.isFeatured === true && (
                        <div className="absolute top-0 -left-2 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                          <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                            FEATURED
                            <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                          </div>
                        </div>
                      )}
                      <p className="text-xs text-white absolute top-2 bg-indigo-600 w-fit px-2 py-1 right-2 rounded-lg">
                        {new Date(blog.createdAt).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        })}{" "}
                        · {blog.time} mins
                      </p>
                    </div>
                    <div className="p-3 mb-6 flex flex-col justify-between">
                      <div>
                        <h5 className="text-gray-100 font-bold text-lg tracking-tight mb-2 break-words whitespace-normal line-clamp-2 capitalize">
                          {blog.title}
                        </h5>
                        <p className="font-normal text-xs text-gray-200 mb-3 line-clamp-3 first-letter:uppercase">
                          {blog.content.substring(0, 155)}...
                        </p>
                      </div>
                      <div className="flex flex-wrap gap-2 absolute bottom-3">
                        {Array.isArray(blog.tags)
                          ? blog.tags.map((tag, index) => (
                              <Badge
                                key={index}
                                className="mr-2 first-letter:uppercase"
                              >
                                {capitalizeFirstLetter(tag)}
                              </Badge>
                            ))
                          : blog.tags?.split(",").map((tag, index) => (
                              <Badge
                                key={index}
                                className="mr-2 first-letter:uppercase"
                              >
                                {capitalizeFirstLetter(tag.trim())}
                              </Badge>
                            ))}
                      </div>
                    </div>
                  </div>
                </Link>
              </CardContainer>
            ))}
          </div>
        ) : (
          <div className="text-center py-24">Blog not found</div>
        )}

        {isFetching && (
          <div className="flex justify-center items-center mt-5">
            <Loader2 className="h-8 w-8 spin-animation font-normal" />
          </div>
        )}

        {!hasMore && !isFetching && (
          <div className="text-center py-4 text-black-400">No more blogs</div>
        )}
        <div id="load-more-trigger" className="h-10"></div>
      </div>
    </div>
  );
};

export default BlogChild;
