import Link from "next/link";
import { getPayloadClient } from "@/server/get-payload";
import RichText from "@/components/RichText";
import { ArrowLeft } from "lucide-react";
import { TracingBeam } from "@/components/ui/tracing-beam";
import Loader from "@/components/Loader";
import { Metadata, ResolvingMetadata } from "next";
import Image from "next/image";
import userIcon from "@/public/img/6c4457344663501.647c92db82cef-1-1024x1024.jpg";
import { User } from "@/server/payload-types";
import { Blog, Media } from "@/server/payload-types";
import { Badge } from "@/components/ui/badge";
import { getServerSideUser } from "../../../../lib/payload-utils";
import { cookies } from "next/headers";
import { Button } from "@/components/ui/button";
import BlogDetailButtons from "@/components/BlogDetailButtons";

interface PageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }): Promise<Metadata> {
  
  const payload = await getPayloadClient();
  // const previousImage = (await parent).openGraph?.images || [];
  

  const blog: any = await payload.find({
    collection: "blogs",
    where: {
      slug: {
        equals: params.slug,
      },
    },
    depth: 0,
  });
  const blogData = blog?.docs[0] as any;
  const img = blog?.docs[0]?.images[0]?.image?.url;
  const blogUrl = `${process.env.PAYLOAD_PUBLIC_SITE_URL}/blog/${blog.slug}`;
  const blogimg = img
    ? img
    : "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp";

  const metadata: Metadata =
    process.env.SERVER_ENV === "prod"
      ? {
          title: blogData?.title,
          description: blogData?.content,
          alternates: {
            canonical: `/blog/${blogData?.slug}`,
          },
          openGraph: {
            title: blogData.title,
            description: blogData?.content,
            url: blogUrl,
            siteName: "Rentprompt.com",
            images: [blogimg],
          },
          verification: {
            google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
          },
        }
      : null;

  return metadata;
}

const BlogDetails = async ({ params: { slug } }: PageProps) => {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);
  const payload = await getPayloadClient();
  const blog = await payload.find({
    collection: "blogs",
    where: {
      slug: {
        equals: slug,
      },
    },
  });

 
  if (!blog) {
    return <p>Blog not found</p>;
  }
  
  const blogData = blog.docs[0] as any;
  const userName = (blogData?.user as User)?.user_name;
  const userEmailName = (blogData?.user as User)?.email?.split("@")[0];
  const userId = (blogData?.user as User)?.id;
  const userImage = (blogData?.user as any)?.profileImage?.url;
  const imageUrls = blogData?.images.map((item) => item.image.url);
  const blogId = blogData?.id;  

  return (
    <>
      <Loader />
      <div className="w-full relative mx-auto justify-center mt-20 px-4">
        <TracingBeam className="w-full">
          <div className="w-full flex flex-col lg:flex-row gap-6">
            {/* Left Side */}
            <div className="lg:w-9/12">
              <div className="md:flex md:flex-wrap justify-between items-center">
                <Link href="/blog" className="absolute -left-32 top-0">
                  <button className="text-indigo-200 px-2 bg-indigo-700 py-2 my-2 rounded-lg shadow shadow-indigo-900">
                    <div className="flex items-center text-md font-bold">
                      <ArrowLeft />
                      {/* <span>Back to Blogs</span> */}
                    </div>
                  </button>
                </Link>
              </div>

              <div className="text-white/70">
                <img
                  alt={blogData?.title}
                  src={imageUrls}
                  className="w-full object-cover rounded-2xl aspect-video shadow-md"
                  loading="lazy"
                />
              </div>

              <div className="mt-4 mb-8">
                <h1 className="text-2xl md:text-4xl font-bold bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text 
                text-transparent mb-2 capitalize">
                  {blogData?.title}
                </h1>
              </div>

              <div className="flex justify-between">
                <div className="flex gap-3 items-center">
                  <Link href={`/users/${userName}`}>
                    <Image
                      alt={userImage}
                      // src={userImage}
                      src={userImage ?? "/png/avatar/2.png"}
                      height={40}
                      width={40}
                      className="object-cover rounded-full aspect-square shadow-md"
                      loading="lazy"
                    />
                  </Link>
                  <div className="flex flex-col md:flex-row md:space-x-2">
                    <div className="flex flex-row gap-2">
                      <div>
                        <Link
                          className="text-md font-medium capitalize"
                          href={`/users/${userName}`}
                        >
                          {userName ?? userEmailName}
                        </Link>
                      </div>
                    </div>
                    <span className="hidden md:flex">•</span>
                    <div className="flex flex-row gap-2">
                      <h4 className="text-md font-medium">
                        {new Date(blogData?.createdAt).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )}
                      </h4>
                      <span>•</span>
                      <h4 className="text-md font-medium">
                        {blogData?.time} min read
                      </h4>
                    </div>
                  </div>
                </div>
                <BlogDetailButtons user={user} userId={userId} blogId={blogId} />
              </div>

              <RichText content={blogData?.richText} className="mt-8 text-white/80  " />
            </div>

            {/* Right Side */}
            <div className="lg:w-3/12 max-w-sm space-y-4">
              <div className="">
                <h2 className="text-xl font-semibold mb-2">Tags:</h2>
                <div className="flex flex-wrap">
                  <Badge variant="gradient">#{blogData?.tags} </Badge>
                </div>{" "}
              </div>
            </div>
          </div>
        </TracingBeam>
      </div>
    </>
  );
};

export default BlogDetails;
