import dynamic from "next/dynamic";
import Container from "@/components/ui/container";
import { Metadata } from "next";
import { Vortex } from "@/components/ui/vortex";
import Link from "next/link";
import fetchUser from "@/app/(mainapp)/marketplace/fetchUser";
import DynamicSquareBackground from "@/components/landingWiget";

const RentPageClient = dynamic(() => import("./components/RentPageClient"), {
  ssr: false,
  loading: () => <div>Loading...</div>,
});
const Loader = dynamic(() => import("@/components/Loader"), { ssr: false });

export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: {absolute:"Explore Top Generative AI Apps | Rentprompts" },
        description:
          "Explore the best Gen AI apps at RentPrompts, Featured text-to-text, text-image, text-audio , text-video, Gen AI Apps.",
        alternates: {
          canonical: "/ai-apps",
        },
        keywords: [
          "Top AI apps",
          "Top rental AI apps",
          "Featured AI apps",
          "AI Apps marketplace",
          "Gen AI Apps",
          "Generative AI Apps",
          "Text to Text AI Apps",
          "Text to Text Gen AI Apps",
          "Text to Image AI Apps",
          "Text to Image Gen AI Apps",
          "Text to Audio AI Apps",
          "Text to Audio Gen AI Apps",
          "Text to Video AI Apps",
          "Text to Video Gen AI Apps",
          "Affordable AI Apps",
          "Accurate AI Apps",
          "Affordable Gen AI Apps",
          "Affordable Text to Text AI Apps",
          "Affordable Text to Text Gen AI Apps",
          "Affordable Tex to Image AI Apps",
          "Affordable Tex to Image Gen AI Apps",
          "One stop AI Apps Platforms",
          "Top Gen AI Platforms",
          "AI-Powered Marketplaces",
          "Cost-Effective AI Apps",
          "Cost-Effective Gen AI Apps",
          "Easy-to-Use AI Apps",
          "Easy-to-Use Gen AI Apps",
          "Time-Saving AI Solutions",
          "Smart AI Apps",
          "Smart Gen AI Apps",
          "Latest AI Technology",
          "Latest Gen AI Technology",
          "Innovative AI Solutions",
          "AI Apps for Businesses",
          "AI Apps for Professionals",
          "AI Apps for Content Creation",
          "AI Apps for Task Automation",
          "AI App Builder for Developers",
          "AI Marketplace for End Consumers",
          "AI Marketplace for Developers",
          "High-Performance AI Rental Apps",
          "Top-Rated AI Solutions",
          "Leading AI",
          "Featured AI Rental Apps",
          "RentPrompts AI",
          "Find AI Rental Apps",
          "Create AI Apps",
          "Create AI Apps and Earn",
          "Create Easy AI Apps",
          "Create Money Making AI Apps",
        ],
        openGraph: {
          title: {absolute: "Explore Top Gen AI Apps | Rentprompts" },
          description:
            "Explore the best AI apps at RentPrompts, Featured text-to-text, text-image, text-audio, text-video, Gen AI Apps.",
          url: "/ai-apps",
          siteName: "Rentprompts",
          images: [
            {
              url: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Group%2048095915.jpg`,
              width: 1200,
              height: 630,
            },
          ],
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
      }
    : null;

const RentPage = async () => {
  const fetchUserLazy = fetchUser();
  return (
    <>
      <Container>
        <div className="w-full mx-auto rounded-md overflow-hidden mb-6">
          <DynamicSquareBackground
            buttonHref="#"
            buttonHref1="https://rentprompts.ai/"
            buttonText=""
            buttonText1="CREATE AI APP"
            description="Explore ready-made curated AI apps made by expert AI engineers, save time and cost on creating content. Create and monetize AI Apps in seconds with no-code app builder and live preview."
            tag="AI APP Store"
            title="World's best AI App Store"
            image={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/ai-apps.png`}
            image1=""
          />
        </div>
        <Loader />
        <RentPageClient user={await fetchUserLazy} />
      </Container>
    </>
  );
};

export default RentPage;
