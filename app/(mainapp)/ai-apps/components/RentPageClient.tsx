"use client";

import React, { useRef, useState, useCallback } from "react";
import dynamic from "next/dynamic";
import { ChevronLeft, ChevronRight } from "lucide-react";
// import RentReel from "@/components/RentReel";
import { User } from "../../../../server/payload-types";
const RentReel = dynamic(() => import("@/components/RentReel"), {
  ssr: false,
  loading: () => <div className="w-full lg:w-72 h-48 bg-gray-200 animate-pulse" />,
});

interface RentPageClientProps {
  href?: string;
  user: User | null;
}
const RentPageClient = ({ user }: RentPageClientProps) => {
  const [sortingString, setSortingString] = useState("isFeatured");

  const scrollContainerRefTopFeatured = useRef<HTMLDivElement>(null);
  const scrollContainerRefNewestApp = useRef<HTMLDivElement>(null);


  const scroll = useCallback((ref: React.RefObject<HTMLDivElement>, direction: "left" | "right") => {
    if (ref.current) {
      ref.current.scrollBy({
        left: direction === "left" ? -300 : 300,
        behavior: "smooth",
      });
    }
  }, []);

  return (
    <div>
      {/* Top Featured Section */}
      <Section
        title="Featured AI Apps"
        description="Highlighted and top-performing AI Applications."
        scrollContainerRef={scrollContainerRefTopFeatured}
        sortingString={sortingString}
        user={user}
        onScroll={scroll}
      />

      {/* Newest App Section */}
      <Section
        title="Latest AI Apps"
        description="Newly launched AI-powered solutions."
        scrollContainerRef={scrollContainerRefNewestApp}
        sortingString={null} // No specific sorting for the newest app
        user={user}
        onScroll={scroll}
      />
    </div>
  );

  // return (
  //   <>
  //     <div className="">
  //       <h2 className="text-3xl font-bold px-4 md:px-8 py-4">Top Featured</h2>
  //       <div className="relative px-4 md:px-8">
  //         <ScrollButton
  //           direction="left"
  //           onClick={() => scrollLeft(scrollContainerRefTopFeatured)}
  //         />
  //         <ScrollButton
  //           direction="right"
  //           onClick={() => scrollRight(scrollContainerRefTopFeatured)}
  //         />
  //         <div
  //           ref={scrollContainerRefTopFeatured}
  //           className="overflow-x-auto no-scrollbar whitespace-nowrap border-t-2 border-indigo-600 py-6"
  //           aria-label="Top Featured AI apps"
  //         >
  //           <RentReel
  //             query={{ limit: 10 }}
  //             sort={sortingString}
  //             className="inline-block mr-2 w-full lg:w-72 h-48"
  //             user={user}
  //           />
  //         </div>
  //       </div>
  //     </div>
  //     <div className="">
  //       <h2 className="text-3xl font-bold px-4 md:px-8 py-4">Newest App</h2>
  //       <div className="relative px-4 md:px-8">
  //         <ScrollButton
  //           direction="left"
  //           onClick={() => scrollLeft(scrollContainerRefNewestApp)}
  //           // className="absolute left-7 sm:left-9 top-1/2 transform -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 transition-transform transition-colors duration-300 ease-in-out active:scale-90"
  //           // aria-label="Scroll left"

  //           // <ChevronLeft className="h-4 w-4 text-indigo-700" />
  //         />
  //         <ScrollButton
  //           direction="right"
  //           onClick={() => scrollRight(scrollContainerRefNewestApp)}
  //           // className="absolute right-7 sm:right-9 top-1/2 transform -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 transition-transform transition-colors duration-300 ease-in-out active:scale-90"
  //           // aria-label="Scroll right"

  //           // <ChevronRight className="h-4 w-4 text-indigo-700" />
  //         />
  //         <div
  //           ref={scrollContainerRefNewestApp}
  //           className="overflow-x-auto no-scrollbar whitespace-nowrap border-t-2 border-indigo-600 py-6"
  //           aria-label="Newest AI apps"
  //         >
  //           <RentReel
  //             query={{ limit: 10 }}
  //             className="inline-block mr-2 w-full lg:w-72 h-48"
  //             user={user}
  //           />
  //         </div>
  //       </div>
  //     </div>
  //   </>
  // );
};

interface SectionProps {
  title: string;
  description: string;
  scrollContainerRef: React.RefObject<HTMLDivElement>;
  sortingString?: string | null;
  user: User | null;
  onScroll: (ref: React.RefObject<HTMLDivElement>, direction: "left" | "right") => void;
}

const Section = ({ title, description, scrollContainerRef, sortingString, user, onScroll }: SectionProps) => (
  <div className="">
    <div className="flex flex-col px-4 md:px-8 py-4">
    <h2 className="text-3xl font-bold">{title}</h2>
    <p className="text-gray-400">{description}</p>
    </div>
    <div className="relative px-4 md:px-8">
      {/* Scroll Buttons */}
      <ScrollButton
        direction="left"
        onClick={() => onScroll(scrollContainerRef, "left")}
      />
      <ScrollButton
        direction="right"
        onClick={() => onScroll(scrollContainerRef, "right")}
      />

      {/* Scrollable Content */}
      <div
        ref={scrollContainerRef}
        className="overflow-x-auto no-scrollbar whitespace-nowrap border-t-2 border-indigo-600 py-6 px-2 space-x-4"
        aria-label={`${title} AI apps`}
      >
        <RentReel
          query={{ limit: 10 }}
          sort={sortingString || undefined}
          className="inline-block  w-full lg:w-72 md:h-48 h-56 transform transition duration-200 hover:scale-105 hover:shadow-xl"
          user={user}
          rentReelFilterCriteria={{}}
        />
      </div>
    </div>
  </div>
);

const ScrollButton = React.memo(
  ({
    direction,
    onClick,
  }: {
    direction: "left" | "right";
    onClick: () => void;
  }) => (
    <button
      onClick={onClick}
      className={`absolute ${
        direction === "left" ? "left-7 sm:left-9" : "right-7 sm:right-9"
      } top-1/2 -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 active:scale-90`}
      aria-label={`Scroll ${direction}`}
    >
      {direction === "left" ? (
        <ChevronLeft className="h-4 w-4 text-indigo-700" />
      ) : (
        <ChevronRight className="h-4 w-4 text-indigo-700" />
      )}
    </button>
  )
);
ScrollButton.displayName = "ScrollButton";

export default RentPageClient;
