import Image from "next/image";
import { notFound } from "next/navigation";
import RentForm from "../../../../components/rent/RentForm";
import Container from "@/components/ui/container";
import Price from "@/components/Price";
import Loader from "@/components/Loader";
import { Metadata, ResolvingMetadata } from "next";
import { cookies } from "next/headers";
import { getServerSideUser } from "../../../../lib/payload-utils";
import Link from "next/link";
import LikeButton from "@/components/RappLikeButton";
import axios from "axios";

import DescriptionToggle from "@/components/DescriptionToggle";
import { metadata } from "@/app/layout";
import ImageGallery from "@/components/ImageGallery";
import StarRating from "@/components/RatingStar";
import { log } from "node:console";
import { AnimatedModalDemo } from "@/components/AnimatedModalDemo";
import GetPrompt from "@/components/ui/GetPrompt";

import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";

// import { AppleCardsCarouselDemo } from "@/components/AppleCardCarousel";

interface PageProps {
  params: {
    slug: string;
  };
}
const BREADCRUMBS = [
  { id: 1, name: "Home", href: "/" },
  { id: 2, name: "AI Apps", href: "/ai-apps" },
];

export async function generateMetadata({
  params: { slug },
}: PageProps): Promise<Metadata> {
  const { data: rapp } = await axios.get(
    `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/getRappSlug/${slug}`
  );

  if (!rapp) return notFound();

  const img = rapp.images[0]?.image?.url;
  const rentUrl = `${process.env.PAYLOAD_PUBLIC_SITE_URL}/ai-apps/${rapp.slug}`;
  const rappsimg = img
    ? img
    : `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}Group%2048095914.png`;

  const metadata: Metadata =
    process.env.SERVER_ENV === "prod"
      ? {
          title: rapp.name,
          description: rapp?.description,
          alternates: {
            canonical: `/ai-apps/${rapp.slug}`,
          },
          openGraph: {
            title: rapp.name,
            description: rapp?.description,
            url: rentUrl,
            siteName: "Rentprompt.com",
            images: [rappsimg],
          },
          twitter: {
            card: "summary_large_image",
            title: rapp.name,
            description: rapp?.description,
            images: [rappsimg],
          },
          verification: {
            google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
          },
        }
      : null;

  return metadata;
}

const Page = async ({ params: { slug } }: PageProps) => {
  const nextCookies = cookies();
  const { user } = await getServerSideUser(nextCookies);
  const { data: rapp } = await axios.get(
    `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/getRappSlug/${slug}`
  );
  if (!rapp) return notFound();

  const validUrls = rapp.images
    .map(({ image }: any) => (typeof image === "string" ? image : image.url))

    .filter(Boolean) as string[];

  const likes = (rapp.likes as string[]).length;
  const isInitiallyLiked = rapp.likes.some(
    (like: any) => like.user?.id === user?.id
  );
  const Profile = (rapp.creatorProfile as any)?.url;

  const getRelativeTime = (dateString: string): string => {
    const createdAt = new Date(dateString).getTime(); // Convert to timestamp (number)
    const now = new Date().getTime(); // Convert to timestamp (number)
    const diffInMs = now - createdAt; // Now both are numbers

    const diffInSec = Math.floor(diffInMs / 1000);
    const diffInMin = Math.floor(diffInSec / 60);
    const diffInHours = Math.floor(diffInMin / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInMonths / 12);

    if (diffInYears > 0)
      return ` ${diffInYears} Year${diffInYears > 1 ? "s" : ""} ago`;
    if (diffInMonths > 0)
      return ` ${diffInMonths} Month${diffInMonths > 1 ? "s" : ""} ago`;
    if (diffInDays > 0)
      return ` ${diffInDays} Day${diffInDays > 1 ? "s" : ""} ago`;
    if (diffInHours > 0)
      return ` ${diffInHours} Hour${diffInHours > 1 ? "s" : ""} ago`;
    if (diffInMin > 0)
      return ` ${diffInMin} Minute${diffInMin > 1 ? "s" : ""} ago`;
    return "Created just now";
  };
  return (
    <Container>
      <Loader />
      <div className="flex flex-col md:flex-row justify-around md:gap-5 lg:gap-10 text-white py-6 px-4  md:mx-8 md:mt-16">
        <div className="w-full md:w-1/2 bg-indigo-800 h-fit md:border-2 border-indigo-600 text-white md:px-6 md:py-4 pt-4 md:mx-0 rounded-lg shadow-lg">
          <div className="flex flex-col mb-4 w-full">
            {/* <Image
              width="500"
              height="500"
              src={validUrls[0] ? validUrls[0] : "https:///placeholder-09.webp"}
              alt="App Icon"
              className="h-60 lg:h-96 object-cover rounded mr-3 mb-3 w-full"
            /> */}
            <ImageGallery images={validUrls} />
            <div>
              {rapp?.status === "approved" && (
                <div className="flex justify-between items-start md:items-center mb-3 flex-col-reverse md:flex-row gap-2">
                  <h1 className="text-3xl font-bold capitalize">{rapp.name}</h1>
                  <LikeButton
                    productId={rapp.id}
                    userId={user?.id ?? null}
                    initialLikes={likes}
                    isInitiallyLiked={isInitiallyLiked}
                  />
                </div>
              )}

              <div className="text-zinc-400 mb-4 flex items-start">
                <DescriptionToggle description={rapp.description} />
              </div>
              {rapp?.status === "approved" && (
                <div className="flex gap-3">
                  <div>
                    {/* Add Star Rating here */}
                    <StarRating rappId={rapp.id} user={user} />
                  </div>
                </div>
              )}
              <div className="mt-4">
                <div>
                  {rapp.affiliated_with && (
                    <div>
                      Affiliated With :{" "}
                      <a
                        href={rapp.affiliated_with}
                        className="text-green-500 underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {rapp.affiliated_with.substring(0, 30) + "..."}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div
            className={`flex items-center mb-4 ${rapp.totalCost + rapp.price === 0 ? "bg-green-500 pl-[7px] tracking-wider" : "bg-white/30"} max-w-fit rounded pl-[10px] text-lg font-bold pr-[-3px]`}
          >
            <p className="text-xl font-bold">
              {" "}
              {rapp.totalCost + rapp.price === 0
                ? "FREE"
                : rapp.totalCost + rapp.price}
            </p>
            {rapp.totalCost + rapp.price === 0 ? (
              ""
            ) : (
              <img
                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                alt="Coin"
                style={{ width: "23px", height: "23px" }}
                loading="lazy"
              />
            )}
            &nbsp;
          </div>

          {rapp.getprompt && user?.id !== rapp.creatorId && (
            <div
              className="flex items-center mb-4 bg-indigo-900 border border-violet-400
             hover:bg-indigo-700 text-white max-w-fit rounded p-1 px-1 text-lg font-bold "
            >
              <GetPrompt user={user} rapp={rapp} id={rapp.id} />
              &nbsp;
            </div>
          )}

          <div className="flex justify-between">
            <div className="flex flex-row gap-2">
              <h2 className="bg-white w-fit text-indigo-600 px-2 py-1 rounded uppercase ">
                {rapp.modelType}
              </h2>
              <h2 className="bg-white w-fit text-indigo-600 px-2 py-1 rounded uppercase">
                {rapp.modelName}
              </h2>
            </div>
            <div className="text-sm bg-white/30 text-white px-2 py-1 rounded">
              {" "}
              {getRelativeTime(rapp.createdAt)}
            </div>
          </div>

          <div className="flex items-center mt-4 mb-4">
            <Image
              width="500"
              height="500"
              // src={rapp?.creator?.profileImage?.url}
              
              src={Profile ?? `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/6c4457344663501.647c92db82cef-1-1024x1024.jpg`}

              alt="User Avatar"
              className="w-10 h-10 rounded-full mr-2 object-cover"
            />
            <Link
              href={`/users/${rapp.creatorName}`}
              // target="_blank"
              className="bg-white text-indigo-600 px-2 py-1 rounded first-letter:uppercase"
            >
              @{" "}
              {capitalizeFirstLetter(
                rapp.creatorName == undefined ? "Anonymous" : rapp.creatorName
              )}
            </Link>
          </div>
          {rapp?.status === "approved" && (
            <div className="-mt-14 md:block hidden ">
              <AnimatedModalDemo user={user} rappId={rapp.id} rapp={rapp} />
            </div>
          )}
        </div>
        <div className="w-full md:w-1/2 mx-auto">
          <div className="bg-dash-foreground border-4 border-muted-foreground text-white pt-4 rounded-lg shadow-lg w-full ">
            <RentForm rapp={rapp} user={user} />
            <div className="flex justify-center items-center"></div>
          </div>
          <p className="-mb-5 mt-3 text-center">
            Need help?{" "}
            <Link
              href="https://discordapp.com/channels/1179720072370065428/1280188340108525661"
              target="_blank"
              className="ml-1 underline text-green-400"
            >
              Contact Support Team
            </Link>
          </p>
        </div>
      </div>
      {/* <div className="-mt-20">
        <AppleCardsCarouselDemo user={user} rappId={rapp.id} />
      </div> */}
      <div className=" md:hidden sm:block p-1">
        <AnimatedModalDemo user={user} rappId={rapp.id} rapp={rapp} />
      </div>
    </Container>
  );
};

export default Page;
