"use client";
import { Label } from "../../../components/ui/label";
import { cn } from "@/lib/utils";
import { Input } from "../../../components/services/servicesinput";
import { useState , useEffect } from "react";
import { toast } from "sonner";

interface FormData {
  userName?: string;
  userType: "indian" | "foreign";
  bankAccount?: string;
  confirmBankAccount?: string;
  ifscCode?: string;
  upiId?: string;
  confirmUpiId?: string;
  acceptTerms: boolean;
  payoutType: string;
  country?: string;
  internationalDetails?: string;
  coinsToWithdrawBank? : string;
  coinsToWithdrawUPI?: string;
  coinsToWithdrawInterNational: string;
}

const supportedCountries = ["USA", "UK", "Canada", "Australia", "Germany"]; // Example countries

export default function UserBankForm() {
  const [formData, setFormData] = useState<FormData>({
    userType: "indian",
    acceptTerms: false,
    payoutType: "bank", // Default to "bank" payout type
    coinsToWithdrawInterNational:"",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

//   //----------------- Fetching Logged in User -----------------
//   const [user, setUser] = useState<User | null>(null);
//   const [userId, setUserId] = useState<string | null>(null);
//   const [loading, setLoading] = useState(true);

//   useEffect(() => {
//    const fetchData = async () => {
//      try {
//        const res = await fetch(
//          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`
//        );
//        const data = await res.json();

//        if (!data) {
//          setLoading(false);
//          return;
//        }
//        setUser(data?.user);
//        setUserId(data?.user?.id);
//      } catch (error) {
//        console.error("Error fetching user data:", error);
//      } finally {
//        setLoading(false);
//      }
//    };
//    fetchData();
//  }, []);


  //------------------------------------- Fetching Logged inUser with custom endpoint ------------------------------
  const [user, setUser] = useState<User | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,              
          {
            method: "GET",
          }
        );
        if (!response.ok) {
           throw new Error("Network response was not ok");
        }
        const data = await response.json();                                    
        setUser(data.data);
        setUserId(data?.user?.id);
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };
   fetchData();
 }, []);

  const validateForm = (updatedData: FormData) => {
    let newErrors: { [key: string]: string } = {};

    // General validations
    if (!updatedData.userName) newErrors.userName = "Account Holder Name is required";

    const coinBalance = user.coinBalance;
    // console.log("userbalance:", coinBalance)
    // console.log("balance:", updatedData.coinsToWithdrawBank)
    //console.log("balance:", coinBalance)
    // if (
    //   !updatedData.coinsToWithdraw ||
    //   isNaN(Number(updatedData.coinsToWithdraw))
    // ) {
    //   newErrors.coinsToWithdraw =
    //     "Please enter a valid number of coins to withdraw";
    if (Number(coinBalance) <= 500) {
      newErrors.coinsToWithdraw = "Balance must be greater than 500 credits to withdraw";
    } else if (Number(updatedData.coinsToWithdrawBank) > coinBalance) {
      newErrors.coinsToWithdrawBank = "Insufficient Credit Balance";
    }else if (Number(updatedData.coinsToWithdrawUPI) > coinBalance) {
      newErrors.coinsToWithdrawUPI = "Insufficient Credit Balance";
    }else if (Number(updatedData.coinsToWithdrawInterNational) > coinBalance) {
      newErrors.coinsToWithdrawInterNational = "Insufficient Credit Balance";
    }

    // Payout type specific validations
    switch (updatedData.payoutType) {
      case "bank":
        // Bank account validation - must be numeric
        if (!updatedData.bankAccount) {
          newErrors.bankAccount = "Bank account is required";
        } else if (!/^\d+$/.test(updatedData.bankAccount)) {
          newErrors.bankAccount = "Bank account must be numeric";
        }

        // Confirm bank account validation - must match
        if (!updatedData.confirmBankAccount) {
          newErrors.confirmBankAccount = "Please confirm your bank account";
        } else if (updatedData.bankAccount !== updatedData.confirmBankAccount) {
          newErrors.confirmBankAccount =
            "Entered Bank Account Number does not match";
        }

        if (Number(updatedData.coinsToWithdrawBank) < 500) {
          newErrors.coinsToWithdrawBank =
            "Minimum 500 credits required for withdrawal";
        }

        // IFSC Code validation - must be alphanumeric and uppercase
        if (!updatedData.ifscCode) {
          newErrors.ifscCode = "IFSC code is required";
        } else if (!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(updatedData.ifscCode)) {
          newErrors.ifscCode =
            "Invalid IFSC code format (should be alphanumeric and uppercase)";
        }
        break;

      case "upi":
        // UPI ID validation
        if (!updatedData.upiId) {
          newErrors.upiId = "UPI ID is required";
        } else if (!/^[a-zA-Z0-9.\-_]+@[a-zA-Z]+$/.test(updatedData.upiId)) {
          newErrors.upiId = "Invalid UPI ID format";
        }

        // Confirm UPI ID validation - must match
        if (!updatedData.confirmUpiId) {
          newErrors.confirmUpiId = "Please confirm your UPI ID";
        } else if (updatedData.upiId !== updatedData.confirmUpiId) {
          newErrors.confirmUpiId = "Entered UPI Address does not match";
        }

        if (Number(updatedData.coinsToWithdrawUPI) < 500) {
          newErrors.coinsToWithdrawUPI =
            "Minimum 500 credits required for withdrawal";
        }
        break;

      case "international":


        if (Number(updatedData.coinsToWithdrawInterNational) < 1000) {
          newErrors.coinsToWithdrawInterNational =
            "Minimum 1000 credits required for withdrawal";
        }

        // International payment details validation
        if (!updatedData.internationalDetails) {
          newErrors.internationalDetails =
            "Please provide international payment details";
        }
        break;
    }

    // Terms and conditions acceptance
    if (!updatedData.acceptTerms) {
      newErrors.acceptTerms = "You must accept terms and conditions";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const filteredData: FormData = { ...formData }; // Create a copy of the formData

    // Remove irrelevant fields based on payoutType
    if (formData.payoutType === "bank") {
      delete filteredData.upiId;
      delete filteredData.confirmUpiId;
      delete filteredData.internationalDetails;
    } else if (formData.payoutType === "upi") {
      delete filteredData.bankAccount;
      delete filteredData.confirmBankAccount;
      delete filteredData.ifscCode;
      delete filteredData.internationalDetails;
    } else if (formData.payoutType === "international") {
      delete filteredData.bankAccount;
      delete filteredData.confirmBankAccount;
      delete filteredData.ifscCode;
      delete filteredData.upiId;
      delete filteredData.confirmUpiId;
    }

    if (validateForm(filteredData)) {
      try {
        toast.loading("Submitting...")
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/Payout`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              bankDetails: {
                accountNumber: formData.bankAccount,
                confirmAccountNumber: formData.confirmBankAccount,
                ifscCode: formData.ifscCode,
                withdrawAmountBank: formData.coinsToWithdrawBank,
              },
              upiDetails: {
                upiAddress: formData.upiId,
                confirmUpiAddress: formData.confirmUpiId,
                withdrawAmountUpi: formData.coinsToWithdrawUPI,
              },
              international:{
                country : formData.country,
                internationalDetails : formData.internationalDetails,
                withdrawAmountInternational: formData.coinsToWithdrawInterNational,
              },
              // withdrawAmount: formData.coinsToWithdraw,
              username: formData.userName,
              payoutType: formData.payoutType,
            }),
          }
        );
        toast.dismiss();

        // const data = await res.json();
        // console.log("Data sent", data);
        if (res.ok) {
          toast.success("Payout Request Submitted");
          setFormData({
            userName: "",
            coinsToWithdrawBank: "",
            coinsToWithdrawUPI:"",
            coinsToWithdrawInterNational: "",
            userType: "indian",
            bankAccount: "",
            confirmBankAccount: "",
            ifscCode: "",
            upiId: "",
            confirmUpiId: "",
            acceptTerms: false,
            payoutType: "bank",
            country: "",
            internationalDetails: "",
          });
        }
      } catch (error) {
        toast.dismiss();
        console.error("There was a problem with the fetch operation:", error);
      }
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;
    let updatedValue = value;

    if (name === "coinsToWithdraw") {
      updatedValue = updatedValue.replace(/[^0-9]/g, "");
    }

    // Automatically convert IFSC code to uppercase
    if (name === "ifscCode") {
      updatedValue = value.toUpperCase();
    }

    let updatedData = { ...formData, [name]: updatedValue };
    if (type === "checkbox") {
      const isChecked = (e.target as HTMLInputElement).checked;
      updatedData = { ...formData, [name]: isChecked };
    }

    const updatedErrors = { ...errors };
    if (name in updatedErrors) {
      delete updatedErrors[name];
      setErrors(updatedErrors);
    }

    setFormData(updatedData);
  };

  const handlePayoutTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    // Clear previous errors when switching payout type
    setErrors({});
    setFormData({ ...formData, payoutType: value });
  };

  return (
    <div className="p-4 md:p-6 w-11/12 md:w-7/12 mx-auto bg-gradient-to-br from-indigo-600 to-indigo-700 border dark:border-b-white/50 border-b-white/20 sm:border-t-white/20  shadow-slate-500/10 dark:shadow-white/20 rounded-lg border-white/20 border-l-white/20 border-r-white/20 sm:shadow-sm lg:rounded-xl lg:shadow-none shadow-md mt-16 md:mt-28">
      <h1 className="text-3xl font-bold tracking-tight sm:text-3xl text-center">
        Payout Request
      </h1>
      <form onSubmit={handleSubmit}>
        {/* Payout Type Radio Buttons */}
        <div className="my-4">
          <label className="block text-white font-semibold my-3 ">
            Payout Type:
          </label>
          <div className="flex flex-col md:flex-row gap-2 md:gap-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="payoutType"
                value="bank"
                defaultChecked
                checked={formData.payoutType === "bank"}
                onChange={handlePayoutTypeChange}
                className="mr-2"
              />
              Bank Details
            </label>

            <label className="flex items-center">
              <input
                type="radio"
                name="payoutType"
                value="upi"
                checked={formData.payoutType === "upi"}
                onChange={handlePayoutTypeChange}
                className="mr-2"
              />
              UPI Details
            </label>

            <label className="flex items-center">
              <input
                type="radio"
                name="payoutType"
                value="international"
                checked={formData.payoutType === "international"}
                onChange={handlePayoutTypeChange}
                className="mr-2"
              />
              International Payout Request
            </label>
          </div>
        </div>

        {/* Common Fields */}
        <LabelInputContainer className="my-5">
          <Label htmlFor="userName">
            Account Holder Name <span className="text-red-500">*</span>
          </Label>
          <Input
            name="userName"
            className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
            placeholder="Enter Account Holder Name"
            type="text"
            value={formData.userName || ""}
            onChange={handleInputChange}
          />
          {errors.userName && (
            <p className="text-yellow-300 text-sm">{errors.userName}</p>
          )}
        </LabelInputContainer>

        {formData.payoutType === "bank" && (
          <>
            <LabelInputContainer className="my-5">
              <Label htmlFor="bankAccount">
                Bank Account Number <span className="text-red-500">*</span>
              </Label>
              <Input
                type="number"
                name="bankAccount"
                placeholder="Enter your Account Number"
                value={formData.bankAccount || ""}
                onChange={handleInputChange}
                className="bg-transparent placeholder:text-slate-400  mb-2 md:mb-0"
              />
              {errors.bankAccount && (
                <p className="text-yellow-300 text-sm">{errors.bankAccount}</p>
              )}
            </LabelInputContainer>

            <LabelInputContainer className="my-5">
              <Label htmlFor="confirmBankAccount">
                Confirm Bank Account Number <span className="text-red-500">*</span>
              </Label>
              <Input
                type="number"
                name="confirmBankAccount"
                placeholder="Re-Enter Account Number"
                value={formData.confirmBankAccount || ""}
                onChange={handleInputChange}
                className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
              />
              {errors.confirmBankAccount && (
                <p className="text-yellow-300 text-sm">
                  {errors.confirmBankAccount}
                </p>
              )}
            </LabelInputContainer>

            <LabelInputContainer className="my-5">
              <Label htmlFor="ifscCode">
                IFSC Code <span className="text-red-500">*</span>
              </Label>
              <Input
                type="text"
                name="ifscCode"
                placeholder="Enter IFSC Code"
                value={formData.ifscCode || ""}
                onChange={handleInputChange}
                className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
              />
              {errors.ifscCode && (
                <p className="text-yellow-300 text-sm">{errors.ifscCode}</p>
              )}
            </LabelInputContainer>

            <LabelInputContainer className="my-5">
          <Label htmlFor="coinsToWithdrawBank">
            Enter Credits to Withdraw <span className="text-red-500">*</span>
          </Label>
          <Input
            type="number"
            name="coinsToWithdrawBank"
            min={500}
            placeholder="Enter Credits Here"
            value={formData.coinsToWithdrawBank || ""}
            onChange={handleInputChange}
            className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
          />
          <p className="text-sm animate-pulse text-yellow-200 font-medium">
                *Minimum 500 Credits balance required for withdraw
          </p>
          {errors.coinsToWithdrawBank && (
            <p className="text-yellow-300 text-sm">{errors.coinsToWithdrawBank}</p>
          )}
        </LabelInputContainer>
          </>
        )}

        {formData.payoutType === "upi" && (
          <>
            <LabelInputContainer className="my-5">
              <Label htmlFor="upiId">
                UPI ID <span className="text-red-500">*</span>
              </Label>
              <Input
                type="text"
                name="upiId"
                placeholder="Enter UPI Address"
                value={formData.upiId || ""}
                onChange={handleInputChange}
                className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
              />
              {errors.upiId && (
                <p className="text-yellow-300 text-sm">{errors.upiId}</p>
              )}
            </LabelInputContainer>

            <LabelInputContainer className="my-5">
              <Label htmlFor="confirmUpiId">
                Confirm UPI ID <span className="text-red-500">*</span>
              </Label>
              <Input
                type="text"
                name="confirmUpiId"
                placeholder="Enter Confirm UPI Address"
                value={formData.confirmUpiId || ""}
                onChange={handleInputChange}
                className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
              />
              {errors.confirmUpiId && (
                <p className="text-yellow-300 text-sm">{errors.confirmUpiId}</p>
              )}
            </LabelInputContainer>

            <LabelInputContainer className="my-5">
          <Label htmlFor="coinsToWithdrawUPI">
            Enter Credits to Withdraw <span className="text-red-500">*</span>
          </Label>
          <Input
            type="number"
            name="coinsToWithdrawUPI"
            min={500}
            placeholder="Enter Credits Here"
            value={formData.coinsToWithdrawUPI || ""}
            onChange={handleInputChange}
            className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
          />
          <p className="text-sm animate-pulse text-yellow-200 font-medium">
                *Minimum 500 Credits balance required for withdraw
          </p>
          {errors.coinsToWithdrawUPI && (
            <p className="text-yellow-300 text-sm">{errors.coinsToWithdrawUPI}</p>
          )}
        </LabelInputContainer>
          </>
        )}

        {formData.payoutType === "international" && (
          <>
            <LabelInputContainer className="my-5">
              <Label htmlFor="internationalDetails">
                Payment Details for Payout <span className="text-red-500">*</span>
              </Label>
              <textarea
                name="internationalDetails"
                placeholder="Please provide your stripe ID or PayPal id"
                value={formData.internationalDetails || ""}
                onChange={handleInputChange}
                className="mb-2 md:mb-0 flex w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background text-slate-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-400 focus:text-slate-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                rows={6} // Increased rows for better visibility
              />
              <p className="text-gray-300 mb-4 text-sm">
                Please Provide All Necessary Information of Your Bank Account
                for International Fund Transfer.
              </p>
              {errors.internationalDetails && (
                <p className="text-yellow-300 text-sm">
                  {errors.internationalDetails}
                </p>
              )}
            </LabelInputContainer>

            <LabelInputContainer className="my-5">
          <Label htmlFor="coinsToWithdrawInterNational">
            Enter Credits to Withdraw <span className="text-red-500">*</span>
          </Label>
          <Input
            type="number"
            name="coinsToWithdrawInterNational"
            min={1000}
            placeholder="Enter Credits Here"
            value={formData.coinsToWithdrawInterNational || ""}
            onChange={handleInputChange}
            className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
          />
          <p className="text-sm animate-pulse text-yellow-200 font-medium">
                *Minimum 1000 Credits balance required for withdraw
          </p>
          {errors.coinsToWithdrawInterNational && (
            <p className="text-yellow-300 text-sm">{errors.coinsToWithdrawInterNational}</p>
          )}
        </LabelInputContainer>
          </>
        )}

        {/* <LabelInputContainer className="my-5">
          <Label htmlFor="coinsToWithdraw">
            Enter Credits to Withdraw <span className="text-red-500">*</span>
          </Label>
          <Input
            type="number"
            name="coinsToWithdraw"
            min={0}
            placeholder="Enter Credits Here"
            value={formData.coinsToWithdraw || ""}
            onChange={handleInputChange}
            className="bg-transparent placeholder:text-slate-400 mb-2 md:mb-0"
          />
          <p className="text-sm animate-pulse text-yellow-200 font-medium">
                *Minimum 500 Credits balance required for withdraw
          </p>
          {errors.coinsToWithdraw && (
            <p className="text-yellow-300 text-sm">{errors.coinsToWithdraw}</p>
          )}
        </LabelInputContainer> */}

        {/* Terms and Conditions */}
        <div className="mb-4">
          <label className="flex items-center text-white">
            <input
              type="checkbox"
              name="acceptTerms"
              checked={formData.acceptTerms}
              onChange={handleInputChange}
              className="mr-2 "
            />
            I accept the terms and conditions
          </label>
          {errors.acceptTerms && (
            <p className="text-yellow-300 text-sm">{errors.acceptTerms}</p>
          )}
        </div>

        <button
          type="submit"
          className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600"
        >
          Submit Request
        </button>
      </form>
    </div>
  );
}
const LabelInputContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn("flex flex-col space-y-2 w-full", className)}>
      {children}
    </div>
  );
};