// (explore)/explore/page.tsx

import RentPlace from "@/components/ui/rentplace";
import fetchUser from "./fetchUser";
import { Metadata } from "next";
import DynamicSquareBackground from "@/components/landingWiget";

export const metadata: Metadata =
  process.env.SERVER_ENV === "prod"
    ? {
        title: "Gen AI Marketplace - Discover AI Apps and Assets | Rentprompts",
        description:
          "The ultimate platform for AI engineers, and creative minds to buy, sell, and monetize AI prompts and applications effortlessly.",
        keywords: [
          "Generative AI Insights",
          "AI Innovations",
          "Explore AI Tools",
          "Explore AI Apps",
          "Explore AI Images",
          "Explore AI Prompts",
          "Explore AI Trends",
          "Emerging AI Trends",
          "AI Marketplace",
          "Gen AI Marketplace",
          "Creative AI Solutions",
          "AI-driven Technology",
          "Next-Gen AI Tools",
          "AI-powered Platforms",
          "AI-powered Platforms",
          "Top rental AI apps",
          "Featured AI apps",
          "AI Apps marketplace",
          "Gen AI Apps",
          "Generative AI Apps",
          "Text to Text AI Apps",
          "Text to Text Gen AI Apps",
          "Text to Image AI Apps",
          "Text to Image Gen AI Apps",
          "Text to Audio AI Apps",
          "Text to Audio Gen AI Apps",
          "Text to Video AI Apps",
          "Text to Video Gen AI Apps",
          "Affordable AI Apps",
          "Accurate AI Apps",
          "Affordable Gen AI Apps",
          "Affordable Text to Text AI Apps",
          "Affordable Text to Text Gen AI Apps",
          "Affordable Tex to Image AI Apps",
          "Affordable Tex to Image Gen AI Apps",
          "One stop AI Apps Platforms",
          "Top Gen AI Platforms",
          "AI-Powered Marketplaces",
          "Cost-Effective AI Apps",
          "Cost-Effective Gen AI Apps",
          "Easy-to-Use AI Apps",
          "Easy-to-Use Gen AI Apps",
          "Time-Saving AI Solutions",
          "Smart AI Apps",
          "Smart Gen AI Apps",
          "Latest AI Technology",
          "Latest Gen AI Technology",
          "Innovative AI Solutions",
          "AI Apps for Businesses",
          "AI Apps for Professionals",
          "AI Apps for Content Creation",
          "AI Apps for Task Automation",
          "AI App Builder for Developers",
          "AI Marketplace for End Consumers",
          "AI Marketplace for Developers",
          "High-Performance AI Rental Apps",
          "Top-Rated AI Solutions",
          "Leading AI",
          "Featured AI Apps",
          "RentPrompts AI",
          "Find AI Rental Apps",
          "Create AI Apps",
          "Create AI Apps and Earn",
          "Create Easy AI Apps",
          "Create Money Making AI Apps"
        ],
        alternates: {
          canonical: `${process.env.PAYLOAD_PUBLIC_SITE_URL}/marketplace`,
        },
        openGraph: {
          title: "Gen AI Marketplace - Discover AI Apps and Assets | Rentprompts",
          description:
            "The ultimate platform for AI engineers, and creative minds to buy, sell, and monetize AI prompts and applications effortlessly.",
          url: `${process.env.PAYLOAD_PUBLIC_SITE_URL}/marketplace`,
          siteName: "Rentprompts",
          images: [
            {
              url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
              alt: "RentPrompts Explore Banner",
              width: 1200,
              height: 630,
            },
          ],
        },
        twitter: {
          card: "summary_large_image",
          title: "Explore - Discover AI Apps and Assets | Rentprompts",
          description:
            "The ultimate platform for AI engineers, and creative minds to buy, sell, and monetize AI prompts and applications effortlessly.",
          images: [
            {
              url: "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/Group%2048095914.webp",
              alt: "RentPrompts Twitter Banner",
            },
          ],
        },
        verification: {
          google: "s_neV4ms92S8ZM-27si9kwUBcBMpnK_q9mPCH_CyHzo",
        },
        robots: {
          index: true,
          follow: true,
        },
      }
    : null;

export default async function ExplorePage() {
  const user = await fetchUser();

  return (
    <>
      <RentPlace user={user} />
    </>
  );
}
