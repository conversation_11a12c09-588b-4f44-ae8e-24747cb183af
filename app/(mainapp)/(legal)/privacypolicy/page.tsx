import Loader from "@/components/Loader";

const page = () => {
  return (
    <>
      <Loader />
      <div className="px-4 py-8 md:mt-10">
        <div className="max-w-4xl mx-auto py-6">
          <h1 className="text-4xl font-bold mb-4">Data Privacy Policy</h1>
          <p className="mb-4 pl-4">
            This Data Privacy Policy governs the manner in which RentPrompts
            collects, uses, maintains, and discloses information collected from
            users (each, a &quot;User&quot;) of the https://rentprompts.com website
            (&quot;Site&quot;). This privacy policy applies to the Site and all
            products and services offered by RentPrompts.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            1. Personal Identification Information:
          </h2>
          <p className="pl-4 mb-4">
            1.1. We may collect personal identification information from Users
            in a variety of ways, including, but not limited to, when Users
            visit our site, register on the site, fill out a form, and in
            connection with other activities, services, features, or resources
            we make available on our Site.
          </p>
          <p className="pl-4 mb-4">
            1.2. Users may be asked for, as appropriate, name, email address,
            mailing address, phone number. Users may, however, visit our Site
            anonymously. We will collect personal identification information
            from Users only if they voluntarily submit such information to us.
            Users can always refuse to supply personally identification
            information, except that it may prevent them from engaging in
            certain Site-related activities.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            2. Non-personal Identification Information:
          </h2>
          <p className="pl-4 mb-4">
            2.1. We may collect non-personal identification information about
            Users whenever they interact with our Site. Non-personal
            identification information may include the browser name, the type of
            computer and technical information about Users means of connection
            to our Site, such as the operating system and the Internet service
            providers utilized and other similar information.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            3. Web Browser Cookies:
          </h2>
          <p className="pl-4 mb-4">
            3.1. Our Site may use &quot;cookies&quot; to enhance User
            experience. User&apos;s web browser places cookies on their hard
            drive for record-keeping purposes and sometimes to track information
            about them. Users may choose to set their web browser to refuse
            cookies or to alert you when cookies are being sent. If they do so,
            note that some parts of the Site may not function properly.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            4. How We Use Collected Information:
          </h2>
          <p className="pl-4 mb-4">
            4.1. RentPrompts may collect and use Users personal information for
            the following purposes: - To improve customer service - To
            personalize user experience - To send periodic Notifications.
          </p>
          <p className="pl-4 mb-4">
            4.2. We may use the email address to respond to their inquiries,
            questions, and/or other requests.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            5. How We Protect Your Information:
          </h2>
          <p className="pl-4 mb-4">
            5.1. We adopt appropriate data collection, storage, and processing
            practices and security measures to protect against unauthorized
            access, alteration, disclosure, or destruction of your personal
            information, username, password, transaction information, and data
            stored on our Site.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            6. Sharing Your Personal Information:
          </h2>
          <p className="pl-4 mb-4">
            6.1. We do not sell, trade, or rent Users personal identification
            information to others. We may share generic aggregated demographic
            information not linked to any personal identification information
            regarding visitors and users with our business partners, trusted
            affiliates, and advertisers for the purposes outlined above.
          </p>
          <p className="pl-4 mb-4">
            6.2 User has the ability to make personal information Public and
            Private.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            7. Changes to This Privacy Policy:
          </h2>
          <p className="pl-4 mb-4">
            7.1. RentPrompts has the discretion to update this privacy policy at
            any time. When we do, we will revise the updated date at the bottom
            of this page. We encourage Users to frequently check this page for
            any changes to stay informed about how we are helping to protect the
            personal information we collect. You acknowledge and agree that it
            is your responsibility to review this privacy policy periodically
            and become aware of modifications.
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            8. Your Acceptance of These Terms:
          </h2>
          <p className="pl-4 mb-4">
            8.1. By using this Site, you signify your acceptance of this policy.
            If you do not agree to this policy, please do not use our Site. Your
            continued use of the Site following the posting of changes to this
            policy will be deemed your acceptance of those changes.
          </p>
          <h2 className="text-2xl font-semibold mb-2">9. Contacting Us:</h2>
          <p className="pl-4 mb-4">
            9.1. If you have any questions about this Privacy Policy, the
            practices of this site, or your dealings with this site, please
            contact us at:
          </p>
          <ul className="list-disc pl-10 mb-4">
            <li>CREX RentPrompts Pvt. Ltd.</li>
            <li>Email : <EMAIL></li>
            <li>Contact No : +************</li>
            <li>Address : VF-29, Treasure Town, IDA State, Indore, MP, India - 452012</li>
          </ul>
          <p className="pl-4 mb-6">
            This Privacy Policy was last updated on 16/08/2024.
          </p>
          <h2 className="text-3xl font-semibold mb-2">Cyber Security Policy</h2>
          <h2 className="text-2xl font-semibold mb-2 pl-4">1. Purpose:</h2>
          <p className="pl-8 mb-4">
            1.1. The purpose of this Cyber Security Policy is to outline the
            principles and guidelines for safeguarding CREX RentPrompts Pvt.
            Ltd. information systems, data, and assets from cyber threats and
            ensuring the confidentiality, integrity, and availability of
            information.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">2. Scope:</h2>
          <p className="pl-8 mb-4">
            2.1. This policy applies to all employees, contractors, consultants,
            temporary workers, and other personnel affiliated with RentPrompts
            who have access to CREX RentPrompts Pvt. Ltd. information systems
            and data.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            3. Information Security Responsibilities:
          </h2>
          <p className="pl-8 mb-4">3.1. Management Responsibilities:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Establishing and maintaining an effective Cyber Security Program.
            </li>
            <li>
              Allocating appropriate resources for implementing and maintaining
              cyber security measures.
            </li>
            <li>
              Reviewing and approving the Cyber Security Policy and related
              procedures.
            </li>
            <li>
              Promoting cyber security awareness and training programs for all
              personnel.
            </li>
          </ul>
          <p className="pl-8 mb-4">3.2. Employee Responsibilities:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Complying with this Cyber Security Policy and related procedures.
            </li>
            <li>
              Reporting any suspected security incidents promptly to the
              designated personnel.
            </li>
            <li>
              Protecting company assets, including information systems and data,
              from unauthorized access, disclosure, modification, or
              destruction.
            </li>
            <li>
              Participating in cyber security awareness and training programs as
              required.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            4. Information Security Controls:
          </h2>
          <p className="pl-8 mb-4">4.1. Access Control:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Implementing access controls to ensure that only authorized
              individuals have access to information systems and data.
            </li>
            <li>
              Regularly reviewing and updating access permissions based on the
              principle of least privilege.
            </li>
          </ul>
          <p className="pl-8 mb-4">4.2. Data Protection:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>Encrypting sensitive data in transit and at rest.</li>
            <li>
              Implementing data loss prevention measures to prevent unauthorized
              disclosure of sensitive information.
            </li>
            <li>
              Establishing data retention and disposal procedures in accordance
              with legal and regulatory requirements.
            </li>
          </ul>
          <p className="pl-8 mb-4">4.3. Network Security:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Implementing firewalls, intrusion detection systems, and other
              technical controls to protect the network from unauthorized access
              and cyber-attacks.
            </li>
            <li>
              Monitoring network traffic for suspicious activities and
              anomalies.
            </li>
          </ul>
          <p className="pl-8 mb-4">4.4. Endpoint Security:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Deploying endpoint protection software (e.g., antivirus,
              antimalware) on all devices connected to RentPrompts network.
            </li>
            <li>
              Enforcing secure configurations and regular updates for all
              endpoint devices.
            </li>
          </ul>
          <p className="pl-8 mb-4">4.5. Incident Response and Management:</p>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Establishing an incident response plan to promptly detect, respond
              to, and recover from security incidents.
            </li>
            <li>
              Designating a team and defining roles and responsibilities for
              incident response and management.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            5. Compliance and Monitoring:
          </h2>
          <p className="pl-8 mb-4">
            5.1. Conducting regular audits and assessments of CREX RentPrompts
            Pvt. Ltd. cyber security controls to ensure compliance with this
            policy and relevant legal and regulatory requirements.
          </p>
          <p className="pl-8 mb-4">
            5.2. Reviewing and updating the Cyber Security Policy and related
            procedures as necessary to address emerging threats and
            vulnerabilities.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            6. Training and Awareness:
          </h2>
          <p className="pl-8 mb-4">
            6.1. Providing regular cyber security awareness and training
            programs to educate employees about cyber threats, best practices,
            and their responsibilities in safeguarding company information.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            7. Legal and Regulatory Compliance:
          </h2>
          <p className="pl-8 mb-4">
            7.1. Adhering to all applicable laws, regulations, and industry
            standards related to cyber security and data protection, including
            but not limited to the Information Technology Act, 2000 and the
            Information Technology (Reasonable Security Practices and Procedures
            and Sensitive Personal Data or Information) Rules, 2011.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            8. Reporting and Communication:
          </h2>
          <p className="pl-8 mb-4">
            8.1. Reporting cyber security incidents, breaches, or violations of
            this policy to designated personnel promptly for investigation and
            remediation.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            9. Review and Revision:
          </h2>
          <p className="pl-8 mb-4">
            9.1. Regularly reviewing and updating this Cyber Security Policy and
            related procedures to address changes in technology, business
            operations, and cyber threats.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            10. Contact Information:
          </h2>
          <p className="pl-8 mb-4">
            10.1. For questions or concerns regarding this Cyber Security
            Policy, please contact:
          </p>
          <ul className="list-disc pl-14 mb-4">
          <li>CREX RentPrompts Pvt. Ltd.</li>
            <li>Email : <EMAIL></li>
            <li>Contact No : +************</li>
            <li>Address : VF-29, Treasure Town, IDA State, Indore, MP, India - 452012</li>
          </ul>
          <p className="pl-4 mb-4">
            This Cyber Security Policy was last updated on 16/08/2024.
          </p>
          <p className="pl-4 mb-6">
            In addition to the Privacy Policy and Cyber Security Policy, several
            other policies are essential for an Indian AI website to ensure
            legal compliance, protect user rights, and manage operational risks
            effectively. Here are some key policies that should be considered:
          </p>
          <h2 className="text-2xl font-semibold mb-2">
            1. Terms of Service (TOS):{" "}
          </h2>
          <ul className="list-disc pl-14 mb-4">
            <li>
              The Terms of Service outline the rules and guidelines for using
              the website or service.
            </li>
            <li>
              It includes provisions on user responsibilities, prohibited
              activities, intellectual property rights, disclaimers of
              warranties, limitations of liability, and dispute resolution
              mechanisms.
            </li>
            <li>
              TOS is crucial for managing user expectations and protecting the
              website&apos;s legal interests.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold mb-2">2. Cookie Policy: </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              A Cookie Policy informs users about the use of cookies or similar
              tracking technologies on the website.
            </li>
            <li>
              It explains what cookies are used for, how they are used, and how
              users can manage their cookie preferences.
            </li>
            <li>
              This policy helps comply with India&apos;s Information Technology
              (Reasonable Security Practices and Procedures and Sensitive
              Personal Data or Information) Rules, 2011.
            </li>
          </ul>
          <h2 className="text-2xl font-semibold mb-2">
            3. Data Retention Policy:{" "}
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
            A Data Retention Policy specifies how long different types of data
            collected from users will be stored.
            </li>
            <li>
            It ensures compliance with legal requirements and helps manage
            storage costs and data privacy risks.
            </li>
            <li>
            The policy should include procedures for securely deleting or
            anonymizing data when it is no longer needed.
            </li>
          </ul>

      
          <h2 className="text-2xl font-semibold mb-2">
            4. Accessibility Policy:{" "}
          </h2>


          <ul className="list-disc pl-14 mb-4">
            <li>
            An Accessibility Policy outlines the website&apos;s commitment to
            making its content accessible to users with disabilities.
            </li>
            <li>
            It may include measures for compliance with the Rights of Persons
            with Disabilities Act, 2016, and the Web Content Accessibility
            Guidelines (WCAG).
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">
            5. Acceptable Use Policy (AUP):{" "}
          </h2>


          <ul className="list-disc pl-14 mb-4">
            <li>
            An Acceptable Use Policy sets out rules and guidelines for
            acceptable behavior when using the website or service.
            </li>
            <li>
            It typically covers prohibited activities, such as spamming,
            hacking, or other misuse of the website.
            </li>
            <li>
            AUP helps protect the website from abuse and ensures a safe and
            positive user experience.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">
            6. Third-Party Links Policy:{" "}
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
            A Third-Party Links Policy explains how the website handles links
            to third-party websites or services.
            </li>
            <li>
            It may include disclaimers of responsibility for the content or
            practices of third-party sites and encourage users to review the
            privacy policies of those sites.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">
            7. Compliance Policy:{" "}
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
            A Compliance Policy ensures adherence to relevant laws,
            regulations, and industry standards.
            </li>
            <li>
            It may include procedures for conducting internal audits,
            addressing compliance issues, and maintaining records of compliance
            activities.
            </li>
          </ul>

         
          <h2 className="text-2xl font-semibold mb-2">
            8. Emergency Response and Business Continuity Plan:{" "}
          </h2>


          <ul className="list-disc pl-14 mb-4">
            <li>
            An Emergency Response and Business Continuity Plan outlines
            procedures for responding to emergencies, such as cyber attacks,
            natural disasters, or other disruptions.
            </li>
            <li>
            It includes measures to ensure the continuity of operations and
            minimize the impact on users and stakeholders.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">
            9. Intellectual Property Policy:
          </h2>


          <ul className="list-disc pl-14 mb-4">
            <li>
            An Intellectual Property Policy clarifies ownership rights and
            permissible uses of intellectual property on the website.
            </li>
            <li>
            It may include guidelines for users on respecting copyrights,
            trademarks, and other intellectual property rights.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">
            10. Employee Policies (e.g., IT Acceptable Use Policy,
            Confidentiality Policy):
          </h2>



          <ul className="list-disc pl-14 mb-4">
            <li>
            Internal policies governing employee conduct related to IT usage,
            confidentiality, and handling of sensitive information.
            </li>
            <li>
            These policies ensure that employees understand their
            responsibilities and help mitigate internal risks to data security
            and confidentiality.
            </li>
          </ul>

          <h1 className="text-3xl font-bold mb-4">AI world Asset Policies:</h1>
          <h2 className="text-2xl font-semibold mb-2 pl-4">1. Purpose</h2>
          <p className="pl-8 mb-4">
            1.1. The purpose of this AI World Asset Policy is to establish
            guidelines for the management, use, and protection of assets related
            to artificial intelligence (AI) within RentPrompts.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4"> 2. Scope </h2>
          <p className="pl-8 mb-4">
            2.1. This policy applies to all AI-related assets owned, developed,
            used, or accessed by employees, contractors, consultants, and third
            parties working on behalf of RentPrompts.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4"> 3. Definitions </h2>
          <p className="pl-8 mb-4">
            3.1. AI Assets: Include but are not limited to data sets,
            algorithms, machine learning models, software applications, hardware
            infrastructure, and any related intellectual property.
          </p>
          <p className="pl-8 mb-4">
            3.2. Data: Refers to any information, whether structured or
            unstructured, used as input or output for AI development, training,
            or deployment.
          </p>
          <p className="pl-8 mb-4">
            3.3. Model: Refers to the trained algorithm or set of algorithms
            used for making predictions, classifications, or decisions based on
            input data.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            4. Asset Management{" "}
          </h2>
          <p className="pl-8 mb-4">
            4.1. Ownership: All AI assets developed or acquired by CREX RentPrompts Pvt. Ltd. are the property of User and must be used solely for
            authorized business purposes.
          </p>
          <p className="pl-8 mb-4">
            4.2. Inventory: Maintain an inventory of all AI assets, including
            descriptions, versions, locations, and responsible parties.
          </p>
          <p className="pl-8 mb-4">
            4.3. Documentation: Ensure that documentation for each AI asset
            includes information on its purpose, functionality, usage
            guidelines, and any dependencies.
          </p>
          <p className="pl-8 mb-4">
            4.4. Lifecycle Management: Implement processes for the lifecycle
            management of AI assets, including development, testing, deployment,
            maintenance, and retirement.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            5. Data Management
          </h2>
          <p className="pl-8 mb-4">
            5.1. Data Governance: Adhere to data governance principles to ensure
            the quality, integrity, and security of data used for AI purposes.
          </p>
          <p className="pl-8 mb-4">
            5.2. Data Privacy: Comply with applicable data privacy laws and
            regulations when collecting, storing, processing, and using personal
            data in AI applications.
          </p>
          <p className="pl-8 mb-4">
            5.3. Data Security: Implement measures to protect data
            confidentiality, integrity, and availability throughout its
            lifecycle, including encryption, access controls, and secure data
            storage.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            6. Intellectual Property{" "}
          </h2>
          <p className="pl-8 mb-4">
            6.1. Ownership Rights: Clarify ownership rights for AI assets,
            including data, algorithms, models, and software developed by
            employees or contractors during the course of their employment or
            engagement.
          </p>
          <p className="pl-8 mb-4">
            6.2. Protection: Protect AI-related intellectual property through
            patents, copyrights, trademarks, or other appropriate legal
            mechanisms.
          </p>
          <p className="pl-8 mb-4">
            6.3. Use of Open Source: Manage the use of open-source software and
            ensure compliance with relevant licenses and restrictions.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            7. Security and Compliance{" "}
          </h2>
          <p className="pl-8 mb-4">
            7.1. Security Measures: Implement security controls to protect AI
            assets from unauthorized access, modification, or disclosure.
          </p>
          <p className="pl-8 mb-4">
            7.2. Compliance: Ensure that the development, deployment, and use of
            AI assets comply with applicable laws, regulations, industry
            standards, and organizational policies.
          </p>
          <p className="pl-8 mb-4">
            7.3. Audit and Monitoring: Conduct regular audits and monitoring of
            AI assets to detect and mitigate security risks and compliance
            violations.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            8. Ethical Considerations{" "}
          </h2>
          <p className="pl-8 mb-4">
            8.1. Fairness: Ensure that AI algorithms and models are designed and
            implemented in a fair and unbiased manner, avoiding discrimination
            based on protected characteristics.
          </p>
          <p className="pl-8 mb-4">
            8.2. Transparency: Strive for transparency in AI decision-making
            processes, providing explanations when AI systems are used to make
            significant decisions affecting individuals.
          </p>
          <p className="pl-8 mb-4">
            8.3. Accountability: Hold individuals and teams accountable for the
            ethical use of AI assets, including addressing any unintended
            consequences or ethical dilemmas that arise.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            9. Training and Awareness{" "}
          </h2>
          <p className="pl-8 mb-4">
            9.1. Education: Provide training and awareness programs for
            employees involved in AI development, deployment, or use, covering
            ethical considerations, data privacy, security best practices, and
            compliance requirements.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            10. Roles and Responsibilities{" "}
          </h2>
          <p className="pl-8 mb-4">
            10.1. Executive Sponsorship: Designate an executive sponsor
            responsible for overseeing the implementation and adherence to this
            AI World Asset Policy.
          </p>
          <p className="pl-8 mb-4">
            10.2. Roles: Define roles and responsibilities for individuals
            involved in managing, using, or overseeing AI assets, including data
            stewards, AI developers, data scientists, and legal and compliance
            personnel.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            11. Policy Review and Updates{" "}
          </h2>
          <p className="pl-8 mb-4">
            11.1. Review: Regularly review and update this AI World Asset Policy
            to reflect changes in technology, business operations, legal and
            regulatory requirements, and emerging best practices.
          </p>
          <h2 className="text-2xl font-semibold mb-2 pl-4">
            {" "}
            12. Contact Information{" "}
          </h2>
          <p className="pl-8 mb-4">
            12.1. For questions or concerns regarding this AI World Asset
            Policy, please contact:
          </p>
          <ul className="list-disc pl-14 mb-4">
          <li>CREX RentPrompts Pvt. Ltd.</li>
            <li>Email : <EMAIL></li>
            <li>Contact No : +************</li>
            <li>Address : VF-29, Treasure Town, IDA State, Indore, MP, India - 452012</li>
          </ul>
          <p className="pl-4 mb-6">
            This AI World Asset Policy was last updated on 16/08/2024.
          </p>
          <h2 className="text-3xl font-semibold mb-2"> Disclaimer </h2>
          <p className="pl-4 mb-4">
            The information provided by CREX RentPrompts Pvt. Ltd. (&quot;the
            Company&quot;) on this website is for general informational purposes
            only. All information on the website is provided in good faith;
            however, we make no representation or warranty of any kind, express
            or implied, regarding the accuracy, adequacy, validity, reliability,
            availability, or completeness of any information on the website.
          </p>
        </div>
      </div>
    </>
  );
};
export default page;
