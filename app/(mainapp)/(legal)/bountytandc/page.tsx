import Loader from "@/components/Loader";

const page = () => {
  return (
    <>
      <Loader />
      <div className="p-4 md:py-8 md:mt-10">
        <div className="max-w-4xl mx-auto py-6">
          <h1 className="text-3xl font-bold mb-5">
            RentPrompts Bounty Program
          </h1>
          <h2 className="text-2xl font-semibold mb-2">Overview:</h2>
          <p className="pl-4 mb-6">
            The RentPrompts Bounty Program is designed to incentivize community
            members to contribute to the development of AI, ML, Gen AI, and
            Software Development projects. Both users and RentPrompts can create
            bounties with specific rewards. Community members are encouraged to
            apply for these bounties, complete the tasks, and claim their
            rewards. RentPrompts manages the process, ensuring fair resolution
            and distribution of rewards.
          </p>
          <h1 className="text-2xl font-semibold mb-2 mt-2">
            Bounty Program Structure:
          </h1>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            1. Bounty Creation:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>Any registered user or RentPrompts can create a bounty.</li>
            <li>Bounties must include.</li>
            <li>A detailed description of the task.</li>
            <li>Clear requirements and deliverables.</li>
            <li>Acceptance criteria.</li>
            <li>Expected timeline and deadline.</li>
            <li>Reward amount and any milestones if applicable.</li>
            <li>
              Bounties are published on the RentPrompts platform and visible to
              all community members.
            </li>
            <li>
              RentPrompts reserves the right to approve or reject any bounty for
              quality control.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            2. Applying for a Bounty:
          </h2>
          <ul className="list-disc pl-14 mb-4">
            <li>Only registered community members can apply for bounties.</li>
            <li>Applicants must submit</li>
            <li>
              A brief proposal outlining their approach to completing the
              bounty.
            </li>
            <li>Any relevant experience or portfolios.</li>
            <li>An estimated timeline for completion.</li>
            <li>
              RentPrompts or the bounty creator reviews applications and selects
              a suitable candidate based on merit and feasibility.
            </li>
            <li>
              Selected candidates are notified through the RentPrompts platform.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            3. Bounty Fulfillment:
          </h2>
          <ul className="list-disc pl-14 mb-4">
            <li>
              The selected candidate is responsible for completing the task as
              per the provided specifications and within the agreed timeline.
            </li>
            <li>
              Regular progress updates should be submitted to the bounty creator
              or RentPrompts for transparency.
            </li>
            <li>
              Communication tools within the RentPrompts platform must be used
              for all bounty-related correspondence.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            4. Delivery and Acceptance:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Once the task is completed, the deliverables are submitted through
              the RentPrompts platform for review.
            </li>
            <li>
              The bounty creator or RentPrompts reviews the submission against
              the initial criteria within a specified period (e.g., 7 days).
            </li>
            <li>
              If the deliverables meet the requirements, acceptance is
              confirmed. If not, feedback is provided and revisions may be
              required.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            5. Rewards and Payouts:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Upon acceptance of the deliverables, RentPrompts releases the
              reward to the bounty hunter.
            </li>
            <li>
              Payments are processed through the RentPrompts payment system
              within a scheduled timeframe (e.g., within 7 days of acceptance).
            </li>
            <li>
              If the bounty has milestones, partial payments may be released
              upon completion of each milestone, as defined in the bounty.
            </li>
          </ul>

          <h1 className="text-2xl font-semibold mb-2">Terms and Conditions:</h1>

          <h2 className="text-xl font-semibold mb-2 pl-4">1. General Terms:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              The RentPrompts Bounty Program is open to registered community
              members only.
            </li>
            <li>
              Users must provide accurate information during registration and
              maintain an active profile.
            </li>
            <li>
              RentPrompts reserves the right to modify the bounty program terms
              and conditions at any time, with notice provided to the community.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            2. Bounty Creation:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Bounties must be clearly described with specific, measurable,
              achievable, relevant, and time-bound (SMART) criteria.
            </li>
            <li>
              RentPrompts holds the reward amount in escrow until the bounty is
              resolved to ensure fairness and security.
            </li>
            <li>
              Bounties must not contain any offensive, illegal, or harmful
              content.
            </li>
            <li>Once bounty is created and any user applies for bounty, user will not be able to delete that bounty .
            </li>
            <li>User has to request admin for bounty deletion and admin Decision will be final.</li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            3. Application Process:
          </h2>
          <ul className="list-disc pl-14 mb-4">
            <li>
              Applicants must submit a proposal through the RentPrompts
              platform.
            </li>
            <li>
              Proposals must be original, detailed, and demonstrate a clear
              understanding of the task.
            </li>
            <li>
              RentPrompts or the bounty creator will select the candidate based
              on qualifications, approach, and feasibility.
            </li>
          </ul>
          <h2 className="text-xl font-semibold mb-2 pl-4">
            4. Conflict Resolution:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              In the event of a dispute, RentPrompts will act as the unbiased
              mediator and resolver.
            </li>
            <li>
              Both parties must provide evidence and documentation to support
              their claims.
            </li>
            <li>
              RentPrompts’ decision will be final and binding to ensure timely
              and fair resolution.
            </li>
            <li>
              RentPrompts encourages amicable resolution between parties before
              escalating to mediation.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            5. Submission and Review:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Deliverables must be submitted on or before the specified
              deadline.
            </li>
            <li>
              RentPrompts or the bounty creator will review the submission
              within a set period (e.g., 7 days).
            </li>
            <li>
              If revisions are required, the bounty hunter must address them
              within the specified timeframe. Multiple rounds of revisions may
              be managed to ensure quality.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            6. Rewards and Payments:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Rewards are released only after the deliverables are accepted by
              the bounty creator or RentPrompts.
            </li>
            <li>
              Payments are processed through the RentPrompts platform and paid
              out within a specified period (e.g., 7 days after acceptance).
            </li>
            <li>
              RentPrompts holds the right to withhold payments if terms and
              conditions are breached or if there is evidence of fraudulent
              activity.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            7. Termination and Suspension:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              RentPrompts reserves the right to terminate or suspend user
              accounts for breach of terms and conditions, including but not
              limited to inappropriate behavior, failure to deliver, or
              fraudulent activities.
            </li>
            <li>
              Users may terminate their participation in the bounty program by
              providing written notice to RentPrompts. Outstanding tasks should
              be completed or transferred.
            </li>
          </ul>

          <h1 className="text-2xl font-semibold mb-2">Additional Policies:</h1>

          <h2 className="text-xl font-semibold mb-2 pl-4">1. Communication:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              All communication regarding bounties must be conducted through the
              RentPrompts platform to ensure transparency and accountability.
            </li>
            <li>
              Users are expected to maintain professionalism, courtesy, and
              respect in their interactions. Harassment, abuse, or
              discriminatory behavior will not be tolerated.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            2. Confidentiality:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Users must respect the confidentiality of the information shared
              during the bounty process.
            </li>
            <li>
              Any breach of confidentiality may result in termination from the
              program and forfeiture of rewards. Confidential information
              includes, but is not limited to, proprietary information, secrets,
              and personal data.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            3. Intellectual Property:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              By participating in the bounty program, users agree to transfer
              any intellectual property created for the bounty to the bounty
              creator or RentPrompts as applicable.
            </li>
            <li>
              Proper attribution will be given to the creators where necessary.
              Licensing terms must be clearly stated if applicable.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">4. Liability:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              RentPrompts is not liable for any loss or damage arising from
              participation in the bounty program.
            </li>
            <li>
              Users participate at their own risk and are responsible for
              ensuring their compliance with local laws and regulations.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2 pl-4">
            5. Feedback and Reviews:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Both bounty creators and hunters are encouraged to provide
              feedback and reviews post-completion to help maintain the quality
              and integrity of the program.
            </li>
            <li>
              Constructive feedback mechanism will help improve the bounty
              process and user satisfaction.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">Final Notes:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Participation in the RentPrompts Bounty Program is a privilege and
              not a right.
            </li>
            <li>
              Adherence to these terms and conditions ensures a fair and
              productive environment for all participants.
            </li>
            <li>
              For any questions, support, or conflict resolution requests, users
              are encouraged to contact RentPrompts support team via the
              platform.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">
            {" "}
            Contact Information{" "}
          </h2>
          <p className="pl-8 mb-4">
            For questions or concerns regarding Bounty
            Policy, please contact:
          </p>
          <ul className="list-disc pl-14 mb-4">
          <li>CREX RentPrompts Pvt. Ltd.</li>
            <li>Email : <EMAIL></li>
            <li>Contact No : +************</li>
            <li>Address : VF-29, Treasure Town, IDA State, Indore, MP, India - 452012</li>
          </ul>

          <p className="pl-4 mb-4">
            By adhering to these guidelines, RentPrompts aims to foster an
            engaging and collaborative community while promoting the growth and
            development of AI, ML, Gen AI, and software development projects.
            Our goal is to ensure that all participants have a rewarding and
            enriching experience.
          </p>
        </div>
      </div>
    </>
  );
};
export default page;
