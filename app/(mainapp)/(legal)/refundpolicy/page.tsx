import Loader from "@/components/Loader";

const page = () => {
  return (
    <>
      <Loader />
      <div className="py-8 px-4 md:mt-10">
        <div className="max-w-4xl mx-auto py-6">
          <h1 className="text-3xl font-bold mb-4">
            Refund Policy for Credits
          </h1>
          <h2 className="text-2xl font-semibold mb-2">Policy Overview:</h2>
          <p className="pl-4 mb-4">
            This Money Refund Policy outlines the terms and conditions under
            which refunds will be processed for users of our AI services. Our
            goal is to ensure transparency and clarity regarding our refund
            practices.
          </p>
          <h2 className="text-xl font-semibold mb-2">1. No Money Refunds:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              Under any circumstances, money will not be credited. All sales are
              final. 
            </li>
            <li>
              User can always request for payout for the outstanding credits.
            </li>
            <li>
               Payout feature is available under user profile section. 
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2">2. Bounty Refunds:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              If the work associated with a bounty is not completed 100%, the
              credits used for the bounty will be refunded to the user&apos;s RentPrompts account.
            </li>
            <li>If user requests for payout, money will be credited to user&apos;s bank account within 5-7 working days.</li>
            <li>
               Payout feature is available under user profile section. 
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2">
            3. Prompt Purchase Refunds:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              If a user purchases a prompt and it does not work as intended, the
              credits used for the purchase will be refunded to the user&apos;s RentPrompts
              account within 24 hours.
            </li>
            <li>If user requests for payout, money will be credited to user&apos;s bank account within 5-7 working days.</li>
            <li>
               Payout feature is available under user profile section. 
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2">4. Refund Process:</h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              To request a refund of credits, users must contact our support <NAME_EMAIL>
              with details of the issue and the associated transaction.
            </li>
            <li>
              The support team will review the request and process the credits refund to user&apos;s RentPrompts
              account within 24 hours
              , if it meets the criteria outlined in this policy.
            </li>
            <li>If user requests for payout, money will be credited to user&apos;s bank account within 5-7 working days.</li>
            <li>
               Payout feature is available under user profile section. 
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2">
            5. Finality of Credits Refunds:{" "}
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>Credits will be refunded to user&apos;s RentPrompts account within 24 hours.</li>
            <li>
              Once credits are refunded, the transaction is considered final and
              cannot be reversed.
            </li>
            <li>
              Users are encouraged to use the credits for other
              transactions within the AI services platform.
            </li>
            <li>If user requests for payout, money will be credited to user&apos;s bank account within 5-7 working days.</li>
            <li>
               Payout feature is available under user profile section. 
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2">
            6. Payment Stuck Due to Technical Glitch:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
              If the payment is stuck due to a technical glitch and the money is
              credited to our bank account, credits will be refunded to user&apos;s RentPrompts
              account within 24 hours. 
            </li>
            <li>If user requests for payout, money will be credited to user&apos;s bank account within 5-7 working days.</li>
            <li>
               Payout feature is available under user profile section. 
            </li>
            <li>
              {" "}
              If the payment is stuck due to a technical glitch and the money is
              not credited to our bank account, users should contact our support
              <NAME_EMAIL> for assistance.
            </li>
          </ul>

          <h2 className="text-xl font-semibold mb-2">
            7. Payout Request:
          </h2>

          <ul className="list-disc pl-14 mb-4">
            <li>
            User can request for payout with minimum 500 credits.

            </li>
            <li>
            Payout request will be processed in 5 to 7 working days and money will be credited to users bank account.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mb-2">Contact Information:</h2>
          <p className="pl-4 mb-6">
          For questions or concerns regarding this policy, or to requests a refund of credits and money, please contact our support <NAME_EMAIL>
          </p>
          
          <ul className="list-disc pl-14 mb-4">
          <li>CREX RentPrompts Pvt. Ltd.</li>
            <li>Email : <EMAIL></li>
            <li>Contact No : +************</li>
            <li>Address : VF-29, Treasure Town, IDA State, Indore, MP, India - 452012</li>
          </ul>
          <h2 className="text-2xl font-semibold mb-2">Effective Date:</h2>
          <p className="pl-4 mb-4">
          This policy is effective as of 16/08/2024.
          </p>
          <p className="font-bold">These are Digital assets and Products and will not be physically Delivered .
          Hence no Product refund Available.</p>
        </div>
      </div>
    </>
  );
};
export default page;
