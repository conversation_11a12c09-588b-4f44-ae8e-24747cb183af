// file name - route.ts
import { getPayloadClient } from '@/server/get-payload';
import { NextResponse } from 'next/server';
import slugify from 'slugify'; // Slugify library import

// Type definition for collection config
type CollectionConfig = {
  searchFields: string[];
  approvalField?: string;
  approvalValue?: string;
  titleField: string;
  imageField: string | null;
  descriptionField: string;
  requiresApproval: boolean;
  type: string;
  uiTypeLabel: string;
};

// COLLECTION_CONFIG with uiTypeLabel for display purposes
const COLLECTION_CONFIG: Record<string, CollectionConfig> = {
  rapps: {
    searchFields: ['name', 'description', 'modelType'],
    approvalField: 'status',
    approvalValue: 'approved',
    titleField: 'name',
    imageField: 'images',            // Corrected
    descriptionField: 'description', // Corrected
    requiresApproval: true,
    type: 'ai-apps',                 // For URL path: /ai-apps/:slug
    uiTypeLabel: 'AI-App'            // For UI display in suggestions
  },
  models: {
    searchFields: ['name', 'description', 'type', 'provider'],
    titleField: 'name',
    imageField: 'modelImage',        // Corrected
    descriptionField: 'description', // Corrected
    requiresApproval: false,         // Corrected (Models.ts mein status field nahi hai)
    type: 'generate',                // For URL path & part of combined display for models
    uiTypeLabel: 'Model'             // Base label, actual display for models will be like "generate-text"
  },
  blogs: {
    searchFields: ['title', 'content'],
    approvalField: 'status',
    approvalValue: 'approved',
    titleField: 'title',
    imageField: 'images',            // Corrected
    descriptionField: 'content',     // Corrected
    requiresApproval: true,
    type: 'blog',                    // For URL path: /blog/:slug
    uiTypeLabel: 'Blog'           // For UI display
  },
  products: {
    searchFields: ['name', 'description'],
    approvalField: 'approvedForSale',
    approvalValue: 'approved',
    titleField: 'name',
    imageField: 'images',            // Corrected
    descriptionField: 'description', // Corrected
    requiresApproval: true,
    type: 'product',                 // For URL path: /product/:slug
    uiTypeLabel: 'Product'           // For UI display
  },
  bounties: {
    searchFields: ['title', 'content'],
    approvalField: 'status',
    approvalValue: 'approved',
    titleField: 'title',
    imageField: null,                // Bounties.ts mein image field nahi mila
    descriptionField: 'content',
    requiresApproval: true,
    type: 'bounties',                // For URL path: /bounties/:slug
    uiTypeLabel: 'Bounty'            // For UI display
  },
  users: { // Key 'users' (lowercase)
    searchFields: ['user_name', 'email', 'genInfo.profession'],
    titleField: 'user_name',
    imageField: 'profileImage',
    descriptionField: 'genInfo.profession',
    requiresApproval: false,
    type: 'users',                   // For URL path: /users/:slug
    uiTypeLabel: 'User'              // For UI display
  },
  // courses: {
  //   searchFields: ['title', 'description'],
  //   approvalField: 'status',
  //   approvalValue: 'approved',
  //   titleField: 'title',
  //   imageField: 'courseThumbnail',
  //   descriptionField: 'shortSummary',
  //   requiresApproval: true,
  //   type: 'courses',
  //   uiTypeLabel: 'Course'
  // },
};

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('q');

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ results: [] });
    }

    const payload = await getPayloadClient();
    const collectionsToSearch = Object.entries(COLLECTION_CONFIG);

    const searchPromises = collectionsToSearch.map(async ([collectionName, config]) => {

      const whereConditions: any[] = [];
      whereConditions.push({
        or: config.searchFields.map(field => ({
          [field]: { contains: query }
        }))
      });
      if (config.requiresApproval) {
         if (config.approvalField && config.approvalValue) {
             whereConditions.push({
               [config.approvalField]: { equals: config.approvalValue }
             });
         } else {
              console.warn(`Approval required but configuration missing/incomplete for collection: ${collectionName}`);
         }
      }
      const whereClause = { and: whereConditions };

      try {
        const { docs } = await payload.find({
          collection: collectionName as any,
          where: whereClause,
          limit: 5,
          depth: 1, // Depth 1 for populating direct relations like images
        });

        return docs.map((doc: any) => {
          let imageUrl: string | undefined = undefined;
          const imageFieldName = config.imageField;

          // --- Image URL Extraction Logic ---
          if (imageFieldName && doc[imageFieldName]) {
            const imageFieldData = doc[imageFieldName];
            if ((collectionName === 'rapps' && imageFieldName === 'images' ||
                 collectionName === 'models' && imageFieldName === 'modelImage' ||
                 collectionName === 'blogs' && imageFieldName === 'images' ||
                 collectionName === 'products' && imageFieldName === 'images')
                && Array.isArray(imageFieldData) && imageFieldData.length > 0)
            {
                const firstImageObject = imageFieldData[0];
                if (firstImageObject?.image && typeof firstImageObject.image === 'object' && firstImageObject.image?.url) {
                    imageUrl = firstImageObject.image.url;
                }
            }
            else if (typeof imageFieldData === 'object' && imageFieldData !== null && imageFieldData?.url) {
                imageUrl = imageFieldData.url;
            }
            else if (typeof imageFieldData === 'string' && imageFieldData.startsWith('http')) {
                 imageUrl = imageFieldData;
            }
          }

          // --- Description Extraction Logic ---
          let description: string | undefined = undefined;
          if (config.descriptionField) {
              const descriptionParts = config.descriptionField.split('.');
              let currentDesc: any = doc;
              for (const part of descriptionParts) {
                  if (currentDesc && typeof currentDesc === 'object' && part in currentDesc) {
                      currentDesc = currentDesc[part];
                  } else {
                      currentDesc = undefined;
                      break;
                  }
              }
              if (typeof currentDesc === 'string') {
                 description = currentDesc;
              }
          }

          // --- Title Extraction Logic ---
          let title: string = 'Untitled';
          if (config.titleField) {
              const titleParts = config.titleField.split('.');
              let currentTitle: any = doc;
              for (const part of titleParts) {
                if (currentTitle && typeof currentTitle === 'object' && part in currentTitle) {
                  currentTitle = currentTitle[part];
                } else {
                  currentTitle = undefined;
                  break;
                }
              }
              if (typeof currentTitle === 'string') {
                title = currentTitle;
              }
          }

          // --- Slug Logic for URL ---
         let slugForUrl: string;
          if (collectionName === 'users') {
             
              if (typeof doc.user_name === 'string' && doc.user_name.trim() !== '') {
                  slugForUrl = doc.user_name.trim(); // Direct use, case aur characters preserve honge
              } else if (typeof doc.slug === 'string' && doc.slug.trim() !== '') {
                  // Agar user_name nahi hai, toh slug use karein (agar hai)
                  slugForUrl = doc.slug.trim();
              } else {
                  slugForUrl = doc.id; // Fallback to ID
                  console.warn(`User (ID: ${doc.id}) missing user_name and slug for URL. Using ID.`);
              }
          } else {
              
              slugForUrl = (typeof doc.slug === 'string' && doc.slug.trim() !== '') ? doc.slug.trim() : doc.id;
          }

          // --- URL and Path Type Logic ---
         // --- URL and Path Type Logic ---
          const pathTypeForUrl = config.type || collectionName; // e.g., 'generate' for models
          let url: string;
          const docModelSpecificTypeRaw = (collectionName === 'models' && typeof doc.type === 'string' && doc.type.trim() !== '') ? doc.type.trim() : undefined;

          // ***** MODIFIED URL LOGIC FOR MODELS *****
          if (collectionName === 'models' && docModelSpecificTypeRaw) {
              let modelTypePathSegment = docModelSpecificTypeRaw.toLowerCase();

              // Agar actual model type 'vision' hai, toh URL path ke liye use 'text' maanein
              if (modelTypePathSegment === 'vision') {
                  modelTypePathSegment = 'text';
              }

              // Ab URL banayein (potentially overridden) modelTypePathSegment ke saath
              if (modelTypePathSegment === 'text') { // Yeh ab original 'text' aur 'vision' (jo 'text' ban gaya) dono ko cover karega
                  // 'text' (aur 'vision') models ke liye URL hoga /config.type/text (bina slug ke)
                  url = `/${pathTypeForUrl}/${modelTypePathSegment}`; // Example: /generate/text
              } else {
                  // Baaki models (audio, image, video etc.) ke liye URL /config.type/:actualModelType/:slug hoga
                  url = `/${pathTypeForUrl}/${modelTypePathSegment}/${slugForUrl}`; // Example: /generate/audio/some-slug
              }
          } else {
              // Baaki sabhi collections ke liye URL pehle jaisa (/config.type/:slug)
              url = `/${pathTypeForUrl}/${slugForUrl}`;
          }
          // ***** END OF MODIFIED URL LOGIC *****
          // --- UI Display Type Logic ---
          let uiDisplayTypeForSuggestion: string;
          if (collectionName === 'models' && docModelSpecificTypeRaw) {
              // For models, combine pathTypeForUrl (e.g., 'generate') and docModelSpecificType (e.g., 'text')
              uiDisplayTypeForSuggestion = `${pathTypeForUrl}-${docModelSpecificTypeRaw.toLowerCase()}`; // e.g., "generate-text"
          } else {
              // For other collections, use the uiTypeLabel from config, or fallback to pathTypeForUrl
              uiDisplayTypeForSuggestion = config.uiTypeLabel || pathTypeForUrl;
          }

          return {
            id: doc.id,
            title: title,
            slug: slugForUrl,
            type: pathTypeForUrl, // Type used for URL path & general backend categorization
            url: url,
            image: imageUrl,
            description: description,
            specificType: docModelSpecificTypeRaw, // Raw specific type from 'models' documents (e.g., "Text", "Image")
            uiDisplayType: uiDisplayTypeForSuggestion, // For UI display in suggestions (e.g., "AI App", "generate-text")
          };
        });
      } catch (e) {
        console.error(`Error searching in collection '${collectionName}' with query '${query}':`, e);
        return [];
      }
    });

    const resultsArrays = await Promise.all(searchPromises);
    const combinedResults = resultsArrays.flat();

    return NextResponse.json({ results: combinedResults });

  } catch (err) {
    console.error('Global search error in GET /api/search:', err);
    return NextResponse.json(
      { error: 'Internal server error during search execution' },
      { status: 500 }
    );
  }
}
