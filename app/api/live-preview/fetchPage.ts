import axios from 'axios'

export const fetchPage = async (slug: string, id: string) => {
  // backend url
  try {
    const res = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/${slug}/${id}?depth=1`)
    const pageRes = res.data
    return pageRes
  } catch (e: any) {
    console.log('Fetch Page error', e.message)
  }
}
// `${process.env.NEXT_PUBLIC_PAYLOAD_URL}/api/pages?where[slug][equals]=${slug}&depth=2`,
