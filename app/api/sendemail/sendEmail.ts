import nodemailer from "nodemailer";

export default async function handler(req, res) {
  if (req.method !== "POST") return res.status(405).json({ message: "Method Not Allowed" });

  const { to, subject, text } = req.body;

  try {
    // Configure the SMTP transporter
    const transporter = nodemailer.createTransport({
      service: "Gmail", // You can use SendGrid, Mailgun, AWS SES, etc.
      auth: {
        user: process.env.EMAIL_USER, // Your email (set in .env)
        pass: process.env.EMAIL_PASS, // Your email password or app password
      },
    });

    // Send the email
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to,
      subject,
      text,
    });

    return res.status(200).json({ message: "Email sent successfully" });
  } catch (error) {
    console.error("Email sending error:", error);
    return res.status(500).json({ message: "Failed to send email", error });
  }
}
