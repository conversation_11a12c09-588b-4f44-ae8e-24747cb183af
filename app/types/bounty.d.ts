interface Bounty {
  id: string;
  estimatedPrice: string;
  rupees_price: string;
  due_date: string;
  status: string;
  title: string;
  content: string;
  creator: string;
  createdAt: string;
  No_of_applicants: string;
  image: string;
  completionDate: string;
  applyExpireDate:string;
  applicantsLimit: number;
  bountyType: string;
  tags: string[];
}
interface FormData {
  title: string;
  content: string;
  completionDate: string;
  applyExpireDate:string;
  estimatedPrice: string;
  bountyType: string;
  user: string;
  images: FileList;
  product_files: FileList;
  applicantsLimit: number;
  tags: string[];
  termsAccepted: Boolean;
}

type User = {
  id: string;
  user: User | null;
  bounties: string[];
  coinBalance: number;
};
