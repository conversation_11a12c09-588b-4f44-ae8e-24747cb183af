"use client";

import { useEffect, useState } from "react";

export interface Notification {
  id: string;
  message: string;
  link?: string;
  read: boolean;
  createdAt: string;
}

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [userId, setUserId] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [readCount, setReadCount] = useState(0);
  const [filter, setFilter] = useState<"all" | "read" | "unread">("all");
  const fetchUserData = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`
      );
      const data = await response.json();
      setUserId(data.data.id);
    } catch (err) {
      console.error("Error fetching user data:", err);
    }
  };

  // const fetchNotifications = async () => {
  //   try {
  //     const res = await fetch(
  //       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/notifications/my-notifications`
  //     );
  //     const data = await res.json();
  //     const docs = Array.isArray(data?.docs) ? data.docs : [];
  //     setNotifications(docs);
  //     setUnreadCount(docs.filter((n) => !n.read).length);
  //   } catch (err) {
  //     console.error("Error fetching notifications:", err);
  //   }
  // };
  const fetchNotifications = async (page = 1, selectedFilter = filter) => {
    if (!userId) return;

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/notifications/my-notifications?page=${page}&limit=20&filter=${filter}`,
        { credentials: "include" }
      );
      if (!res.ok) throw new Error("Notification fetch failed");

      const data = await res.json();
      const docs = Array.isArray(data?.docs) ? data.docs : [];

      setNotifications(docs);
      setUnreadCount(data.unreadCount?.totalDocs || 0);
      setTotalPages(data.totalPages || 1);
      setCurrentPage(data.page || 1);
      setReadCount(data.readCount?.totalDocs || 0)
    } catch (err) {
      console.error("❌ Error fetching notifications:", err);
    }
  };

  
  const markAsRead = async (id: string) => {
    try {
      await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/notifications/markRead/${id}`,
        { method: "PATCH", credentials: "include" }
      );
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, read: true } : n))
      );
      setUnreadCount((prev) => Math.max(prev - 1, 0));
    } catch (err) {
      console.error("Error marking as read:", err);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  const changePage = (newPage: number) => {
    setCurrentPage(newPage);
    fetchNotifications(newPage, filter);
  };

  const changeFilter = (tab: "all" | "unread" | "read") => {
    setFilter(tab);
    setCurrentPage(1);
  };

  useEffect(() => {
    if (userId) fetchNotifications(1, filter);
  }, [userId, filter, currentPage]);

  return {
    notifications,
    unreadCount,
    markAsRead,
    totalPages,
    currentPage,
    userId,
    filter,
    changePage,
    changeFilter,
    readCount,
    setUnreadCount,
  };
};
