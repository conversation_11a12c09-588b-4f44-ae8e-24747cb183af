import { getPayloadClient } from "@/server/get-payload";
import type { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const url = "https://rentprompts.com";

  const payload = await getPayloadClient();

  const blogs: any = await payload.find({
    collection: "blogs",
    limit: 0,
    where: {
      status: {
        equals: "approved",
      },
    },
  });
  // console.log("blogs sitemap", blogs);
  const getBlogsDetailsUrl = blogs?.docs?.map((blogs: any) => {
    return {
      // url: `http://localhost:3000/blog/slug?id=${blogs.id}`,

      url: `${url}/blog/${blogs.slug}`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    };
  });

  const rapps: any = await payload.find({
    collection: "rapps",
    limit: 0,
    where: {
      status: {
        equals: "approved",
      },
    },
  });
  // console.log("rapps sitemap", rapps);
  const getRappsDetailsUrl = rapps?.docs?.map((rapps: any) => {
    return {
      url: `${url}/ai-apps/${rapps.slug}`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    };
  });

  // const models: any = await payload.find({
  //   collection: "models",
  //   limit: 0,
  // });
  // console.log("models sitemap", models);
  // const getAllModel = models?.docs?.map((modelsData: any) => {
  //   return {
  //     url: `${url}/generate/${modelsData.type}`,
  //     lastModified: new Date(),
  //     changeFrequency: "weekly",
  //     priority: 0.5,
  //   };
  // });

  const bounties: any = await payload.find({
    collection: "bounties",
    limit: 0,
    where: {
      status: {
        equals: "approved",
      },
    },
  });
  // console.log("bounties sitemap", bounties);
  const getBountiessDetailsUrl = bounties?.docs?.map((bountiesData: any) => {
    return {
      url: `${url}/bounties/${bountiesData.slug}`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    };
  });

  const products: any = await payload.find({
    collection: "products",
    limit: 0,
    where: {
      approvedForSale: {
        equals: "approved",
      },
    },
  });
  // console.log("products", products);
  const getProductsDetailsUrl = products?.docs?.map((productsData: any) => {
    return {
      url: `${url}/product/${productsData.slug}`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    };
  });

  // const publicProfile: any = await payload.find({
  //   collection: "users",
  //   limit: 0,
    
  // });
  // const getPublicProfileDetails = publicProfile?.docs?.map((PublicProfile: any) => {
  //   return {
  //     url: `${url}/users/${PublicProfile.user_name}`,
  //     lastModified: new Date(),
  //     changeFrequency: "weekly",
  //     priority: 0.5,
  //   };
  // });

  return [
    {
      url: url,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 1,
    },
    {
      url: `${url}/community`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/bounties`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    ...getBountiessDetailsUrl,
    {
      url: `${url}/ai-apps`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    ...getRappsDetailsUrl,
    {
      url: `${url}/marketplace`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/image`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/audio`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/text`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/video`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/academy`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/pricing`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/blog`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    ...getBlogsDetailsUrl,
    ...getProductsDetailsUrl,
    // ...getPublicProfileDetails,
  ];
}
