import { getPayloadClient } from "@/server/get-payload";
import type { MetadataRoute } from "next";

// Import Payload-generated types (adjust path based on your setup)
import type { Blog, Rapp, Bounty, Product } from "@/server/payload-types";

// Force dynamic rendering to avoid static prerendering issues
export const dynamic = "force-dynamic";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const url = "https://rentprompts.com";

  let blogsDetails: MetadataRoute.Sitemap = [];
  let rappsDetails: MetadataRoute.Sitemap = [];
  let bountiesDetails: MetadataRoute.Sitemap = [];
  let productsDetails: MetadataRoute.Sitemap = [];

  try {
    const payload = await getPayloadClient();

    // Fetch blogs
    const blogs = await payload.find({
      collection: "blogs",
      limit: 0,
      where: {
        status: { equals: "approved" },
      },
    });
    blogsDetails = blogs?.docs
      ?.filter((doc: Blog) => typeof doc.slug === "string")
      .map((doc: Blog) => ({
        url: `${url}/blog/${encodeURIComponent(doc.slug)}`,
        lastModified: doc.updatedAt ? new Date(doc.updatedAt) : new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.5,
      })) || [];

    // Fetch rapps
    const rapps = await payload.find({
      collection: "rapps",
      limit: 0,
      where: {
        status: { equals: "approved" },
      },
    });
    rappsDetails = rapps?.docs
      ?.filter((doc: Rapp) => typeof doc.slug === "string")
      .map((doc: Rapp) => ({
        url: `${url}/ai-apps/${encodeURIComponent(doc.slug)}`,
        lastModified: doc.updatedAt ? new Date(doc.updatedAt) : new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.5,
      })) || [];

    // Fetch bounties
    const bounties = await payload.find({
      collection: "bounties",
      limit: 0,
      where: {
        status: { equals: "approved" },
      },
    });
    bountiesDetails = bounties?.docs
      ?.filter((doc: Bounty) => typeof doc.slug === "string")
      .map((doc: Bounty) => ({
        url: `${url}/bounties/${encodeURIComponent(doc.slug)}`,
        lastModified: doc.updatedAt ? new Date(doc.updatedAt) : new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.5,
      })) || [];

    // Fetch products
    const products = await payload.find({
      collection: "products",
      limit: 0,
      where: {
        approvedForSale: { equals: "approved" },
      },
    });
    productsDetails = products?.docs
      ?.filter((doc: Product) => typeof doc.slug === "string")
      .map((doc: Product) => ({
        url: `${url}/product/${encodeURIComponent(doc.slug)}`,
        lastModified: doc.updatedAt ? new Date(doc.updatedAt) : new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.5,
      })) || [];
  } catch (error) {
    console.error("Sitemap generation error:", error);
    // Return minimal sitemap on error
    return [
      {
        url: url,
        lastModified: new Date(),
        changeFrequency: "yearly",
        priority: 1,
      },
    ];
  }

  return [
    {
      url: url,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 1,
    },
    {
      url: `${url}/community`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/bounties`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    ...bountiesDetails,
    {
      url: `${url}/ai-apps`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    ...rappsDetails,
    {
      url: `${url}/marketplace`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/image`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/audio`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/text`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/generate/video`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/academy`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/pricing`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    {
      url: `${url}/blog`,
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.5,
    },
    ...blogsDetails,
    ...productsDetails,
  ];
}