"use client";
import { ImagesSlider } from "@/components/ui/image-slider";
import { motion } from "framer-motion";
import React from "react";
import { ChevronDown } from "lucide-react";

import {
  IconArrowWaveRightUp,
  IconBoxAlignRightFilled,
  IconBoxAlignTopLeft,
  IconClipboardCopy,
  IconFileBroken,
  IconSignature,
  IconTableColumn,
} from "@tabler/icons-react";
import { EventCards, EventCardItems } from "@/components/EventCards";


const ImagesSliderDemo = () => {
  const images = [
    "/img/event1.jpg",
    "/img/event2.jpg",
    "/img/event3.jpg",
    "/img/event4.jpg",
    "/img/event5.jpg",
    "/img/event6.jpg",
  ];

  const Skeleton = ({ url }: any) => (
    <div className="flex w-full h-full object-cover min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-200 to-neutral-100">
      <img
        src={url}
        alt="events"
        className="rounded-xl w-full h-52 object-cover"
         loading="lazy"
      />
    </div>
  );
  
  const items = [
    {
      title: "The Dawn of Innovation",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/Event1.jpeg" />,
      icon: <IconClipboardCopy className="h-4 w-4 text-white" />,
      location: "New Delhi, IN",
      eventDate: "31 May 2024, 10:30 AM",
    },
    {
      title: "The Digital Revolution",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/event_image2.jpg" />,
      icon: <IconFileBroken className="h-4 w-4 text-white" />,
      location: "Banglore, IN",
      eventDate: "04 June 2024, 12:30 PM",
    },
    {
      title: "Cybersecurity World Expo",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/techevent6.jpg" />,
      icon: <IconSignature className="h-4 w-4 text-white" />,
      location: "Hydrabad, IN",
      eventDate: "10 June 2024, 4:30 PM",
    },
    {
      title: "Future of Blockchain Conference",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/techevent3.jpg" />,
      icon: <IconTableColumn className="h-4 w-4 text-white" />,
      location: "New York, UK",
      eventDate: "20 June 2024, 10:30 AM",
    },
    {
      title: "Tech Startups Showcase",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/techevent4.jpg" />,
      icon: <IconArrowWaveRightUp className="h-4 w-4 text-white" />,
      location: "Pune, IN",
      eventDate: "05 July 2024, 2:30 PM",
    },
    {
      title: "Green Tech Forum",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/event_image6.jpg" />,
      icon: <IconBoxAlignTopLeft className="h-4 w-4 text-white" />,
      location: "Banglore, IN",
      eventDate: "31 July 2024, 6:30 PM",
    },
    {
      title: "Virtual Reality Conclave",
      hostedBy: "RentPrompts",
      header: <Skeleton url="/img/Event2.jpeg" />,
      icon: <IconBoxAlignRightFilled className="h-4 w-4 text-white" />,
      location: "Indore, IN",
      eventDate: "15 August 2024, 10:30 AM",
    },
  ];

  return (
    <>
      <ImagesSlider
        className="h-[80vh] lg:-mt-[2.8rem] object-cover"
        images={images}
      >
        <motion.div
          initial={{
            opacity: 0,
            y: -80,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          transition={{
            duration: 0.6,
          }}
          className="z-50 flex flex-col justify-center items-center"
        >
          <motion.p className="font-bold text-xl md:text-6xl text-center bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400 py-4">
            Join Us <br /> for an Unforgettable Experience
          </motion.p>
          <button className="px-4 py-2 backdrop-blur-sm border bg-emerald-300/10 border-emerald-500/20 text-white mx-auto text-center rounded-full relative mt-4">
            <span>Join now →</span>
            <div className="absolute inset-x-0  h-px -bottom-px bg-gradient-to-r w-3/4 mx-auto from-transparent via-emerald-500 to-transparent" />
          </button>
        </motion.div>
      </ImagesSlider>

      <div className="w-11/12 mx-auto py-12">
        <h2 className="text-3xl md:text-4xl text-white font-medium">
          Upcoming Events
        </h2>

        <div className="py-12">
          {/* ------------------------Event Cards------------------- */}
          <EventCards className="">
            {items.map((item, i) => (
              <EventCardItems
                key={i}
                title={item.title}
                description={item.hostedBy}
                location={item.location}
                eventDate={item.eventDate}
                header={item.header}
                icon={item.icon}
                className={i === 3 || i === 6 ? "md:col-span-2" : ""}
              />
            ))}
          </EventCards>
        </div>

        <div className="pt-12 text-lg flex justify-center">
          <a
            className="hover:text-indigo-500  flex justify-center items-center gap-1 w-fit"
            href="#"
          >
            <p>Read More</p>
            <div>
              <ChevronDown />
            </div>
          </a>
        </div>
      </div>
    </>
  );
};

export default ImagesSliderDemo;
