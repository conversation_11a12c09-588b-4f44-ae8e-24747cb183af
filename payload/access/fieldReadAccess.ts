import { Access, PayloadRequest } from "payload/types";
import { User } from "../../server/payload-types";

const fieldReadAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin");

  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
        return true 
    } else {
     return false;
    }
  } else{
    return true;
  }
};

export default fieldReadAccess;
