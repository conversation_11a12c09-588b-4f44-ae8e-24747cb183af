import { CollectionAfter<PERSON>hangeHook, CollectionAfterDeleteHook } from "payload/types";
import { invalidateCollectionCache } from "../../lib/cache-manager";

// Generic hook to invalidate cache after any change
export const invalidateCacheAfterChange: CollectionAfterChangeHook = async ({
  doc,
  req: _req,
  operation,
  collection,
}) => {
  try {
    // Invalidate cache for the collection
    invalidateCollectionCache(collection.slug);
    
    // Also invalidate related collections if needed
    const relatedCollections = getRelatedCollections(collection.slug);
    relatedCollections.forEach(relatedCollection => {
      invalidateCollectionCache(relatedCollection);
    });
    
    console.log(`🔄 Cache invalidated for ${collection.slug} after ${operation}`);
  } catch (error) {
    console.error(`Error invalidating cache for ${collection.slug}:`, error);
  }
  
  return doc;
};

// Generic hook to invalidate cache after deletion
export const invalidateCacheAfterDelete: CollectionAfterDeleteHook = async ({
  doc,
  req: _req,
  collection,
}) => {
  try {
    // Invalidate cache for the collection
    invalidateCollectionCache(collection.slug);
    
    // Also invalidate related collections if needed
    const relatedCollections = getRelatedCollections(collection.slug);
    relatedCollections.forEach(relatedCollection => {
      invalidateCollectionCache(relatedCollection);
    });
    
    console.log(`🔄 Cache invalidated for ${collection.slug} after deletion`);
  } catch (error) {
    console.error(`Error invalidating cache for ${collection.slug}:`, error);
  }
  
  return doc;
};

// Define relationships between collections
const getRelatedCollections = (collection: string): string[] => {
  const relationships: Record<string, string[]> = {
    // When products change, also invalidate categories and tags
    products: ["category", "tags"],
    
    // When categories change, invalidate products
    category: ["products"],
    
    // When tags change, invalidate products and rapps
    tags: ["products", "rapps"],
    
    // When users change, invalidate products, rapps, blogs
    users: ["products", "rapps", "blogs", "bounties"],
    
    // When rapps change, invalidate tags
    rapps: ["tags"],
    
    // When blogs change, invalidate tags
    blogs: ["tags"],
    
    // When bounties change, invalidate users
    bounties: ["users"],
    
    // When courses change, no related collections
    courses: [],
    
    // When orders change, invalidate products and users
    orders: ["products", "users"],
    
    // When purchases change, invalidate products and users
    purchases: ["products", "users"],
  };
  
  return relationships[collection] || [];
};

// Specific hooks for different collections
export const productCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const categoryCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const tagsCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const usersCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const rappsCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const blogsCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const bountiesCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const coursesCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const ordersCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const purchasesCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};

export const modelsCacheHooks = {
  afterChange: [invalidateCacheAfterChange],
  afterDelete: [invalidateCacheAfterDelete],
};
