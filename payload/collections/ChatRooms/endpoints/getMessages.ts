// payload/collections/chatrooms/endpoints/getMessages.ts
import { Endpoint } from "payload/config";

export const getMessages: Endpoint = {
  path: "/:chatId/messages",
  method: "get",
  handler: async (req, res) => {
    try {
      const { chatId } = req.params;
      const { user } = req;
      const { page = 1, limit = 20 } = req.query;

      // Verify user has access to this chat
      const chatRoom = await req.payload.findByID({
        collection: "chatrooms",
        id: chatId,
        depth: 0,
      });

      if (!user || (chatRoom.owner !== user.id && chatRoom.applicant !== user.id)) {
        return res.status(403).json({ error: "Not authorized to view these messages" });
      }

      const messages = await req.payload.find({
        collection: "messages",
        where: { chatRoom: { equals: chatId } },
        sort: "-createdAt", // Newest first
        depth: 1,
        limit: Number(limit),
        page: Number(page),
      });

      res.status(200).json({
        docs: messages.docs.reverse(), // Reverse to get oldest first for the current page
        totalDocs: messages.totalDocs,
        limit: messages.limit,
        page: messages.page,
        totalPages: messages.totalPages,
        hasNextPage: messages.hasNextPage,
        hasPrevPage: messages.hasPrevPage,
      });
    } catch (error) {
      console.error("Error fetching messages:", error);
      res.status(500).json({ error: error.message || "Internal server error" });
    }
  },
};