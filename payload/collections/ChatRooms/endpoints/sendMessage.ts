// payload/collections/chatrooms/endpoints/sendMessage.ts
import { Endpoint } from "payload/config";

export const sendMessage: Endpoint = {
  path: "/:chatId/send",
  method: "post",
  handler: async (req, res) => {
    try {
      const { chatId } = req.params;
      const { text, senderId, mediaId,fileId } = req.body;
      
      // Validate input - either text or media must be present
      if (!text?.trim() && !mediaId && !fileId) {
        return res
          .status(400)
          .json({ error: 'Message text, media, or file is required'  });
      }

      const chatRoom = await req.payload.findByID({
        collection: "chatrooms",
        id: chatId,
        depth: 0,
      });

      // Check if user is part of this chat
      if (chatRoom.owner !== senderId && chatRoom.applicant !== senderId) {
        return res
          .status(403)
          .json({ error: "Not authorized to send messages in this chat" });
      }

      const messageData: any = { chatRoom: chatId, sender: senderId };

      // Only add text if it exists
      if (text?.trim()) {
        messageData.text = text;
      }

      // Add media if it exists
      if (mediaId) {
        messageData.media = mediaId;
      }

      if (fileId) messageData.file = fileId;

      const message = await req.payload.create({
        collection: "messages",
        data: messageData,
      });

      // console.log("sendMessage message", message);
      res.status(201).json(message);
    } catch (error) {
      console.error("Error sending message:", error);
      res.status(500).json({ error: error.message || "Internal server error" });
    }
  },
};
