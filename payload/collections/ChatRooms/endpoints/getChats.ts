// payload/collections/chatrooms/endpoints/getChats.ts
import { Endpoint } from 'payload/config';

export const getChats: Endpoint = {
  path: '/:userId/chats',
  method: 'get',
  handler: async (req, res) => {
    try {
      const { userId } = req.params;
      // console.log("userId getChats", userId);
      
      const chatRooms = await req.payload.find({
        collection: 'chatrooms',
        where: {
          or: [
            { owner: { equals: userId } },
            { applicant: { equals: userId } },
          ],
        },
        depth: 2,
      });
      // console.log("userId getChats", userId);

      res.status(200).json(chatRooms);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  },
};