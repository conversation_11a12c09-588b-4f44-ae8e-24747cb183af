import { CollectionConfig, FieldAccess } from 'payload/types';
import { getChats } from './endpoints/getChats';
import { getMessages } from './endpoints/getMessages';
import { sendMessage } from './endpoints/sendMessage';

export const ChatRooms: CollectionConfig = {
  slug: 'chatrooms',
  admin: {
    group: "Admin",
  },
  endpoints: [
    getChats,
    getMessages,
    sendMessage,
  ],
  fields: [
    {
      name: 'bounty', // Kis Bounty se related hai
      type: 'relationship',
      relationTo: 'bounties',
      required: true,
    },
    {
      name: 'owner', // Bounty Owner (jo user ne create kiya)
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: 'applicant', // Accepted Applicant (user jisko accept kiya gaya)
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
  ],
  access: {
    // Sirf Owner ya Applicant hi chat room dekh sakte hain
    read: ({ req: { user } }) => ({
      or: [
        { owner: { equals: user?.id } },
        { applicant: { equals: user?.id } },
      ],
    }),
  },
};