import { Access, PayloadRequest } from 'payload/types';
import { User } from '../../../../server/payload-types';

const createAccess: Access = ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  if (!user) {
    // If user is not logged in, deny access
    return false;
  }

  const url = req.headers.referer || '';
  const isAdmin = user?.role?.includes('admin');
  const isDashboard = url.includes('/admin/collections/paymentdetails');


  // If user is in dashboard and not an admin, deny access
  if (isDashboard && !isAdmin) {
    return false;
  }

  // Otherwise, allow access
  return true;
};

export default createAccess;
