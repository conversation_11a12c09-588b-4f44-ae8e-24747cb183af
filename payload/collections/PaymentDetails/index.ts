
import { CollectionConfig } from "payload/types";
import { admins } from "../../../payload/access/admins";
import { adminsOrLoggedIn } from "../../../payload/access/adminsOrLoggedIn";
import createAccess from "./access/createAccess";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";
// import { admins } from "../../access/admins";

// import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
// import { addAuditLogs } from "../../access/auditlog";

export const PaymentDetails: CollectionConfig = {
  slug: "paymentdetails",
  admin: {
    useAsTitle: "Your Orders",
    description: "A summary of all your orders on RentPrompts.",
    group:"Admin",
    hidden: ({ user }) => user.role !== 'admin',
  },
  access: {
    read: readAccess,
    update: admins,
    create: createAccess,
    delete: admins,
  },
  hooks: {
    beforeChange: [addAuditLogs],
  },
  fields: [
    {
      name: "paymentMethod",
      type: "text",
      label: "Payment Method",
      required: true,
    },
    {
      name: "providerTxId",
      type: "text",
      label: "Payment Provider Tx Id",
      required: false,
    },
    {
      name: "totalAmountPaid",
      type: "number",
      label: "Total Amount Paid",
      required: true,
    },
    {
      name: "tax",
      type: "number",
      label: "Total Tax Paid",
      min: 0,
      required: false,
    },
    {
      name: "status",
      type: "select",
      label: "Payment Status",
      defaultValue: ["init"],
      options: [
        {
          label: "Initiated",
          value: "init",
        },
        {
          label: "Paid",
          value: "paid",
        },
        {
          label: "Pending",
          value: "pending",
        },
        {
          label: "Rejected",
          value: "rejected",
        },
      ],
      required: true,
    },
    {
      name: "payload",
      label: "Last Response Payload",
      type: "json"
    },
    // {
    //   name: "createdBy",
    //   label: "Created By",
    //   type: "text",
    //   admin: {
    //     position: "sidebar",
    //     readOnly: true,
    //   },
    // },
    // {
    //   name: "updatedBy",
    //   label: "Updated By",
    //   type: "text",
    //   admin: {
    //     position: "sidebar",
    //     readOnly: true,
    //   },
    // },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
