import { slateEditor } from "@payloadcms/richtext-slate";
import { CollectionConfig } from "payload/types";

export const test : CollectionConfig = {
    slug: "test",
    fields: [
        {
            name: "title",
            label: "Title of Blog",
            type: "text",
            required: true,
          },
          {
            name: "richText", // required
            type: "richText", // required
            editor: slateEditor({}),
          },
    ]
}

