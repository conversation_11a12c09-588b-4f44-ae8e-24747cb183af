import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const readAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  const isAdmin = user?.role?.includes("admin");

    //check to prevent api security 
    if(req.originalUrl === '/api/wallet')
      {
        return false
    } 
    if(user){
        return true;
    }
 
};

export default readAccess;
