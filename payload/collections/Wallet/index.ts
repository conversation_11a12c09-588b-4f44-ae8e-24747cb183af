import type { CollectionConfig } from "payload/types";
import { anyone } from "../../access/anyone";
import { admins } from "../../access/admins";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import { addAuditLogs } from "../globalHooks/auditlog";

export const Wallet: CollectionConfig = {
  slug: "wallet",
  admin: {
    useAsTitle: "commisionFrom",
    defaultColumns: ["commisionFrom", "totalComputeCostReceived", "totalCommisionReceived"],
    group: "Admin",
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: adminsOrLoggedIn,
    create: adminsOrLoggedIn,
    update: adminsOrLoggedIn,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs]
  },
  fields: [
    {
      name: 'commisionFrom',
      label: 'Commision From',
      type: 'select',
      required: true,
      admin: {
        position: 'sidebar',
        // readOnly: true,
      },
      options: [
        {
          label: 'Rapps Purchase',
          value: 'rapps',
        },
        {
          label: 'Generate Section',
          value: 'generate',
        },
        {
          label: 'Assets Purchase',
          value: 'assets',
        },
        {
          label: 'Course Purchase',
          value: 'course',
        },
        {
          label: 'Bounty Purchase',
          value: 'bounty',
        },
      ],
    },
    {
      name: "totalComputeCostReceived",
      type: "number",
      label: "Total Compute Cost Received",
      required: true,
      defaultValue: 0,
    },
    {
      name: "totalCommisionReceived",
      type: "number",
      label: "Total Commision Received",
      required: true,
      defaultValue: 0,
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
