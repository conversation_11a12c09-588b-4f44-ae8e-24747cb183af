import { CollectionConfig } from "payload/types";
import { buyCoins } from "../../endpoints/buy-coins";
import { admins } from "../../access/admins";
import readAccess from "./access/readAccess";
import fieldReadAccess from "../../access/fieldReadAccess";
import { addAuditLogs } from "../globalHooks/auditlog";

export const Orders: CollectionConfig = {
  slug: "orders",
  admin: {
    useAsTitle: "status",
    group: "User",
    description: "A summary of all your orders on RentPrompts.",
    defaultColumns: [
      "status",
      "totalCoins",
      "totalBasePrice",
      "total",
      "paymentMethod",
    ],
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: readAccess,
    update: admins,
    create: admins,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs]
  },
  endpoints: [
    {
      path: "/buy-coins",
      method: "post",
      handler: buyCoins,
    },
  ],
  fields: [
    {
      name: "totalCoins",
      label: "Total Coins Purchased",
      type: "number",
      min: 1,
      required: true,
    },
    {
      name: "totalBasePrice",
      label: "Total Price of Coins Purchased",
      type: "number",
      min: 1,
      required: true,
    },
    {
      name: "total",
      label: "Total Amount Paid",
      type: "number",
      required: true,
      min: 0,
    },
    {
      name: "tax",
      type: "number",
      label: "Total Tax Paid",
      min: 0,
      required: false,
      
    },
    {
      name: "paymentMethod",
      label: "Payment Method used",
      type: "select",
      admin: {
        position: "sidebar",
      },
      defaultValue: "RAZORPAY",
      options: [
        {
          label: "RAZORPAY",
          value: "RAZORPAY",
        },
        {
          label: "PHONEPE",
          value: "PHONEPE",
        },
      ],
    },
    {
      name: "paymentId",
      type: "relationship",
      relationTo: "paymentdetails",
      maxDepth: 1,
    },
    {
      name: "razorPayOrderId",
      label: "Razor Payment Intent ID",
      type: "text",
      admin: {
        position: "sidebar",
      },
    },
    {
      name: "status",
      label: "Payment Status",
      type: "select",
      admin: {
        position: "sidebar",
      },
      defaultValue: "init",
      options: [
        {
          label: "Initiated",
          value: "init",
        },
        {
          label: "Pending Payment",
          value: "pending",
        },
        {
          label: "Completed",
          value: "completed",
        },
        {
          label: "Rejected",
          value: "rejected",
        },
      ],
    },
    {
      name: "_isPaid",
      type: "checkbox",
      label: "Is Paid",
      defaultValue: false,
      access: {
        read: fieldReadAccess,
        create: admins,
        update: admins,
      },
      required: true,
    },
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      access: {
        read: fieldReadAccess,
      },
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin", // Only admins can see
      },
      admin: {
        position: "sidebar",
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
