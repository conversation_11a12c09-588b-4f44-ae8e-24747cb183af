import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const readAccess: Access = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  if (!user) {
    // If user is not logged in, return false
    return false;
  }

  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/orders");

  if(req.originalUrl === '/api/orders')
    {
      return false
    }

  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return true;
    } else {
      // Fetch only the user's paymentdetails
      const userOrderDetails = await payload.find({
        collection: "orders",
        where: {
          user: {
            equals: user.id,
          },
        },
        limit: 0, // Setting limit to 0 will fetch all documents
      });

      // Return the user's payment details
      return {
        id: {
          in: userOrderDetails.docs.map((order: any) => order.id),
        },
      };
    }
  } else {
    // For non-dashboard logic, return true
    return true;
  }
};

export default readAccess;
