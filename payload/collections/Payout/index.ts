import { CollectionConfig } from "payload/types";
import { admins } from "../../access/admins";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import readAccess from "./access/readAccess";
import beforeChange from "./access/beforeChange";
import createAccess from "./access/createAccess";
import { addAuditLogs } from "../globalHooks/auditlog";


export const Payout: CollectionConfig = {
  slug: "payout",
  admin: {
    defaultColumns: ["username", "status", "user"],
    group:"User",
    hidden: ({ user }) => user.role !== "admin",
  },

  access: {
    read: readAccess,
    // create: adminsOrLoggedIn,
    create: createAccess,
    update: adminsOrLoggedIn, 
    delete: adminsOrLoggedIn, 
  },
  hooks: {
    beforeChange: [beforeChange,addAuditLogs]
  },

  fields: [
    {
      name: "username",
      type: "text",
      label: "Account Holder Name",
    },
    {
        name: "bankDetails",
        label: "Bank Details",
        type: "group",
        fields: [
          {
            name: "accountNumber",
            type: "number",
            label: "Account Number",
          },
          {
            name: "confirmAccountNumber",
            type: "number",
            label: "Confirm Account Number",
          },
          {
            name: "ifscCode",
            type: "text",
            label: "IFSC Code",
          },
          {
            name: "withdrawAmountBank",
            label: "Withdraw Amount",
            type: "number",
            min: 500,
        },
        ],
    },

    {
        name: "upiDetails",
        label: "UPI Details",
        type: "group",
        fields: [
          {
            name: "upiAddress",
            type: "text",
            label: "UPI Address",
          },
          {
            name: "confirmUpiAddress",
            type: "text",
            label: "Confirm UPI Address",
          },
          {
            name: "withdrawAmountUpi",
            label: "Withdraw Amount",
            type: "number",
            min: 500,
        },
        ],
    },
    {
      name : "international",
      label: "International Details",
      type: "group",
        fields: [
          {
            name: "internationalDetails",
            type: "text",
            label: "InternationDetails",
          },
          {
            name: "withdrawAmountInternational",
            label: "Withdraw Amount",
            type: "number",
            min: 1000,
        },
        ],

    },
    // {
    //     name: "withdrawAmount",
    //     label: "Withdraw Amount",
    //     type: "number",
    //     required: true,
    // },
    {
        name: "status",
        label: "Payout Status",
        type: "select",
        defaultValue: "submitted",
        admin:{
          description:"Status will be approved by admin only."
        },
        access: {
          create: admins,
          read: () => true,
          update: admins,
        },
        options: [
          {
            label: "Submitted",
            value: "submitted",
          },
          {
            label: "Processed",
            value: "processed",
          },
          {
            label: "Completed",
            value: "completed",
          },
          {
            label: "Denied",
            value: "denied",
          },
        ],
    },
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      hasMany: false,
      admin: {
        allowCreate: false,
        readOnly: true,
      },
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  
  timestamps: true,
};
