import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const readAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/payout");

  if (!user) {
    return false;
  }

  if(req.originalUrl === '/api/payout')
    {
      return false
    }


  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return true;
    } else {
      const userPayout = await payload.find({
        collection: "payout",
        where: {
          user: {
            equals: user.id,
          },
        },
        limit: 0, // Setting limit to 0 will fetch all documents
      });

      return {
        id: {
          in: userPayout.docs.map((payout: any) => payout.id),
        },
      };
    }
  } else {
   return true
  }
};

export default readAccess;
