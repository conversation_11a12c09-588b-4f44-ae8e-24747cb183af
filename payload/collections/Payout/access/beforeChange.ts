import { PayloadRequest } from "payload/types";
import CustomError from "../../../access/customError"; // Adjust the import path
import { toast } from "sonner";

const beforeChange = async ({ data, req }: { data: any; req: PayloadRequest }) => {
  const user = req.user;
  const isAdmin = user?.role?.includes("admin");



  if (!user) {
    throw new CustomError("You must be logged in to create or update a payout request.", 403);
  }
  

   // Check if this is a create operation
   if (req.method == 'POST' || req.method === 'PATCH') {
    if (isAdmin) {
      return data;
    } 

    if (user.coinBalance < 500) 
    {
      throw new CustomError("*Minimum 500 Credits balance required for withdraw ", 403);
    }

    if((data.bankDetails.withdrawAmountBank == "" || data.bankDetails.withdrawAmountBank == null) && (data.upiDetails.withdrawAmountUpi == "" || data.upiDetails.withdrawAmountUpi == null) && (data.international.withdrawAmountInternational == "" || data.international.withdrawAmountInternational == null)){
      throw new CustomError("You must to fill withdraw amount", 403);
    }

    if(data.bankDetails.withdrawAmountBank > user.coinBalance){
      throw new CustomError("Insufficient Credit Balance for Withdraw", 403);
    }

    if(data.upiDetails.withdrawAmountUpi > user.coinBalance){
      throw new CustomError("Insufficient Credit Balance for Withdraw", 403);
    }

    if(data.international.withdrawAmountInternational > user.coinBalance){
      throw new CustomError("Insufficient Credit Balance for Withdraw", 403);
    }
  }


  // If coin balance is sufficient, proceed with the creation or update
  return data;
};

export default beforeChange;
