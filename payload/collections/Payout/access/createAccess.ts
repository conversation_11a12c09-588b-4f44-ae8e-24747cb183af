import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const createAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin");

  if (!user) {
    return false;
  }

  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return isAdmin;
    } else {
      return false;
    }
  } else {
   return true
  }
};

export default createAccess;
