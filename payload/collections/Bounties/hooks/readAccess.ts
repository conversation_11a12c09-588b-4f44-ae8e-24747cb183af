import { PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const readAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;

    //check to prevent api security 
    if(req.originalUrl === '/api/bounties')
      {
        return false
      }
  
  const allBounties = await payload.find({
    collection: "bounties",
    limit: 0,
  });
  const allBountiesData = allBounties.docs;

  // Filter for approved bounties
  const approvedBounties = allBountiesData.filter(
    (bounty) => bounty.status === "approved"
  );

  // Combine approvedBounties and userBounties without duplicates
  const uniqueBountiesMap = new Map();
  approvedBounties.forEach((bounty) =>
    uniqueBountiesMap.set(bounty.id, bounty)
  );
  (user?.bounties || []).forEach((bounty: any) => {
    if (bounty && bounty.id) {
      uniqueBountiesMap.set(bounty.id, bounty);
    }
  });

  const combinedBounties = Array.from(uniqueBountiesMap.values());

  const combinedBountiesIDs = combinedBounties.map((bounty) => bounty.id);

  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/bounties");

  if (!user) {
    if (isDashboard) {
      return false;
    }
    return {
      status: {
        equals: "approved",
      },
    };
  }

  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return {
        or: [
          { needsApproval: { equals: true } }, // Items needing approval
          {
            or: [{status: {equals: "approved"}}]
          },
           {
            or: [{status: {equals: "completed"}}]
          },
          { 
            or: [
              { user: { equals: user.id } }, // Admin's own items
              // { 
              //   or: [
              //     { status: { equals: "pending" } }, 
              //     { status: { equals: "approved" } }, 
              //   ]
              // }
            ]
          }
        ],
      };
    } else {
      // Regular users see only their own items
      const userBountyIDs = (user.bounties || []).reduce<string[]>(
        (acc, bounty) => {
          if (!bounty) return acc;
          if (typeof bounty === "string") {
            acc.push(bounty);
          } else {
            acc.push(bounty.id);
          }
          return acc;
        },
        []
      );

      return {
        id: {
          in: userBountyIDs,
        },
      };
    }
  } else {
    return {
      id: {
        in: combinedBountiesIDs,
      },
    };
  }
};

export default readAccess;
