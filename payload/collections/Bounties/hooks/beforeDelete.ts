import { CollectionBeforeDeleteHook } from "payload/types";
import CustomError from "../../../access/customError";

const beforeDeleteHook: CollectionBeforeDeleteHook = async ({ req, id }) => {
  const { user } = req;
  const { role } = user;

  // Fetch the document to delete
  const bounty = await req.payload.findByID({
    collection: "bounties",
    id,
  });

  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/bounties");
  
    // Check if the user is an admin
    if(isDashboard){
      if (!role.includes("admin")) {
          throw new CustomError("Only admin can delete bounty.", 403);
    }
    
  } else {
    // Allow delete for non-approved bounties for logged-in users
    if (!user) {
      throw new CustomError("You must be logged in to delete a bounty.", 403);
    }
  }
};

export default beforeDeleteHook;
