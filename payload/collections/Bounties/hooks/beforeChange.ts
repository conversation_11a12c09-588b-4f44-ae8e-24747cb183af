import { PayloadRequest } from "payload/types";
import CustomError from "../../../access/customError";
import { BeforeChangeHook } from "payload/dist/collections/config/types";
const beforeChange = async ({
  data,
  req,
}: {
  data: any;
  req: PayloadRequest;
}) => {
  const user = req.user;

  if (!user) {
    throw new CustomError(
      "You must be logged in to create or update a bounty.",
      403
    );
  }

  // Check if this is a create operation
  if (req.method === "POST") {
    if (user.coinBalance < data.estimatedPrice) {
      throw new CustomError("Insufficient balance, Please recharge.", 403);
    }
  }

  // Get the current date (without time for accurate comparison)
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);
  const completionDate = new Date(data.completionDate);

  if (completionDate < currentDate) {
    throw new CustomError("Completion date cannot be in the past.", 400);
  }

  // If coin balance is sufficient, proceed with the creation or update
  return data;
};

export default beforeChange;



export const clearAdminReviewOnStatusChange: BeforeChangeHook<any> = async ({
  data,
  originalDoc,
}) => {
  // console.log("originalDoc", originalDoc);
  if (
    originalDoc?.status === "denied" &&
    (data.status === "pending" || data.status === "approved")
  ) {
    return {
      ...data,
      adminReview: "", // Clear the adminReview field
    };
  }
  return data;
};