import { handleStatusChange } from "../../../utilities/emailHandlers/handleStatusChange";
import { handleNewApplicant } from "../../../utilities/emailHandlers/handleNewApplicant";
import { handleApplicantStatusUpdate } from "../../../utilities/emailHandlers/handleApplicantStatusUpdate";

import { PayloadRequest } from "payload/types";
import { handleApplicantNotifications } from "../../../utilities/emailHandlers/handleApplicantNotifications";

export const sendApprovalEmail = async ({
  doc,
  previousDoc,
  req,
  operation,
}: {
  doc: any;
  previousDoc: any;
  req: PayloadRequest;
  operation: "create" | "update";
}) => {
  if (operation !== "update" || !previousDoc) {
    // console.log("New bounty creation detected. Skipping email.");
    return;
  }
  try {
    const bountyOwner = await req.payload.findByID({
      collection: "users",
      id: doc?.user?.id ? doc?.user?.id : doc?.user,
    });
// console.log("bountyOwner",bountyOwner)
    if (!bountyOwner || typeof bountyOwner !== "object") {
      console.warn("Bounty owner not found or invalid format.");
      return;
    }

    const userEmail = bountyOwner.email;
    const userName = bountyOwner.user_name || "User";

    // STATUS CHANGED BY ADMIN THEN BOUNTY OWNER GET EMAIL
    if (doc.status !== previousDoc?.status) {
      if (!["approved", "denied"].includes(doc.status)) return;
      try {
        await handleStatusChange({ doc, userEmail, userName, req });
      } catch (error) {
        console.error("❌ Error sending bounty status change email:", error);
      }
    } else {
      const oldApplicants = previousDoc?.applicants || [];
      const newApplicants = doc?.applicants || [];

      // WHEN NEW APPLICANT APPLY FOR BOUNTIE THEN OWNER GET EMAIL ABOUT ITS
      try {
        const newEntries = newApplicants.filter((newApp) => {
          return !oldApplicants.some(
            (oldApp) => oldApp.userId === newApp.userId
          );
        });
// console.log("akdjfkd",newEntries)
        if (newEntries.length > 0) {
          const newlyAdded = newEntries[newEntries.length - 1];
          await handleNewApplicant({
            newlyAdded,
            userEmail,
            userName,
            doc,
            req,
          });
        }
      } catch (error) {
        console.error("❌ Error sending new applicant email:", error);
      }

      // WHEN OWNER ACCECPT THE PROPSAL OF APPLICATN THEN APPLICANT WEIL GET EMAI

      try {
        const updatedApplicants = newApplicants.filter((newApp) => {
          const oldApp = oldApplicants.find((o) => o.userId === newApp.userId);
          return (
            oldApp &&
            newApp.applicantStatus &&
            newApp.applicantStatus !== oldApp.applicantStatus &&
            ["approved", "rejected"].includes(newApp.applicantStatus)
          );
        });
        if (updatedApplicants.length) {
          await handleApplicantStatusUpdate({ updatedApplicants, doc, req });
        }
      } catch (error) {
        console.error("❌ Error sending applicant status update email:", error);
      }

      try {
        await handleApplicantNotifications({ doc, previousDoc ,req});
      } catch (error) {
        console.error("❌ Error handling applicant notifications:", error);
      }
    }

    return doc;
  } catch (err) {
    console.error("❌ Error in sendApprovalEmail:", err);
  }
};
