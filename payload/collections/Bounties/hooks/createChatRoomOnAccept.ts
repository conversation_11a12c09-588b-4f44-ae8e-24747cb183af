import { CollectionAfterChangeHook } from 'payload/types';

export const createChatRoomOnAccept: CollectionAfterChangeHook = async ({ doc, previousDoc, req }) => {
    const bountyId = doc.id;
    const ownerId = doc.user;
  
    const applicants = doc.applicants || [];
  
    for (const applicant of applicants) {
      const prevApplicant = previousDoc.applicants?.find(a => a.userId === applicant.userId);
  
      if (applicant.applicantStatus === 'approved' && prevApplicant?.applicantStatus !== 'approved') {
        const existing = await req.payload.find({
          collection: 'chatrooms',
          where: {
            bounty: { equals: bountyId },
            applicant: { equals: applicant.userId },
          },
        });
  
        if (existing.totalDocs === 0) {
          await req.payload.create({
            collection: 'chatrooms',
            data: {
              bounty: bountyId,
              owner: ownerId,
              applicant: applicant.userId,
            },
          });
        }
      }
    }
  };
  