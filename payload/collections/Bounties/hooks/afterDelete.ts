import CustomError from "../../../access/customError";
import { Bounty, User } from "@/server/payload-types";
import { CollectionAfterDeleteHook } from "payload/types";

const afterDeleteHook: CollectionAfterDeleteHook = async ({ req, doc }) => {
  const { user } = req;
  const { role } = user;

  if (!user) {
    throw new CustomError("You must be logged in to delete a bounty.");
  }

  const bounty = doc as Bounty;

  let userId = user.id;

  // Check if the user is an admin
  if (role.includes("admin")) {
    // If the user is an admin, update the bounty creator's coin balance
    if (!bounty.user) {
      throw new CustomError("Bounty creator not found.");
    }
    userId = (bounty.user as User).id; // Assuming createdBy holds the ID of the bounty creator
  }

  const userDoc: any = await req.payload.findByID({
    collection: "users",
    id: userId,
  });

  if (!userDoc) {
    throw new CustomError("User not found.");
  }

  // Remove the bounty from the user's bounties field
  const updateUserBounties =
    userDoc?.bounties && Array.isArray(userDoc.bounties)
      ? userDoc.bounties.filter(
          (singleBounty: any) => singleBounty.id !== bounty.id
        )
      : [];

  const updatedBounties = updateUserBounties?.map((bounty: any) => bounty.id);

  // Add bounty's estimated coin value to the user's coinBalance
  const updatedCoinBalance = (userDoc.coinBalance || 0) + bounty.estimatedPrice;

  // Update the user document
  try{
    await req.payload.update({
      collection: "users",
      id: userId,
      data: {
        bounties: updatedBounties,
        coinBalance: updatedCoinBalance,
      },
    });
  } catch(error){
    throw new CustomError("Error updating user document.");
  }
  

};

export default afterDeleteHook;
