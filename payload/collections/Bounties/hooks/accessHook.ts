import { FieldAccess } from "payload/types";

export const isOwnerOrAdminFieldAccess: FieldAccess = ({ req }) => {
  const user = req?.user;

  if (!user) return false;

  // ✅ Debug role
  // console.log("User role in field access:", user.role);

  if (user.role === "admin") return true;

  // You can customize this further if needed
  return true; // Allow for now (e.g., bounty owner will be handled at collection level)
};
