// import type { BeforeChangeHook } from "payload/dist/collections/config/types";

// export const updateBountyStatus: BeforeChangeHook = async ({
//   data,
//   originalDoc,
// }) => {
//   const now = new Date();

//   const applyExpireDate = data.applyExpireDate
//     ? new Date(data.applyExpireDate)
//     : null;

//   const completionDate = data.completionDate
//     ? new Date(data.completionDate)
//     : null;

//   const currentStatus = originalDoc?.status;

//   // 1. If completed date is in the past → completed
//   if (
//     (currentStatus === "approved" || currentStatus === "expired") &&
//     completionDate &&
//     completionDate < now
//   ) {
//     data.status = "completed";
//     return data;
//   }

//   // 2. If it was completed but completion date is now in the future → expired (fallback)
//   if (
//     currentStatus === "completed" &&
//     completionDate &&
//     completionDate > now
//   ) {
//     data.status = "expired";
//     return data;
//   }

//   // 3. If approved and applyExpireDate is in the past → expired
//   if (
//     currentStatus === "approved" &&
//     applyExpireDate &&
//     applyExpireDate < now
//   ) {
//     data.status = "expired";
//     return data;
//   }

//   // 4. If expired but applyExpireDate is extended → approved
//   if (
//     currentStatus === "expired" &&
//     applyExpireDate &&
//     applyExpireDate > now
//   ) {
//     data.status = "approved";
//     return data;
//   }

//   return data;
// };
import type { BeforeChangeHook } from "payload/dist/collections/config/types";

export const updateBountyStatus: BeforeChangeHook = async ({
  data,
  originalDoc,
}) => {
  const now = new Date();

  // Helper to strip time from Date
  const getDateOnly = (date: Date) =>
    new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const today = getDateOnly(now);

  const applyExpireDate = data.applyExpireDate
    ? getDateOnly(new Date(data.applyExpireDate))
    : null;

  const completionDate = data.completionDate
    ? getDateOnly(new Date(data.completionDate))
    : null;

  const currentStatus = originalDoc?.status;
  const newStatus = data.status;

  const applyExpired = applyExpireDate && today > applyExpireDate;
  const completedExpired = completionDate && today > completionDate;

  const isManuallyChanged = newStatus && newStatus !== currentStatus;

  // If manually updated by admin, skip automatic updates
  if (isManuallyChanged) {
    return data;
  }

  if (currentStatus === "pending" || applyExpired || completedExpired) {
    data.status = currentStatus; // Preserve original status
    return data;
  }
  // 1. If completion date has passed → mark as completed
  if (
    (currentStatus === "approved" || currentStatus === "expired") &&
    completionDate &&
    today > completionDate
  ) {
    data.status = "completed";
    return data;
  }

  // 2. If previously completed, but completionDate is extended into the future → revert to expired
  if (
    currentStatus === "completed" &&
    completionDate &&
    today <= completionDate
  ) {
    data.status = "expired";
    return data;
  }

  // 3. If approved and applyExpireDate has passed → mark as expired
  if (
    (currentStatus === "approved" || currentStatus === "pending") &&
    applyExpireDate &&
    today > applyExpireDate
  ) {
    data.status = "expired";
    return data;
  }

  // 4. If expired but applyExpireDate is extended → revert to approved
  if (
    currentStatus === "expired" &&
    applyExpireDate &&
    today <= applyExpireDate
  ) {
    data.status = "approved";
    return data;
  }

  return data;
};
