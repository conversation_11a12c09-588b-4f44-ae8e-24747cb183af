import type { Before<PERSON><PERSON>eHook } from "payload/dist/collections/config/types";

export const addAuditLogs: BeforeChangeHook = async ({
  req,
  data,
  operation,
}) => {
  if (operation === "create" && req.user) {
    return {
      ...data,
      createdBy: req.user.email,
    };
  } else if (operation === "create" && !req.user) {
    return {
      ...data,
      createdBy: "Anon",
    };
  } else if (operation === "update" && req.user) {
    return {
      ...data,
      updatedBy: req.user.email,
    };
  }

  return data;
};
