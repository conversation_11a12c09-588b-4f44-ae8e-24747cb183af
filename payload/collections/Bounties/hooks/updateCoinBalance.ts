import type { BeforeChangeHook } from "payload/dist/collections/config/types";
import { PayloadRequest } from "payload/types";
import { getPayloadClient } from "../../../../server/get-payload";
import { Bounty, Purchase } from "@/server/payload-types";
import CustomError from "../../../access/customError";

export const updateCoinBalance: any = async ({
  req,
  data,
  operation,
  originalDoc,
}: {
  req: PayloadRequest;
  data: Bounty;
  res: any;
  operation: string;
  originalDoc: any;
}): Promise<Bounty> => {

  const { user } = req;
  const userId = user?.id;
  const bounty: any = data as Bounty;
  const bountyUserId = bounty?.user;

  if (!(req.method === "PATCH" && operation === "update")) {
    return data;
  }

  
  if (req.method === "PATCH" && operation === "update") {

    const userDoc: any = await req.payload.findByID({
        collection: "users",
        id: bountyUserId,
    });
  
    if (!userDoc) {
        throw new CustomError("User not found.");
    }


    let updatedCoinBalance;

    
    if (bounty?.status === "denied") {

      updatedCoinBalance = (userDoc.coinBalance || 0) + bounty.estimatedPrice;
  
    } else if (bounty?.status === "pending" || bounty?.status === "approved" || bounty?.status === "expired") {
  
      {originalDoc?.status === "denied" ?
      updatedCoinBalance = userDoc?.coinBalance - bounty?.estimatedPrice : 
      updatedCoinBalance = userDoc?.coinBalance - bounty?.updatedEstimatedPrice
      }
    }

    try {
        await req.payload.update({
          collection: "users",
          id: bountyUserId,
          data: {
            coinBalance: updatedCoinBalance,
          },
          overrideAccess: true,
        });
      } catch (error) {
        throw new CustomError("Error updating user document.");
      }
    bounty.updatedEstimatedPrice = 0;
    return bounty;
  } else {
        throw new CustomError("Bounty Creation Failed", 403);
  }
};
