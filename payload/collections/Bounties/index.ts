import {
  Collection<PERSON>onfig,
  <PERSON>Hook,
  GlobalBeforeChangeHook,
} from "payload/types";
import { BOUNTY_TAGS, Bounty_Type } from "../../../constants";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import { admins } from "../../access/admins";
import { adminsOrLoggedUser } from "../../access/adminsOrLoggedUser";
import beforeDeleteHook from "./hooks/beforeDelete";
import afterDeleteHook from "./hooks/afterDelete";
import readAccess from "./hooks/readAccess";
import { purchaseBounty } from "../../endpoints/purchaseBounty";
import { addAuditLogs } from "./hooks/auditlog";
import beforeChange, {
  clearAdminReviewOnStatusChange,
} from "./hooks/beforeChange";
import fieldReadAccess from "../../access/fieldReadAccess";
import slugify from "slugify";
import autoGenerateSlug from "./hooks/generateSlug";
import { bountyDetail } from "../../endpoints/bounty-detail";

import { sendApprovalEmail } from "./hooks/sendApprovalEmail";
import { isOwnerOrAdminFieldAccess } from "./hooks/accessHook";

import { createChatRoomOnAccept } from "./hooks/createChatRoomOnAccept";

import CustomUseCaseSelect from "../../components/CustomUseCaseSelect";

import updateUserAppliedBounties from "../Users/<USER>/updateUserAppliedBounties";
import { bountyReward } from "../../endpoints/bounty-reward";
import { updateBountyStatus } from "./hooks/updateBountyStatus";
import { updateCoinBalance } from "./hooks/updateCoinBalance";
export const Bounties: CollectionConfig = {
  slug: "bounties",
  admin: {
    useAsTitle: "title",
    group: "Community",
    defaultColumns: ["title", "status", "estimatedPrice"],
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: readAccess,
    create: adminsOrLoggedIn,
    update: adminsOrLoggedIn,
    delete: adminsOrLoggedIn,
  },
  hooks: {
    beforeChange: [
      clearAdminReviewOnStatusChange,
      beforeChange,
      addAuditLogs,
      updateBountyStatus,
      updateCoinBalance,
    ],
    afterChange: [
      purchaseBounty,
      sendApprovalEmail,
      createChatRoomOnAccept,
      updateUserAppliedBounties,
    ],
    beforeDelete: [beforeDeleteHook],
    afterDelete: [afterDeleteHook],
  },

  endpoints: [
    {
      path: "/bounty-detail/:bountyId",
      method: "get",
      handler: bountyDetail,
    },
    {
      path: "/bounty-reward/:bountyId",
      method: "post",
      handler: bountyReward,
    },
  ],

  fields: [
    {
      name: "title",
      label: "Bounty Title",
      type: "text",
      required: true,
      admin: {
        description: "Mention the title for the bounty.",
      },
      index: true,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      required: false,
      unique: true,
      hooks: {
        beforeValidate: [autoGenerateSlug], // Use the field-level hook here for real-time slug generation
      },
      admin: {
        hidden: true,
        readOnly: true,
        position: "sidebar", // Make it read-only so users cannot change it manually
      },
    },
    {
      name: "content", // required
      type: "textarea", // required
      label: "Bounty Requirements.",
      required: true,
      admin: {
        description:
          "Provide a detailed description of the requirements and criteria needed to complete the bounty, also can use markup for increase readability.",
      },
      index: true,
    },
    {
      name: "completionDate", // required
      type: "date", // required
      label: "Completion Date",
      required: true,
      admin: {
        description:
          "Specify the deadline by which the bounty must be completed.",
        date: {
          minDate: new Date(), // today's date as the minimum
        },
      },
    },

    {
      name: "applyExpireDate", // required
      type: "date", // required
      label: "Apply Expire Date",
      required: true,
      admin: {
        description:
          "Specify the date after which the bounty will no longer be valid",
      },
    },

    {
      name: "status",
      label: "Bounty Status",
      type: "select",
      defaultValue: "pending",
      admin: {
        description: "Status will be approved by admin only.",
      },
      access: {
        create: adminsOrLoggedUser,
        read: () => true,
        update: adminsOrLoggedUser,
      },
      options: [
        {
          label: "Pending verification",
          value: "pending",
        },
        {
          label: "Approved",
          value: "approved",
        },
        {
          label: "Denied",
          value: "denied",
        },
        {
          label: "Expired",
          value: "expired",
        },
        {
          label: "Completed",
          value: "completed",
        },
      ],
    },
    {
      name: "adminReview",
      label: "Admin Review",
      type: "textarea",
      admin: {
        condition: (data) => data.status === "denied", // Show only if status is "denied"
        description: "Provide a reason for denying this product.",
      },
    },
    {
      type: "checkbox",
      name: "needsApproval",
      label: "Send for Approval",
      admin: {
        description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
    },
    {
      type: "checkbox",
      name: "termsAccepted",
      label: "Terms & condition Accepted",
      admin: {
        // description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
    },
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      //required: true,
      access: {
        read: fieldReadAccess,
        create: admins,
        update: admins,
      },
      admin: {
        description:
          "Highlight the bounty in the list by marking it as featured. (Allowed to admin only)",
      },
    },
    {
      name: "estimatedPrice",
      label: "Bounty Price",
      type: "number",
      min: 0,
      required: true,
      access: {
        create: adminsOrLoggedUser,
        read: () => true,
        update: adminsOrLoggedUser,
      },
      admin: {
        description: "Price in credit (1 rupee = 1 credit)",
      },
    },
    {
      name: "updatedEstimatedPrice",
      label: "Updated Bounty Price",
      type: "number",
      min: 0,
      required: true,
      access: {
        create: adminsOrLoggedUser,
        read: () => true,
        update: adminsOrLoggedUser,
      },
      admin: {
        description: "Price in credit (1 rupee = 1 credit)",
      },
    },
    // {
    //   name: "bountyType",
    //   label: "Bounty Related To",
    //   type: "select",
    //   // defaultValue: "AI",
    //   options: Bounty_Type.map(({ label, value }) => ({ label, value })),
    //   // required: true,
    //   admin: {
    //     description: "Mention the category or field the bounty is related to.",
    //   },
    // },
    {
      name: "bountyType",
      label: "Bounty Related To",

      type: "text",
      // defaultValue: "AI",
      required: true,

      admin: {
        components: {
          Field: CustomUseCaseSelect, // 👈 Use our custom field
        },
      },
    },

    {
      name: "applicantsLimit",
      label: "Applicants Limit",
      type: "number",
      required: true,
      admin: {
        description: "Limit the number of applicants for this bounty.",
      },
    },
    {
      name: "product_files",
      label: "Upload attachments",
      type: "relationship",
      required: false,
      relationTo: "product_files",
      hasMany: false,
    },
    {
      name: "useCases",
      label: "Related Use Cases",
      type: "relationship",
      relationTo: "useCases",
      hasMany: true, // or false if you only want to relate a single use case
      required: false,
      admin: {
        hidden: true,
        description: "Select one or more related use cases for this bounty.",
      },
    },

    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      access: {
        read: fieldReadAccess,
      },
      hasMany: false,
      admin: {
        allowCreate: false,
        readOnly: true,
      },
    },
    {
      name: "applicants",
      label: "Applied Users",
      type: "array",
      required: false,
      access: {
        update: () => true,
      },

      fields: [
        {
          name: "userId",
          label: "User IDs",
          type: "text",
          // relationTo: "users",
          required: true,
          // access: {
          //   read: ({ req: { user } }) => user?.role === "admin",
          // },
        },
        {
          name: "userImage",
          label: "User Image",
          type: "text", // 👈 just stores string now
          required: false,
          admin: {
            readOnly: true,
            description: "Fetched from the user's profile",
          },
        },

        {
          name: "userName",
          label: "User Name",
          type: "text",
          // relationTo: "users",
          required: false,
        },
        {
          name: "linkedin",
          label: "Linkedin ID",
          type: "text",
          required: true,
          // access: {
          //   read: ({ req: { user } }) => user?.role === "admin",
          // },
        },
        {
          name: "phone",
          label: "Phone Number",
          required: true,
          type: "number",
          // access: {
          //   read: ({ req: { user } }) => user?.role === "admin",
          // },
        },
        {
          required: true,
          name: "approach",
          label: "Approach",
          type: "text",
        },
        {
          name: "relatedFile",
          label: "Related Product Files",
          type: "relationship",
          required: false,
          relationTo: "product_files",
          hasMany: true,
        },

        // add filed to accept/reject applicaitons
        {
          name: "canReapply",
          label: "Can Reapply?",
          type: "checkbox",
          defaultValue: true,
          admin: {
            description: "Allow user to re-apply after rejection.",
            condition: (data) => data.applicationStatus === "rejected",
          },
        },
        {
          name: "applicantStatus",
          label: "Status",
          type: "select",
          options: [
            { label: "Pending", value: "pending" },
            { label: "Approved", value: "approved" },
            { label: "Rejected", value: "rejected" },
          ],
          defaultValue: "pending",
          access: {
            update: isOwnerOrAdminFieldAccess,
          },
        },
        {
          name: "rejectionReason",
          label: "Rejection Reason",
          type: "textarea",
          admin: {
            condition: (_, siblingData) =>
              siblingData?.applicantStatus === "rejected",
          },
          access: {
            update: isOwnerOrAdminFieldAccess,
          },
        },
        {
          name: "winner",
          label: "Winner",
          type: "checkbox",
          defaultValue: false,
          access: {
            update: isOwnerOrAdminFieldAccess,
          },
          admin: {
            description:
              "Mark if this applicant is a winner. Multiple winners are allowed.",
          },
        },
        // Add this new field for final submissions
        {
          name: "finalSubmissions",
          label: "Final Submissions",
          type: "array",
          admin: {
            description:
              "Final work submissions from the applicant (max 3 allowed)",
            condition: (data, siblingData) =>
              siblingData.applicantStatus === "approved",
          },
          fields: [
            {
              name: "file",
              type: "upload",
              relationTo: "product_files",
              required: true,
              admin: {
                description: "Upload the final submission file",
              },
            },
            {
              name: "submittedAt",
              type: "date",
              required: true,
              admin: {
                readOnly: true,
                date: {
                  pickerAppearance: "dayAndTime",
                  displayFormat: "MMM dd yyyy HH:mm",
                },
              },
              defaultValue: () => new Date(),
            },
            {
              name: "notes",
              type: "textarea",
              required: false,
              admin: {
                description: "Any additional notes about this submission",
              },
            },
            {
              name: "status",
              label: "Submission Status",
              type: "select",
              options: [
                { label: "Pending", value: "pending" },
                { label: "Accepted", value: "accepted" },
                { label: "Rejected", value: "rejected" },
              ],
              defaultValue: "pending",
              access: {
                update: isOwnerOrAdminFieldAccess,
              },
              required: false,
            },
            {
              name: "rejectionReason",
              label: "Rejection Reason",
              type: "textarea",
              admin: {
                condition: (data, siblingData) =>
                  siblingData?.status === "rejected",
                description: "Provide the reason for rejecting this submission",
              },
              access: {
                update: isOwnerOrAdminFieldAccess,
              },
              required: false,
            },
          ],
          maxRows: 3,
          hooks: {
            beforeValidate: [
              async ({ value }) => {
                if (value && value.length > 3) {
                  throw new Error(
                    "Maximum of 3 final submissions allowed per applicant"
                  );
                }
                return value;
              },
            ],
          },
        },
        {
          name: "rewardJoules",
          label: "Rewarded Joules",
          required: false,
          type: "number",
        },
        {
          name: "message",
          label: "Message",
          type: "textarea",
          required: false,
          admin: {
            readOnly: false,
            description: "Write a message for this user (based on status).",
            condition: (data, siblingData) =>
              siblingData.status === "accepted" ||
              siblingData.status === "rejected",
          },
        },
      ],
    },
    {
      name: "rewardReleased",
      label: "Reward Released",
      type: "checkbox", // Use checkbox for boolean flags
      defaultValue: false,
      admin: {
        position: "sidebar", // Optional: makes it show in the sidebar in the admin UI
      },
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
