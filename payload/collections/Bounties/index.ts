import {
  CollectionConfig,
  <PERSON>Hook,
  GlobalBeforeChangeHook,
} from "payload/types";
import { BOUNTY_TAGS, Bounty_Type } from "../../../constants";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import { admins } from "../../access/admins";
import { adminsOrLoggedUser } from "../../access/adminsOrLoggedUser";
import beforeDeleteHook from "./hooks/beforeDelete";
import afterDeleteHook from "./hooks/afterDelete";
import readAccess from "./hooks/readAccess";
import { purchaseBounty } from "../../endpoints/purchaseBounty";
import { addAuditLogs } from "./hooks/auditlog";
import beforeChange, { clearAdminReviewOnStatusChange } from "./hooks/beforeChange";
import fieldReadAccess from "../../access/fieldReadAccess";
import slugify from "slugify";
import autoGenerateSlug from "./hooks/generateSlug";
import { bountyDetail } from "../../endpoints/bounty-detail";
import { bountiesCacheHooks } from "../../hooks/cache-invalidation";

export const Bounties: CollectionConfig = {
  slug: "bounties",
  admin: {
    useAsTitle: "title",
    group: "Community",
    defaultColumns: ["title", "status", "estimatedPrice"],
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: readAccess,
    create: adminsOrLoggedIn,
    update: adminsOrLoggedIn,
    delete: adminsOrLoggedIn,
  },
  hooks: {
    beforeChange: [clearAdminReviewOnStatusChange,beforeChange, addAuditLogs],
    afterChange: [purchaseBounty, ...bountiesCacheHooks.afterChange],
    beforeDelete: [beforeDeleteHook],
    afterDelete: [afterDeleteHook, ...bountiesCacheHooks.afterDelete],
  },

  endpoints: [
    {
      path: "/bounty-detail/:bountyId",
      method: "get",
      handler: bountyDetail, 
    },
  ],

  fields: [
    {
      name: "title",
      label: "Bounty Title",
      type: "text",
      required: true,
      admin: {
        description: "Mention the title for the bounty.",
      },
      index: true,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      required: false,
      unique: true,
      hooks: {
        beforeValidate: [autoGenerateSlug], // Use the field-level hook here for real-time slug generation
      },
      admin: {
        hidden: true,
        readOnly: true,
        position: "sidebar", // Make it read-only so users cannot change it manually
      },
    },
    {
      name: "content", // required
      type: "textarea", // required
      label: "Bounty Requirements.",
      required: true,
      admin: {
        description:
          "Provide a detailed description of the requirements and criteria needed to complete the bounty, also can use markup for increase readability.",
      },
      index: true,
    },
    {
      name: "completionDate", // required
      type: "date", // required
      label: "Completion Date",
      required: true,
      admin: {
        description:
          "Specify the deadline by which the bounty must be completed.",
        date: {
          minDate: new Date(), // today's date as the minimum
        },
      },
    },
    {
      name: "status",
      label: "Bounty Status",
      type: "select",
      defaultValue: "pending",
      admin: {
        description: "Status will be approved by admin only.",
      },
      access: {
        create: admins,
        read: () => true,
        update: admins,
      },
      options: [
        {
          label: "Pending verification",
          value: "pending",
        },
        {
          label: "Approved",
          value: "approved",
        },
        {
          label: "Denied",
          value: "denied",
        },
        {
          label: "Completed",
          value: "completed",
        },
      ],
    },
    {
      name: "adminReview",
      label: "Admin Review",
      type: "textarea",
      admin: {
        condition: (data) => data.status === "denied", // Show only if status is "denied"
        description: "Provide a reason for denying this product.",
      },
    },
    {
      type: "checkbox",
      name: "needsApproval",
      label: "Send for Approval",
      admin: {
        description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
    },
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      //required: true,
      access: {
        read: fieldReadAccess,
        create: admins,
        update: admins,
      },
      admin: {
        description:
          "Highlight the bounty in the list by marking it as featured. (Allowed to admin only)",
      },
    },
    {
      name: "estimatedPrice",
      label: "Bounty Price",
      type: "number",
      min: 0,
      required: true,
      access: {
        create: adminsOrLoggedUser,
        read: () => true,
        update: admins,
      },
      admin: {
        description: "Price in credit (1 rupee = 1 credit)",
      },
    },
    {
      name: "bountyType",
      label: "Bounty Related To",
      type: "select",
      // defaultValue: "AI",
      options: Bounty_Type.map(({ label, value }) => ({ label, value })),
      required: true,
      admin: {
        description: "Mention the category or field the bounty is related to.",
      },
    },
    {
      name: "product_files",
      label: "Upload attachments",
      type: "relationship",
      required: false,
      relationTo: "product_files",
      hasMany: false,
    },
    {
      name: "tags",
      label: "Tags",
      type: "select",
      hasMany: true,
      options: BOUNTY_TAGS.map(({ label, value }) => ({ label, value })),
      required: false,
      admin: {
        description:
          "Add tag for the bounty based on its type. You can also select multiple tags.",
      },
    },
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      access: {
        read: fieldReadAccess,
      },
      hasMany: false,
      admin: {
        allowCreate: false,
        readOnly: true,
      },
    },
    {
      name: "applicants",
      label: "Applied Users",
      type: "array",
      required: false,
      access: {
        update: () => true,
      },
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: "userId",
          label: "User IDs",
          type: "text",
          // relationTo: "users",
          required: true,
          access: {
            read: ({ req: { user } }) => user?.role === "admin",
          },
        },
        {
          name: "userName",
          label: "User Name",
          type: "text",
          // relationTo: "users",
          required: false,
        },
        {
          name: "linkedin",
          label: "Linkedin ID",
          type: "text",
          required: true,
          access: {
            read: ({ req: { user } }) => user?.role === "admin",
          },
        },
        {
          name: "phone",
          label: "Phone Number",
          required: true,
          type: "number",
          access: {
            read: ({ req: { user } }) => user?.role === "admin",
          },
        },
        {
          required: true,
          name: "approach",
          label: "Approach",
          type: "text",
        },
        {
          name: "relatedFile",
          label: "Related Product Files",
          type: "relationship",
          required: false,
          relationTo: "product_files",
          hasMany: true,
        },
      ],
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
