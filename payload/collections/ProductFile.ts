import { User } from "../../server/payload-types";
import { Before<PERSON><PERSON>eHook } from "payload/dist/collections/config/types";
import { CollectionConfig } from "payload/types";
import { adminsOrLoggedIn } from "../access/adminsOrLoggedIn";

const addUser: BeforeChangeHook = ({ req, data }) => {
  const user = req.user as User | null;
  return { ...data, user: user?.id };
};

export const ProductFiles: CollectionConfig = {
  slug: "product_files",
  admin: {
    hidden: ({ user }) => user.role !== "admin",
    group: "Admin",
  },
  hooks: {
    beforeChange: [addUser],
  },
  access: {
    // read: async ({ req }) => {
    //   const { user } = req;

    //   // Allow admins to see all media files
    //   if (user && user.role === "admin") {
    //     return true;
    //   }

    //   // Allow users to see only their own media files
    //   if (user) {
    //     return {
    //       user: {
    //         equals: user.id,
    //       },
    //     };
    //   }

    //   // If no user is logged in, deny access
    //   return false;
    // },

    read: async ({ req }) => {
      const { user } = req;

      if(req.originalUrl === '/api/product_files')
        {
          return false
      } 

      if (!user) {
        // If user is not logged in, show all productfiles
        return true;
      }
    
      const url = req.headers.referer || "";
      const isDashboard = url.includes("/admin/collections");
    

      // Allow admins to see all media files

      if(isDashboard){
        if (user && user.role === "admin") {
          return true;
        }
  
        // Allow users to see only their own media files
        if (user) {
          return {
            user: {
              equals: user.id,
            },
          };
        }
      }
      
      return true;
    },
    update: ({ req }) => req.user.role === "admin",
    delete: ({ req }) => req.user.role === "admin",
  },

  upload: {
    disableLocalStorage: true,
    // staticURL: "/product_files",
    staticURL: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_PUBLIC_R2 || "/product_files",
    staticDir: "product_files",
    mimeTypes: [
      "image/*",
      "font/*",
      "application/postscript",
      "text/*",
      "audio/*",
      "video/*",
      "application/*",
    ],
  },
  fields: [
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      admin: {
        condition: () => false,
      },
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      hasMany: false,
      required: true,
    },
  ],
};
