import type { Payload<PERSON>and<PERSON> } from 'payload/config';
import type { PayloadRequest } from 'payload/types';

const logs = process.env.LOGS_SECURE_PRICE_DATA === '1';

export const securePriceData: PayloadHandler = async (req: PayloadRequest, res) => {
  const { priceID } = req.params;

  if (!req.user) {
    if (logs) req.payload.logger.error({ err: 'You are not authorized to access this data' });
    return res.status(401).json({ error: 'You are not authorized to access this data' });
  }

  try {
    const price = await req.payload.findByID({
      collection: 'prices',
      id: priceID,
      depth: 1,
    });

    if (!price) {
      if (logs) req.payload.logger.error({ err: `Price ${priceID} not found` });
      return res.status(404).json({ message: 'Price not found' });
    }

    // Select specific fields to return, excluding sensitive data
    const priceData = {
      packageName: price.packageName,
      numberOfCoins: price.numberOfCoins,
      priceInDollars: price.priceInDollars,
      isTopUp: price.isTopUp,
      rate: price.rate,
      tax: price.tax,
      discount: price.discount,
      active: price.active,
      benefits: price.benefits,
    };

    res.status(200).json(priceData);
  } catch (error) {
    if (logs) req.payload.logger.error({ err: `Error fetching price data: ${error}` });
    res.status(500).json({ error: 'Internal server error' });
  }
};
