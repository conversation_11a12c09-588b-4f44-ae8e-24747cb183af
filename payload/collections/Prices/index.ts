import type { CollectionConfig } from "payload/types";
import { anyone } from "../../access/anyone";
import { admins } from "../../access/admins";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";

export const Prices: CollectionConfig = {
  slug: "prices",
  admin: {
    useAsTitle: "packageName",
    defaultColumns: ["packageName", "numberOfCoins", "isTopUp"],
    group: "Admin",
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: readAccess,
    create: admins,
    update: admins,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs]
  },
  fields: [
    {
      name: "packageName",
      type: "text",
      label: "Name of the Package",
      required: true,
    },
    {
      name: "numberOfCoins",
      type: "number",
      label: "Number of Coins",
      required: true,
    },
    {
      name: "priceInDollars",
      type: "text",
      label: "Price in Dollars",
    },
    {
      name: "isTopUp",
      type: "checkbox",
      label: "Is Top Up",
      defaultValue: false,
      required: true,
    },
    {
      name: "rate",
      type: "number",
      label: "Rate of a single coin in Rupees",
      required: true,
    },
    {
      name: "tax",
      type: "number",
      label: "Tax (GST) in percentages",
      required: true,
      defaultValue: 18,
    },
    {
      name: "discount",
      type: "number",
      label: "Discount of the Package",
      defaultValue: 0,
      required: true,
    },
    {
      name: "active",
      type: "checkbox",
      label: "Activate",
      defaultValue: false,
      required: true,
    },
    {
      name: "benefits",
      type: "textarea",
      label: "Benefits",
      required: false,
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
