
import { Access, PayloadRequest } from 'payload/types';
import { User } from '../../../../server/payload-types'
import payload from 'payload';

const readAccess: any = async({ req }: { req: PayloadRequest}) => {
  const user = req.user as User | undefined;
  const url = req.headers.referer || '';
  const isAdmin = user?.role?.includes('admin');
  const isDashboard = url.includes('/admin/collections/courses');

    //check to prevent api security 
    if(req.originalUrl === '/api/courses')
      {
        return false
      }
      
      if (!user) {
        if (isDashboard) {
          return false;
        }
        return false
      }

  if (!user) {
    if (isDashboard) {
      return false;
    }
    return {
      status: {
        equals: 'approved',
      },
    };
  }


  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return {
        or: [
          { needsApproval: { equals: true } }, // Items needing approval
          {
            or: [{status: {equals: "approved"}}]
          },
          { 
            and: [
              { user: { equals: user.id } }, // Admin's own items
              { 
                or: [
                  { status: { equals: "pending" } },
                  { status: { equals: "approved" } },
                ]
              }
            ]
          }
        ],
      };
    }
    else {
      const userCourse = await payload.find({
        collection: 'courses',
        where: {
          user: {
            equals: user.id,
          },
        },
        limit: 0,
      });

      return {
        id: {
          in: userCourse.docs.map((course: any) => course.id),
        },
      };
    }
  }
  else {
    return {
      status: {
        equals: 'approved',
      },
    };
  }
};

export default readAccess;
