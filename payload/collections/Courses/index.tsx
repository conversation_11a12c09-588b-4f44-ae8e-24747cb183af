import { CollectionConfig } from "payload/types";
import { admins } from "../../access/admins";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "./hooks/auditlog";
import { purchaseCourse } from "../../endpoints/purchaseCourse";
import beforeDeleteHook from "./hooks/beforeDelete";
import { Course_Types } from "../../../constants";
import fieldReadAccess from "../../access/fieldReadAccess";
import autoGenerateSlug from "./hooks/generateSlug";
import { coursesCacheHooks } from "../../hooks/cache-invalidation";

const Courses: CollectionConfig = {
  slug: "courses",
  admin: {
    useAsTitle: "title",
    group: "Community",
    defaultColumns: ["title", "status", "level"],
    hidden: ({ user }) => user.role !== "admin",
  },

  access: {
    read: readAccess,
    create: adminsOrLoggedIn,
    update: adminsOrLoggedIn,
    delete: adminsOrLoggedIn,
  },

  endpoints: [
    {
      path: "/purchaseCourse",
      method: "post",
      handler: purchaseCourse,
    },
  ],

  hooks: {
    beforeChange: [addAuditLogs],
    beforeDelete: [beforeDeleteHook],
    afterChange: coursesCacheHooks.afterChange,
    afterDelete: coursesCacheHooks.afterDelete,
  },

  fields: [
    {
      name: "title",
      label: "Course Title",
      type: "text",
      required: true,
      admin: {
        description: "Title of the Course",
      },
      index: true,
    },
   
    {
      name: "description",
      label: "Course Description",
      type: "textarea",
      required: true,
      admin: {
        description:
          "Short description about the course by which user will get clear understanding about the course, also can use markup for increase readability.",
      },
      index: true,
    },
    {
      name: "cost",
      label: "Course Price",
      type: "number",
      required: true,
      admin: {
        description: "Mention course price in Joule (1 rupee = 1 credit)",
      },
    },
    {
      name: "level",
      label: "Course Level",
      type: "select",
      defaultValue: "beginner",
      admin: {
        description:
          "To correctly categorize the course in level will helps learners choose the course that best fits their current skill level.",
      },
      options: [
        {
          label: "Beginner",
          value: "beginner",
        },
        {
          label: "Intermediate",
          value: "intermediate",
        },
        {
          label: "Advance",
          value: "advance",
        },
      ],
    },
    {
      name: "status",
      label: "Course Status",
      type: "select",
      defaultValue: "pending",
      admin: {
        description: "Status will be approved by admin only.",
      },
      access: {
        create: admins,
        read: () => true,
        update: admins,
      },
      options: [
        {
          label: "Pending verification",
          value: "pending",
        },
        {
          label: "Approved",
          value: "approved",
        },
        {
          label: "Denied",
          value: "denied",
        },
      ],
    },
    {
      type: "checkbox",
      name: "needsApproval",
      label: "Send for Approval",
      admin: {
        description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      required: false,
      unique: true,
      hooks: {
        beforeValidate: [autoGenerateSlug], // Use the field-level hook here for real-time slug generation
       
      },
      admin: {
        hidden: true,
        readOnly: true,
        position: "sidebar", // Make it read-only so users cannot change it manually
      },
    },
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      admin: {
        description:
          "Highlight the course in the list by marking it as featured. (Allowed to admin only)",
      },
      access: {
        read: fieldReadAccess,
        create: admins,
        update: admins,
      },
    },
    {
      name: "attachment",
      label: "Upload Course Thumbnail ",
      type: "relationship",
      required: false,
      relationTo: "media",
      hasMany: true,
      admin: {
        description:
          "Select Image which you want to display as thumbnail for the course.",
      },
    },
    {
      name: "time",
      label: "Course Time Period",
      type: "select",
      defaultValue: "1 week",
      admin: {
        description:
          "Mention time required to complete the course by the leaners.",
      },
      options: [
        {
          label: "1 day",
          value: "1day",
        },
        {
          label: "2 days",
          value: "2days",
        },
        {
          label: "4 days",
          value: "4days",
        },
        {
          label: "1 week",
          value: "1week",
        },
        {
          label: "2 week",
          value: "2week",
        },
        {
          label: "1 month",
          value: "1month",
        },
        {
          label: "Above 1 month",
          value: "above 1month",
        },
      ],
    },
    {
      name: "type",
      label: "Type",
      type: "select",
      hasMany: true,
      options: Course_Types.map(({ label, value }) => ({ label, value })),
      required: true,
      admin: {
        description:
          "Select course type like web development, programming, data science etc.",
      },
    },
    {
      name: "pdf",
      label: "Upload Related Document (pdf format)",
      type: "upload", // Use the 'upload' field type to handle file uploads
      relationTo: "product_files", // You need a media collection to handle file uploads
      required: true,
      admin: {
        description: "Upload Course file",
      },
    },
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      access: {
        read: fieldReadAccess
      },
      hasMany: false,
      admin: {
        allowCreate: false,
        readOnly: true,
      },
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};

export default Courses;
