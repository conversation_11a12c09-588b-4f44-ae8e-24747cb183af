import { CollectionBeforeDeleteHook } from "payload/types";
import CustomError from "../../../access/customError";
import { toast } from "sonner";

const beforeDeleteHook: CollectionBeforeDeleteHook = async ({ req, id }) => {
  const { user } = req;
  const { role } = user;

  // Fetch the document to delete
  const course = await req.payload.findByID({
    collection: "courses",
    id,
  });

  // Check the bounty status
  if (course.status === "approved") {
    if (!role.includes("admin")) {
      throw new CustomError("Only admin can delete approved Course.", 403);
    }
  } else {
    if (!user) {
      throw new CustomError("You must be logged in to delete a Course.", 403);
    }
  }
};

export default beforeDeleteHook;
