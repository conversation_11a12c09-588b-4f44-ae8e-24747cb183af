import { Access, PayloadRequest } from 'payload/types';
import { User } from '../../../../server/payload-types';
import payload from 'payload';

const readAccess: Access = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  if (!user) {
    // If user is not logged in, only show approved bounties
    return false;
  }

  const url = req.headers.referer || '';
  const isAdmin = user?.role?.includes('admin');
  const isDashboard = url.includes('/admin/collections/purchases');

  if(req.originalUrl === '/api/purchases')
    {
      return false
    }

  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return true;
    } else {
      // Fetch only the user's purchases
      const userPurchases = await payload.find({
        collection: 'purchases',
        where: {
          user: {
            equals: user.id,
          },
        },
        limit: 0, // Setting limit to 0 will fetch all documents
      });

      return {
        id: {
          in: userPurchases.docs.map((purchase: any) => purchase.id),
        },
      };
    }
  } else {
    // Frontend logic: show only approved bounties
    return true;
  }
};

export default readAccess;
