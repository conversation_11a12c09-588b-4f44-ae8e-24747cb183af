import { purchaseBounty } from '../../endpoints/purchaseBounty';
import { purchaseProduct } from '../../endpoints/purchaseProduct';
import { purchaseCourse } from  '../../endpoints/purchaseCourse';
import type { CollectionConfig } from 'payload/types';
import readAccess from './access/readAccess';
import { adminsOrLoggedIn } from '../../../payload/access/adminsOrLoggedIn';
import { admins } from '../../../payload/access/admins';
// import { purchasePrompt } from '@/payload/endpoints/purchasePrompt';
import { purchasePrompt } from '../../endpoints/purchasePrompt';
import { addAuditLogs } from '../globalHooks/auditlog';

export const Purchases: CollectionConfig = {
  slug: 'purchases',
  admin: {
    useAsTitle: 'purchaseType',
    group: "User",
    defaultColumns: ["purchaseType", " prompt " , "product", "bounty" , "course"],
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: readAccess,
    create: ({ req }) => req.user.role === 'admin',
    update: admins,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs]
  },
  endpoints: [
    {
      path: '/purchaseProduct',
      method: 'post',
      handler: purchaseProduct,
    },
    {
      path: '/purchaseBounty',
      method: 'post',
      handler: purchaseBounty,
    },
    {
      path: '/purchasecourse',
      method: 'post',
      handler: purchaseCourse,
    },
    {
      path: '/purchasePrompt',
      method: 'post',
      handler: purchasePrompt,
    }
  ],
  fields: [
    {
      name: 'purchaseType',
      label: 'Purchase Type',
      type: 'radio',
      required: true,
      admin: {
        position: 'sidebar',
      },
      options: [
        {
          label: 'Bounty Purchase',
          value: 'bounty',
        },
        {
          label: 'Assets Purchase',
          value: 'assets',
        },
        {
          label: 'Course Purchase',
          value: 'course',
        },
        {
          label: 'Prompt Purchase',
          value: 'prompt',
        },
      ],
    },
    {
      name: 'prompt',
      label: 'Prompt Purchased',
      type: 'relationship',
      relationTo: 'rapps',
      // required: true,
      admin: {
        condition: (data) => data.purchaseType === 'prompts',
      },
    },
    {
      name: 'product',
      label: 'Product Purchased',
      type: 'relationship',
      relationTo: 'products',
      // required: true,
      admin: {
        condition: (data) => data.purchaseType === 'assets',
      },
    },
    {
      name: 'bounty',
      label: 'Bounty Purchased',
      type: 'relationship',
      relationTo: 'bounties',
      // required: true,
      admin: {
        condition: (data) => data.purchaseType === 'bounty',
      },
    },
    {
      name: 'course',
      label: 'Course Purchased',
      type: 'relationship',
      relationTo: 'courses',
      // required: true,
      admin: {
        condition: (data) => data.purchaseType === 'course',
      },
    },
    {
      name: 'user',
      label: 'UserName',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
};
