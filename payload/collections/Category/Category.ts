import { CollectionConfig } from "payload/types";
// import adminsAndUser from "";
import { anyone } from "../../access/anyone";
import { admins } from "../../access/admins";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";
import { categoryCacheHooks } from "../../hooks/cache-invalidation";

export const Category: CollectionConfig = {
  slug: "category",
  admin: {
    hidden: ({ user }) => user.role !== "admin",
    group: "Admin",
    useAsTitle: "label",
  },
  access: {
    read: readAccess,
    create: admins,
    update: admins,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs],
    afterChange: categoryCacheHooks.afterChange,
    afterDelete: categoryCacheHooks.afterDelete,
  },
  fields: [
    {
      name: "label",
      type: "text",
      required: true,
      label: "Category Name",
    },
    {
      name: "value",
      type: "text",
      label: "Category Value",
      required: true,
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
};
