import { CollectionConfig } from "payload/types";

export const Media: CollectionConfig = {
  slug: "media",
  access: {
    // read: () => true,
    read: async ({ req }) => {
      const { user } = req;

      if(req.originalUrl === '/api/media')
        {
          return false
      } 

      if (!user) {
        // If user is not logged in, show all images
        return true;
      }
    
      const url = req.headers.referer || "";
      const isDashboard = url.includes("/admin");
    

      // Allow admins to see all media files

      if(isDashboard){
        if (user && user.role === "admin") {
          return true;
        }
  
        // Allow users to see only their own media files
        if (user) {
          return {
            user: {
              equals: user.id,
            },
          };
        }
      }
      
      return true;
    },
  },
  admin: {
    hidden: ({ user }) => user.role !== "admin",
    group: "Admin",
  },
  upload: {
    disableLocalStorage: true,
    staticURL: process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2 || "asset.rentprompts.com",
    staticDir: "media",
    imageSizes: [
      {
        name: "thumbnail",
        width: 400,
        height: 300,
        position: "centre",
      },
      {
        name: "card",
        width: 768,
        height: 1024,
        position: "centre",
      },
      {
        name: "tablet",
        width: 1024,
        height: undefined,
        position: "centre",
      },
    ],
    mimeTypes: ["image/*"],
  },
  fields: [
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      hasMany: false,
      admin: {
        condition: () => false,
      },
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
    },
  ],
};
