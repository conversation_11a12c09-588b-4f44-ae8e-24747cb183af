import type { CollectionConfig } from 'payload/types';
import { adminsOrLoggedIn } from '../../../payload/access/adminsOrLoggedIn';
import { admins } from '../../../payload/access/admins';
import { purchaseRapps } from '../../endpoints/purchaseRapps';
import readAccess from './access/readAccess';
import { addAuditLogs } from '../globalHooks/auditlog';

export const RappsPurchase: CollectionConfig = {
  slug: 'rappsPurchases',
  admin: {
    group: "User",
    useAsTitle: "rapps", 
    defaultColumns: [ "rapps"],
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    read: readAccess,
    create: ({ req }) => req.user.role === 'admin',
    update: admins,
    delete: admins,
  },
  endpoints: [
    {
      path: '/purchaseRapps',
      method: 'post',
      handler: purchaseRapps,
    },
  ],
  hooks:{
    beforeChange:[addAuditLogs]
  },
  fields: [
    {
      name: 'rapps',
      label: 'Rap<PERSON> Purchased',
      type: 'relationship',
      relationTo: 'rapps',
      // required: true,
    //   admin: {
    //     condition: (data) => data.purchaseType === 'rapps',
    //   },
    },
    {
      name: 'user',
      label: 'UserName',
      type: 'relationship',
      relationTo: 'users',
      required: true,
    },
  ],
};