import { BeforeChangeHook } from "payload/dist/collections/config/types";


export const clearAdminReviewOnStatusChange: BeforeChangeHook<any> = async ({ data, originalDoc }) => {
    if (originalDoc?.status === "denied" && (data.status === "pending" || data.status === "approved")) {
      return {
        ...data,
        adminReview: "", // Clear the adminReview field
      };
    }
    return data;
  };