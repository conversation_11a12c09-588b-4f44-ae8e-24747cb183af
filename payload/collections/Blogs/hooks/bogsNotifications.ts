// import { blogsEmailTemplate } from "@/payload/utilities/emailTemplates/blogsEmailTemplates";
// import sendEmail from "@/payload/utilities/sendEmail";
import { blogsEmailTemplate} from "../../../utilities/emailTemplates/blogsEmailTemplates";
import sendEmail from "../../../utilities/sendEmail";
import { PayloadRequest } from "payload/types";

export const blogsNotifications = async ({
  doc,
  previousDoc,
  req,
  operation,
}: {
  doc: any;
  previousDoc: any;
  req: PayloadRequest;
  operation: "create" | "update";
}) => {
  const logoUrl = `https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/RentpromptsIcon.png`;
  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL;
  if (operation !== "update" || !previousDoc) {
    // console.log("New bounty creation detected. Skipping email.");
    return;
  }

  try {
    const blogsOwner = await req.payload.findByID({
      collection: "users",
      id: doc?.user?.id ? doc?.user?.id : doc?.user,
    });
    console.log("BlogsOwner", blogsOwner);
    console.log("doc.user", doc?.user);
    console.log("doc ===>>", doc);
    if (!blogsOwner || typeof blogsOwner !== "object") {
      console.warn("blog owner not found or invalid format.");
      return;
    }

    const userEmail = blogsOwner.email;
    const userName = blogsOwner.user_name || "User";
    const htmlContent = blogsEmailTemplate({
      userName,
      BlogsTitle: doc.title,
      status: doc.status,
      adminReview: doc.adminReview,
      slug: doc.slug,
      logoUrl,
      baseUrl,
      id: doc.id,
    });
    // STATUS CHANGED BY ADMIN THEN BOUNTY OWNER GET EMAIL
    if (doc.status !== previousDoc?.status) {
      if (!["approved", "denied"].includes(doc.status)) return;

      try {
        await req.payload.create({
          collection: "notifications",
          data: {
            user: doc.user,
            message: `Your blog "${doc?.title}" has been ${doc?.status.toUpperCase()}. ${
              doc?.status === "denied"
                ? `Reason: ${doc?.adminReview}`
                : `You can now view it live.`
            }`,
            link: `/blog/${doc?.slug || ""}`,
          },
        });
      } catch (error) {
        console.log("notification is not created :", error);
      }
      await sendEmail({
        to: userEmail,
        subject: `Your Blog Has Been ${doc?.status === "denied" ? "Rejected" : "Approved"}!`,
        html: htmlContent,
      });
    } else {
      // Handle other status changes or default behavior
      console.log("No status change detected, skipping email.");
    }
  } catch (error) {
    console.error("❌ Error in sendApprovalEmail:", error);
  }
};
