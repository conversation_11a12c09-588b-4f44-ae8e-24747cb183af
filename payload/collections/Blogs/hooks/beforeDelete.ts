import { CollectionBeforeDeleteHook } from "payload/types";
import CustomError from "../../../access/customError";

const beforeDeleteHook: CollectionBeforeDeleteHook = async ({ req, id }) => {
  const { user } = req;
  const { role } = user;

  const blog = await req.payload.findByID({
    collection: "blogs",
    id,
  });

  // Check the bounty status
  if (blog.status === "approved") {
    if (!role.includes("admin")) {
      throw new CustomError("Only admin can delete approved blog.", 403);
    }
  } else {
    if (!user) {
      throw new CustomError("You must be logged in to delete a blog.", 403);
    }
  }
};

export default beforeDeleteHook;

