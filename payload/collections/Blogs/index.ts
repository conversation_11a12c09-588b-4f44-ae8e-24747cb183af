import { BeforeChangeHook } from 'payload/dist/collections/config/types';
import { CollectionConfig, FieldHook } from "payload/types";
import { addAuditLogs } from "./hooks/auditlog";
import { BLOG_TAGS } from "../../../constants";
import { slateEditor } from "@payloadcms/richtext-slate";
import { admins } from "../../access/admins";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import isAdminOrHasAccess from "./access/isAdminOrHasAccess";
import beforeDeleteHook from "./hooks/beforeDelete";
import fieldReadAccess from "../../access/fieldReadAccess";
import slugify from "slugify";
import { getBlogById } from './access/getBlog';
import  { getAllBlogs } from '../../endpoints/get-all-blogs';
import { getFeaturedBlogs } from '../../endpoints/get-featured-blogs';
import { clearAdminReviewOnStatusChange } from './hooks/beforeChange';
import { blogsCacheHooks } from "../../hooks/cache-invalidation";

const autoGenerateSlug: FieldHook = ({ data, value }) => {
  if (data?.title && !value) {
    return slugify(data.title, { lower: true, strict: true });
  }
  return value; // Return the current value if no title or value already exists
};

const generateUniqueSlug: BeforeChangeHook = async ({ data, req, originalDoc }) => {
  if (data.slug) {
    let slug = slugify(data.slug, { lower: true, strict: true });

    // If we are updating the document, and the slug hasn't changed, return early
    if (originalDoc && originalDoc.slug === slug) {
      return data;
    }

    // Check if a document with the same slug already exists
    const existingDoc = await req.payload.find({
      collection: 'blogs',
      where: { slug: { equals: slug } },
    });

    if (existingDoc.totalDocs > 0) {
      // Append a unique identifier to the slug if it already exists
      slug = `${slug}-${Date.now()}`;
    }

    // Assign the unique slug to the data object
    data.slug = slug;
  }

  return data;
};

export const Blogs: CollectionConfig = {
  slug: "blogs",
  admin: {
    useAsTitle: "title",
    group: "Community",
    defaultColumns: ["title", "status"],
    livePreview: {
      url: ({ data, documentInfo }) => {
        return `${process.env.PAYLOAD_PUBLIC_SITE_URL}/livepreview/${documentInfo.slug}?id=${data.id}`;
      },
    },
    hidden: ({ user }) => user.role !== "admin",
  },
  endpoints: [
    {
      path: "/getBlogById/:blogId",
      method: "get",
      handler: getBlogById,
    },
    {
      path: "/getFeaturedBlogs",
      method: "get",
      handler: getFeaturedBlogs,
    },
    {
      path: "/getAllBlogs",
      method: "get",
      handler: getAllBlogs,
    },
  ],
  access: {
    read: isAdminOrHasAccess,
    create: adminsOrLoggedIn,
    update: adminsOrLoggedIn,
    delete: adminsOrLoggedIn,
  },

  hooks: {
    beforeChange: [clearAdminReviewOnStatusChange,addAuditLogs,generateUniqueSlug],
    beforeDelete: [beforeDeleteHook],
    afterChange: blogsCacheHooks.afterChange,
    afterDelete: blogsCacheHooks.afterDelete,
  },
  fields: [
    {
      name: "title",
      label: "Title of the Blog",
      type: "text",
      required: true,
      index:true,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      required: false,
      unique: true,
      hooks: {
        beforeValidate: [autoGenerateSlug], // Use the field-level hook here for real-time slug generation
      },
      admin: {
        hidden: true,
        readOnly: true, // Make it read-only so users cannot change it manually
        position: "sidebar",
      },
    },
    {
      name: "content",
      type: "textarea", 
      label: "Give a brief of (30–40 words) synopsis of the blog.",
      required: true,
      index:true,
    },
    {
      name: "richText",
      label: "Write your blog",
      type: "richText",
      editor: slateEditor({}),
      required:true,
    },
    {
      name: "status",
      label: "Blog Status",
      type: "select",
      defaultValue: "pending",
      access: {
        create: admins,
        read: () => true,
        update: admins,
      },
      options: [
        {
          label: "Pending verification",
          value: "pending",
        },
        {
          label: "Approved",
          value: "approved",
        },
        {
          label: "Denied",
          value: "denied",
        },
      ],
      admin:{
        description:"Status will be approved by admin only."
      }
    },
    {
      name: "adminReview",
      label: "Admin Review",
      type: "textarea",
      admin: {
        condition: (data) => data.status === "denied", // Show only if status is "denied"
        description: "Provide a reason for denying this blog.",
      },
    },
    {
      type: "checkbox",
      name: "needsApproval",
      label: "Send for Approval",
      admin: {
        description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
    },
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      admin:{
        description:"Highlight the blog in the list by marking it as featured.(Only Admin can edit)",
        // readOnly :true,
      },
      access: {
        read:fieldReadAccess,
        create:admins,
        update: admins,
      },
    },
    {
      name: "time",
      label: "Mention reading time for this blog (in mins)",
      type: "number",
      required: true,
    },
    {
      name: "images",
      type: "array",
      label: "Add thumbnail image for this blog",
      minRows: 1,
      maxRows: 4,
      required: true,
      index:true,
      labels: {
        singular: "Image",
        plural: "Images",
      },
      fields: [
        {
          name: "image",
          type: "upload",
          relationTo: "media",
          required: true,
          index:true,
        },
      ],
    },
    {
      name: "tags",
      label: "Tag",
      type: "select",
      options: BLOG_TAGS.map(({ label, value }) => ({ label, value })),
      required: true,
      admin:{
        description: "Select tag best suitable for the blog for better user understanding."
      }
    },
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      access: {
        read:fieldReadAccess,
      },
      hasMany: false,
      admin: {
        allowCreate: false,
        readOnly: true,
      },
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
  timestamps: true,
};
