import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const isAdminOrHasAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/blogs");

    //check to prevent api security 
    if(req.originalUrl === '/api/blogs')
      {
        return false
      }

  if (!user) {
    if (isDashboard) {
      return false;
    }
    return {
      status: {
        equals: "approved",
      },
    };
  }


  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) {
      return {
        or: [
          { needsApproval: { equals: true } }, // Items needing approval
          {
            or: [{status: {equals: "approved"}}]
          },
          { 
            and: [
              { user: { equals: user.id } }, // Admin's own items
              { 
                or: [
                  { status: { equals: "pending" } }, 
                  { status: { equals: "approved" } },
                ]
              }
            ]
          }
        ],
      };
    } else {
      const userBlogs = await payload.find({
        collection: "blogs",
        where: {
          user: {
            equals: user.id,
          },
        },
        limit: 0, // Setting limit to 0 will fetch all documents
      });

      return {
        id: {
          in: userBlogs.docs.map((blog: any) => blog.id),
        },
      };
    }
  } else {
    return {
      status: {
        equals: "approved",
      },
    };
  }
};

export default isAdminOrHasAccess;
