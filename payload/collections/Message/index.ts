// payload/collections/messages.ts
import { CollectionConfig } from "payload/types";

export const Messages: CollectionConfig = {
  slug: "messages",
  admin: { group: "Admin" ,
    hidden: ({ user }) => user.role !== "admin",

  },
  fields: [
    {
      name: "chatRoom",
      type: "relationship",
      relationTo: "chatrooms",
      required: true,
    },
    {
      name: "sender",
      type: "relationship",
      relationTo: "users",
      required: true,
    },
    { name: "text", type: "textarea", required: false },
    { name: "media", type: "upload", relationTo: "media", required: false },
    { name: "file", type: "upload", relationTo: "product_files", required: false },
  ],
  access: {
    read: async ({ req }) => {
      const user = req.user;
      if (!user) return false;

      return {
        chatRoom: { in: await getUserChatRoomIDs(req.payload, user.id) },
      };
    },
    create: ({ req }) => Boolean(req.user),
  },
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        // Custom validation to ensure either text or media is present
        if (operation === "create") {
          if (!data.text && !data.media && !data.file) {
            throw new Error("Either text or media must be provided");
          }
        }
        return data;
      },
    ],
  },
};

const getUserChatRoomIDs = async (payload, userId) => {
  const chatRooms = await payload.find({
    collection: "chatrooms",
    where: {
      or: [{ owner: { equals: userId } }, { applicant: { equals: userId } }],
    },
    limit: 100,
  });

  return chatRooms.docs.map((room) => room.id);
};
