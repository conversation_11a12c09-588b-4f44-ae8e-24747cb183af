import { CollectionConfig } from "payload/types";
import { modelType } from "../../fields/modelType";
import { listing } from "../../fields/list";
import { rappsLikes } from "../../endpoints/rappsLikes";
import readAccess from "./access/readAccess";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import { admins } from "../../access/admins";
import { extractVariablesInBrackets } from "../../../app/utils/extractVariables";
import { getRappRating, rappRating } from "../../endpoints/rappRating";
import { purchasePrompt } from "../../endpoints/purchasePrompt";
import { rappPurchased } from "../../endpoints/rappPurchased";
import { addAuditLogs } from "../globalHooks/auditlog";
import { clearAdminReviewOnStatusChange } from "./component/beforeChange";
import { rappsCacheHooks } from "../../hooks/cache-invalidation";
//import { pdfimport } from "../../endpoints/pdf-import";

//const mongoose = require("mongoose");

export const Rapps: CollectionConfig = {
  slug: "rapps",
  // versions: {
  //   drafts: {
  //     autosave: true,
  //   },
  // },
  admin: {
    useAsTitle: "name",
    group: "Listing",
    livePreview: {
      url: ({ data, documentInfo }) => {
        return `${process.env.PAYLOAD_PUBLIC_SITE_URL}/livepreview/${documentInfo.slug}?id=${data.id}`;
      },
    },
    defaultColumns: ["name", "description", "status"],
    hidden: () => true,
  },
  access: {
    create: adminsOrLoggedIn,
    read: readAccess,
    update: adminsOrLoggedIn,
    delete: adminsOrLoggedIn,
  },
  hooks:{
    beforeChange:[addAuditLogs,clearAdminReviewOnStatusChange],
    afterChange: rappsCacheHooks.afterChange,
    afterDelete: rappsCacheHooks.afterDelete,
  },
  endpoints: [
    {
      path: "/rappsLikes/:rentproductId",
      method: "get",
      handler: rappsLikes,
    },
    {
      path: "/rappPurchased/:rappId",
      method: "get",
      handler: rappPurchased,
    },
    {
      path: "/livepreview/:id",
      method: "get",
      handler: async (req, res) => {
        const id = req.params.id;
        const rapp = await req.payload.findByID({ collection: "rapps", id });

        res.send({
          images: rapp.images,
          name: rapp.name,
          description: rapp.description,
          modelType: rapp.modelType,
        });
      },
    },
    {
      path: "/getRapp/:rentspaceId",
      method: "get",
      handler: async (req, res) => {
        const rentproductId = req.params.rentspaceId;
        const rapp: any = await req.payload.findByID({
          collection: "rapps",
          id: rentproductId,
          depth: 2,
        });
        // console.log(rapp);
        const systemVariables = extractVariablesInBrackets(rapp.systemprompt);
        const userVariables = extractVariablesInBrackets(rapp.prompt);
        const negativeVariables = extractVariablesInBrackets(
          rapp.negativeprompt
        );
        res.json({
          likes: rapp.likes,
          images: rapp.images,
          name: rapp.name,
          slug: rapp.slug,
          modelType: rapp.modelType,
          modelName: rapp.model?.name,
          id: rapp.id,
          description: rapp.description,
          price: rapp.price,
          totalCost: rapp.totalCost,
          user: rapp.creator.user_name,
          userId: rapp.creator.id,
          systemVariables: systemVariables,
          userVariables: userVariables,
          negativeVariables: negativeVariables,
          creatorName: rapp.creator.user_name,
          creatorId: rapp.creator.id,
          creatorProfile: rapp.creator.profileImage,
          imageinput: rapp.imageinput,
          PDFinput: rapp.PDFinput,
          isFeatured: rapp.isFeatured,
          rating: rapp.rating,
          getprompt: rapp.getprompt,
          userRating: rapp.userRatings,
          // comment: rapp.comment,
        });
      },
    },
    {
      path: "/comment/:rappId",
      method: "post",
      handler: async (req, res) => {
        const { comments } = req.body;
        const { rappId } = req.params;
        const { payload, user } = req;

        try {
          const rapp: any = await payload.findByID({
            collection: "rapps",
            id: rappId,
          });

          if (!rapp) {
            return res.status(404).json({ error: "Rapp not found." });
          }

          // Extract userComments from the rapp document
          const userIds =
            (rapp?.userComment as { user: { id: string }; comment: {} }[])?.map(
              (commentedUser) => ({
                user: commentedUser?.user?.id,
                comment: commentedUser?.comment, // Update to match your schema
              })
            ) ?? [];

          // Check if the current user has already commented

          const isCommented = userIds.some(
            (commentedUser) => commentedUser.user === user?.id
          );

          // Update or add comment
          const updatedComment = isCommented
            ? userIds.map((commentedUser) =>
                commentedUser.user === user?.id
                  ? { ...commentedUser, comment: comments }
                  : commentedUser
              )
            : [...userIds, { user: user?.id, comment: comments }];

          // Ensure updatedComment matches the expected structure for userComment
          const comment = await payload.update({
            collection: "rapps",
            id: rappId,
            data: {
              userComment: updatedComment,
            },
          });

          return res.status(201).json({ success: true, comment });
        } catch (error) {
          console.error("Error updating comment:", error);
          return res
            .status(500)
            .json({ error: error.message || "Failed to comment." });
        }
      },
    },

    {
      path: "/comment/:rappId",
      method: "get",
      handler: async (req, res) => {
        const { rappId } = req.params;
        const { payload, user } = req;

        try {
          const rapp: any = await payload.findByID({
            collection: "rapps",
            id: rappId,
          });
          if (!rapp) {
            return res.status(404).json({ error: "Rapp not found." });
          }

          // Extract userComments from the rapp document
          const userComments =
            (rapp?.userComment as { user: { id: string }; comment: {} }[]) ??
            [];

          return res
            .status(200)
            .json({ success: true, comments: userComments });
        } catch (error) {
          console.error("Error retrieving comments:", error);
          return res
            .status(500)
            .json({ error: error.message || "Failed to retrieve comments." });
        }
      },
    },

    {
      path: "/getRappSlug/:slug",
      method: "get",
      handler: async (req, res) => {
        const slug = req.params.slug;
        try {
          const rapp: any = await req.payload.find({
            collection: "rapps",
            where: {
              slug: {
                equals: slug, // Match the slug in the collection
              },
            },
            depth: 2,
          });

          // Check if the Rapp was found
          if (!rapp || rapp.totalDocs === 0) {
            return res.status(404).json({ message: "Rapp not found" });
          }

          const rappData = rapp.docs[0];

          const systemVariables = extractVariablesInBrackets(rappData.systemprompt);
          const userVariables = extractVariablesInBrackets(rappData.prompt);
          const negativeVariables = extractVariablesInBrackets(
            rappData.negativeprompt
          );

          const systemVar = Array.isArray(rappData.systemVariables) && rappData.systemVariables.length > 0 
            ? rappData.systemVariables 
            : systemVariables;

          const userVar = Array.isArray(rappData.promptVariables) && rappData.promptVariables.length > 0 
            ? rappData.promptVariables 
            : userVariables;

          const negativeVar = Array.isArray(rappData.negativeVariables) && rappData.negativeVariables.length > 0 
            ? rappData.negativeVariables 
            : negativeVariables;


          res.json({
            likes: rappData.likes,
            purchases: rappData.purchases,
            images: rappData.images,
            name: rappData.name,
            slug: rappData.slug,
            modelType: rappData.modelType,
            modelName: rappData.model?.name,
            id: rappData.id,
            description: rappData.description,
            price: rappData.price,
            totalCost: rappData.totalCost,
            user: rappData.creator.user_name,
            userId: rappData.creator.id,
            systemVariables: systemVar,
            userVariables: userVar,
            negativeVariables: negativeVar,
            creatorName: rappData.creator.user_name,
            creatorId: rappData.creator.id,
            creatorProfile: rappData.creator.profileImage,
            imageinput: rappData.imageinput,
            imageinputopt: rappData.imageinputopt,
            isFeatured: rappData.isFeatured,
            rating: rappData.rating,
            affiliated_with: rappData.affiliated_with,
            PDFinput: rappData.PDFinput,
            getprompt: rappData.getprompt,
            negativeprompt: rappData.negativeprompt,
            settings: rappData.settings,
            systemprompt: rappData.systemprompt,
            promptpurchase: rappData.promptpurchase,
            promptcost: rappData.promptcost,
            createdAt: rappData.createdAt,
            status:rappData.status
          });
        } catch (error) {
          res
            .status(500)
            .json({ message: "An error occurred", error: error.message });
        }
      },
    },
    {
      path: "/rating/:rappId",
      method: "post",
      handler: rappRating,
    },
    {
      path: "/rating/:rappId",
      method: "get",
      handler: getRappRating,
    },
    {
      path: "/purchasePrompt",
      method: "post",
      handler: purchasePrompt,
    },
  ],

  fields: [
    modelType,
    // model
    {
      type: "relationship",
      relationTo: "models",
      name: "model",
      label: "Generation Model",
      required: true,
      admin: {
        allowCreate: false,
        description: "Select the AI model ",
      },
      filterOptions: ({ data }) => {
        return {
          type: {
            equals: data.modelType,
          },
        };
      },
    },
    // key
    {
      name: "key",
      label: "Model key to run this Rapp",
      type: "select",
      required: true,
      defaultValue: "test",
      options: [
        {
          label: "Test",
          value: "test",
        },
        {
          label: "Prod",
          value: "prod",
        },
      ],
    },
    // systemprompt
    {
      name: "systemprompt",
      type: "textarea",
      label: "System Prompt",
      admin: {
        placeholder: "give instructions to the model",
        description: "Set context for the model",
        // condition: (data) => data.modelType === "text",
      },
      // required: true,
    },
    // prompt
    {
      type: "textarea",
      name: "prompt",
      label: "User Prompt",
      // required: true,
      admin: {
        placeholder:
          "a cute minimalistic simple [hedgehog] side profile Clipart",
        description: "Put any variables in [square brackets]",
      },
    },
    // negativeprompt
    {
      type: "textarea",
      name: "negativeprompt",
      label: "Negative Prompt",
      // required: true,
      admin: {
        placeholder: "give negative prompt",
        description: "Add something to neglect",
        // condition: (data) => data.modelType === "image",
      },
    },
    // average rating
    { 
      name: "rating",
      label: "Rating",
      type: "number",
      required: true,
      min: 0,
      max: 5,
      defaultValue: 0,
      admin: {
        description: "Rate between 1 and 5 stars",
        step: 0.1,
        hidden: true,
      },
    },
    // user ratings
    {
      name: "userRatings",
      label: "User Ratings",
      type: "array",
      admin: {
        hidden: true,
      },
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
        {
          name: "rating",
          type: "number",
          required: true,
          min: 0,
          max: 5,
        },
      ],
    },
    // user comments
    {
      name: "userComment",
      label: "user comment",
      type: "array",
      required: false,
      admin: {
        hidden: true,
      },  
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
        {
          name: "comment",
          type: "text",
          required: true,
        },
      ],
    },
    // status
    {
      name: "status",
      label: "Ai Apps Status",
      type: "select",
      defaultValue: "pending",
      required: true,
      access: {
        create: admins,
        read: () => true,
        update: admins,
      },
      options: [
        {
          label: "Approved",
          value: "approved",
        },
        {
          label: "Denied",
          value: "denied",
        },
        {
          label: "Pending verification",
          value: "pending",
        },
      ],
    },
    {
      name: 'approvedDate',
      label: "Approval Date",
      type: 'date',
      required: false,
      admin: {
        readOnly: true, 
      },
    },
    {
      name: "adminReview",
      label: "Admin Review",
      type: "textarea",
      admin: {
        condition: (data) => data.status === "denied", // Show only if status is "denied"
        description: "Provide a reason for denying this Raap.",
      },
    },
    // needsApproval only public rapp
    {
      type: "checkbox",
      name: "needsApproval",
      label: "Send for Approval",
      admin: {
        description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
      required: true,
    },
    // isFeatured (no need for private)
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      required: true,
      admin: {
        description:
          "Highlight the rapp in the list by marking it as featured.",
      },
      access: {
        read: admins,
      },
    },
    // imageinput (disables if model doesn't accept image)
    {
      type: "checkbox",
      name: "imageinput",
      label: "Allow user to send image as Input",
      admin: {
        description: "User will be able to give image as input to your Rapp",
        // condition: (data) => data.modelType === "image",
      },
      defaultValue: false,
      required: true,
    },
    {
      type: "checkbox",
      name: "imageinputopt",
      label: "Allow user to send image as Input",
      admin: {
        description: "User will be able to give image as input to your Rapp",
        // condition: (data) => data.modelType === "image",
      },
      defaultValue: false,
    },
    {
      type: "checkbox",
      name: "PDFinput",
      label: "Allow user to import PDF as Input",
      admin: {
        description: "User will be able to import PDF as input to your Rapp",
        // condition: (data) => data.modelType === "image",
      },
      defaultValue: false,
      required: true,
    },
    {
      name: "affiliated_with",
      label: "Affiliated With",
      type: "text",
      required: false,
    },
    {
      name: "likes",
      type: "array",
      admin: {
        hidden: true,
      },
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
      ],
    },
    // {
    //   name: "category",
    //   type: "select",
    //   label: "Category",
    //   hasMany: true,
    //   options: Rapp_Category.map(({ label, value }) => ({ label, value })),
    //   defaultValue: "none",
    // },
    {
      name: "purchases",
      label: "Purchases",
      type: "array",
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
      ],
      admin: {
        hidden: true,
      },
    },
    {
      name: "category",
      type: "relationship",
      relationTo: "category",
      hasMany: false,
      label: "Category",
      // required: true,
      admin: {
        description: "Select categories for this Rapp",
      },
    },
    {
      name: "tags",
      type: "relationship",
      relationTo: "tags",
      hasMany: true,
      label: "Tags",
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    // textGroqSettings,
    // textReplicateSettings,
    // imageReplicateSettings,
    // ModelAudioFalSettings,
    // falAudioSettings,
    ...listing,
  ],
};
