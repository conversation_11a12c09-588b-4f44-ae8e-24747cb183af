import { BeforeChangeHook } from "payload/dist/collections/config/types";

export const clearAdminReviewOnStatusChange: BeforeChangeHook<any> = async ({
  data,
  originalDoc,
}) => {
  if (
    originalDoc?.approvedForSale === "denied" &&
    (data.approvedForSale === "pending" || data.approvedForSale === "approved")
  ) {
    return {
      ...data,
      adminReview: "", // Clear the adminReview field
    };
  }
  return data;
};