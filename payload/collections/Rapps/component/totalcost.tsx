import React, { useEffect } from "react";
import { useField, useFormFields } from "payload/components/forms";

const totalCost = ({ path }) => {
  const { value: ComputationCost } = useFormFields<any>(
    ([fields, dispatch]) => fields.computationcost
  );
  const { value: estimatedPrice } = useFormFields<any>(
    ([fields, dispatch]) => fields.price
  );
  const { value: commision } = useFormFields<any>(
    ([fields, dispatch]) => fields.commission
  );

  const { value, setValue } = useField<number>({ path });

  useEffect(() => {
    //setValue(ComputationCost + estimatedPrice + commision);
    if (commision !== null && commision !== undefined) {
      // If commission is not null/undefined, include it in the total cost
      setValue(ComputationCost + estimatedPrice + commision);
    } else {
      // If commission is null/undefined, exclude it from the total cost
      setValue(ComputationCost + estimatedPrice);
    }
  }, [ComputationCost, estimatedPrice, commision]);

  // return <div>Total Cost:{value}</div>;

  
  return (
    <div className="padding">
      {/* <h1>{value}</h1> */}
      <span className="field-label">Total Cost</span>
      <input
        type="number"
        className="input-custom-field py-12"
        value={value}
        required
      />
    </div>
  );
};

export default totalCost;