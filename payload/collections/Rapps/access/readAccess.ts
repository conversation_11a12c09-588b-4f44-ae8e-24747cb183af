import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";
import { equal } from "assert";

const readAccess: Access = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/rapps");
  
  //check to prevent api security 
  if(req.originalUrl === '/api/rapps')
  {
    return false
  }
  
  if (!user) {
    if (isDashboard) {
      return false;
    }
    return false
  }

  if (isDashboard) {
    if (isAdmin) {
      // <PERSON><PERSON> should see:
      // 1. Items that require approval
      // 2. Their own items that are pending or approved
      return {
        or: [
          { needsApproval: { equals: true } }, // Items needing approval
          {
            or: [{status: {equals: "approved"}}]
          },
          { 
            and: [
              { creator: { equals: user.id } }, // Admin's own items
              { 
                or: [
                  { status: { equals: "pending" } }, // Admin's pending items
                  { status: { equals: "approved" } }, // Admin's approved items
                ]
              }
            ]
          }
        ],
      };
    } else {
      const userRapps = await payload.find({
        collection: "rapps",
        where: {
          creator: {
            equals: user.id,
          },
        },
        limit: 0, // Fetch all documents
      });

      return {
        id: {
          in: userRapps.docs.map((rapp: any) => rapp.id),
        },
      };
    }
  } else {
    // Non-dashboard views
    return {
      status: {
        equals: "approved",
      },
    };
  }
};

export default readAccess;
