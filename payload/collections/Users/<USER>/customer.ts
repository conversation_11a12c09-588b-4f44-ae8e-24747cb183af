import type { PayloadHandler } from "payload/config";
import type { PayloadRequest } from "payload/types";
import Stripe from "stripe";

const logs = process.env.LOGS_STRIPE_PROXY === "1";
export const customerProxy: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  const { userID } = req.params;

  if (!req.user) {
    if (logs)
      req.payload.logger.error({
        err: `You are not authorized to access this customer`,
      });
    res
      .status(401)
      .json({ error: "You are not authorized to access this customer" });
    return;
  }

  if (!req.user?.stripeCustomerID) {
    const message = `No stripeCustomerID found for user ${userID}`;
    if (logs) req.payload.logger.error({ err: message });
    res.status(401).json({ error: message });
    return;
  }

  try {
    let response:
      | Stripe.Customer
      | Stripe.DeletedCustomer
      | Array<Stripe.Customer | Stripe.DeletedCustomer>
      | Stripe.ApiList<Stripe.Customer | Stripe.DeletedCustomer>;

    let customer: Stripe.Customer | Stripe.DeletedCustomer | null = null;
  } catch (error: unknown) {
    if (logs)
      req.payload.logger.error({ err: `Error using Stripe API: ${error}` });
    res.status(500).json({ error: `Error using Stripe API: ${error}` });
  }
};
