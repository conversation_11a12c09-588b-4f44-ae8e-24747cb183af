import { <PERSON><PERSON>onfig, <PERSON>Access, <PERSON>Hook } from "payload/types";
import adminsAndUser from "./access/adminAndUser";
import { anyone } from "../../access/anyone";
import { admins } from "../../access/admins";
import { customerProxy } from "./endpoints/customer";
import { publicProfile } from "./endpoints/publicProfile";
import { followDetails } from "../../endpoints/follow-details";
import { likesDetails } from "../../endpoints/likes-details";
// import Dashboard from "@/payload/components/views/Dashboard";
import { PrimaryActionEmailHtml } from "../../../components/emails/PrimaryActionEmail";
import { rappCount } from "./endpoints/rappCount";
import { productCount } from "./endpoints/productCount";
import { meapi } from "./endpoints/meapi";
import { User_Interests } from "../../../constants";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";
import updateUserAppliedBounties from "./hooks/updateUserAppliedBounties";

const userOrAdminAccess: FieldAccess = ({ req: { user }, id }) => {
  if (user && user.role === "admin") return true;
  if (user) {
    return user.id === id;
  }
  return false; // or handle the case when user is not authenticated
};

const setDefaultUsername: FieldHook = ({ value, data, originalDoc }) => {
  if (!value || value.trim() === "") {
    const email = data?.email || originalDoc?.email;
    if (email) {
      const username = email.split("@")[0];
      return username;
    }
  }
  return value;
};

// TODO: finish this function
// const setAge: FieldHook = ({ value, originalDoc }) => {
//   return 18;
// };

export const Users: CollectionConfig = {
  slug: "users",
  admin: {
    hidden: ({ user }) => user.role !== "admin",
    group: "Admin",
    useAsTitle: "user_name",
    defaultColumns: ["user_name", "email", "coinBalance"],
  },
  auth: {
    tokenExpiration: 60 * 60 * 24,
    maxLoginAttempts: 7,
    verify: {
      generateEmailHTML: ({ token }) => {
        return PrimaryActionEmailHtml({
          actionLabel: "verify your account",
          buttonText: "Verify Account",
          href: `${process.env.NEXT_PUBLIC_SERVER_URL}/verify-email?token=${token}`,
        });
      },
    },

    forgotPassword: {
      generateEmailHTML: ({ token, user }) => {
        return `<div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background: linear-gradient(135deg, #3730a3, #9d50bb);  color: #ffffff;">
            <h1 style="color: #ffffff; text-align: center;">Forgot Your Password?</h1>
            <p style="color: #f4f4f4; text-align: center; font-size: 16px; line-height: 1.6;">No worries! You can reset your password with a single click. Just hit the button below and follow the instructions.</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/reset-password?token=${token}" style="display: inline-block; padding: 15px 30px; background-color: #ff5e57; color: #ffffff; font-size: 18px; font-weight: bold; text-decoration: none; border-radius: 50px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);">Reset My Password</a>
            </div>
            <p style="color: #d1d1d1; text-align: center; font-size: 14px; margin-top: 20px;">If you didn’t request this, feel free to ignore this email.</p>
            <div style="text-align: center; margin-top: 40px;">
              <hr style="border: 0; height: 1px; background-color: #ffffff33; margin: 20px 0;" />
              <p style="font-size: 12px; color: #e0e0e0;">Having trouble? <a href="mailto:<EMAIL>" style="color: #ff5e57; text-decoration: none;">Contact Support</a></p>
              <p style="color: #ccc; font-size: 12px;">© 2024 RentPrompts. All rights reserved.</p>
            </div>
          </div> `;
      },
      generateEmailSubject: () => "Your Password Reset Request",
    },
  },

  access: {
    read: readAccess,
    create: anyone,
    update: adminsAndUser,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs],
    // afterChange: [updateUserAppliedBounties],
  },
  endpoints: [
    {
      path: "/follow-details/:userId",
      method: "get",
      handler: followDetails,
    },
    {
      path: "/likes-details/:userId",
      method: "get",
      handler: likesDetails,
    },
    {
      path: "/:teamID/customer",
      method: "get",
      handler: customerProxy,
    },
    {
      path: "/:teamID/customer",
      method: "patch",
      handler: customerProxy,
    },
    {
      path: "/:userID/details",
      method: "get",
      handler: publicProfile,
    },
    //custom endpoint for me api-----------------------------
    {
      path: "/meapi",
      method: "get",
      handler: meapi,
    },
    {
      path: "/getRappCount/:userID",
      method: "get",
      handler: rappCount,
    },
    {
      path: "/getProductCount/:userID",
      method: "get",
      handler: productCount,
    },
  ],
  fields: [
    {
      name: "user_name",
      type: "text",
      label: "User Name",
      defaultValue: "",
      index: true,
    },
    {
      name: "AuthProvider",
      type: "text",
      label: "Authentication Provider",
      defaultValue: "",
    },
    {
      name: "coinBalance",
      type: "number",
      label: "Credit Balance",
      required: true,
      defaultValue: 0,
      access: {
        update: admins,
      },
    },
    {
      name: "bountyHuntBalance",
      type: "number",
      label: "Bounty Hunting Balance",
      required: false,
      defaultValue: 0,
      access: {
        update: admins,
      },
    },
    {
      name: "role",
      defaultValue: "user",
      required: true,
      type: "select",
      options: [
        { label: "Admin", value: "admin" },
        { label: "Enterprise Admin", value: "entAdmin" },
        { label: "Enterprise User", value: "entUser" },
        { label: "User", value: "user" },
      ],
      access: {
        update: admins,
      },
    },
    {
      name: "products",
      label: "Products",
      admin: {
        condition: () => false,
      },
      type: "relationship",
      relationTo: "products",
      hasMany: true,
    },
    {
      name: "product_files",
      label: "Product files",
      admin: {
        condition: () => false,
      },
      type: "relationship",
      relationTo: "product_files",
      hasMany: true,
    },
    {
      name: "purchases",
      label: "Purchases",
      type: "relationship",
      relationTo: "purchases",
      admin: {
        condition: () => false,
      },
      hasMany: true,
    },
    {
      name: "rappsPurchases",
      label: "Rapps Purchases",
      type: "relationship",
      relationTo: "rappsPurchases",
      admin: {
        condition: () => false,
      },
      hasMany: true,
    },
    {
      name: "bounties",
      label: "Bounties",
      admin: {
        condition: () => false,
      },
      type: "relationship",
      relationTo: "bounties",
      hasMany: true,
    },

    {
      name: "bountiesApplied",
      label: "Applied Bounties",
      type: "array",
      required: false,
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: "bountyId",
          type: "text",
          required: true,
        },
      ],
    },   
    {
      name: "rapps",
      label: "Spaces",
      type: "relationship",
      relationTo: "rapps",
      filterOptions: ({ data }) => {
        return {
          creator: {
            equals: data.id,
          },
        };
      },
      hasMany: true,
      admin: {
        hidden: true,
      },
    },
    {
      // TODO: we have to add default images in this
      name: "profileImage",
      type: "upload",
      relationTo: "media",
      index:true,
    },
    {
      // TODO: we have to add default images in this
      name: "coverImage",
      type: "upload",
      relationTo: "media",
    },
    {
      name: "genInfo",
      label: "General Information",
      type: "group",
      index : true,
      access: {
        update: userOrAdminAccess,
      },
      fields: [
        {
          // TODO: change its type later
          name: "education",
          type: "text",
          label: "Education",
        },
        {
          // TODO: change its type later
          name: "skills",
          type: "text",
          label: "Skills",
        },
        {
          name: "gender",
          type: "select",
          label: "Gender",
          hasMany: false,
          options: [
            { label: "Male", value: "male" },
            { label: "Female", value: "female" },
            { label: "Other", value: "other" },
          ],
        },
        {
          name: "age",
          type: "number",
          label: "Age",
          // min: 18,
          // defaultValue: 18,
          // required: true,
          // hooks: {
          //   beforeChange: [setAge],
          // },
        },
        {
          name: "profession",
          type: "text",
          label: "Profession",
        },
        {
          name: "workExperience",
          type: "number",
          label: "Work Experience",
        },
        {
          name: "interests",
          label: "Interests",
          type: "select",
          hasMany: true,
          options: User_Interests.map(({ label, value }) => ({ label, value })),
          required: false,
        },
      ],
    },
    {
      name: "socialMediaLinks",
      label: "Social Media Links",
      type: "group",
      fields: [
        {
          name: "facebook",
          type: "text",
          label: "Facebook",
        },
        {
          name: "instagram",
          type: "text",
          label: "Instagram",
        },
        {
          name: "twitter",
          type: "text",
          label: "Twitter",
        },
        {
          name: "github",
          type: "text",
          label: "GitHub",
        },
        {
          name: "discord",
          type: "text",
          label: "Discord",
        },
      ],
    },
    {
      name: "likes",
      type: "array",
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
      ],
      admin: {
        hidden: true,
      },
    },
    {
      name: "following",
      type: "array",
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
      ],
      admin: {
        hidden: true,
      },
    },
    {
      name: "followers",
      type: "array",
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
      ],
      admin: {
        hidden: true,
      },
    },
    {
      name: "productListing",
      type: "number",
      label: "Product Listing",
      admin: {
        condition: () => false,
      },
    },
    {
      name: "userType",
      // defaultValue: null,
      // required: false,
      type: "radio",
      options: [
        { label: "Individual", value: "individual" },
        { label: "Enterprise", value: "enterprise" },
        { label: "Student", value: "student" },
      ],
      // access: {
      //   update: admins,
      // },
    },
    // {
    //   name: "Individual",
    //   type: "checkbox",
    //   label: "Individual",
    //   defaultValue: false,
    // },
    // {
    //   name: "enterprise",
    //   type: "checkbox",
    //   label: "Enterprise Domain",
    //   defaultValue: false,
    // },
    {
      name: "over18",
      type: "checkbox",
      label: "Is Adult",
      defaultValue: false,
    },
    {
      name: 'domain',
      required: false,
      label: 'Domain',
      defaultValue: '',
      type: 'text',
    },
    {
      name: 'members',
      label: 'Members',
      type: 'relationship',
      relationTo: 'users',
      hasMany: true,
      admin: {
        condition: ({ role }) => { return role && role.includes('admin')},
      },
      
    },
    {
      name: 'associatedWith',
      label: 'Associated with',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        condition: ({ role }) => { return role && role.includes('admin')},
      },
      filterOptions: () => {
        return {
          role: {
            equals: 'entAdmin',
          },
        }
      },
    },
    // normalize this
    {
      name: 'rappAccess',
      label: 'Rapps Access',
      type: 'array',
      required: false,
      access: {
        update: () => true,
      },
      admin: {
        readOnly: true,
        condition: ({ role }) => { return role && role.includes('admin')},
      },
      fields: [
        {
          name: 'rappId',
          label: 'Rapp ID',
          type: 'relationship',
          relationTo: 'privateRapps',
          required: true,
        },
        {
          name: 'getAccess',
          label: 'Get Access',
          type: 'select',
          hasMany: true, // This allows multiple selections
          required: false,
          options: [
            { label: 'Read', value: 'read' },
            { label: 'Delete', value: 'delete' },
            { label: 'Create', value: 'create' },
            { label: 'Update', value: 'update' },
          ],
        },
      ],
    },
    {
      name: 'privateRapps',
      label: 'Private Rapps',
      type: 'relationship',
      relationTo: 'privateRapps',
      filterOptions: ({ data }) => {
        return {
          creator: {
            equals: data.id,
          },
        }
      },
      hasMany: true,
      admin: {
        hidden: true,
      },
    },
    {
      name: 'createRappPermission',
      type: 'checkbox',
      label: 'Give Create Rapp Permission',
      required: false,
      defaultValue: false,
      access: {
        update: admins,
      },
      admin: {
        condition: ({ role }) => { return role && role.includes('admin')},
      },
    },
    {
      name: 'tokens',
      label: 'Tokens (Consumed)',
      type: 'number',
      defaultValue: 0,
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    //i want to  make a user session expired in 30 seconds
     


  ],
};
