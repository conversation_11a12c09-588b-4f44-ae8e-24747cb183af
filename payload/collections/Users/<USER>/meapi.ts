import type { Payload<PERSON>and<PERSON> } from "payload/config";
import type { PayloadRequest } from "payload/types";

const logs = process.env.LOGS_PUBLIC_PROFILE === "1";

export const meapi: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  const {  user}: {user:any} = req;
  const userId = user?.id;

  try {
    const user = await req.payload.findByID({
      collection: "users",
      id: userId,
      depth: 2,                                                             
    });

    if (!user) {
      if (logs) req.payload.logger.error({ err: `User not found` });
      return res.status(404).json({ message: "User not found" });
    }

     const followersLength = (user?.followers as any[])?.length ?? 0;
     const followingLength = (user?.following as any[])?.length ?? 0;
     const productsLength = (user?.products as any[])?.length ?? 0;
     const likesLength = (user?.likes as any[])?.length ?? 0;

    // Define profileImage and coverImage 
    const profileImage = (user?.profileImage as { url?: string })?.url;
    const coverImage = (user?.coverImage as { url?: string })?.url;

   // Extract social media links 
  const socialMediaLinks = {
    facebook: (user?.socialMediaLinks as { facebook?: string })?.facebook ?? null,
    instagram: (user?.socialMediaLinks as { instagram?: string })?.instagram ?? null,
    twitter: (user?.socialMediaLinks as { twitter?: string })?.twitter ?? null,
    github: (user?.socialMediaLinks as { github?: string })?.github ?? null,
    discord: (user?.socialMediaLinks as { discord?: string })?.discord ?? null
  };

    // Select specific fields to return
    const userDetails = {
      id : user.id,
      name: user.user_name,
      coinBalance : user.coinBalance ,
      email :  user?.email ,
      followersLength,
      followingLength ,
      productsLength , 
      likesLength ,
      profileImage ,
      coverImage ,
      genInfo : user?.genInfo ,
      socialMediaLinks ,
      // age : user.genInfo.age ,
      // profession : user.genInfo?.profession ,
      // workExperience : user.genInfo.workExperience ,
      // interests : user.genInfo?.interests,
      // skills : user.genInfo.skills ,
      productFiles: user.product_files,               
      // socialMediaLinks: user?.socialMediaLinks,
      // facebook: user?.socialMediaLinks?.facebook ?? "",
         };

    return res.status(200).json( {success: true, data :userDetails });
  }
   catch (error) {
    if (logs)
      req.payload.logger.error({
        err: `Error fetching user details: ${error}`,
      });
    res.status(500).json({ error: "Internal server error" });
  }
};
