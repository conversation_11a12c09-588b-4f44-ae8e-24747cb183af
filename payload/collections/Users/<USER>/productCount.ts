import { PayloadHandler } from "payload/config";
import { PayloadRequest } from "payload/types";

export const productCount: PayloadHandler = async (req: PayloadRequest, res) => {
  const { userID } = req.params;

  try {
    const count = await req.payload.count({
      collection: "products",
      where: { user: { equals: userID } },
    });
    res.status(200).json({ count });
  } catch (error) {
    console.log("error", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
