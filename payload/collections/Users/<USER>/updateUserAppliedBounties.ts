import { AfterChangeHook } from "payload/dist/globals/config/types";

const updateUserAppliedBounties: AfterChangeHook = async ({
  doc,
  previousDoc,
  req,
}) => {
  const newApplicants = doc.applicants || [];
  const oldApplicants = previousDoc?.applicants || [];

  const oldApplicantIds = oldApplicants.map((app) => app.userId);
  const newlyAdded = newApplicants.filter(
    (app) => !oldApplicantIds.includes(app.userId)
  );

  for (const app of newlyAdded) {
    const userId = app.userId;

    try {
      const user = await req.payload.findByID({
        collection: "users",
        id: userId,
      });

      const existingBounties = (user.bountiesApplied || []) as { bountyId: string }[];

      const alreadyApplied = existingBounties.some(
        (entry) => entry.bountyId === doc.id
      );

      if (!alreadyApplied) {
        const updatedBounties = [
          ...existingBounties,
          { bountyId: doc.id },
        ] as any; // 👈 Cast as `any` to satisfy Payload's type checking

        await req.payload.update({
          collection: "users",
          id: userId,
          data: {
            bountiesApplied: updatedBounties,
          },
        });
      }
    } catch (err) {
      console.error("Error updating user's applied bounties:", err);
    }
  }

  return doc;
};

export default updateUserAppliedBounties;
