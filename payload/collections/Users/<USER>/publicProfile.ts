import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "payload/config";
import type { PayloadRequest } from "payload/types";

const logs = process.env.LOGS_PUBLIC_PROFILE === "1";

export const publicProfile: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  const { userID } = req.params;

  try {
    const user = await req.payload.findByID({
      collection: "users",
      id: userID,
      depth: 2, // Adjust the depth as needed
    });

    if (!user) {
      if (logs) req.payload.logger.error({ err: `User ${userID} not found` });
      return res.status(404).json({ message: "User not found" });
    }

    // Select specific fields to return
    const userDetails = {
      username: user.user_name,
      products: user.products,
      productFiles: user.product_files,
      socialMediaLinks: user.socialMediaLinks,
      followers: user.followers,
      following: user.following,
      //generalInformation: user.generalInformation,
    };

    res.status(200).json(userDetails);
  } catch (error) {
    if (logs)
      req.payload.logger.error({
        err: `Error fetching user details: ${error}`,
      });
    res.status(500).json({ error: "Internal server error" });
  }
};
