import { PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const readAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;
  
  const url = req.headers.referer || "";
  const isAdmin = user?.role?.includes("admin");
  const isDashboard = url.includes("/admin/collections/products");

    //check to prevent api security 
    if(req.originalUrl === '/api/products')
      {
        return false
      }

  if (!user) {
    if (isDashboard) {
      return false;
    }
    return {
        approvedForSale: {
        equals: "approved",
      },
    };
  }

  // Means user is in dashboard
  if (isDashboard) {
    if (isAdmin) return {
      or: [
        { needsApproval: { equals: true } }, // Items needing approval
        {
          or: [{approvedForSale: {equals: "approved"}}]
        },
        { 
          and: [
            { user: { equals: user.id } }, // Admin's own items
            { 
              or: [
                { approvedForSale: { equals: "pending" } }, // Admin's pending items
                { approvedForSale: { equals: "approved" } }, // Admin's approved items
              ]
            }
          ]
        }
      ],
    };
    else{
      const userProducts = await payload.find({
        collection: "products",
        where: {
          user: {
            equals: user.id,
          },
        },
        limit: 0, // Setting limit to 0 will fetch all documents
      });

      return {
        id: {
          in: userProducts.docs.map((product: any) => product.id),
        },
      };
    }

  } else {
    // Frontend logic: show only approved bounties
    return {
        approvedForSale: {
          equals: "approved",
        },
    };
  }
};

export default readAccess;
