import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "payload/config";
import type { PayloadRequest } from "payload/types";

const logs = process.env.LOGS_SECURE_PRODUCT_DATA === "1";

export const secureProductData: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  const { productID } = req.params;

  if (!req.user) {
    if (logs)
      req.payload.logger.error({
        err: "You are not authorized to access this data",
      });
    return res
      .status(401)
      .json({ error: "You are not authorized to access this data" });
  }

  try {
    const product = await req.payload.findByID({
      collection: "products",
      id: productID,
      depth: 2, // Adjust the depth as needed
    });

    if (!product) {
      if (logs)
        req.payload.logger.error({ err: `Product ${productID} not found` });
      return res.status(404).json({ message: "Product not found" });
    }

    // Select specific fields to return, excluding sensitive data
    const productData = {
      name: product.name,
      description: product.description,
      price: product.price,
      productType: product.listingCategory,
      // category: product.category,
      productFiles: product.product_files,
      images: product.images,
      likes: product.likes,
      // generalInformation: product.generalInformation,
    };

    res.status(200).json(productData);
  } catch (error) {
    if (logs)
      req.payload.logger.error({
        err: `Error fetching product data: ${error}`,
      });
    res.status(500).json({ error: "Internal server error" });
  }
};
