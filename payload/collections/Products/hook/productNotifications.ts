// import { getProductStatusEmailHTML } from "../payload/utilities/emailTemplates/productStatusEmailTemplate";
import { getProductStatusEmailHTML } from "../../../utilities/emailTemplates/productStatusEmailTemplate";
// import sendEmail from "@/payload/utilities/sendEmail";
import sendEmail from "../../../utilities/sendEmail";
import { PayloadRequest } from "payload/types";

export const productNotifications = async ({
  doc,
  previousDoc,
  req,
  operation,
}: {
  doc: any;
  previousDoc: any;
  req: PayloadRequest;
  operation: "create" | "update";
}) => {
  const logoUrl = `https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/RentpromptsIcon.png`;
  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL;
  if (operation !== "update" || !previousDoc) {
    // console.log("New bounty creation detected. Skipping email.");
    return;
  }

  try {
    const ProductsOwner = await req.payload.findByID({
      collection: "users",
      id: doc?.user?.id ? doc?.user?.id : doc?.user,
    });
    // console.log("ProductsOwner", ProductsOwner);
    // console.log("doc.user", doc?.user);
    // console.log("doc ===>>", doc);
    if (!ProductsOwner || typeof ProductsOwner !== "object") {
      console.warn("Product owner not found or invalid format.");
      return;
    }

    const userEmail = ProductsOwner.email;
    const userName = ProductsOwner.user_name || "User";
    const htmlContent = getProductStatusEmailHTML({
      userName,
      productName: doc.name,
      status: doc.approvedForSale,
      adminReview: doc.adminReview,
      slug: doc.slug,
      logoUrl,
      baseUrl,
      id: doc.id,
    });
    // STATUS CHANGED BY ADMIN THEN BOUNTY OWNER GET EMAIL
    if (doc.approvedForSale !== previousDoc?.approvedForSale) {
      if (!["approved", "denied"].includes(doc.approvedForSale)) return;

      try {
        await req.payload.create({
          collection: "notifications",
          data: {
            user: doc.user,
            message: `Your product "${doc?.name}" has been ${doc?.approvedForSale.toUpperCase()}. ${
              doc?.approvedForSale === "denied"
                ? `Reason: ${doc?.adminReview}`
                : `You can now view it live.`
            }`,
            link: `/products/${doc?.slug || ""}`,
          },
        });
      } catch (error) {
        console.log("notification is not created :", error);
      }
      await sendEmail({
        to: userEmail,
        subject: `Your Product Has Been ${doc?.approvedForSale === "denied" ? "Rejected" : "Approved"}!`,
        html: htmlContent,
      });
    } else {
      // Handle other status changes or default behavior
      console.log("No status change detected, skipping email.");
    }
  } catch (error) {
    console.error("❌ Error in sendApprovalEmail:", error);
  }
};
