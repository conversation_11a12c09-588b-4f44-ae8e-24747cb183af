import { FieldHook } from "payload/types";
import slugify from "slugify";

const autoGenerateSlug: FieldHook = async ({ data, value, originalDoc, siblingData, operation, req }) => {
  if (data?.name && !value) {
    const baseSlug = slugify(data.name, { lower: true, strict: true });
    let uniqueSlug = baseSlug;
    let isUnique = false;
    let counter = 1;

    // Check if we are creating or updating the document
    if (operation === "create" || (operation === "update" && data.name !== originalDoc?.name)) {
      // Query the collection to check for existing slugs
      while (!isUnique) {
        const existingDocs = await req.payload.find({
          collection: "products",
          where: {
            slug: {
              equals: uniqueSlug,
            },
          },
        });

        if (existingDocs.totalDocs === 0) {
          // If no document exists with this slug, it's unique
          isUnique = true;
        } else {
          // If the slug exists, append a number to make it unique
          uniqueSlug = `${baseSlug}-${counter}`;
          counter++;
        }
      }
    }

    return uniqueSlug;
  }

  // Return the current value if it already exists or title has not changed
  return value;
};

export default autoGenerateSlug;
