import { Generation_Type, PRODUCT_CATEGORIES, Product_Type } from "../../../constants";
import { Access, CollectionConfig } from "payload/types";
import { User } from "../../../server/payload-types";
import { likes } from "../../endpoints/likes";
import { secureProductData } from "./endpoints/secureProductData";
import { admins } from "../../access/admins";
import readAccess from "./access/readAccess";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import { getProductRating, productRating } from "../../endpoints/productRating";
import autoGenerateSlug from "./hook/generateSlug";
import { addAuditLogs } from "../globalHooks/auditlog";
import { productDetail } from "../../endpoints/product-detail";
import { clearAdminReviewOnStatusChange } from "./hook/beforeChange";
import { productCacheHooks } from "../../hooks/cache-invalidation";

export const Products: CollectionConfig = {
  slug: "products",
  admin: {
    useAsTitle: "name",
    group: "Listing",
    defaultColumns: ["name", "approvedForSale", "price"],
    hidden: ({ user }) => user.role !== "admin",
  },
  access: {
    create: adminsOrLoggedIn,
    read: adminsOrLoggedIn,
    update: adminsOrLoggedIn,
    delete: adminsOrLoggedIn,
  },
  hooks: {
    beforeChange:[addAuditLogs,clearAdminReviewOnStatusChange],
    afterChange: productCacheHooks.afterChange,
    afterDelete: productCacheHooks.afterDelete,
  },
  endpoints: [
    {
      path: "/product-detail/:productId",
      method: "get",
      handler: productDetail, 
    },
    {
      path: "/likes/:productId",
      method: "get",
      handler: likes,
    },
    {
      path: "/:productID/secure-data",
      method: "get",
      handler: secureProductData,
    },
    {
      path: "/rating/:productId",
      method: "post",
      handler: productRating,
    },
    {
      path: "/rating/:productId",
      method: "get",
      handler: getProductRating,
    },
    {
      path: "/comment/:productId",
      method: "post",
      handler: async (req, res) => {
        const { comments } = req.body;
        const { productId } = req.params;
        const { payload, user } = req;
        // console.log("productId collection",productId);

        try {
          const product: any = await payload.findByID({
            collection: "products",
            id: productId,
          });
          // console.log("product post", product);

          if (!product) {
            return res.status(404).json({ error: "Product not found." });
          }

          const userIds =
            (
              product?.userComment as { user: { id: string }; comment: {} }[]
            )?.map((commentedUser) => ({
              user: commentedUser?.user?.id,
              comment: commentedUser?.comment,
            })) ?? [];

          const isCommented = userIds.some(
            (commentedUser) => commentedUser.user === user?.id
          );
          // console.log("isCommented",isCommented);

          // console.log("check userIds", userIds);

          // Update or add comment
          const updatedComment = isCommented
            ? userIds.map((commentedUser) =>
                commentedUser.user === user?.id
                  ? { ...commentedUser, comment: comments }
                  : commentedUser
              )
            : [...userIds, { user: user?.id, comment: comments }];

          // console.log("updatedComment+++", updatedComment);

          // Ensure updatedComment matches the expected structure for userComment
          const comment = await payload.update({
            collection: "products",
            id: productId,
            data: {
              userComment: updatedComment,
            },
          });

          return res.status(201).json({ success: true, comment });
        } catch (error) {
          console.error("Error updating comment:", error);
          return res
            .status(500)
            .json({ error: error.message || "Failed to comment." });
        }
      },
    },
    {
      path: "/comment/:productId",
      method: "get",
      handler: async (req, res) => {
        const { productId } = req.params;
        const { payload, user } = req;

        try {
          const product: any = await payload.findByID({
            collection: "products",
            id: productId,
          });
          if (!product) {
            return res.status(404).json({ error: "Product not found." });
          }

          // Extract userComments from the product document
          const userComments =
            (product?.userComment as { user: { id: string }; comment: {} }[]) ??
            [];

          return res
            .status(200)
            .json({ success: true, comments: userComments });
        } catch (error) {
          console.error("Error retrieving comments:", error);
          return res
            .status(500)
            .json({ error: error.message || "Failed to retrieve comments." });
        }
      },
    },
  ],
  fields: [
    {
      name: "name",
      label: "Product Name",
      type: "text",
      // required: true,
      admin: {
        description: "This will be the title of the product.",
      },
      index: true,
    },
    {
      name: "description",
      type: "textarea",
      label: "Product Description",
      admin: {
        description:
          "Highlight what makes your product unique and valuable to potential buyers. A thorough and engaging description can help boost your sales, also can use markup for increase readability.",
      },
      index: true,
    },
    {
      name: "generationType",
      label: "Generation Type",
      type: "select",
      options: Generation_Type.map(({ label, value }) => ({ label, value })),
      required: true,
      admin: {
        description: "Mention the generation type, which type of response product generates",
      },
    },
    {
      name: "generationModel",
      label: "Generation Model",
      type: "text",
    },
    // {
    //   name: "generationModel",
    //   type: "relationship",
    //   relationTo: "models",
    //   hasMany: false,
    //   label: "Generation Model",
    //   // required: true,
    //   // admin: {
    //   //   description: "Select categories for this Rapp",
    //   // },
    // },
    {
      name: "price",
      label: "Product Price",
      min: 0,
      max: 100,
      type: "number",
      // required: true,
      admin: {
        description: "Price in credit (1 rupee = 1 credit)",
      },
    },
    {
      name: "rating",
      label: "Rating",
      type: "number",
      // required: false,
      min: 0,
      max: 5,
      defaultValue: 0,

      admin: {
        description: "Rate between 1 and 5 stars",
        step: 0.1,

        hidden: true,
      },
    },
    {
      name: "userRatings",
      label: "User Ratings",
      type: "array",
      admin: {
        hidden: true,
      },
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: false,
        },
        {
          name: "rating",
          type: "number",
          required: false,
          min: 0,
          max: 5,
        },
      ],
    },

    {
      name: "userComment",
      label: "user comment",
      type: "array",
      required: false,
      admin: {
        hidden: true,
      },
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
        {
          name: "comment",
          type: "text",
        },
      ],
    },

    {
      name: "listingCategory",
      label: "Product Type",
      type: "select",
      options: Product_Type.map(({ label, value }) => ({ label, value })),
      // required: false,
      admin: {
        description: "Select the type for the product.",
      },
    },
    // {
    //   name: "listingCategory",
    //   label: "Listing Category",
    //   type: "select",
    //   options: [
    //     { label: "Asset", value: "asset" },
    //     { label: "Prompt", value: "prompt" },
    //   ],
    //   defaultValue: ({ data }) => (data?.category ? "asset" : "prompt"),
    //   access: {
    //     create: admins,
    //     read: admins,
    //     update: admins,
    //     // create: ({ req }) => req.user.role === 'admin',
    //   },
    // },
    {
      name: "product_files",
      label: "Product File(s)",
      type: "relationship",
      // required: true,
      relationTo: "product_files",
      hasMany: false,
    },
    {
      name: "approvedForSale",
      label: "Product Status",
      type: "select",
      defaultValue: "pending",
      access: {
        create: admins,
        read: () => true,
        update: admins,
      },
      admin: {
        description: "Product status will be approved by admin only.",
      },
      options: [
        {
          label: "Pending verification",
          value: "pending",
        },
        {
          label: "Approved",
          value: "approved",
        },
        {
          label: "Denied",
          value: "denied",
        },
      ],
    },
    {
      name: "adminReview",
      label: "Admin Review",
      type: "textarea",
      admin: {
        condition: (data) => data.approvedForSale === "denied", // Show only if status is "denied"
        description: "Provide a reason for denying this product.",
      },
    },
    {
      name: "productCategory",
      type: "relationship",
      relationTo: "category",
      hasMany: false,
      label: "Category",
      // required: true,
      // admin: {
      //   description: "Select categories for this Rapp",
      // },
    },
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      //required: true,
      admin: {
        description:
          "Highlight the product in the list by marking it as featured.",
      },
      access: {
        read: admins,
      },
    },
    {
      name: "priceId",
      access: {
        create: () => false,
        read: () => false,
        update: () => false,
      },
      type: "text",
      admin: {
        hidden: true,
      },
    },
    {
      name: "stripeId",
      access: {
        create: () => false,
        read: () => false,
        update: () => false,
      },
      type: "text",
      admin: {
        hidden: true,
      },
    },
    {
      name: "images",
      type: "array",
      label: "Product images",
      minRows: 1,
      maxRows: 4,
      // required: true,
      labels: {
        singular: "Image",
        plural: "Images",
      },
      fields: [
        {
          name: "image",
          type: "upload",
          relationTo: "media",
          // required: true,
        },
      ],
      admin: {
        description: "Atleast one image is required.",
      },
    },
    {
      type: "checkbox",
      name: "needsApproval",
      label: "Send for Approval",
      admin: {
        description: "Mark this item as needing approval.",
        position: "sidebar",
      },
      defaultValue: false,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      // required: false,
      unique: true,
      hooks: {
        beforeValidate: [autoGenerateSlug], // Use the field-level hook here for real-time slug generation
      },
      admin: {
        hidden: true,
        readOnly: true,
        position: "sidebar", // Make it read-only so users cannot change it manually
      },
    },
    {
      name: "affiliated_with",
      label: "Affiliated With",
      type: "text",
      // required: false,
    },
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      // required: true,
      defaultValue: ({ user }) => {
        if (user) return user.id;
        return "";
      },
      hasMany: false,
      admin: {
        allowCreate: false,
        readOnly: true,
      },
    },
    {
      name: "likes",
      type: "array",
      admin: {
        hidden: true,
      },
      fields: [
        {
          name: "user",
          type: "relationship",
          relationTo: "users",
          required: true,
        },
      ],
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
};
