import type { CollectionConfig } from 'payload/types'
import { Model_type } from '../../../constants'

export const ModelSpaces: CollectionConfig = {
  slug: 'modelLab',
  admin: {
    useAsTitle: 'title',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'model_type',
      label: 'Model type',
      type: 'select',
      options: Model_type.map(
        ({ label, value }) => ({ label, value })
      ),
      required: true,
    },
    {
      name: 'model_url',
      label: 'Model Url',
      type: 'text',
      required: true,
    },
  ],
}
