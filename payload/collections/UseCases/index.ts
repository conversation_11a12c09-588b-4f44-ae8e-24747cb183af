import { CollectionConfig } from "payload/types";

const UseCases: CollectionConfig = {
  slug: 'useCases',
  admin: {
    useAsTitle: 'label',
    group: 'Admin',
    hidden: ({ user }) => user.role !== "admin",
  },
  timestamps: true,
  fields: [
    {
      name: 'value',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'label',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'price',
      type: 'number',
      required: true,
    },
  ],
};

export default UseCases;
