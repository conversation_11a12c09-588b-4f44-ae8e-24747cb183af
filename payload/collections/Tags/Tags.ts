import { CollectionConfig } from "payload/types";
import adminsAndUser from "./access/adminsAndUser";
import { anyone } from "../../access/anyone";
import { admins } from "../../access/admins";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";

export const Tags: CollectionConfig = {
  slug: "tags",
  admin: {
    hidden: ({ user }) => user.role !== "admin",
    group: "Admin",
    useAsTitle: "label",
  },
  access: {
    read: readAccess,
    create: adminsAndUser,
    update: adminsAndUser,
    delete: admins,
  },
  hooks:{
    beforeChange:[addAuditLogs]
  },
  fields: [
    {
      name: "label",
      type: "text",
      required: true,
      label: "Tag Name",
    },
    {
      name: "value",
      type: "text",
      label: "Tag Value",
      required: true,
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
};
