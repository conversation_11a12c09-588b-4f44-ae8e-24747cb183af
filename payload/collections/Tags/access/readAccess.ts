import { Access, PayloadRequest } from "payload/types";
import { User } from "../../../../server/payload-types";
import payload from "payload";

const readAccess: any = async ({ req }: { req: PayloadRequest }) => {
  const user = req.user as User | undefined;

  const acceptHeader = req.headers['accept'];

  if (acceptHeader?.includes("text/html")) {
    return false;
  } else {
    return true; 
  }
 
};

export default readAccess;
