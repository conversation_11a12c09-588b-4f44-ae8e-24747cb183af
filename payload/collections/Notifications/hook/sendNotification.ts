
import { pusher } from "../../../../payload/lib/pusher";
import type { AfterChangeHook } from "payload/dist/collections/config/types";

export const notifyUser: AfterChangeHook = async ({ doc, operation }) => {
 
        if (operation !== "create") return;

        // Safely get user ID from the doc
        const userId =
          typeof doc.user === "object" && doc.user !== null
            ? doc.user.id
            : doc.user;

        if (!userId || typeof userId !== "string") {
          console.error(
            "❌ No valid user ID found in notification doc:",
            doc.user
          );
          return;
        }

        const channelName = `my-channel-${userId}`;
        // console.log("doc notification", doc);
        try {
          await pusher.trigger(channelName, "new-notification", {
            // message: "You have a new notification",
            // notification: doc,
            message: doc.message || "📨 You have a new notification",
            notificationId: doc.id,
            link:doc?.link,
            createdAt: doc.createdAt,
          });

          // console.log("✅ Notification sent to:", channelName);
        } catch (err) {
          console.error("❌ Failed to trigger Pusher event:", err);
        }
};
