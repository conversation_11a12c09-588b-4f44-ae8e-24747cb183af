import { CollectionConfig } from "payload/types";
import { notifyUser } from "./hook/sendNotification";
import { getUserNotifications } from "../../endpoints/notificatons-endpoints/getUserNotifications";
// import { markAsRead } from "../../payload/endpoints/notificatons-endpoints/markAsRead";
import {markAsRead} from "../../../payload/endpoints/notificatons-endpoints/markAsRead"
// import { getIO } from "@/server/socketServer";
export const Notifications: CollectionConfig = {
  slug: "notifications",
  admin: {
    hidden: ({ user }) => user.role !== "admin",
  },
  hooks: {
    afterChange: [notifyUser],
  },
  access: {
    read: ({ req }) => {
      if (!req.user) return false;

      if (req.user.role === "admin") {
        return true;
      }

      return {
        user: { equals: req.user.id },
      };
    },
     create: () => true,
  },

  endpoints: [
    getUserNotifications, 
    markAsRead
  ],
  fields: [
    {
      name: "user",
      type: "relationship",
      relationTo: "users",
      required: true,
    },
    {
      name: "message",
      type: "text",
      required: true,
    },
    {
      name: "link",
      type: "text",
      required: false,
    },
    {
      name: "read",
      type: "checkbox",
      defaultValue: false,
    },
  ],
  timestamps: true,
};
