import { Field } from "payload/types";

export const replicatevideosettings: Field = {
  type: "select",
  name: "replicatevideosettings",
  label: "Select Video Model Settings",
  options: [
    { label: "cfg", value: "cfg" },
    { label: "seed", value: "seed" },
    { label: "steps", value: "steps" },
    { label: "length", value: "length" },
    { label: "target_size", value: "target_size" },
    { label: "aspect_ratio", value: "aspect_ratio " },
  ],
  hasMany: true,
  required: false,
  admin: {
    condition: (data) =>
      data.type === "video" && data.provider.video === "replicate",
    description: "Add settings to your video model",
  },
};
