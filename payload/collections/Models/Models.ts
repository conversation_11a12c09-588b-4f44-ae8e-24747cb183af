import { CollectionConfig } from "payload/types";
// import { slateEditor } from "@payloadcms/richtext-slate";
import { groq } from "./providers/Groq/field";
import { replicate } from "./providers/Replicate/field";
import { openai } from "./providers/Openai/field";
import { falai } from "./providers/FalAi/field";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";
import { runway } from "./providers/Runway/field";
import { modelsCacheHooks } from "../../hooks/cache-invalidation";
export const Models: CollectionConfig = {
  slug: "models",
  admin: {
    useAsTitle: "name",
    defaultColumns: ["name", "type", "description"],
    group: "Admin",
    livePreview: {
      url: ({ data, documentInfo }) => {
        return `${process.env.PAYLOAD_PUBLIC_SITE_URL}/livepreview/${documentInfo.slug}?id=${data.id}`;
      },
    },
    hidden: ({ user }) => user.role !== "admin",
  },
  hooks:{
    beforeChange:[addAuditLogs],
    afterChange: modelsCacheHooks.afterChange,
    afterDelete: modelsCacheHooks.afterDelete,
  },
  endpoints: [
    {
      path: "/livepreview/:id",
      method: "get",
      handler: async (req, res) => {
        const id = req.params.id;
        const model = await req.payload.findByID({ collection: "models", id });
        res.send({
          name: model.name,
          description: model.description,
          modelType: model.type,
          cost: model.cost,
          commission: model.commision,
          // ModelImageReplicateSettings: model.ModelImageReplicateSettings,
          provider: model.provider,
          settings: model.settings,
          examples: model.examples,
          createdAt: model.createdAt,
          updatedAt: model.updatedAt,
        });
      },
    },
    {
      path: "/get-model-name",
      method: "get",
      handler: async (req, res) => {
        const model = await req.payload.find({ collection: "models", limit: 0 });
        const modelsArr = model.docs.map(m => m.name);
        res.send({modelsArr});
      },
    },
  ],
  access: {
    read: readAccess,
  },
  fields: [
    // model name
    {
      type: "text",
      name: "name",
      label: "Model Name",
      required: true,
      admin: {
        placeholder: "Enter name of your model",
      },
      index: true
    },
    // model description
    {
      type: "textarea",
      name: "description",
      label: "Model Description",
      required: true,
      admin: { placeholder: "About the model" },
      index: true,
    },
    // about model Page (seo benefit)
    // {
    //   type: "richText",
    //   name: "about",
    //   editor: slateEditor({}),
    //   required: true,
    //   label: "About Model Page",
    // },
    // model type
    {
      name: "type",
      type: "select",
      label: "Model Type",
      admin: { description: "Select type of your model" },
      defaultValue: "text",
      options: [
        {
          label: "Text Model",
          value: "text",
        },
        {
          label: "Image Model",
          value: "image",
        },
        {
          label: "Audio Model",
          value: "audio",
        },
        {
          label: "Video Model",
          value: "video",
        },
        {
          label: "Vision Model",
          value: "vision",
        },
        // {
        //   label: "3D Model",
        //   value: "threed",
        // },
      ],
      required: true,
    },
    //model images  
    {
      name: "modelImage",
      type: "array",
      label: "Add model image here ( can you also add model generated images).",
      // minRows: 1,
      // maxRows: 4,
      index: true,
      required: true,
      labels: {
        singular: "Image",
        plural: "Images",
      },
      fields: [
        {
          name: "image",
          type: "upload",
          relationTo: "media",
          required: true,
        },
      ],
    },

    //model organization
    {
      type: "textarea",
      name: "organization",
      label: "Model Organization",
      required: true,
      admin: { placeholder: "About the model organization." },
    },
    //model organization logo 
    {
      name: "organizationImage",
      type: "array",
      label: "Add model organization logo here.",
      // minRows: 1,
      // maxRows: 4,
      required: true,
      labels: {
        singular: "Image",
        plural: "Images",
      },
      fields: [
        {
          name: "image",
          type: "upload",
          relationTo: "media",
          required: true,
        },
      ],
    },
    {
      name: "isFeatured",
      type: "checkbox",
      label: "Is Featured",
      defaultValue: false,
      //required: true,
      admin: {
        description:
          "Do you want to highlight the model by marking it as featured.",
      },
   },

   //mandatory image input 
    {
      type: "checkbox",
      name: "imageinput",
      label: "This model accept image as mandatory.",
      admin: {
        description: "User will be able to give image as input to your Rapp",
      },  
      defaultValue: false,
      required: true,
    },
    //optional image input 
    {
      type: "checkbox",
      name: "imageinputopt",
      label:  "This model accept image as optional.",
      admin: {
        description: "User will be able to give image as input to your Rapp",
      },  
      defaultValue: false,
      required: true,
    },

    // provider
    {
      name: "provider",
      type: "select",
      label: "Model Provider",
      defaultValue: "groq",
      required: true,
      options: [
        { label: "Groq", value: "groq" },
        { label: "OpenAI", value: "openai" },
        { label: "Replicate", value: "replicate" },
        { label: "Fal AI", value: "falai" },
        { label: "Runway", value: "runway" },

        // { label: 'Azure OpenAI', value: 'azureopenai' },
        // { label: 'Hugging Face', value: 'huggingface' },
      ],
      admin: {
        description: "Select where your model is hosted",
      },
    },
    // settings
    {
      name: "settings",
      type: "array",
      required: false,
      defaultValue: [],
      label: "Model Settings",
      admin: { description: "Select the settings that your model accepts" },
      fields: [
        {
          type: "text",
          name: "name",
          label: "Setting Name",
          required: true,
          admin: { placeholder: `Name as mentioned in model's docs` },
        },
        {
          type: "select",
          name: "type",
          label: "Setting Type",
          required: true,
          defaultValue: "integer",
          admin: { description: `Data type as mentioned in model's docs` },
          options: [
            { label: "integer", value: "integer" },
            { label: 'float', value: 'float' },
            { label: "string", value: "string" },
            { label: "boolean", value: "boolean" },
            { label: "select", value: "select" },
          ],
        },
        {
          type: 'number',
          name: 'minValue',
          label: 'Minimum Value',
          admin: {
            description: 'Minimum value for number inputs (integer/float)',
            condition: (data, siblingData) => 
              ['integer', 'float'].includes(siblingData?.type),
          },
        },
        {
          type: 'number',
          name: 'maxValue',
          label: 'Maximum Value',
          admin: {
            description: 'Maximum value for number inputs (integer/float)',
            condition: (data, siblingData) => 
              ['integer', 'float'].includes(siblingData?.type),
          },
        },
        {
          type: 'number',
          name: 'defaultValue',
          label: 'Default Value',
          admin: {
            description: 'Default value for the setting',
            condition: (data, siblingData) => 
              ['integer', 'float'].includes(siblingData?.type),
          },
          validate: (value, { siblingData }) => {
            if (['integer', 'float'].includes(siblingData?.type)) {
              const min = siblingData?.minValue
              const max = siblingData?.maxValue
              if (value < min) return `Default value must be >= ${min}`
              if (value > max) return `Default value must be <= ${max}`
              if (siblingData?.type === 'integer' && !Number.isInteger(value)) {
                return 'Default value must be an integer'
              }
            }
            return true
          },
        },
        {
          type: "text",
          label: "Setting Description",
          name: "description",
          required: true,
          admin: {
            placeholder: "Add information like the range of input",
            description: `Description as mentioned in model's docs`,
          },
        },
        {
          name: "options",
          type: "text",
          hasMany: true,
          // required: true,
          // defaultValue: [],
          label: 'Options for "select" type',
        },
        {
          name: "allowMultiple",
          type: "checkbox",
          label: "Allow Multiple",
          required: true,
          defaultValue: false,
          admin: {
            description:
              'Allow user to select multiple options for "select" type',
          },
        },
      ],
    },
    {
      type: "checkbox",
      name: "enablePrompt",
      label: "Accepts Prompt",
      defaultValue: true,
      required: true,
    },
    {
      type: "checkbox",
      name: "systemprompt",
      label: "Accepts System Prompt",
      defaultValue: false,
      required: true,
    },
    {
      type: "checkbox",
      name: "negativeprompt",
      label: "Accepts Negative Prompt",
      defaultValue: false,
      required: true,
    },
    // prodkeys
    {
      name: "prodkeys",
      label: "Prod Provider Keys",
      type: "group",
      fields: [groq, replicate, openai, falai , runway],
    },
    // testkeys
    {
      name: "testkeys",
      label: "Test Provider Keys",
      type: "group",
      fields: [groq, replicate, openai, falai , runway],
    },
    {
      type: "number",
      name: "cost",
      label: "Computation Cost",
      required: true,
      defaultValue: 0,
      validate: (val) => {
        if (val < 0) {
          return "Cost cannot be negative";
        }
        return true;
      },
      admin: {
        description: "Enter the cost of your model",
      },
    },
    // applicable or not
    {
      type: "checkbox",
      label: "Commission Applicable",
      name: "commissionapplicable",
      required: true,
      defaultValue: false,
    },
    // commission
    {
      type: "number",
      name: "commision",
      label: "Commission",
      required: true,
      defaultValue: 0,
      validate: (val) => {
        if (val < 0) {
          return "Commission cannot be negative";
        }
        return true;
      },
      admin: {
        condition: (data) => data.commissionapplicable,
        description: "Enter the commission",
      },
    },
    
    // model examples
    {
      type: "array",
      name: "examples",
      label: "Prompt Examples",
      required: false,
      fields: [
        {
          type: "text",
          name: "example",
          label: "Example Prompt",
          required: false,
          admin: {
            placeholder: "your prompt",
          },
        },
      ],
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
  ],
};
