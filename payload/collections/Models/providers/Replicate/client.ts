import Replicate from "replicate";
enum ASRTIO {
  "1:1",
  "16:9",
  "21:9",
  "2:3",
  "3:2",
  "4:5",
  "5:4",
  "9:16",
  "9:21",
}
enum OPFRMT {
  "webp",
  "jpg",
  "png",
}

interface ReplicateInputProps {
  apiKey: string;
  model: `${string}/${string}` | `${string}/${string}:${string}`;
  prompt: string;
  systemPrompt?: string;
  negativePrompt?: string;
  image?: any;
  settings: {
    seed?: number;
    top_k?: number; 
    top_p?: number; // default 0.95
    temperature?: number; // default 0.7
    length_penalty?: number; // default 1
    presense_penalty?: number; // default 0
    maxTokens?: number; // default 128
    minTokens?: number;
    stopSequences?: Array<string>;
    face_image?: string;
    number_of_images?: 1;
    cfg?: number; // default 3.5, max 20 min 0
    steps?: number; // default 28 max 28 min 1
    aspect_ratio?: ASRTIO; // default 1:1
    output_format?: OPFRMT;
    output_quality?: number; // default 90 max 100 min 10
    prompt_strength?: number;
    upscale?: number; // default 2 max 10 min 1
    face_upsample?: boolean; //default true
    background_enhance?: boolean; //default true
    codeformer_fidelity?: number; //default 0.5 max 1 min 0
    voice?: string;
    speed?: number;
  };
}

const runReplicate = async (args: ReplicateInputProps) => {
  const {
    apiKey,
    model,
    prompt,
    settings,
    image,
    negativePrompt,
    systemPrompt,
  } = args;
  const {
    length_penalty,
    maxTokens,
    minTokens,
    presense_penalty,
    seed,
    stopSequences,
    temperature,
    top_k,
    top_p,
    face_image,
    aspect_ratio,
    background_enhance,
    cfg,
    codeformer_fidelity,
    output_format,
    face_upsample,
    number_of_images,
    output_quality,
    prompt_strength,
    steps,
    upscale,
  } = settings;

  const replicate = new Replicate({ auth: apiKey });

  if (model.includes("kokoro")) {
    const kokoroInput = {
      text: prompt,
      voice: settings.voice || "hf_alpha",
      speed: settings.speed || 1.0, 
    };

    const output = await replicate.run(model, { input: kokoroInput });
    return {audioUrl: output};
  }

  const input = {
    system_prompt: systemPrompt,
    prompt: prompt,
    seed: seed,
    top_k: top_k,
    top_p: top_p,
    temperature: temperature,
    length_penalty: length_penalty,
    max_new_tokens: maxTokens,
    min_new_tokens: minTokens,
    stop_sequences: stopSequences,
    presense_penalty: presense_penalty,
    width: 768,
    height: 768,
    negative_prompt: negativePrompt,
    face_image: image,
    number_of_images: 1,
    cfg: cfg,
    steps: steps,
    aspect_ratio: aspect_ratio,
    output_format: output_format || "jpg",
    output_quality: output_quality,
    prompt_strength: prompt_strength,
    image: image,
  };
  const output = await replicate.run(model, { input: input });
  return output;
};
export default runReplicate;
