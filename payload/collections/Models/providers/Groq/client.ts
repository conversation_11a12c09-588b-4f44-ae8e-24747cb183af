import Groq from "groq-sdk";
import fs from "fs";

interface GroqInputProps {
  apiKey: string;
  model: string;
  prompt: string;
  systemPrompt?: string;
  negativePrompt?: string;
  image?: any;
  audio?: any;
  settings: {
    temperature?: number;
    maxtokens?: number;
    seed?: number;
    stopSequences?: Array<string>;
    // streaming?: boolean;
    frequency_penalty?: number;
    presence_penalty?: number;
    top_p?: number;
    maxRetries?: number;
    language: string;
  };
}

const runGroq = async (args: GroqInputProps) => {
  const {
    apiKey,
    model,
    settings,
    audio,
    image,
    prompt,
    negativePrompt,
    systemPrompt,
  } = args;
  const {
    maxtokens,
    temperature,
    seed,
    stopSequences,
    frequency_penalty,
    presence_penalty,
    top_p,
    maxRetries,
    language,
    // streaming,
  } = settings;
  try {
    const groq = new Groq({ apiKey: apiKey, maxRetries: maxRetries });

    let result, metadata;
    if (audio) {
      const transcriptions = await groq.audio.transcriptions.create({
        // audio
        file: fs.createReadStream("./audio.mp3"),
        model: "whisper-large-v3",
        prompt: prompt,
        temperature: temperature,
        language: language,
      });

      result = transcriptions.text;
    } else {
      if (image) {
        const res = await groq.chat.completions.create({
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: negativePrompt,
                },
                {
                  type: "image_url",
                  image_url: {
                    url: image,
                  },
                },
              ],
            },
          ],
          model: model,
          temperature: temperature,
          max_tokens: maxtokens,
          seed: seed,
          stop: stopSequences,
          frequency_penalty: frequency_penalty,
          presence_penalty: presence_penalty,
          top_p: top_p,
          // stream: streaming,
        });

        result = res.choices[0].message.content;
        metadata = res.usage;
      } else {
        const res = await groq.chat.completions.create({
          messages: [
            {
              role: "system",
              content: systemPrompt || "",
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          model: model,
          temperature: temperature,
          max_tokens: maxtokens || 500,
          seed: seed,
          stop: stopSequences,
          frequency_penalty: frequency_penalty,
          presence_penalty: presence_penalty,
          top_p: top_p,
          // stream: streaming,
        });

        result = res.choices[0].message.content;
        metadata = res.usage;
      }
    }

    return {
      result: result,
      metadata: metadata,
    };
  } catch (e) {
    console.log("Error in the client of groq", e.message);
    return { error: e.message };
  }
};
export default runGroq;