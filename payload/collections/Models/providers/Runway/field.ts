import { Field } from "payload/dist/fields/config/types";

export const runway: Field = {
  type: "group",
  name: "runway",
  admin: {
    description: "Add your Runway keys",
    condition: (data) => data.provider === "runway",
  },
  fields: [
    {
      type: "collapsible",
      label: "Keys",
      fields: [
        {
          type: "text",
          name: "modelname",
          required: true,
        },
        {
          type: "text",
          name: "apikey",
          required: true,
        },
      ],
    },
  ],
};
