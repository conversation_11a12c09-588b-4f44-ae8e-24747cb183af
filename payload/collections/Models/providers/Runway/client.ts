// npm install --save @runwayml/sdk
import RunwayML from '@runwayml/sdk';

// The env var RUNWAYML_API_SECRET is expected to contain your API key.
//const client = new RunwayML();

// enum ASRTIO {
//   "1280:720",
// "720:1280",
// "1104:832",
// "832:1104",
// "960:960",
// "1584:672",
// "1280:768",
// "768:1280",
// }

interface RunwayInputProps {
    apiKey: string;
    model: 'gen4_turbo' | 'gen3a_turbo' | any;
    promptImage?: string,
    promptText?: string;
    ratio?:  "1280:720" | "720:1280" | "1104:832" | "832:1104" | "960:960" | "1584:672" | "1280:768" | "768:1280" ; 
      settings?: {
        seed?: number;
        duration?: '5' | '10' | any ;
       // ratio?: string;s
        //ratio?: ASRTIO; // default 1:1
      };
  }

const runRunway = async(args: RunwayInputProps)  => {

  const { apiKey, model , promptImage ,promptText  , ratio,settings } = args;
  const { seed, duration  }  = settings|| {};

  try {

  // Instantiate the client with the provided API key (not from env)
  const client = new RunwayML({ apiKey:apiKey });

  const imageToVideo = await client.imageToVideo.create({
    // model: 'gen4_turbo',
    model: model,
    promptImage: promptImage,
    promptText: promptText,
    ratio: "1280:720",
    // seed: 2164844203,
    seed: seed ? seed : 2164844203, // Use the provided seed or undefined
   // ratio: ratio ? ASRTIO[ratio] : undefined, // Map ASRTIO enum to expected string literals
    duration: duration , 
  });
  
  const taskId = imageToVideo.id;

  // Poll the task until it's complete
  let task: Awaited<ReturnType<typeof client.tasks.retrieve>>;
  do {
    // Wait for ten seconds before polling
    await new Promise(resolve => setTimeout(resolve, 10000));

    task = await client.tasks.retrieve(taskId);
  } while (!['SUCCEEDED', 'FAILED'].includes(task.status));

  return task.output;
}
catch (error) {
  console.error("Runway Client Error:", error);
    throw new Error(
      "Please contact support."
    );
}

}

export default runRunway;



 
  
   

    