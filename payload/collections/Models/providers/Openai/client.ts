import fs from "fs";
import OpenA<PERSON>, { toFile } from "openai";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage } from "@langchain/core/messages";
import os from "os";
import path from "path";

interface OpenaiInputProps {
  apiKey: string;
  model: string;
  prompt: string;
  image?: string | string[]; // Can be a URL, Base64 string, or file path(s)
  settings: {
    maxtokens?: number;
    frequencyPenalty?: number;
    maxConcurrency?: number;
    stopSequences?: Array<string>;
    temperature?: number;
    topP?: number;
    presencePenalty?: number;
  };
  saveImageToFile?: boolean;
}

const isLocalFilePath = (str: string) => {
  return fs.existsSync(str);
};

const runOpenAi = async (args: OpenaiInputProps) => {
  const { apiKey, model, prompt, settings, image, saveImageToFile } = args;
  const {
    maxtokens,
    frequencyPenalty,
    maxConcurrency,
    presencePenalty,
    stopSequences,
    temperature,
    topP,
  } = settings;

  try {
    const openai = new OpenAI({ apiKey });
    if (model === "gpt-image-1") {
      const isDataUrl = typeof image === "string" && image.startsWith("data:image");
      let localImagePath: string | null = null;

      if (isDataUrl) {
        //Extract base64 from data URL
        const base64Data = image.split(";base64,").pop();
        if (!base64Data) {
          return { error: "Invalid base64 image data." };
        }

        // Create temporary image file path
        const tempDir = os.tmpdir();
        localImagePath = path.join(tempDir, `temp_image_${Date.now()}.png`);

        // Write to file
        fs.writeFileSync(localImagePath, Buffer.from(base64Data, "base64"));
      }

      if (image && localImagePath) {
        const result = await openai.images.edit({
          model: "gpt-image-1",
          image: await toFile(fs.createReadStream(localImagePath), null, {
            type: "image/png",
          }),
          prompt,
        });

        const image_base64 = result.data[0].b64_json;
        const imageUrl = `data:image/png;base64,${image_base64}`;
        if (saveImageToFile && image_base64) {
          fs.writeFileSync("edited-image.png", Buffer.from(image_base64, "base64"));
        }

        return {
          result: "Image edited",
          imageUrl,
        };
      }

      if (!image) {
        const result = await openai.images.generate({
          model: "gpt-image-1",
          prompt,
        });

        const image_base64 = result.data[0].b64_json;
        const imageUrl = `data:image/png;base64,${image_base64}`;
        if (saveImageToFile && image_base64) {
          fs.writeFileSync("generated-image.png", Buffer.from(image_base64, "base64"));
        }

        return {
          result: "Image generated",
          imageUrl,
        };
      }

      return {
        error: "Invalid image input. Must be a base64 string or a valid local path.",
      };
    }
    
    // ✅ TEXT/VISION MODEL (image as URL or Base64)
    let config: any;
    if (model === "o3-mini") {
      config = {
        model,
        max_completion_tokens: maxtokens || 500,
        apiKey,
        frequencyPenalty: frequencyPenalty || 0,
        maxConcurrency: maxConcurrency || 1,
        topP: topP || 1,
        presencePenalty: presencePenalty || 0,
      };
    } else {
      config = {
        model,
        maxTokens: maxtokens || 500,
        apiKey,
        frequencyPenalty: frequencyPenalty || 0,
        maxConcurrency: maxConcurrency || 1,
        temperature: temperature || 0.5,
        topP: topP || 1,
        presencePenalty: presencePenalty || 0,
      };
    }

    if (stopSequences && stopSequences.length > 0) {
      config.stopSequences = stopSequences;
    }

    const chat = new ChatOpenAI(config);
    const textContent = { type: "text", text: prompt };

    let imageContent = null;
    if (typeof image === "string" && !isLocalFilePath(image)) {
      imageContent = {
        type: "image_url",
        image_url: {
          url: image,
        },
      };
    }

    const message = new HumanMessage({
      content: imageContent ? [textContent, imageContent] : [textContent],
    });

    const res = await chat.invoke([message]);

    return {
      result: res.content,
      metadata: res.usage_metadata,
    };
  } catch (e: any) {
    console.error("Error in runOpenAi:", e.message);
    return { error: e.message };
  }
};

export default runOpenAi;