
import { fal } from "@fal-ai/client";

interface FalAiInputProps {
  apiKey: string;
  model: string;
  prompt?: string; // Made optional since image models might not need it
  text?: string;
  image?: string; // Changed to string for URL
  settings?: {
    seed?: number | null;
    steps?: number | 1;
    seconds_start?: number;
    seconds_total?: number;
    num_inference_steps?: number;
    guidance_scale?: number;
    num_frames?: number;
    export_fps?: number;
    strength?: number;
    stability?: number;
    similarity_boost?: number;
    voice?: string;
    temperature?: number;
    repetition_penalty?: number;
  };
  image_url?: string;
}

interface FalAiResponse {
  audioUrl?: string;
  imageUrl?: string;
  videoUrl?: string;
  requestId?: string;
  rawResponse: any;
}

interface FalAIOutput {
  image_url?: string;
  video_url?: string;
  audio_url?: string;
  // Add other potential output properties based on Fal AI API documentation
  [key: string]: any; // Allow for other dynamic properties
}

const runFalAi = async (args: FalAiInputProps): Promise<FalAiResponse> => {
  const { apiKey, model, prompt, text,  image, settings } = args;
  const {
    export_fps,
    guidance_scale,
    num_frames,
    num_inference_steps,
    seconds_start,
    seconds_total,
    seed,
    strength,
    steps,
    stability,
    similarity_boost,
    voice,
    temperature,
    repetition_penalty,
  } = settings || {};

  try {
    fal.config({
      credentials: apiKey,
    });

    // Base input object
    const input: any = {
      ...(prompt && { prompt }),
      ...(image && { image_url: image }),
       ...(prompt && { text:prompt }),
    };

    //integrating run code hngbbg bghuiii nhff 

    // Add settings if they exist
    if (settings) {
      input.seed = seed ?? 800000;
      input.seconds_start = seconds_start || 0;
      input.seconds_total = seconds_total || 30;
      input.strength = strength || 0.95
      input.num_inference_steps = num_inference_steps || 40;
      input.guidance_scale = guidance_scale || 3.5;
      input.num_frames = num_frames || 16;
      input.export_fps = export_fps || 8;
      input.steps = steps || 10;
      input.stability = stability || 0.5;
      input.similarity_boost = similarity_boost || 0.75;
      input.voice = voice          //not working with voide
      input.temperature = temperature || 0.7;
      input.repetition_penalty = repetition_penalty || 1.2;
    } 

    // Submit the request to the Fal AI API
    const result = await fal.subscribe(model, {
      input,
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === "IN_PROGRESS") {
          update.logs.map((log) => log.message).forEach(console.log);
        }
      },
    });

    // Determine the output type based on the model/response
    const response: FalAiResponse = {
      rawResponse: result,
      requestId: result.requestId,
    };

    // Check for audio output
    if (result.data?.audio_file?.url || result.data?.audio?.url) {
      response.audioUrl = result?.data?.audio_file?.url || result?.data?.audio?.url ;
    }

      // Check for image output based on the provided response structure
      if (result.data?.images) {
        response.imageUrl = result.data.images[0]?.url;
      }else{
        response.imageUrl = result.data.image?.url;
      }

     // Check for video output
     if (result.data?.video?.url) {
      response.videoUrl = result.data.video.url;
    }
    
    return response;

  } catch (error) {
    console.error("FalAIClient Error:", error);
    throw new Error("Failed to communicate with Fal AI. Please contact support.");
  }
};

export default runFalAi;