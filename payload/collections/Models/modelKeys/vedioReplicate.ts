import { Field } from "payload/types";

export const replicateVedioKeys: Field = {
  type: "group",
  name: "videoreplicate",
  label: "Replicate Keys",
  admin: {
    description: "Add your Replicate keys",
    condition: (data) =>
      data.type === "video" && data.provider.video === "replicate",
  },
  fields: [
    {
      type: "collapsible",
      label: "Test Keys",
      fields: [
        {
          type: "text",
          name: "testname",
          label: "Model Name",
          admin: { placeholder: "model name" },
          required: true,
        },
        {
          type: "text",
          label: "Replicate Api Token",
          name: "testapikey",
          admin: {
            placeholder: "REPLICATE_API_TOKEN",
          },
          required: true,
        },
      ],
    },
    {
      type: "collapsible",
      label: "Prod Keys",
      fields: [
        {
          type: "text",
          name: "prodname",
          label: "Model Name",
          admin: { placeholder: "model name" },
          required: true,
        },
        {
          type: "text",
          label: "Replicate Api Token",
          name: "prodapi<PERSON>",
          admin: {
            placeholder: "REPLICATE_API_TOKEN",
          },
          required: true,
        },
      ],
    },
  ],
};
