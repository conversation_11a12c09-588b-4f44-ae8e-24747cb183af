import { CollectionConfig } from "payload/types";
import { admins } from "../../access/admins";
import { adminsOrLoggedIn } from "../../access/adminsOrLoggedIn";
import readAccess from "./access/readAccess";
import { addAuditLogs } from "../globalHooks/auditlog";


export const ContactInfo: CollectionConfig = {
  slug: "contactInfo",
  admin: {
    useAsTitle: "email",
    defaultColumns: ["userName", "email"],
    group:"Admin",
    hidden: ({ user }) => user.role !== 'admin',
  },

  access: {
    read: readAccess, 
    create: () => true, 
    update: admins, 
    delete: admins, 
  },
  hooks:{
    beforeChange:[addAuditLogs]
  },
  fields: [
    {
      name: "userName",
      label: "User Name",
      type: "text",
      // required: true,
    },
    {
      name: "email",
      type: "email", 
      label: "Email Address",
      // required: true,
    },
    {
      name: 'phoneNumber',
      type: 'number',
      label: "Phone Number",
      // required: true,
    },
    {
      name: "organizationName",
      label: "Organization Name",
      type: "text",
      // required: true,
    },
    {
      name: "query",
      type: "text",
      required: true,
      label: "Query",
    },
    {
      name: "courseName",
      label: "Course Name",
      type: "text",
      // required: true,
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
    {
      name: "updatedBy",
      label: "Updated By",
      type: "text",
      access: {
        read: ({ req: { user } }) => user?.role === "admin",
      },
      admin: {
        position: "sidebar",
        readOnly: true,
      },
    },
   ],
  timestamps: true,
};
