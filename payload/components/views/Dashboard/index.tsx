import React from 'react';
import { useTranslation } from 'react-i18next';
//import { useNavigate } from 'react-router-dom'; // Updated import

import type { Props } from './types';
import { Gutter } from '../../elements/Gutter';
import './index.scss';
import HeroComponent from '../../elements/HeroComponent';

const baseClass = 'dashboard';

const Dashboard: React.FC = () => {
 // const navigate = useNavigate(); // Replaced useHistory with useNavigate
  const { i18n, t } = useTranslation('general');

  return (
    <>
      <div className={baseClass}>
        <Gutter className={`${baseClass}__wrap`}>
          <HeroComponent />
        </Gutter>
      </div>
    </>
  );
};

export default Dashboard;