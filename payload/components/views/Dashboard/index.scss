@import '../../../style.css';

$blue: #3498db;
$dark-blue: #2980b9;
$carrot: #e67e22;
$green: #2ecc71;
$silver: #bdc3c7;
$cloud: #ecf0f1;
$general-padding: 20px;


.dashboard {
  width: 100%;
  --gap: var(--base);
  --cols: 5;

  &__wrap {
    padding-bottom: var(--spacing-view-bottom);
    display: flex;
    flex-direction: column;
    gap: var(--base);
  }

  &__group {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }

  &__label {
    margin: 0;
  }

  &__card-list {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    gap: var(--gap);
    flex-wrap: wrap;

    li {
      width: calc(100% / var(--cols) - var(--gap) / var(--cols) * (var(--cols) - 1));
    }

    .card {
      height: 100%;
      border-radius: var(--style-radius-m);
    }

    
  }

  h1 {
    color: blueviolet;
  }

  // @include large-break {
  //   --cols: 4;
  // }

  // @include mid-break {
  //   --gap: var(--base);
  //   --cols: 2;
  // }

  // @include small-break {
  //   --cols: 1;

  //   &__wrap {
  //     gap: var(--base);
  //   }
  // }
  
h1, h2, h3, h4, h5, h6 {
  margin: 0;
}

.icon {
  width: 35px;
  height: 35px;
  margin-right: 10px;
  
  &:last-child {
    margin: 0;
  }
}

.icon-orange {
  background-color: $carrot;
}
.icon-green {
  background-color: $green;
}
.icon-rounded {
  border-radius: 50%;
}
.icon-triangle {
  width: 0px;
  height: 0px;
  border: 5px solid transparent;
  border-left: 10px solid gray;
}
.icon-small {
  width: 20px;
  height: 20px;
}

.container {
  padding: 0px $general-padding;
}


.header-right {
  display: flex;
}

.content-body {
  display: flex;
  flex-direction: row;
}

.main-body {
  display: flex;
  flex-direction: column;
  background-color: $cloud;
  width: calc(100vw - 200px);
  /*
    100vw width of the viewport's width
    200px is the width of the sidebar.
  */
}

.main-content {
  padding: $general-padding;
  
  /*
    100vh height content of the viewport's height
    150px is the height of the header-bar(50px), breadcrumb(50px), and current-bar(50px).
  */
}

.sidebar {
  width: 200px;
  display: flex;
  flex-direction: column;
  background-color: $silver;
  height: calc(100vh - 50px);
  /*
    100vh height content of the viewport's height
    50px is the height of the header-bar
  */
}

.sidebar-link {
  padding: 15px $general-padding;
  border-bottom: 1px solid black;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  
  > .icon {
    margin-right: 10px;
  }
}

.breadcrumb {
  background-color: $cloud;
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0px $general-padding;
}

.current-bar {
  height: 50px;
  background-color: $dark-blue;
  display: flex;
  align-items: center;
  padding: 0px $general-padding;
  color: white;
}

.status {
  display: inline-flex;
  margin-right: $general-padding;
}
.status-row {
  margin-bottom: $general-padding;
}
.status-logo {
  height: 60px;
  width: 60px;
  background-color: $carrot;
}
.status-info {
  height: 60px;
  width: 130px;
  background-color: $silver;
}

.stat-content {
  height: 60px;
  width: 100%;
  background-color: $green;
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100% - 64px - 20px);
  /*
    100% height content of main content
    64px is the height of status-row
    20px is the height of the top-padding of main-content
  */
}
}

