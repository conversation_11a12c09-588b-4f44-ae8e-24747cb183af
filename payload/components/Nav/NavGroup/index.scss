@import '../../../scss/styles.scss';

.nav-group {
  width: 100%;
  margin-bottom: base(0.5);

  &__toggle {
    cursor: pointer;
    color: var(--theme-elevation-400);
    background: transparent;
    padding-left: 0;
    border: 0;
    margin-bottom: base(0.25);
    width: 100%;
    text-align: left;
    display: flex;
    align-items: flex-start;
    padding: 0;
    gap: base(0.5);
    justify-content: space-between;

    svg {
      flex-shrink: 0;
      margin-top: base(-0.2);
    }

    &:hover,
    &:focus-visible {
      color: var(--theme-elevation-1000);

      .stroke {
        stroke: var(--theme-elevation-1000);
      }
    }

    &:focus-visible {
      outline: none;
    }
  }

  &__indicator {
    position: relative;
    flex-shrink: 0;

    svg .stroke {
      stroke: var(--theme-elevation-200);
    }
  }

  &--collapsed {
    .collapsible__toggle {
      border-bottom-right-radius: $style-radius-s;
      border-bottom-left-radius: $style-radius-s;
    }
  }
}