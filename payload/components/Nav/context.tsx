import { useWindowInfo } from '@faceless-ui/window-info';
import { clearAllBodyScrollLocks, disableBodyScroll, enableBodyScroll } from 'body-scroll-lock';
import React, { useEffect, useRef } from 'react';
//import { useNavigate } from 'react-router-dom'; // Changed from useHistory
import { usePreferences } from 'payload/dist/admin/components/utilities/Preferences';

type NavContextType = {
  navOpen: boolean;
  navRef: React.RefObject<HTMLDivElement>;
  setNavOpen: (value: boolean) => void;
};

export const NavContext = React.createContext<NavContextType>({
  navOpen: true,
  navRef: null,
  setNavOpen: () => {},
});

export const useNav = () => React.useContext(NavContext);

const getNavPreference = async (getPreference): Promise<boolean> => {
  const navPrefs = await getPreference('nav');
  const preferredState = navPrefs?.open;
  return typeof preferredState === 'boolean' ? preferredState : true;
};

export const NavProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    breakpoints: { l: largeBreak, m: midBreak, s: smallBreak },
  } = useWindowInfo();

  const { getPreference } = usePreferences();
 // const navigate = useNavigate(); // Changed from useHistory
  const navRef = useRef(null);
  const [navOpen, setNavOpen] = React.useState(false);

  useEffect(() => {
    if (largeBreak === false) {
      const setNavFromPreferences = async () => {
        const preferredState = await getNavPreference(getPreference);
        setNavOpen(preferredState);
      };
      setNavFromPreferences();
    }
  }, [largeBreak, getPreference, setNavOpen]);

  // Replace history.listen with a navigation effect
  useEffect(() => {
    if (midBreak) {
      const unlisten = () => {
        setNavOpen(false);
      };
      // In v6, you might need a different approach for navigation events
      // (e.g., using a global listener or a custom solution)
      return unlisten;
    }
  }, [midBreak, setNavOpen]);

  useEffect(() => {
    if (navRef.current) {
      if (navOpen && midBreak) {
        disableBodyScroll(navRef.current);
      } else {
        enableBodyScroll(navRef.current);
      }
    }
  }, [navOpen, midBreak]);

  useEffect(() => {
    if (largeBreak === false || midBreak === false || smallBreak === false) {
      setNavOpen(false);
    }
  }, [largeBreak, midBreak, smallBreak]);

  useEffect(() => {
    return () => {
      clearAllBodyScrollLocks();
    };
  }, []);

  return (
    <NavContext.Provider value={{ navOpen, navRef, setNavOpen }}>
      {children}
    </NavContext.Provider>
  );
};