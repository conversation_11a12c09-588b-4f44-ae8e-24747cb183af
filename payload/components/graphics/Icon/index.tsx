import Link from "next/link";
import React from "react";
import Button from "../../elements/Button";

import './index.scss'
import { ArrowLeft } from "lucide-react";

export const Icon: React.FC = () => {
  return (
  <>
    {/* <Link href="/">
      <button className="button">
        <ArrowLeft />
      </button>
    </Link> */}

    {/* <Link href="/"> */}
    <svg width="25" height="25" viewBox="0 0 227 259" fill="var(--theme-text)" xmlns="http://www.w3.org/2000/svg" className="mx-auto">
      <path d="M154.791 0.850622C194.721 5.62062 227.141 28.2106 226.101 72.2906C226.094 72.604 225.934 72.7606 225.621 72.7606H53.4206C53.1472 72.7606 52.9306 72.6506 52.7706 72.4306L0.0705611 0.600622C0.0313526 0.546671 0.00742821 0.482873 0.00146925 0.416372C-0.00448971 0.349871 0.00775278 0.283307 0.0368239 0.224126C0.065895 0.164946 0.110651 0.115495 0.166066 0.0813192C0.22148 0.0471429 0.285376 0.0295893 0.350575 0.0306295C45.1506 -0.0160372 89.9772 -0.00938152 134.831 0.0506185C143.811 0.0639518 150.464 0.330622 154.791 0.850622Z" />
      <path d="M52.5406 82.3006L225.391 82.3506C225.697 82.3506 225.817 82.5373 225.751 82.9106C219.011 121.531 193.331 147.861 154.511 153.901C149.951 154.614 143.307 154.974 134.581 154.981C93.3606 155.027 52.4573 155.034 11.8706 155.001C10.6973 154.994 10.4039 154.481 10.9906 153.461L51.3106 83.0106C51.4353 82.7947 51.6145 82.6154 51.8304 82.4908C52.0464 82.3662 52.2913 82.3006 52.5406 82.3006Z" />
      <path d="M34.2606 258.171C5.55057 235.601 -5.22943 199.641 6.67057 165.121C6.75724 164.881 6.92723 164.761 7.18057 164.761H87.6006C87.6532 164.761 87.705 164.774 87.7506 164.801C87.7963 164.827 87.8342 164.865 87.8605 164.911C87.8869 164.956 87.9008 165.008 87.9008 165.061C87.9008 165.113 87.8869 165.165 87.8606 165.211L35.0906 258.011C34.8773 258.384 34.6006 258.437 34.2606 258.171Z" />
    </svg>
    {/* </Link> */}

    {/* <Link href="/">
      <button className="button">
      RentPrompts
      </button>
    </Link> */}
  </>
)};
