import React from 'react'
import { Link } from 'react-router-dom'

import type { Props, RenderedTypeProps } from './types'

import './index.scss'

const baseClass = 'banner'

export const Banner: React.FC= (prop : Props) => {
  const { alignIcon, children,className, icon, onClick, to = "#", type} = prop
  const classes = [
    baseClass,
    `${baseClass}--type-${type}`,
    className && className,
    to && `${baseClass}--has-link`,
    (to || onClick) && `${baseClass}--has-action`,
    icon && `${baseClass}--has-icon`,
    icon && `${baseClass}--align-icon-${alignIcon}`,
  ]
    .filter(Boolean)
    .join(' ')

  let RenderedType: React.ComponentType<RenderedTypeProps> | string = 'div'

  if (onClick && !to) RenderedType = 'button'
  

  return (
    <div className={classes} onClick={onClick}>
      {icon && alignIcon === 'left' && <React.Fragment>{icon}</React.Fragment>}
      <span className={`${baseClass}__content`}>{children}

    {/*custom images */}
      <img
            className="header-desktop"
            src="https://github.com/ecemgo/mini-samples-great-tricks/assets/13468728/cce4084a-01a4-428d-961f-935bafe7a6e3"
            alt="Image"
             loading="lazy"
      />

      </span>
      {icon && alignIcon === 'right' && <React.Fragment>{icon}</React.Fragment>}
    </div>
  )
}

export default Banner
