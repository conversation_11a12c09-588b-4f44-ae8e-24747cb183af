
.banner {
  font-size: 1rem;
  line-height: base(1);
  border: 0;
  vertical-align: middle;
  background: var(--theme-elevation-100);
  color: var(--theme-elevation-800);
  // border-radius: $style-radius-s;
  padding: base(0.5);
  // margin-bottom: $baseline;

  &--has-action {
    cursor: pointer;
    text-decoration: none;
  }

  &--has-icon {
    display: flex;

    svg {
      display: block;
    }
  }

  &--type-default {
    &.button--has-action {
      &:hover {
        background: var(--theme-elevation-900);
      }

      &:active {
        background: var(--theme-elevation-950);
      }
    }
  }

  &--type-error {
    background: var(--theme-error-100);
    color: var(--theme-error-500);

    // svg {
    //   @include color-svg(var(--theme-error-500));
    // }

    &.button--has-action {
      &:hover {
        background: var(--theme-error-200);
      }

      &:active {
        background: var(--theme-error-300);
      }
    }
  }

  &--type-success {
    background: var(--theme-success-500);
    color: var(--color-base-800);

    &.button--has-action {
      &:hover {
        background: var(--theme-success-200);
      }

      &:active {
        background: var(--theme-success-200);
      }
    }
  }
}
