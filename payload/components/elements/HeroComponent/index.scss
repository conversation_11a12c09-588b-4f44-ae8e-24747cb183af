@import url('https://fonts.cdnfonts.com/css/hubot-sans');
@import '../../../style.css';

*{ box-sizing: border-box; -webkit-font-smoothing: antialiased; text-rendering: optimizeLegibility; scroll-behavior: smooth;}
html, body { height: 100%; }

html::-webkit-scrollbar { display: none; }
html { -ms-overflow-style: none; scrollbar-width: none; }

body {
    margin: 0;
    font-family: Untitled Sans, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
    background: --theme-overlay linear-gradient(0deg,rgba(216,236,248,.06),rgba(152,192,239,.06));
    //background: --theme-overlay linear-gradient(0deg,rgba(67,56,202),rgba(67,56,200,));

    font-size: max(calc(var(--_size) * 0.03), 10px);
    --_factor: min(600px, 80vh);
    --_size: min(var(--_factor), 80vw);
}

body.gold .header h2 ,
body.gold p,
body.gold > * > * :not(.contact-btn) {
    filter: invert(1) brightness(4.7);
}
body.gold .header h2 a{
    filter: hue-rotate(0deg);
}
body.gold canvas {
    filter: drop-shadow(2em 4em 0px #d8bd10) drop-shadow(-8em -14em 0px #d8bd10);
}
body.gold .header .spotlight {
    filter: invert(1) brightness(4.7) opacity(0.5);
}
body.gold .mountains > div {
    box-shadow: 
    -1em -0.2em 0.4em -1.1em #c2ccff,
    inset 0em 0em 0em 2px #d8a910,
    inset 0.2em 0.3em 0.2em -0.2em #c2ccff,
    inset 10.2em 10.3em 2em -10em #d4e6ff2f;
}

body.gold .content-section,
body.gold .content-section ::before,
body.gold .content-section ::after{
    filter: invert(1) brightness(4.4) opacity(1);
}

h2 {
    font-family: 'Hubot-Sans', sans-serif;
}
h2, p {
    margin: 0; padding: 0;
}
h2 a {
    text-decoration: none;
    color: unset;
}

.header {
    display: flex; width: 100%;
    justify-content: center;
    color: #bad6f7;
    padding: 2em;
    position: absolute;
    top: 0; left: 0; right: 0; margin: 0 auto;

    opacity: 0;
    translate: 0 -1em;
    animation: load 2s ease-in 2s forwards, up 1.4s ease-out 2s forwards;
}

.header > div.mid-spot {
    width: 2em; height: 2em;
    border-radius: 50%;
    background: black;
    position: absolute;
    
    box-shadow: 0 0 1em 0 #98c0ef;
    cursor: pointer;
    transition: box-shadow 1s ease-in-out;
}
.header > div.mid-spot:hover {
    box-shadow: -0.3em 0.1em 0.2em 0 #d8bd10;
}
body.gold .header > div.mid-spot:hover {
    box-shadow: -0.3em 0.1em 0.2em 0 #98c0ef;
}

body.gold .header > div.mid-spot {
    box-shadow: 0 0 1em 0 #d8bd10;
}

.header > :nth-child(2):hover ~ .spotlight {
    animation: colorize 10s linear infinite;
}
@keyframes colorize {
    0%{filter: hue-rotate(0deg); }
    100% {filter: hue-rotate(-380deg);}
}

button.contact-btn {
    position: absolute; left: min(46em, 60vw); right: 0; margin: 0 auto;
    width: 8em; height: 2.4em;
    cursor: pointer;
    border-radius: 20em;
    border: none;
    transition: background 0.5s, transform 0.5s;
    
    border: 1px solid #c2ccff33;
    opacity: 1;
    padding-top: 0.3em;

    background: #121521;
    color: #9dc3f7;
    white-space:nowrap;
}
button.contact-btn::before {
    content: ''; display: block; position:absolute; left: 0; top:0; border-radius: 20em;
    width: 100%; height: 100%;
    background: #c2ccff91 radial-gradient(farthest-side at 50% 100%, #c2ccff, transparent);
    opacity: 0.1;
    transition: all 0.4s ease-in-out;
}
button.contact-btn:hover::before {
    background: #c2ccff1e radial-gradient(farthest-side at 50% 100%, #c2ccff, transparent);
    opacity: 0.3;
}
.contact-btn .glow {
    --border-width: 1px;
    --loop-cycle: 24s;
    position: absolute;
    inset: calc(var(--border-width)* -1);
    border-radius: 50px;
    border: var(--border-width) solid transparent;
    -webkit-mask: linear-gradient(transparent, transparent), linear-gradient(white, white);
    mask: linear-gradient(transparent, transparent), linear-gradient(white, white);
    -webkit-mask-clip: padding-box, border-box;
    mask-clip: padding-box, border-box;
    -webkit-mask-composite: source-in, xor;
    mask-composite: intersect;
    pointer-events: none;
}
button.contact-btn:hover .glow {
    --loop-cycle: 2s;
}
.contact-btn .glow::after, .contact-btn .glow::before {
    content: "";
    height: 100%;
    offset-anchor: 100% 50%;
    background: radial-gradient(circle at 50% 50%, hsla(0, 0%, 100%, 0.75), transparent 50%), radial-gradient(circle at 50% 50%, #c2ccff 50%, transparent);
    opacity: 0.4;
    offset-path: rect(0 100% 100% 0 round 33px);
    position: absolute;
    display: inline-block;
    -webkit-animation: loop 4s linear infinite;
    animation: loop var(--loop-cycle) linear infinite;
    aspect-ratio: 1 / 1;
    transition: opacity 1s ease-in-out;
}
.contact-btn .glow::before {
    animation-delay: calc( var(--loop-cycle) / -2 );
}
@keyframes loop {
    100% {
        offset-distance: 100%;
    }    
}
.contact-btn-content {
    background: linear-gradient(0deg, #d8ecf8, #98c0ef);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.2em;
    line-height: 142%;
}
button.contact-btn:hover .glow::after, button.contact-btn:hover .glow::before {
    opacity: 0.6;
}
@keyframes btn-rot {
    0% { rotate: 0deg; }
    100% { rotate: 360deg; } 
}

.header .spotlight {
    pointer-events: none;
    position: absolute; left: 0; right: 0; top:0; margin: 0 auto;
    transition: filter 1s ease-in-out;

    height: 42em; width: 100%;
    overflow: hidden;
}
.header .spotlight > div {
    border-radius: 0 0 50% 50%;
    position: absolute; left: 0; right: 0; margin: 0 auto; 
    top: 3em;
    width: 30em; height: max(42em, 86vh);
    background-image: conic-gradient(from 0deg at 50% -5%, transparent 45%, rgba(124, 145, 182, .3) 49%, rgba(124, 145, 182, .5) 50%, rgba(124, 145, 182, .3) 51%, transparent 55%);
    transform-origin: 50% 0;
    filter: blur(15px) opacity(0.5);
    z-index: -1;
    animation: load 2s ease-in-out forwards, loadrot 2s ease-in-out forwards, spotlight 21s ease-in-out infinite reverse;
}
.header .spotlight > div:nth-child(1){ 
    rotate: 20deg;
    animation: load 2s ease-in-out forwards, loadrot 2s ease-in-out forwards, spotlight 17s ease-in-out infinite;
}
.header .spotlight > div:nth-child(2){ 
    rotate: -20deg;
    animation: load 2s ease-in-out forwards, loadrot 2s ease-in-out forwards, spotlight 14s ease-in-out infinite;
}
@keyframes loadrot {
    0% { rotate: 0deg; scale: 0;}
    100% { scale: 1;}
}
@keyframes spotlight {
    0% {
        transform: rotateZ(0deg) scale(1);
        filter: blur(15px) opacity(0.5);
    }
    20% {
        transform: rotateZ(-1deg) scale(1.2);
        filter: blur(16px) opacity(0.6);
    }    
    40% {
        transform: rotateZ(2deg) scale(1.3);
        filter: blur(14px) opacity(0.4);
    }    
    60% {
        transform: rotateZ(-2deg) scale(1.2);
        filter: blur(15px) opacity(0.6);
    }    
    80% {
        transform: rotateZ(1deg) scale(1.1);
        filter: blur(13px) opacity(0.4);
    }    
    100% {
        transform: rotateZ(0deg) scale(1);
        filter: blur(15px) opacity(0.5);
    }    
}

canvas#particleCanvas {
    position: absolute; pointer-events: none;
    animation: load 0.4s ease-in-out forwards;
    z-index: 1;
    width: 100%;
}

p {
    font-size: 1.5em;
}

.hero {
    width: 100%;
    height: 100%;
    max-height: 140px;
    position: absolute; 
    top: 16em;
    left: 5em;
}

.heroT {
    // font-size: 1em;
    font-size: 1em;
    position: absolute; 
    left: -11%;
    right: 0; 
    margin: auto;
    // height: 20em;
    padding-top: 2em;
    translate: 0 -1.6em;
    opacity: 0;
    animation: load 2s ease-in-out 0.6s forwards;
    text-align: center;
}
@media (max-width: 600px) {
    .heroT {
        font-size: 0.5em;
        // left: -5%;
    }
    .hero {
        left: 2.5em;
    }
}

@keyframes load {  
    0% { opacity: 0;}    
    100% { opacity: 1;}    
}
.heroT > h2 {
    position: absolute;
    left: 0; 
    right: 0; 
    margin: auto;
    width: fit-content;
    font-size: 3.5em;
    font-weight: 600;
    color: #9dc3f7;
    background:
    radial-gradient( 2em 2em at 50% 50%,
        transparent calc(var(--p) - 2em),
        #fff calc(var(--p) - 1em), 
        #fff calc(var(--p) - 0.4em), 
        transparent var(--p) 
    ),
    linear-gradient(0deg, #bad1f1 30%, #9dc3f7 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 16px rgba(174,207,242,.24);

    --p:0%;
    transition:--p 3s linear;
    
    animation: pulse 10s linear 1.2s infinite;
}

@media (max-width: 600px) {
    .heroT > h2{
        font-size: 4.2em;
    }
}

.heroT h2:nth-child(2) {
    background:
    radial-gradient( 2em 2em at 50% 50%,
        transparent calc(var(--p) - 2em),
        transparent calc(var(--p) - 1em),
        #fff calc(var(--p) - 1em), 
        #fff calc(var(--p) - 0.4em), 
        transparent calc(var(--p) - 0.4em), 
        transparent var(--p) 
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: blur(16px) opacity(0.4);
}
@keyframes pulse { 
    0% { --p:0%; }
    50% { --p:300%;}
    100% { --p:300%;}
}
@property --p {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 0%;
}

.heroP {
    font-size: 1.2em;
    position: absolute; left: 0; right: 0; top: 20.6em; margin: auto;
    height: fit-content; width: fit-content; text-align: center;
    opacity: 0;
    translate: 0 1em;
    animation: load 2s ease-out 2s forwards, up 1.4s ease-out 2s forwards;
    color: #d8ecf8;
    text-shadow: 0 2px 16px rgba(174,207,242,.24);
    background: linear-gradient(0deg, #d8ecf8 0, #98c0ef 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
@keyframes up {      
    100% { translate: 0; }    
}

@media (max-width: 600px) {
    .heroP{
        font-size: 1.5em;
        top: 15.6em;
    }
}

.heroSubP {
    position: absolute; left: 0; right: 0; top: 13em; margin: auto;
    height: fit-content;
    opacity: 0;
    translate: 0 -1em;
    animation: load3 2s ease-in 0s forwards, up 1.4s ease-out 0s forwards;
}
@keyframes load3 {  
    0% { opacity: 0;}    
    100% { opacity: 0.7;}    
}
.heroSubP p {
    font-size: 1em;
    position: relative; width: fit-content; margin: auto;
    color: #d8ecf8;
    text-shadow: 0 2px 16px rgba(174,207,242,.24);
    background: linear-gradient(0deg, #d8ecf8 0, #98c0ef 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.heroSubP p::before,
.heroSubP p::after {
    position: absolute; top: 60%; 
    display: block; content: '';
    width: 5em; height: 1px;
    opacity: 0;
    animation: load2 1.4s ease-in-out 0s forwards, up 1.4s ease-out 0s forwards;
}
@keyframes load2 {  
    0% { opacity: 0;}    
    100% { opacity: 0.3;}    
}
.heroSubP p::before{
    background: linear-gradient( -90deg, #9dc3f7 0%, transparent 100%);
    right: 120%;
    translate: -5em 0;
}
.heroSubP p::after {
    background: linear-gradient( 90deg, #9dc3f7 0%, transparent 100%);
    left: 120%;
    translate: 5em 0;
}

.accent-lines {
    pointer-events: none;
    position: absolute; top: 0; left: 0; right: 0;
    width: 100%; height: 42em;
    z-index: -2;
    --accent-lines-clr: rgba(186, 215, 247, .18);
}
.accent-lines > div {
    position: absolute; top: 0; right: 0; left: 0; margin: auto;
    height: 100%; width: 100%;
}
.accent-lines > div:nth-child(1) > div{
    position: absolute; top: 0; right: 0; left: 0; margin: auto;
    width: 100%; height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-lines-clr), transparent);
    opacity: 0; scale: 0;
    animation: accentload 2s ease-out 2.4s forwards;
}
.accent-lines > div:nth-child(1) > div:nth-child(1){ top: 6em; }
.accent-lines > div:nth-child(1) > div:nth-child(2){ top: 11em; }
.accent-lines > div:nth-child(1) > div:nth-child(3){ top: 16em; }
.accent-lines > div:nth-child(1) > div:nth-child(4){ top: 24em; }
.accent-lines > div:nth-child(1) > div:nth-child(5){ top: 29em; }
.accent-lines > div:nth-child(2) > div{
    position: absolute; top: 0; right: 0; left: 0; margin: auto;
    width: 1px; height: 100%;
    background: var(--accent-lines-clr);
}
@keyframes accentload {
    0% {
        opacity: 0; scale: 0;
    }
    100% {
        opacity: 1; scale: 1;
    }
}

.accent-lines > div:nth-child(2) > div {
    opacity: 0; scale: 0;
    animation: accentload 2s ease-out 2s forwards;
}
.accent-lines > div:nth-child(2) > div:nth-child(1){ left: 24em; }
.accent-lines > div:nth-child(2) > div:nth-child(2){ left: 34em; }
.accent-lines > div:nth-child(2) > div:nth-child(3){ right: 24em; }
.accent-lines > div:nth-child(2) > div:nth-child(4){ right: 34em; }

.accent-lines > div:nth-child(1) > div::before, 
.accent-lines > div:nth-child(1) > div::after { 
    content: ''; display: none; position: absolute;
    width: 0.2em; height: 0.2em; border-radius: 1em;
    background: #9dc3f7;
    left: 0; right: 0; margin: auto;
    translate: 0 -1px;

    opacity: 0; scale: 0;
    animation: accentload 2s ease-out 4.4s forwards;
}

.accent-lines > div:nth-child(1) > div:nth-child(3)::before{ display: block; left: 24em; }
.accent-lines > div:nth-child(1) > div:nth-child(3)::after{ display: block; right: 24em; }
.accent-lines > div:nth-child(1) > div:nth-child(4)::before{ display: block; left: 24em; }
.accent-lines > div:nth-child(1) > div:nth-child(4)::after{ display: block; right: 24em; }
.accent-lines > div:nth-child(1) > div:nth-child(5)::before{ display: block; left: 24em; }
.accent-lines > div:nth-child(1) > div:nth-child(5)::after{ display: block; right: 24em; }

.accent-lines > div:nth-child(1) > div:nth-child(2)::before,
.accent-lines > div:nth-child(1) > div:nth-child(2)::after{ 
    display: block; width: 5em; height: 1px; border-radius: 0; opacity: 0.12;
}
.accent-lines > div:nth-child(1) > div:nth-child(2)::before{
    right: 24em; rotate: 45deg; translate: -2.5em 2.5em;
}
.accent-lines > div:nth-child(1) > div:nth-child(2)::after{
    right: 24em; rotate: -45deg; translate: -2.5em 2.5em;
}
.accent-lines > div:nth-child(1) > div:nth-child(1)::before,
.accent-lines > div:nth-child(1) > div:nth-child(1)::after{ 
    display: block; width: 5em; height: 1px; border-radius: 0; opacity: 0.12;
}
.accent-lines > div:nth-child(1) > div:nth-child(1)::before{
    left: 24em; rotate: 45deg; translate: 2.5em 7.5em;
}
.accent-lines > div:nth-child(1) > div:nth-child(1)::after{
    left: 24em; rotate: -45deg; translate: 2.5em 7.5em;
}

.accent-lines > div:nth-child(1) > div:nth-child(2)::before,
.accent-lines > div:nth-child(1) > div:nth-child(2)::after {
    opacity: 0; scale: 0;
    animation: accentload2 2s ease-out 2.4s forwards;
}
.accent-lines > div:nth-child(1) > div:nth-child(1)::before,
.accent-lines > div:nth-child(1) > div:nth-child(1)::after {
    opacity: 0; scale: 0;
    animation: accentload3 2s ease-out 2.4s forwards;
} 
@keyframes accentload2 {
    0% {
        opacity: 0; scale: 0; transform: rotate(360deg);
    }
    50% { scale: 0; }
    100% {
        opacity: 0.12; scale: 1; transform: rotate(0deg);
    }
}
@keyframes accentload3 {
    0% {
        opacity: 0; scale: 0; transform: rotate(-360deg);
    }
    50% { scale: 0; }
    100% {
        opacity: 0.12; scale: 1; transform: rotate(0deg);
    }
}

.mountains {
    position: absolute; left: 0; right: 0; top: 31em; margin: auto;
    width: 100%; height: 10em;
    pointer-events: none;
}
.mountains::before {
    content: ''; display: block;
    width: 100%; height: 500%;
    position: absolute; top: 0%;
    // background: linear-gradient(90deg, #121521 0%, transparent 50%);
    // background: linear-gradient(0deg, #121521 80%, transparent 90%);
    background: linear-gradient(90deg, #02183e 0%, transparent 50%);
    background: linear-gradient(0deg, #02183e 80%, transparent 90%);
    z-index: 2;
}
.mountains > div {
    box-shadow: 
    -1em -0.2em 0.4em -1.1em #c2ccff,
    inset 0em 0em 0em 2px #c2ccff,
    inset 0.2em 0.3em 0.2em -0.2em #c2ccff,
    inset 10.2em 10.3em 2em -10em #d4e6ff2f;
    // background: #121521;
    background: #02183e;
    z-index: 1;
    filter: brightness(0.8);
    position: absolute; left: 0; right: 0; margin: auto;
    width: 20em; height: 20em;
    rotate: 45deg;
}
.mountains > div:nth-child(1) {
    bottom: -240%; 
    translate: -6em 2em;
    animation: mountainload1 2s ease-out 2.4s forwards;
}
.mountains > div:nth-child(2) {
    bottom: -240%;
    translate: -2em 0em;
    width: 14em; height: 20em;
    animation: mountainload2 2s ease-out 2.2s forwards;
}
.mountains > div:nth-child(3) {
    bottom: -240%; 
    translate: 6em 3em;
    animation: mountainload1 2s ease-out 2s forwards;
}
@keyframes mountainload1 {
    0% { bottom: -240%; }
    100% { bottom: -140%; }
}
@keyframes mountainload2 {
    0% { bottom: -240%; }
    100% { bottom: -108%; }
}
.mountains > div::before {
    content: ''; display: block;
    background: repeating-radial-gradient( at 100% 100%, transparent 0%, #c2ccff22 2px, transparent 4px);
    width: 12em; height: 12em;
    position: absolute;
    left: 0; top: 0;
    border-bottom-right-radius: 100%;
}

.hero-spacer {
    height: 40em;
    pointer-events: none;
}

.content-section {
    position: relative;
    z-index: 1112;
    color: #fff;
    width: 100%;
    text-align: center;
    padding: 16em 0 12em 0;
    overflow: hidden;
}

.content-section p.subt {
    color: #d8ecf8be;
    font-size: 0.8em;
    font-weight: 200;
    position: relative;
    width: fit-content;
    margin: auto;
}
.content-section h3 {
    margin: 0.4em 0 0.6em 0;
    font-size: 2.3em;
    font-weight: 600;
    color: #d8ecf8;
    text-shadow: 0 2px 16px rgba(174,207,242,.24);
    background: linear-gradient(0deg, #d8ecf8 0, #98c0ef 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.content-section p.subp {
    color: #d8ecf8be;
    font-size: 1em;
    font-weight: 400;
    max-width: 26em;
    margin: auto;
}

.content-section p.subt::before,
.content-section p.subt::after {
    position: absolute; top: 60%; 
    display: block; content: '';
    width: 5em; height: 1px;
    opacity: 0.5;
}
.content-section p.subt::before{
    background: linear-gradient( -90deg, #9dc3f7 0%, transparent 100%);
    right: 120%;
    top: 50%;
}
.content-section p.subt::after {
    background: linear-gradient( 90deg, #9dc3f7 0%, transparent 100%);
    left: 120%;
    top: 50%;
}

.content-section .content-acc {
    position: absolute;
    left: 0; right: 0; top: 25%; margin: auto;
    display: flex; justify-content: center;
}
.content-section .content-acc div{
    width: 10em; height: 10em;
    border-radius: 50%;
    box-shadow: 
    -1em -0.2em 0.4em -1.1em #c2ccff,
    inset 0em 0em 0em 2px #c2ccff,
    inset 0.2em 0.3em 0.2em -0.2em #c2ccff,
    inset -1.2em 2.3em 2em -0.5em #d4e6ff2f;
    background: #121521;
    z-index: 1;
    filter: brightness(0.8);
    background: repeating-radial-gradient( at 50% 50%, transparent 0%, #c2ccff22 2px, transparent 4px);
    translate: -18em;
}
.content-section .content-acc div:nth-child(2){
    translate: 18em 12em;
    border-radius: 2em;
    box-shadow: 
    -1em -0.2em 0.4em -1.1em #c2ccff,
    inset 0em 0em 0em 2px #c2ccff,
    inset 0.2em 0.3em 0.2em -0.2em #c2ccff,
    inset 10.8em 11.3em 2em -10.5em #d4e6ff2f;
}