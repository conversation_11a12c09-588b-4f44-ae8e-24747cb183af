import React from 'react';
import './index.scss'

const HeroComponent: React.FC = () => {
  const handleToggleGold = () => {
    document.body.classList.toggle('gold');
  };

  return (
    <>
    <div className="header">
          {/* <h2><a href="https://codepen.io/RAFA3L" target="_blank" rel="noopener noreferrer">RAFA</a></h2> */}
          <div className="mid-spot" onClick={handleToggleGold}></div>
          {/* <button className="contact-btn">
              <span className="glow"></span>
              <span className="contact-btn-content">Contact Us</span>
          </button> */}

          <div className="spotlight">
              <div></div>
              <div></div>
              <div></div>
          </div>
      </div><canvas id="particleCanvas"></canvas><div className="accent-lines">
              <div>
                  <div></div>
                  <div></div>
                  <div></div>
                  <div></div>
                  <div></div>
              </div>
              <div>
                  <div></div>
                  <div></div>
                  <div></div>
                  <div></div>
              </div>
          </div>
          <div className="heroSubP">
              <p>Introducing</p>
          </div>
          <div className="hero">
              <div className="heroT">
                  {/* <h2>rentprompts.ai</h2> */}
                   <h2>rentprompts.ai</h2>
              </div>
          </div><p className="heroP">The world's best platform, <br />
              For building Fast AI Application</p>
              <div className="mountains">
              <div></div>
              <div></div>
              <div></div>
          </div>
          <div className="hero-spacer"></div><div className="content-section">
              <div className="content-acc">
                  <div></div>
                  <div></div>
              </div>
              <p className="subt">Revolution by design</p>
              <h3 className="title">Harness. Empower.<br />
                  Unmatched Versatility.</h3>
              <p className="subp">At the core lies our revolutionary framework, <br />ensuring adaptability across all AI application architectures.</p>
          </div>
    </>
  );
};

export default HeroComponent;