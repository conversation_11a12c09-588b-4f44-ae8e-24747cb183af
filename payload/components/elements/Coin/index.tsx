// /// <reference types="next-cookies" />

// import React, { useEffect, useState } from 'react';
// import axios from 'axios';
// import cookies from 'next-cookies';

// const API_BASE_URL = `${process.env.NEXT_PUBLIC_SERVER_URL}/api`; // Replace with your Payload CMS base URL

// const UserDetails = ({ initialUser, initialError }) => {
//   const [user, setUser] = useState(initialUser);
//   const [error, setError] = useState(initialError);

//   useEffect(() => {
//     if (!initialUser && !initialError) {
//       const fetchUserDetails = async () => {
//         try {
//           const token = cookies(null).token;
//           if (!token) {
//             throw new Error('No authentication token found');
//           }

//           const response = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/api/users/me`, {
//             headers: {
//               Authorization: `Bearer ${token}`
//             }
//           });

//           setUser(response.data);
//         } catch (error) {
//           console.error('Error fetching user details:', error);
//           setError(error.message);
//         }
//       };

//       fetchUserDetails();
//     }
//   }, [initialUser, initialError]);

//   if (error) {
//     return <div className="text-red-500">Error: {error}</div>;
//   }

//   if (!user) {
//     return <div className="text-gray-500">Loading...</div>;
//   }

//   return (
//     <div className="user-details p-4 bg-white rounded shadow-md">
//       <h2 className="text-2xl font-bold mb-4">User Details</h2>
//       <p><strong>Name:</strong> {user.name}</p>
//       <p><strong>Email:</strong> {user.email}</p>
//       {/* Add more fields as needed */}
//     </div>
//   );
// };

// UserDetails.getInitialProps = async (ctx) => {
//   try {
//     const { token } = cookies(ctx);

//     if (!token) {
//       throw new Error('No authentication token found');
//     }

//     const response = await axios.get(`${API_BASE_URL}/api/users/me`, {
//       headers: {
//         Authorization: `Bearer ${token}`
//       }
//     });

//     return { initialUser: response.data, initialError: null };
//   } catch (error) {
//     console.error('Error fetching user details:', error);
//     return { initialUser: null, initialError: error.message };
//   }
// };

// export default UserDetails;



















// // // UserDetails.js
// // import React, { useEffect, useState } from 'react';
// // import axios from 'axios';
// // import Cookies from 'js-cookie';

// // import { cookies } from "next/headers"
// // import { getServerSideUser } from "../../../../lib/payload-utils";

// // const API_BASE_URL = 'http://localhost:3000'; // Replace with your Payload CMS base URL

// // const UserDetails = () => {
// //   const [user, setUser] = useState(null);
// //   const [error, setError] = useState(null);

// //   useEffect(() => {
// //     const fetchUserDetails = async () => {
// //       try {
// //         // Log to check the cookies
// //         console.log('Cookies:', Cookies.get());

// //         // Extract the token from cookies
// //         //const token = Cookies.get('token'); // Replace 'token' with the actual name of your cookie
// //         const nextCookies = cookies()
// //         const { user } = await getServerSideUser(nextCookies)

// //         if (!user) {
// //           throw new Error('No authentication token found');
// //         }

// //         // Make the API request to get user details
// //         const response = await axios.get(`http://localhost:3000/api/users/me`, {
// //           headers: {
// //             Authorization: `Bearer ${user}`
// //           }
// //         });

// //         setUser(response.data);
// //       } catch (error) {
// //         console.error('Error fetching user details:', error);
// //         setError(error.message);
// //       }
// //     };

// //     fetchUserDetails();
// //   }, []);

// //   if (error) {
// //     return <div className="text-red-500">Error: {error}</div>;
// //   }

// //   if (!user) {
// //     return <div className="text-gray-500">Loading...</div>;
// //   }

// //   return (
// //     <div className="user-details p-4 bg-white rounded shadow-md">
// //       <h2 className="text-2xl font-bold mb-4">User Details</h2>
// //       <p><strong>Name:</strong> {user.name}</p>
// //       <p><strong>Email:</strong> {user.email}</p>
// //       {/* Add more fields as needed */}
// //     </div>
// //   );
// // };

// // export default UserDetails;
