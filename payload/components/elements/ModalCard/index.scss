$color-bg-primary: #fff;
$color-bg-primary-hovered: #eee;
$color-primary: #212121;
$color-secondary: #9E9E9E;
$color-border: #eee;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 100vw;
  min-height: 100vh;
  font-family: 'Inter', sans-serif;
  color: $color-primary;
}

i {
  font-family: 'Material Icons Round';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;  /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;
  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;
  /* Support for IE. */
  font-feature-settings: 'liga';
}

.btn {
  position: relative;
  margin: 0 .5rem;
  padding: 0.5rem;
  background: #fff;
  border: 2px dashed $color-secondary;
  border-radius: 8px;
  font: inherit;
  transition: .35s ease-out;
  cursor: pointer;
  &:hover {
    border-color: $color-primary;
  }
  &:first-of-type {
    margin-left: 0;
  }
  &:last-of-type {
    margin-right: 0;
  }
  &-icon {
    @extend i;
    background: none;
    border: none;
    transition: .35s ease-out;
    cursor: pointer;
  }
  &-primary {
    border: 2px solid $color-primary;
  }
}

@keyframes modal-content-open {
	from {
		transform: scale(1.1);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;	
	}
}

@keyframes modal-content-close {
	from {
		transform: scale(1);
		opacity: 1;
	}
	to {
		transform: scale(.9);
		opacity: 0;	
	}
}

@keyframes modal-background-open {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;	
	}
}

@keyframes modal-background-close {
	from {
		opacity: 1;
	}
	to {
		opacity: 0;	
	}
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  &-content {
    min-width: 200px;
    max-width: 600px;
    max-height: calc(100vh - 4rem);
    margin: 2rem;
    padding: .5rem 1rem;
    background: $color-bg-primary;
    border-radius: 8px;
    box-shadow: 0 0.15rem 0.5rem rgba(33, 33, 33, 0.2);
    overflow: auto;
    animation: modal-content-close .25s;
  }
  &.active &-content {
    animation: modal-content-open .25s;
  }
  &-header {
    display: flex;
    align-items: center;
    padding: .5rem 0;
    border-bottom: 2px solid $color-border;
    & .btn-icon {
      margin-left: auto;
      color: $color-secondary;
      &:hover{
        color: $color-primary;
      }
    }
  }
  &-headline {
    line-height: 1.5;
    font-size: 20px;
  }
  &-main {
    padding: 1rem 0;
  }
  &-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1rem .5rem .5rem 0;
    border-top: 2px solid $color-border;
  }
  &-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: -1;
    animation: modal-background-close .25s;
  }
  &.active &-background {
    animation: modal-background-open .25s;
  }
}
