import React, { useState, useEffect, ReactNode, MouseEvent } from 'react';

interface ModalProps {
  trigger: ReactNode;
  children: ReactNode;
}

interface ModalSubComponentProps {
  children: ReactNode;
  close?: (e: MouseEvent<HTMLButtonElement | HTMLDivElement>) => void;
}

const Modal: React.FC<ModalProps> = ({ trigger, children }) => {
  const [active, setActive] = useState(false);
  const [show, setShow] = useState(false);

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.keyCode === 27) {
      close(e);
    }
  };

  const open = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setActive(true);
    setShow(true);
    document.body.style.overflow = 'hidden';
  };

  const close = (e: Event | MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
    e.preventDefault();
    setShow(false);
    setTimeout(() => {
      setActive(false);
      document.body.style.overflow = 'unset';
    }, 240);
  };

  useEffect(() => {
    if (active) {
      document.addEventListener('keydown', handleKeyDown);
    } else {
      document.removeEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [active]);

  return (
    <>
      {React.cloneElement(trigger as React.ReactElement, { onClick: open })}
      {active && (
        <div className={`modal${show ? ' active' : ''}`}>
          <div className="modal-content">
            {React.Children.map(children, (child) =>
              React.cloneElement(child as React.ReactElement, { close })
            )}
          </div>
          <div className="modal-background" onClick={close}></div>
        </div>
      )}
    </>
  );
};

const ModalHeader: React.FC<ModalSubComponentProps> = ({ children, close }) => (
  <div className="modal-header">
    <h1 className="modal-headline">{children}</h1>
    <button className="btn-icon" onClick={close}>
      Close
    </button>
  </div>
);

// const ModalMain: React.FC<ModalSubComponentProps> = ({ children, close }) => (
//   <div className="modal-main">
//     {React.Children.map(children, (child) =>
//       React.cloneElement(child as React.ReactElement, {
//         onClick: child.props.close
//           ? (e: MouseEvent<HTMLButtonElement>) => {
//               if (close) close(e);
//               if (child.props.onClick) child.props.onClick();
//             }
//           : child.props.onClick,
//       })
//     )}
//   </div>
// );

// const ModalFooter: React.FC<ModalSubComponentProps> = ({ children, close }) => (
//   <div className="modal-footer">
//     {React.Children.map(children, (child) =>
//       React.cloneElement(child as React.ReactElement, {
//         onClick: child.props.close
//           ? (e: MouseEvent<HTMLButtonElement>) => {
//               if (close) close(e);
//               if (child.props.onClick) child.props.onClick();
//             }
//           : child.props.onClick,
//       })
//     )}
//   </div>
// );

const ModalCard: React.FC = () => (
  <>
    <Modal trigger={<button className="btn">Open modal</button>}>
      {/* <ModalMain> */}
      <ModalHeader>Modal</ModalHeader>
        <p>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
      {/* </ModalMain>
      <ModalFooter> */}
        <button className="btn" onClick={(e) => e.stopPropagation()}>
          Close
        </button>
        <button className="btn btn-primary" onClick={() => alert('Done!')}>
          Done
        </button>
      {/* </ModalFooter> */}
    </Modal> 
  </>
);

export default ModalCard;
