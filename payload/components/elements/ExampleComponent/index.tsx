import type { Ref } from 'react'

import React, { forwardRef } from 'react'

import './index.scss'
import { ArrowUpRight } from 'lucide-react';
import Link from 'next/link';

const baseClass = 'gutter'

const ExampleComponent: React.FC /* <YourComponentProps> */ = () => {
  return (
    <section>
      <div className="container">
        <div className="card">
          <div className="card-inner" >
            <div className="box">
              <div className="imgBox">
                {/* <img
                  src="https://images.unsplash.com/photo-1601049676869-702ea24cfd58?q=80&w=2073&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                  alt="Trust & Co."
                /> */}
                <img
                src='..\public\img\2.png'
                alt="image"
                 loading="lazy"
                 />
              </div>
              <div className="icon">
                <Link href="#" className="iconBox">
                <ArrowUpRight />
                </Link>
              </div>
            </div>
          </div>
          <div className="content">
            <h3 className='text-white'>trust &amp; co.</h3>
            <p className='text-white' >Fill out the form and the algorithm will offer the right team of experts</p>
            <ul>
              <li className="branding">
                branding
              </li>
              <li className="packaging">
                packaging
              </li>
            </ul>
          </div>
        </div>
        {/* Repeat the structure for other cards as needed */}
      </div>
    </section>
  );
};

export default ExampleComponent;