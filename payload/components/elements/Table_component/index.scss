.getting-started {
    background-color: #1e1e1e;
    padding: 20px;
    color: #e0e0e0;

    h2 {
        margin-bottom: 20px;
    }

    .tasks-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .tasks-row {
        display: flex;
        justify-content: space-between;
    }

    .task {
        display: flex;
        align-items: center;
        background-color: #000;
        padding: 10px;
        border: 1px solid #333;
        border-radius: 5px;
        width: 100%;
        margin-bottom: 10px;

        .status {
            margin-right: 10px;
            .checkmark {
                color: #00b894; /* Green color for completed */
            }
            .circle {
                color: #fff;
            }
        }

        .text {
            color: #e0e0e0;
        }

        &.completed {
            .text {
                color: #00b894;
            }

            .strikethrough {
                text-decoration: line-through;
            }
        }
    }
}
