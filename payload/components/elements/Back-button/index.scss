// BackButton.scss
.back-button-container {
    padding-bottom: 40px;
    padding-left: 4px;
    padding-right: 4px;
    width: 100%;
  }
  
  .buttons-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    max-width: 7xl;
    margin: 0 auto;
  
    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  
    @media (min-width: 1024px) {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
  
  .tailwind-connect-button {
    background-color: #1e293b; /* bg-slate-800 */
    cursor: pointer;
    position: relative;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.75); /* shadow-2xl */
    border-radius: 9999px; /* rounded-full */
    padding: 1px;
    font-size: 12px; /* text-xs */
    font-weight: 600; /* font-semibold */
    line-height: 1.5rem; /* leading-6 */
    color: white;
    display: inline-block;
  
    .button-background {
      position: absolute;
      inset: 0;
      overflow: hidden;
      border-radius: 9999px; /* rounded-full */
      background-image: radial-gradient(
        75% 100% at 50% 0%,
        rgba(56, 189, 248, 0.6) 0%,
        rgba(56, 189, 248, 0) 75%
      );
      opacity: 0;
      transition: opacity 0.5s;
    }
  
    &:hover .button-background {
      opacity: 1;
    }
  
    .button-content {
      position: relative;
      display: flex;
      align-items: center;
      //space-between: 0.5rem;
      z-index: 10;
      border-radius: 9999px; /* rounded-full */
      padding: 0.125rem 1rem; /* py-0.5 px-4 */
      background-color: #18181b; /* bg-zinc-950 */
      border: 1px solid rgba(255, 255, 255, 0.1); /* ring-1 ring-white/10 */
    }
  
    .button-hover-line {
      position: absolute;
      bottom: 0;
      left: 1.125rem;
      height: 1px;
      width: calc(100% - 2.25rem);
      background-image: linear-gradient(
        to right,
        rgba(16, 185, 129, 0) 0%,
        rgba(16, 185, 129, 0.9) 50%,
        rgba(16, 185, 129, 0) 100%
      );
      opacity: 0;
      transition: opacity 0.5s;
    }
  
    &:hover .button-hover-line {
      opacity: 0.4;
    }
  }
  