// "use client";
// import React from "react";
// import reactElementToJSXString from "react-element-to-jsx-string";
// import { toast, Toaster } from "sonner";
// import { ButtonsCard } from "@/components/ui/tailwindcss-buttons";

// export function Backbutton () {

//   return (
//     <div className="pb-40 px-4 w-full">
//       <Toaster position="top-center" />
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full  max-w-7xl mx-auto gap-10">
//         {buttons.map((button, idx) => (
//            <ButtonsCard key={idx}>
//             {button.component}
//           </ButtonsCard>
//         ))}
//       </div>
//     </div>
//   );
// }
// export const buttons = [
//   {
//     name: "Tailwindcss Connect",
//     description: "Button featured on Tailwindcss Connect website",
//     showDot: false,
//     component: (
//       <button className="bg-slate-800 no-underline group cursor-pointer relative shadow-2xl shadow-zinc-900 rounded-full p-px text-xs font-semibold leading-6  text-white inline-block">
//         <span className="absolute inset-0 overflow-hidden rounded-full">
//           <span className="absolute inset-0 rounded-full bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(56,189,248,0.6)_0%,rgba(56,189,248,0)_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
//         </span>
//         <div className="relative flex space-x-2 items-center z-10 rounded-full bg-zinc-950 py-0.5 px-4 ring-1 ring-white/10 ">
//           <span>{`Tailwind Connect`}</span>
//           <svg
//             width="16"
//             height="16"
//             viewBox="0 0 24 24"
//             fill="none"
//             xmlns="http://www.w3.org/2000/svg"
//           >
//             <path
//               stroke="currentColor"
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               strokeWidth="1.5"
//               d="M10.75 8.75L14.25 12L10.75 15.25"
//             ></path>
//           </svg>
//         </div>
//         <span className="absolute -bottom-0 left-[1.125rem] h-px w-[calc(100%-2.25rem)] bg-gradient-to-r from-emerald-400/0 via-emerald-400/90 to-emerald-400/0 transition-opacity duration-500 group-hover:opacity-40"></span>
//       </button>
//     ),
//   },
// ];



// BackButton.js
"use client";
import React from "react";
import { Toaster } from "sonner";
import { ButtonsCard } from "@/components/ui/tailwindcss-buttons";
import './index.scss'

export function Backbutton() {
  return (
    <div className="back-button-container">
      <Toaster position="top-center" />
     <div className="buttons-grid"> 
        {buttons.map((button, idx) => (
          <ButtonsCard key={idx}>
            {button.component}
          </ButtonsCard>
        ))}
      </div>
     </div>
  );
}

// buttonsData.js
export const buttons = [
    {
      name: "Tailwindcss Connect",
      description: "Button featured on Tailwindcss Connect website",
      showDot: false,
      component: (
        <button className="tailwind-connect-button">
          <span className="button-background"></span>
          <div className="button-content">
            <span>{`Rentprompts.com`}</span>
       <Backbutton />   
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1.5"
                d="M10.75 8.75L14.25 12L10.75 15.25"
              ></path>
            </svg>
          </div>
          <span className="button-hover-line"></span>
        </button>
      ),
    },
  ];
  