import React from 'react';
import './index.scss';

// // Import your images here
// import image1 from '..';  // Replace with the correct path
// import image2 from '../assets/image2.png';  // Replace with the correct path
// import image3 from '../assets/image3.png';  // Replace with the correct path
// import image4 from '../assets/image4.png';  // Replace with the correct path

// import image1 from "../../../../public/img/bg-services3.jpg";
// import image2 from "../../../../public/img/coin-png.png";
// import image3 from "../../../../public/img/coin-png.png";
// import image4 from "../../../../public/img/coin-png.png";
// import prompt_4 from "../../../../public/img/prompt_4.jpg";


const Custom_test = () => {
    const cards = [
        {
            title: "GETTING STARTED",
            subtitle: "RIVE 101",
            description: "Get started",
            detail: "Rive beginners: watch this first! Learn the basics fast with easy onboarding tips.",
            // image: prompt_4,
            bgColor: "#FF3A78",
            borderColor: "#FF3A78"
        },
        {
            title: "DESIGN Mode",
            subtitle: "RIVE 101",
            description: "Design",
            detail: "Get to know the Rive features not typically found in other design tools.",
            //image: image2,
            bgColor: "#FFB24C",
            borderColor: "#FFB24C"
        },
        {
            title: "STATE MACHINE",
            subtitle: "RIVE 101",
            description: "State Machine",
            detail: "Build interactive animations that are ready to run with Rive's State Machine.",
            //image: image3,
            bgColor: "#7592c5",
            borderColor: "#7592c5"
        },
        {
            title: "ANIMATION MIXING",
            subtitle: "RIVE 101",
            description: "Animation mixing",
            detail: "Build layered interactions that look complex, but are simple under the hood.",
            //image: image4,
            bgColor:  "#90EE90",
            borderColor: "#90EE90"
        }
    ];

    return (
        <div className="learn-rive-section">
            <h2>ACTIVITY</h2>
            <div className="cards-container">
                {cards.map((card, index) => (
                <div key={index}>
                    <div className="card" style={{ backgroundColor: card.bgColor, borderColor: card.borderColor }}>
                        <div className="card-header">
                            <h3>{card.title}</h3>
                            <div className="subtitle" style={{ color: card.bgColor }}>{card.subtitle}</div>
                        </div>
                        <div className="subcards">
                        <h3 style={{ color: card.bgColor }}>CREATE</h3>
                            <div className="subtitle" style={{ color: card.bgColor }}>EXPLORE</div>
                        </div>
                    {/*
                     <img src={card.image.src} alt={card.title} className="card-image" />
                
                        <div className="card-content">
                            <h4>{card.description}</h4>
                            <p>{card.detail}</p>
                        </div>
                    */}
                    </div>
                    <div className="card-content">
                            <h4>{card.description}</h4>
                            {/* <p>{card.detail}</p> */}
                        </div>
                </div>
                ))}
            </div>
          
        </div>
    );
};

export default Custom_test;


