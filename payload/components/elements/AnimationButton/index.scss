:root {
    --color-dark: #110d1a;
    --color-primary: #b197fc;
    accent-color: var(--color-primary);
  }
  
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  
  body {
    font-family: system-ui;
    //height: 20vh;
    background-color: var(--color-dark);
  }
  
  img {
    display: block;
    max-width: 100%;
    object-fit: cover;
  }
  
  h1 {
    margin-block-end: 1rem;
    font-size: 3rem;
  }
  
  a {
    color: var(--color-primary);
    text-decoration: none;
  }
  
  .wrapper {
    display: grid;
    place-content: center;
    height: 85vh;               //top-height of button 
  }
  
  .c-btn {
    position: relative;
    overflow: hidden;
    //padding: 0.85rem 2rem;            padding : height weight 
    padding : 0.5rem  ;
  
    display: inline-flex;
    align-items: center;
    justify-content: center;
  
    color: var(--color-primary);
    text-decoration: none;
  
    border-radius: 8px;
    background-color: transparent;
    backface-visibility: hidden;
    box-shadow: inset 0 0 0 1px var(--color-primary);
  
    transform: translateZ(0);
  
    &::after {
      content: "";
      pointer-events: none;
      position: absolute;
  
      top: 0;
      left: 0;
      z-index: 1;
     // height: 20%;              
      width: 120%;
  
      border-radius: 20%;
      background-color: var(--color-primary);
      scale: 0 0;
      translate: 0 140%;
      transition: scale 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
        translate 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    }
  }
  
  .c-btn__label {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
  
    z-index: 2;
    font-size: 1.2rem;
    letter-spacing: 0.025em;
  
    transition: color 0.32s ease-in-out;
  }
  
  .c-btn:hover {
    span {
      color: var(--color-dark);
    }
  
    &:after {
      scale: 1.5 1.5;
      translate: 0 0;
      border-radius: 50%;
    }
  }