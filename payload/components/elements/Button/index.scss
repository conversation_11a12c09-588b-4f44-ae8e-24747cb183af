a.btn {
  display: inline-block;
}

.btn {
  background: transparent;
  line-height: base(1);
  //border-radius: $style-radius-m;
  font-size: 1rem;
  color: black;
  margin-top: base(1);
  margin-bottom: base(1);
  border: 0;
  cursor: pointer;
  font-weight: normal;
  text-decoration: none;
  text-align: center;
  //color: inherit;

  .btn__icon {
    border: 1px solid;
    border-radius: 100%;
    //@include color-svg(currentColor);
  }

  &--has-tooltip {
    position: relative;
  }

  &--icon-style-without-border {
    .btn__icon {
      border: none;
    }
  }

  &--icon-style-none {
    .btn__icon {
      border: none;
    }
  }

  span {
    line-height: base(1);
  }

  span,
  svg {
    vertical-align: top;
  }

  &--size-medium {
    padding: base(0.5);
  }

  &--size-small {
    padding: base(0.25) base(0.5);
  }

  &--style-primary {
    background-color: var(--theme-elevation-800);
    color: var(--theme-elevation-0);

    &.btn--disabled {
      background-color: var(--theme-elevation-400);
    }

    &:not(.btn--disabled) {
      &:hover,
      &:focus-visible {
        background: var(--theme-elevation-750);
      }

      &:active {
        background: var(--theme-elevation-700);
      }
    }

    &:focus:not(:focus-visible) {
      //box-shadow: $focus-box-shadow;
      outline: none;
    }
  }

  &--style-secondary {
   // $base-box-shadow: inset 0 0 0 $style-stroke-width var(--theme-elevation-800);
    //$hover-box-shadow: inset 0 0 0 $style-stroke-width var(--theme-elevation-700);

    //box-shadow: $base-box-shadow;
    color: var(--theme-elevation-800);
    background: none;
    backdrop-filter: blur(5px);

    &:hover,
    &:focus-visible {
      background: var(--theme-elevation-100);
      box-shadow: hover-box-shadow;
    }

    &:active {
      background: var(--theme-elevation-200);
    }

    &.btn--disabled {
      color: var(--theme-elevation-400);
      background: none;
      //box-shadow: inset 0 0 0 $style-stroke-width var(--theme-elevation-400);
    }

    &:focus:not(:focus-visible) {
      outline: none;
      //box-shadow: $hover-box-shadow, $focus-box-shadow;
    }
  }

  &--style-none {
    padding: 0;
    margin: 0;
    border-radius: 0;

    &:focus {
      opacity: 0.8;
    }

    &:active {
      opacity: 0.7;
    }
  }

  &--round {
    border-radius: 100%;
  }

  [dir='rtl'] &--icon {
    span {
      margin-left: 5px;
    }
  }

  &--icon {
    span {
      display: flex;
      justify-content: space-between;
    }

    // &.btn--style-primary {
    //   // .icon {
    //   //   @include color-svg(var(--theme-elevation-0));
    //   // }
    // }
  }

  &--style-icon-label {
    padding: 0;
    font-weight: 600;
  }

  // &--style-light-gray {
  //   box-shadow: inset 0 0 0 $style-stroke-width var(--theme-elevation-800);
  // }

  &--icon-position-left {
    .btn__content {
      flex-direction: row-reverse;
    }

    .btn__icon {
      margin-right: base(0.5);
    }
  }

  &--icon-position-right {
    .btn__icon {
      margin-left: base(0.5);
    }
  }

  &--icon-only {
    .btn__icon {
      padding: 0;
      margin: 0;
    }
  }

  &--disabled {
    cursor: default;
  }

  &:hover,
  &:focus-visible {
    .btn__icon {
      //@include color-svg(var(--theme-elevation-0));
      background: var(--theme-elevation-800);
    }
  }

  &:focus:not(:focus-visible) {
    .btn__icon {
      //@include color-svg(var(--theme-elevation-800));
      background: var(--theme-elevation-150);
    }

    outline: none;
  }

  &:active {
    .btn__icon {
    //  @include color-svg(var(--theme-elevation-0));
      background: var(--theme-elevation-700);
    }
  }

  &:focus-visible {
    outline: var(--accessibility-outline);
    outline-offset: var(--accessibility-outline-offset);
  }
}
