@import '../../../scss/styles';

.card {
  background: var(--theme-elevation-50);
  padding: base(1.25) $baseline;
  //position: relative;

  //i added css
  display: flex;
  justify-content: center; // Aligns items horizontally (center, flex-start, flex-end, space-between, space-around, space-evenly)
  align-items: center; // Aligns items vertically (center, flex-start, flex-end, stretch, baseline)
  flex-wrap: wrap; // Allows the items to wrap to the next line if necessary
  gap: 20px; // Optional: Adds space between the cards
  padding: 20px; // Optional: Adds padding around the container

  &__title {
    @extend %h5;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__actions {
    position: relative;
    z-index: 2;
    margin-top: base(0.5);
    display: inline-flex;

    .btn {
      margin: 0;
    }
  }

  &--has-onclick {
    cursor: pointer;

    &:hover {
      background: var(--theme-elevation-100);
    }
  }

  &__click {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
