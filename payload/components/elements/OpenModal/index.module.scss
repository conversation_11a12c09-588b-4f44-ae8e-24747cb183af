@import "https://unpkg.com/open-props";

/* ==========================================================================
   $Table of Contents
   ========================================================================== */
/**
  * Variables
  *   Colors
  * Functions
  *   strip-unit
  *   rem
  * Mixins
  *   Active states
  *   Hide text
  * Globals
 */

// ==========================================================================
// $Variables
// ==========================================================================
$root-font-size: 16px;


// ==========================================================================
// $Functions
// ==========================================================================

// $strip-unit
// ==========================================================================
@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }

  @return $number;
}


// $rem - `rem(20px)`
// ==========================================================================
@function rem($number) {
  $raw: $number/strip-unit($root-font-size);
  $rounded: ceil($raw * 1000) / 1000;
  @return $rounded * 1rem;
}


// Active states
@mixin is-active {

  &:hover,
  &:focus,
  &:active,
  &.is-active { @content; }  

}

// Accessible Text
@mixin hide-text {
  color: transparent;
  font: 0/0 serif;
}

/* ==========================================================================
   $Globals
   ========================================================================== */
*,
*:before,
*:after {
  box-sizing: border-box;
}

html {
  font-size: $root-font-size;
  box-sizing: inherit;
  height: 100%;
  padding: var(--size-4);
}

body {
  background-color: var(--cyan-0);
  height: 100%;
  min-height: 100%;
}

a {
  color: var(--blue-8);
  transition: color 0.2s;
  
  &:hover,
  &:focus {
    color: var(--blue-12);
  }
  
}

p + p {
  margin-top: var(--size-4);
}

img {
  vertical-align: middle;
}

.absolute-center {
  display: grid;
  place-items: center;  
  height: 100%;
}

.c-popover {
  font-family: var(--font-sans);
  font-size: var(--font-size-4);
  font-weight: 300;
  position: relative;
  
  &__trigger,
  &__target {
    border-radius: var(--radius-2);
  }
  
  &__trigger {
    background-color: white;
    border: var(--border-size-1) solid var(--gray-4);
    box-shadow: var(--shadow-2);
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    font-weight: 300;
    line-height: 1;
    padding: var(--size-4) var(--size-6);
  }
  
  &__trigger:active,
  &__trigger:focus {
    border-color: var(--cyan-3);
    outline-color: var(--cyan-3);
    outline-offset: 6px;
  }
  
  &__target {
    border: var(--border-size-1) solid var(--gray-3);
    box-shadow: var(--shadow-2);
    line-height: 1.2;
    position: absolute;
    // ! Experimental features, won't work.    
    top: anchor(top);
    left: anchor(center);
    top: 30%;
    left: 50%;
    translate: -50% -50%;
    padding: var(--size-3) var(--size-6);
    max-width: 400px;
  }
  
  &__title {
    font-size: var(--font-size-5);
    font-weight: 700;
    margin-bottom: var(--size-2);
  }
}