import React, { useState, useRef } from "react";
import "./index.module.scss";
import Card_payload from "../Card_payload";

const OpenModal = () => {
  const [popoverVisible, setPopoverVisible] = useState(false);
  const popoverRef = useRef(null);

  const togglePopover = () => {
    setPopoverVisible(!popoverVisible);
  };

  return (
    <div className="absolute-center">
      <div className="c-popover">
        <button
          id="centered-trigger"
          className="c-popover__trigger"
          onClick={togglePopover}
        >
          Getting Started →
        </button>
        {popoverVisible && (
          <div id="my-popover" className="c-popover__target" ref={popoverRef}>
            <h2 className="c-popover__title">Popover element</h2>
            <p>
              Popover is a native HTML feature. It has limited support and could
              be used to create tooltips.
            </p>
            <Card_payload title={"ACTIVITY"} />
          </div>
        )}
      </div>
    </div>
  );
};

export default OpenModal;
