import React, { useEffect, useState } from "react";
import { useField } from "payload/components/forms";

const CustomUseCaseSelect: React.FC<any> = ({ path, label }) => {
  const { value, setValue } = useField<string>({ path });
  const [options, setOptions] = useState<{ label: string; value: string }[]>(
    []
  );
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const fetchUseCases = async () => {
      try {
        const res = await fetch("/api/useCases");
        const data = await res.json();

        const opts = data.docs.map((doc: any) => ({
          label: doc.label,
          value: doc.value,
        }));

        setOptions(opts);
      } catch (err) {
        console.error("Failed to fetch useCases:", err);
      }
    };

    fetchUseCases();
  }, []);

  return (
    <div style={{ marginBottom: 16 }}>
      <label style={{ display: "block", marginBottom: 8 }}>
        {label || "Use Case"}
      </label>
      <select
        value={value || ""}
        onChange={(e) => setValue(e.target.value)}
        onMouseEnter={() => setIsHovered(true)}
onMouseLeave={() => setIsHovered(false)}
        className="w-full p-2 rounded bg-indigo-600 text-white border border-white focus:outline-none focus:ring-2 focus:ring-white"
        style={{
          padding: "0.5rem",
          borderRadius: "6px",
          width: "100%",
          backgroundColor: "#021c48", // indigo-600
          color: "white",
          border: `1px solid  ${isHovered ? '#032c72' : '#7592c5'}`,
          outline: "none",
          height: "50px",
        }}
      >
        <option value=""  style={{ backgroundColor: '#021c48', color: 'white' }}>
          -- Select Use Case --
        </option>
        {options.map((opt) => (
          <option
            key={opt.value}
            value={opt.value}
            style={{ backgroundColor: value === opt.value ? '#021c48' : '#021c48', color: 'white' }}
          >
            {opt.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default CustomUseCaseSelect;
