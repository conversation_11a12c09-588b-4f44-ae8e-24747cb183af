import runFalAi from "../collections/Models/providers/FalAi/client";
import runGroq from "../collections/Models/providers/Groq/client";
import runOpenAi from "../collections/Models/providers/Openai/client";
import runReplicate from "../collections/Models/providers/Replicate/client";
import runRunway from "../collections/Models/providers/Runway/client";

interface GetOutputPropts {
  provider: "groq" | "openai" | "replicate" | "falai" | "runway";
  apiKey: string;
  modelName: string;
  prompt: string;
  text?: string;
  promptImage?: string;
  promptText?: string;
  negativePrompt?: string;
  systemPrompt?: string;
  settings?: any;
  image?: any;
  audio?: any;
}

const getOutput = async (props: GetOutputPropts) => {
  const {
    modelName,
    apiKey,
    image,
    audio,
    negativePrompt,
    prompt,
    promptImage,
    promptText,
    systemPrompt,
    settings,
    provider,
    text,
  } = props;

  let output;
  switch (provider) {
    case "groq": {
      output = await runGroq({
        apiKey: apiKey,
        model: modelName,
        prompt: prompt,
        negativePrompt: negativePrompt,
        systemPrompt: systemPrompt,
        settings: settings,
        image: image,
        audio: audio,
      });
      break;
    }
    case "openai":
      // Handle OpenAI integration
      output = await runOpenAi({
        apiKey: apiKey,
        model: modelName,
        prompt: prompt,
        settings: settings,
        image: image,
      });
      break;
    case "replicate":
      output = await runReplicate({
        apiKey: apiKey,
        model: modelName as
          | `${string}/${string}`
          | `${string}/${string}:${string}`,
        prompt: prompt,
        settings: settings,
        image: image,
        systemPrompt: systemPrompt,
        negativePrompt: negativePrompt,
      });
      break;
    case "falai":
      output = await runFalAi({
        apiKey: apiKey,
        model: modelName,
        prompt: prompt,
       // text: text,
        settings: settings,
        image: image,
      });
      break;
    case "runway":
      output = await runRunway({
          apiKey: apiKey,
          model: modelName,
          promptImage: image,
          promptText: promptText,
          settings: settings,
      });
      break;
  }

  return output;
};
export default getOutput;