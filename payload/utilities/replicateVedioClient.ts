import Replicate from "replicate";
import { writeFile } from "fs/promises";

enum ASRTIO {
//   "1:1",
  "16:9",
//   "21:9",
//   "2:3",
//   "3:2",
//   "4:5",
//   "5:4",
//   "9:16",
//   "9:21",
}


interface args {
  Prompt: string;
  //negative_prompt: string;
  image : any;
  settings: {
    apiKey: string;
    model: `${string}/${string}` | `${string}/${string}:${string}`;
    cfg?: number; // default 3.5, max 20 min 0
    steps?: number; // default 30
    seeds?: number;
    length?: number ;
    target_size ?: number ;
    aspect_ratio?: string; // default 16:9
  };
}

export default async function one(arg: args) {
  try {
    const { Prompt,image , settings } = arg;
    const replicate = new Replicate({ auth: settings.apiKey });
    const input = {
      prompt: Prompt,
      negative_prompt: "low quality, worst quality, deformed, distorted, watermark" ,
      image : image,
      steps: 50,
      cfg: settings.cfg,
      length: 129,
      target_size: settings.target_size,
      seeds : settings.seeds ,
      aspect_ratio: settings.aspect_ratio,
    };

    const output = await replicate.run(settings.model, {
      input: input,
    });    

for (const [index, item] of Object.entries(output)) {
  await writeFile(`output_${index}.mp4`, item);
}
//=> output_0.mp4 written to disk

    return output;
  } catch (error) {
    console.log("ReplicateImageClient", error);
    throw new Error(`Please contact support team`);
  }
}
