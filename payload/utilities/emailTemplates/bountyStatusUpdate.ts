export const getBountyStatusEmailHTML = ({
  userName,
  title,
  status,
  adminReview,
  slug,
  logoUrl,
  baseUrl,
  price,
}: {
  userName: string;
  title: string;
  status: string;
  adminReview?: string;
  slug;
  logoUrl: any;
  baseUrl: string;
  price: string | number;
}) => {
  return `
      <div style="font-family: Arial, sans-serif; background-color: #f4f4f4;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0px 0px 10px rgba(0,0,0,0.1);">
                   <div style="text-align: center; padding: 20px; background-color: #222831; border-radius: 10px;">

            <img src="${logoUrl}" alt="Logo" style="max-width: 150px; margin-bottom: 20px;" />
      
           <svg width="320" height="48" viewBox="0 0 367 56" fill="var(--theme-text)" xmlns="http://www.w3.org/2000/svg">
            <path d="M32.8577 0.180563C41.3337 1.1931 48.2156 5.98833 47.9948 15.3453C47.9934 15.4118 47.9595 15.445 47.8929 15.445H11.3397C11.2817 15.445 11.2357 15.4217 11.2017 15.375L0.0149781 0.127495C0.00665529 0.116043 0.0015768 0.1025 0.000311881 0.0883841C-0.000953039 0.0742678 0.0016457 0.0601381 0.00781668 0.0475757C0.0139877 0.0350134 0.0234882 0.0245164 0.0352511 0.0172618C0.0470141 0.0100071 0.0605773 0.00628097 0.0744173 0.00650178C9.58421 -0.00340425 19.0997 -0.00199143 28.6208 0.0107449C30.527 0.0135752 31.9393 0.0701817 32.8577 0.180563Z" />
            <path d="M11.1528 17.4701L47.844 17.4807C47.9091 17.4807 47.9346 17.5203 47.9204 17.5996C46.4897 25.7975 41.0386 31.3867 32.7982 32.6688C31.8302 32.8202 30.42 32.8966 28.5676 32.898C19.8177 32.9079 11.1351 32.9093 2.51969 32.9023C2.27063 32.9009 2.20836 32.7919 2.3329 32.5754L10.8917 17.6208C10.9182 17.575 10.9562 17.5369 11.0021 17.5105C11.0479 17.484 11.0999 17.4701 11.1528 17.4701Z" />
            <path d="M7.27258 54.8025C1.17825 50.0115 -1.11004 42.3782 1.416 35.0505C1.43439 34.9996 1.47048 34.9741 1.52425 34.9741H18.5952C18.6064 34.9741 18.6173 34.9771 18.627 34.9826C18.6367 34.9882 18.6448 34.9963 18.6504 35.0059C18.6559 35.0156 18.6589 35.0266 18.6589 35.0378C18.6589 35.049 18.656 35.06 18.6504 35.0696L7.44877 54.7685C7.40349 54.8477 7.34475 54.8591 7.27258 54.8025Z" />
            <path d="M350.977 43.672C348.705 43.672 346.697 43.432 344.953 42.952C343.225 42.472 341.809 41.928 340.705 41.32L342.481 34.936C343.457 35.784 344.713 36.576 346.249 37.312C347.801 38.048 349.617 38.416 351.697 38.416C352.993 38.416 354.153 38.256 355.177 37.936C356.201 37.616 357.001 37.112 357.577 36.424C358.169 35.736 358.465 34.832 358.465 33.712C358.465 32.672 358.209 31.824 357.697 31.168C357.201 30.496 356.473 29.912 355.513 29.416C354.569 28.92 353.417 28.424 352.057 27.928C350.937 27.512 349.889 27.072 348.913 26.608C347.937 26.144 347.089 25.592 346.369 24.952C345.649 24.312 345.089 23.544 344.689 22.648C344.289 21.752 344.089 20.664 344.089 19.384C344.089 18.024 344.361 16.712 344.905 15.448C345.465 14.184 346.281 13.048 347.353 12.04C348.441 11.016 349.785 10.208 351.385 9.61603C353.001 9.02403 354.849 8.72803 356.929 8.72803C359.185 8.72803 361.177 9.00003 362.905 9.54403C364.649 10.088 366.001 10.648 366.961 11.224L365.377 16.984C363.857 15.992 362.417 15.24 361.057 14.728C359.697 14.216 358.177 13.96 356.497 13.96C355.409 13.96 354.361 14.112 353.353 14.416C352.361 14.72 351.553 15.208 350.929 15.88C350.305 16.536 349.993 17.4 349.993 18.472C349.993 19.736 350.505 20.68 351.529 21.304C352.569 21.928 353.913 22.544 355.561 23.152C356.665 23.536 357.761 23.96 358.849 24.424C359.937 24.872 360.929 25.44 361.825 26.128C362.721 26.8 363.441 27.648 363.985 28.672C364.529 29.696 364.801 30.984 364.801 32.536C364.801 33.832 364.569 35.136 364.105 36.448C363.657 37.76 362.897 38.96 361.825 40.048C360.753 41.136 359.329 42.016 357.553 42.688C355.777 43.344 353.585 43.672 350.977 43.672Z" />
            <path d="M323.937 43L329.049 13.744H334.617L329.505 43H323.937ZM319.953 14.68L320.889 9.40002H344.361L343.425 14.68H319.953Z" />
            <path d="M298.409 26.56H305.849C307.257 26.56 308.473 26.272 309.497 25.696C310.521 25.104 311.313 24.264 311.873 23.176C312.433 22.072 312.713 20.736 312.713 19.168C312.713 17.568 312.345 16.336 311.609 15.472C310.889 14.592 309.673 14.152 307.961 14.152H300.089L303.017 11.704L297.473 43H291.881L297.785 9.40002H308.441C310.665 9.40002 312.521 9.76003 314.009 10.48C315.513 11.2 316.641 12.248 317.393 13.624C318.145 15 318.521 16.672 318.521 18.64C318.521 20.864 318.177 22.8 317.489 24.448C316.817 26.096 315.873 27.456 314.657 28.528C313.457 29.584 312.057 30.376 310.457 30.904C308.857 31.432 307.129 31.696 305.273 31.696H297.497L298.409 26.56Z" />
            <path d="M254.553 43L264.825 9.40002H269.721L275.097 27.616L273.009 27.304L284.625 9.40002H289.473L287.937 43H282.129L283.737 17.296L285.009 17.752L272.577 35.608L266.337 17.416L267.873 17.392L260.361 43H254.553Z" />
            <path d="M236.795 43.672C234.139 43.672 231.835 43.104 229.883 41.968C227.931 40.816 226.427 39.168 225.371 37.024C224.315 34.88 223.787 32.312 223.787 29.32C223.787 26.36 224.283 23.632 225.275 21.136C226.267 18.624 227.635 16.44 229.379 14.584C231.123 12.728 233.123 11.288 235.379 10.264C237.651 9.24003 240.059 8.72803 242.603 8.72803C245.259 8.72803 247.555 9.30403 249.491 10.456C251.443 11.592 252.947 13.232 254.003 15.376C255.075 17.52 255.611 20.088 255.611 23.08C255.611 26.04 255.115 28.776 254.123 31.288C253.131 33.784 251.763 35.96 250.019 37.816C248.275 39.672 246.267 41.112 243.995 42.136C241.739 43.16 239.339 43.672 236.795 43.672ZM237.683 38.416C239.523 38.416 241.163 38.008 242.603 37.192C244.059 36.376 245.291 35.272 246.299 33.88C247.323 32.472 248.107 30.88 248.651 29.104C249.195 27.312 249.467 25.456 249.467 23.536C249.467 21.632 249.187 19.968 248.627 18.544C248.083 17.104 247.235 15.984 246.083 15.184C244.947 14.384 243.483 13.984 241.691 13.984C239.851 13.984 238.203 14.392 236.747 15.208C235.291 16.024 234.059 17.136 233.051 18.544C232.043 19.936 231.267 21.52 230.723 23.296C230.195 25.072 229.931 26.928 229.931 28.864C229.931 30.752 230.203 32.416 230.747 33.856C231.291 35.296 232.131 36.416 233.267 37.216C234.419 38.016 235.891 38.416 237.683 38.416Z" />
            <path d="M196.297 43L202.201 9.40002H212.593C216.049 9.40002 218.641 10.12 220.369 11.56C222.097 12.984 222.961 15.08 222.961 17.848C222.961 19.576 222.665 21.112 222.073 22.456C221.497 23.784 220.721 24.944 219.745 25.936C218.785 26.928 217.713 27.744 216.529 28.384C215.345 29.024 214.145 29.504 212.929 29.824C211.713 30.144 210.585 30.304 209.545 30.304H201.241L201.961 25.048H210.073C211.593 25.048 212.865 24.744 213.889 24.136C214.929 23.528 215.713 22.72 216.241 21.712C216.769 20.704 217.033 19.6 217.033 18.4C217.033 17.104 216.681 16.056 215.977 15.256C215.273 14.456 214.065 14.056 212.353 14.056H204.193L207.481 11.464L201.889 43H196.297ZM214.441 43L209.425 28.384L215.041 27.184L220.273 43H214.441Z"/>
            <path d="M177.417 26.56H184.857C186.265 26.56 187.481 26.272 188.505 25.696C189.529 25.104 190.321 24.264 190.881 23.176C191.441 22.072 191.721 20.736 191.721 19.168C191.721 17.568 191.353 16.336 190.617 15.472C189.897 14.592 188.681 14.152 186.969 14.152H179.097L182.025 11.704L176.481 43H170.889L176.793 9.40002H187.449C189.673 9.40002 191.529 9.76003 193.017 10.48C194.521 11.2 195.649 12.248 196.401 13.624C197.153 15 197.529 16.672 197.529 18.64C197.529 20.864 197.185 22.8 196.497 24.448C195.825 26.096 194.881 27.456 193.665 28.528C192.465 29.584 191.065 30.376 189.465 30.904C187.865 31.432 186.137 31.696 184.281 31.696H176.505L177.417 26.56Z" />
            <path d="M153.113 43L158.225 13.744H163.793L158.681 43H153.113ZM149.129 14.68L150.065 9.40002H173.537L172.601 14.68H149.129Z" />
            <path d="M115.948 43L121.852 9.40002H127.42L138.1 34.648L136.684 34.816L141.172 9.40002H146.74L140.788 43H136.228L124.852 16.384L126.268 16.216L121.516 43H115.948Z" />
            <path d="M98.1377 40.168L96.0737 37.72H113.378L112.442 43H91.9457L97.8497 9.40002H117.938L117.002 14.68H100.13L103.082 12.232L98.1377 40.168ZM99.3857 22.816H112.37L111.434 28.096H98.4497L99.3857 22.816Z" />
            <path d="M65.272 43L71.176 9.40002H81.568C85.024 9.40002 87.616 10.12 89.344 11.56C91.072 12.984 91.936 15.08 91.936 17.848C91.936 19.576 91.64 21.112 91.048 22.456C90.472 23.784 89.696 24.944 88.72 25.936C87.76 26.928 86.688 27.744 85.504 28.384C84.32 29.024 83.12 29.504 81.904 29.824C80.688 30.144 79.56 30.304 78.52 30.304H70.216L70.936 25.048H79.048C80.568 25.048 81.84 24.744 82.864 24.136C83.904 23.528 84.688 22.72 85.216 21.712C85.744 20.704 86.008 19.6 86.008 18.4C86.008 17.104 85.656 16.056 84.952 15.256C84.248 14.456 83.04 14.056 81.328 14.056H73.168L76.456 11.464L70.864 43H65.272ZM83.416 43L78.4 28.384L84.016 27.184L89.248 43H83.416Z" />
            </svg>
            </div>
          <h2 style="color: #333;">Hello ${userName},</h2>
          <p style="font-size: 16px; color: #555;">
            Your bounty titled  <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/bounties/${slug}" style="color: #555;">
    <strong>"${title}"</strong>
  </a>  has been <strong>${status.toUpperCase()}</strong>.
          </p>
          ${
            status === "denied"
              ? `<p style="font-size: 16px; color: #555;" > Your bounty joules <strong>${price || "Not provided."}</strong>has been refunded to you </p>
              <p style="color: red;"><strong>Reason:</strong> ${adminReview || "Not provided."}</p>`
              : `<p style="color: green;">You can now view it live on the platform.</p>`
          }
          <p style="font-size: 14px; color: #888;">If you have any questions, feel free to reach out.</p>
          <p style="margin-top: 30px; font-size: 14px;">Thanks,<br/>Rentprompts Team</p>
          <hr style="margin-top: 40px; border: none; border-top: 1px solid #ddd;" />
          <p style="text-align: center; font-size: 12px; color: #999;">
            This is an automated message. Please do not reply.
          </p>
        </div>
      </div>
    `;
};
