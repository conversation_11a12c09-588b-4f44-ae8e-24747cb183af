export const rewardJoulesEmailTemplate = ({
  userName,
  bountyTitle,
  bountySlug,
  rewardJoules,
  logoUrl,
}: {
  userName: string;
  bountyTitle: string;
  bountySlug: string;
  rewardJoules: number;
  logoUrl: string;
}) => {
  return `
    <div style="font-family: Arial, sans-serif; background-color: #f9f9f9;">
      <div style="max-width: 600px; margin: auto; background: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
        
        <div style="text-align: center; padding: 20px; background-color: #222831; border-radius: 10px;">
          <img src="${logoUrl}" alt="Company Logo" style="height: 60px;" />
        </div>

        <div style="padding: 30px;">
          <p style="font-size: 16px;">Hello <strong>${userName}</strong>,</p>
          
          <p style="font-size: 16px;">
            Congratulations! You’ve received a reward of <strong style="color: #ffc107;">${rewardJoules} Joules</strong> 
            for your valuable contribution to the bounty: <strong>${bountyTitle}</strong>.
          </p>

          <div style="margin: 30px 0; text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/bounties/${bountySlug}"
              style="background-color: #007bff; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              View Bounty
            </a>
          </div>

          <p style="margin-top: 30px; font-size: 14px; color: #888;">
            Keep up the great work! Thank you for being a part of our community.
          </p>
        </div>

        <div style="text-align: center; padding: 15px; background-color: #eeeeee; font-size: 13px; color: #666;">
          &copy; ${new Date().getFullYear()} <a href="https://rentprompts.com/" style="color: #666;">Rentprompts.com</a>. All rights reserved.
        </div>
      </div>
    </div>
  `;
};
