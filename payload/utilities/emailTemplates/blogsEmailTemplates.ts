export const blogsEmailTemplate = ({
  userName,
  BlogsTitle,
  status,
  adminReview,
  slug,
  logoUrl,
  baseUrl,
  id,
}: {
    id?:any
  userName: string;
  BlogsTitle: string;
  status: string;
  adminReview?: string;
  slug: string;
  logoUrl: string;
  baseUrl: string;
}) => {
  const isApproved = status === "approved";
  const statusColor = isApproved ? "#22C55E" : "#EF4444";
  const statusBg = isApproved ? "#ECFDF5" : "#FEF2F2";
 const statusLabel = isApproved
    ? `🎉 Your blog is live!`
    : `⚠️ Needs your attention`;

  const actionSection = isApproved
    ? `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${baseUrl}/blog/${slug}"
          style="background: #2563EB; color: white; padding: 12px 24px; font-weight: 600; text-decoration: none; border-radius: 8px; display: inline-block;">
          View Blog
        </a>
      </div>
    `
    : `
      <div style="margin-top: 20px; background: #FFFBEB; padding: 16px 20px; border-left: 4px solid #F59E0B; border-radius: 8px;">
        <p style="margin: 0 0 6px 0; font-size: 15px; font-weight: 600; color: #92400E;">
          Feedback from our reviewers:
        </p>
        <p style="margin: 0; font-size: 15px; color: #7C3A00;">
          ${adminReview || "No specific comments provided. Please make sure your blog meets our publishing standards."}
        </p>
      </div>

      <div style="margin-top: 24px;">
        <a href="${baseUrl}/dashboard/create/blog?Id=${id}"
          style="display: inline-block; background-color: #F59E0B; color: white; padding: 12px 20px; border-radius: 6px; text-decoration: none; font-weight: 600;">
          Edit & Resubmit
        </a>
      </div>
    `;

  return `
  <div style="background: #F3F4F6; padding: 40px; font-family: 'Segoe UI', Roboto, sans-serif;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 16px rgba(0,0,0,0.05);">
      
      <!-- Header -->
      <div style="background: linear-gradient(90deg, #1F2937, #111827); padding: 24px; text-align: center;">
        <img src="${logoUrl}" alt="Rentprompts Logo" style="height: 50px;" />
      </div>

      <!-- Content -->
      <div style="padding: 32px;">
        <h2 style="font-size: 20px; color: #111827; margin-bottom: 12px;">
          Blog Submission Update
        </h2>

        <p style="font-size: 16px; color: #374151; margin: 0 0 10px;">
          Hello <strong>${userName}</strong>,
        </p>

        <p style="font-size: 16px; color: #4B5563; margin: 0 0 18px;">
          Your blog titled 
          <a href="${baseUrl}/blog/${slug}" style="color: #3B82F6; text-decoration: none; font-weight: 500;">
            "${BlogsTitle}"
          </a> has been reviewed by our content team.
        </p>

        <!-- Status Block -->
        <div style="background: ${statusBg}; border-left: 4px solid ${statusColor}; padding: 16px 20px; border-radius: 8px; margin-bottom: 20px;">
          <p style="margin: 0; font-size: 16px; font-weight: 600; color: ${statusColor};">
            ${statusLabel}
          </p>
          <p style="margin: 6px 0 0; font-size: 15px; color: #4B5563;">
            Status: <strong style="text-transform: capitalize;">${status}</strong>
          </p>
        </div>

        ${actionSection}

        <p style="font-size: 14px; color: #6B7280; margin-top: 40px; line-height: 1.6;">
          Have questions or need help? Just reply to this email — we’re here for you.
        </p>
      </div>

      <!-- Footer -->
      <div style="background: #F9FAFB; padding: 20px; text-align: center;">
        <p style="font-size: 13px; color: #9CA3AF; margin: 0;">
          © ${new Date().getFullYear()} Rentprompts. All rights reserved.
        </p>
        <p style="font-size: 12px; color: #D1D5DB; margin: 4px 0 0;">
          This is an automated message. Please do not reply directly.
        </p>
      </div>
    </div>
  </div>
  `;
};
