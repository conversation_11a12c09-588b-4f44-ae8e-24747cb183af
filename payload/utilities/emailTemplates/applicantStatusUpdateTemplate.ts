
export const applicantStatusUpdateTemplate = ({
    userName,
    bountyTitle,
    bountySlug,
    applicantStatus,
    rejectionReason,
    logoUrl,
    baseUrl,
  }: {
    userName: string;
    bountyTitle: string;
    bountySlug: string;
    applicantStatus: string;
    rejectionReason?: string;
    logoUrl: string;
    baseUrl:string;
  }) => {
    const isApproved = applicantStatus === "approved";
  
    return `
      <div style="font-family: Arial, sans-serif; background-color: #f9f9f9;">
        <div style="max-width: 600px; margin: auto; background: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
          
          <div style="text-align: center; padding: 20px; background-color: #222831;">
            <img src="${logoUrl}" alt="Company Logo" style="height: 60px;" />
          </div>
  
          <div style="padding: 30px;">
            <p style="font-size: 16px;">Hello <strong>${userName}</strong>,</p>
            
            <p style="font-size: 16px;">
              Your application for the bounty <strong>${bountyTitle}</strong> has been 
              <strong style="color: ${isApproved ? "#28a745" : "#dc3545"}; text-transform: uppercase;">
                ${applicantStatus}
              </strong>.
            </p>
  
            ${
              !isApproved && rejectionReason
                ? `<p style="font-size: 15px;"><strong>Rejection Reason:</strong> ${rejectionReason}</p>`
                : ""
            }
  
            <div style="margin: 30px 0; text-align: center;">
              <a href="${baseUrl}/bounties/${bountySlug}"
                style="background-color: #007bff; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                View Bounty
              </a>
            </div>
  
            <p style="margin-top: 30px; font-size: 14px; color: #888;">
              This is an automated update from our bounty platform. No action is needed unless prompted.
            </p>
          </div>
  
          <div style="text-align: center; padding: 15px; background-color: #eeeeee; font-size: 13px; color: #666;">
            &copy; ${new Date().getFullYear()} <a href="https://rentprompts.com/" style="color: #666;">Rentprompts.com</a>. All rights reserved.
          </div>
        </div>
      </div>
    `;
  };
  