export const newApplicantTemplate = ({
  userName,
  bountyTitle,
  bountySlug,
  applicant,
  logoUrl,
  baseUrl,
}: {
  userName: string;
  bountyTitle: string;
  bountySlug: string;
  applicant: {
    userName?: string;
    linkedin?: string;
    phone?: string;
    approach?: string;
  };
  logoUrl: any;
  baseUrl: string;
}) => `
  <div style="background-color: #F3F4F6; padding: 40px; font-family: 'Segoe UI', Roboto, sans-serif;">
    <div style="max-width: 600px; margin: auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 16px rgba(0,0,0,0.05);">

      <!-- Header -->
      <div style="background: linear-gradient(90deg, #1F2937, #111827); padding: 24px; text-align: center;">
        <img src="${logoUrl}" alt="Rentprompts Logo" style="height: 50px;" />
      </div>

      <!-- Body -->
      <div style="padding: 32px;">
        <h2 style="margin: 0 0 12px 0; font-size: 20px; color: #111827;">
          New Bounty Application Received
        </h2>

        <p style="font-size: 16px; color: #374151; margin-bottom: 18px;">
          Hello <strong>${userName}</strong>,
        </p>

        <p style="font-size: 16px; color: #4B5563; margin-bottom: 20px;">
          <strong>${applicant.userName || "An applicant"}</strong> has applied for your bounty titled
          <a href="${baseUrl}/bounties/${bountySlug}" style="color: #3B82F6; text-decoration: none; font-weight: 500;">
            "${bountyTitle}"
          </a>.
        </p>

        <table style="width: 100%; border-collapse: collapse; font-size: 15px; margin-bottom: 24px;">
          <tr style="border-bottom: 1px solid #E5E7EB;">
            <td style="padding: 12px 8px; font-weight: 600; color: #111827;">LinkedIn</td>
            <td style="padding: 12px 8px; color: #4B5563;">${applicant.linkedin || "Not Available"}</td>
          </tr>
          <tr style="border-bottom: 1px solid #E5E7EB;">
            <td style="padding: 12px 8px; font-weight: 600; color: #111827;">Phone</td>
            <td style="padding: 12px 8px; color: #4B5563;">${applicant.phone || "Not Available"}</td>
          </tr>
          <tr>
            <td style="padding: 12px 8px; font-weight: 600; color: #111827;">Approach</td>
            <td style="padding: 12px 8px; color: #4B5563;">${applicant.approach || "Not Available"}</td>
          </tr>
        </table>

        <div style="margin-top: 10px;">
          <a href="${baseUrl}/bounties/${bountySlug}" 
            style="background: #2563EB; color: white; padding: 12px 24px; text-decoration: none; font-weight: 600; border-radius: 6px; display: inline-block;">
            View Bounty Details
          </a>
        </div>

        <p style="font-size: 14px; color: #6B7280; margin-top: 30px;">
          You received this email because you're listed as the owner of this bounty.
        </p>
      </div>

      <!-- Footer -->
      <div style="background: #F9FAFB; padding: 20px; text-align: center;">
        <p style="font-size: 13px; color: #9CA3AF; margin: 0;">
          © ${new Date().getFullYear()} <a href="https://rentprompts.com" style="color: #6B7280; text-decoration: none;">Rentprompts.com</a>. All rights reserved.
        </p>
        <p style="font-size: 12px; color: #D1D5DB; margin-top: 6px;">
          This is an automated notification — please don’t reply to this email.
        </p>
      </div>

    </div>
  </div>
`;
