export const getProductStatusEmailHTML = ({
  userName,
  productName,
  status,
  adminReview,
  slug,
  id,
  logoUrl,
  baseUrl,
}: {
  id?: string;
  userName?: string;
  productName?: string;
  status?: string;
  adminReview?: string;
  slug?: string;
  logoUrl?: string;
  baseUrl?: string;
}) => {
  const statusColor = status === "approved" ? "#10B981" : "#EF4444";
  const statusBg = status === "approved" ? "#ECFDF5" : "#FEF2F2";
  const borderColor = status === "approved" ? "#10B981" : "#EF4444";

  return `
    <div style="font-family: 'Segoe UI', sans-serif; background-color: #f9fafb; padding: 30px;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.06); overflow: hidden;">
        
        <!-- Header -->
        <div style="text-align: center; background: linear-gradient(90deg, #1F2937, #111827); padding: 24px;">
          <img src="${logoUrl}" alt="Rentprompts Logo" style="height: 60px;" />
        </div>

        <!-- Greeting -->
        <div style="padding: 30px;">
          <p style="font-size: 16px; color: #374151; margin-bottom: 10px;">
            Hello <strong>${userName}</strong>,
          </p>

          <p style="font-size: 16px; color: #4B5563;">
            Your product
            <a href="${baseUrl}/products/${slug}" style="color: #2563EB; font-weight: 600; text-decoration: none;">
              "${productName}"
            </a> has been reviewed.
          </p>

          <!-- Status Block -->
          <div style="background-color: ${statusBg}; padding: 16px 20px; border-radius: 8px; border-left: 5px solid ${borderColor}; margin: 24px 0;">
            <p style="margin: 0; font-size: 16px; color: ${borderColor}; font-weight: 600; text-transform: uppercase;">
              Status: ${status}
            </p>
          </div>

          <!-- Action Message -->
          <p style="font-size: 16px; color: #4B5563;">
            ${
              status === "approved"
                ? `🎉 Congratulations! Your product is now available on our marketplace.`
                : `🔍 Your product requires some updates before it can be approved.`
            }
          </p>

          <!-- Admin Feedback (If Denied) -->
          ${
            status === "denied"
              ? `
              <div style="background-color: #FFFBEB; padding: 16px 20px; border-radius: 8px; border-left: 5px solid #F59E0B; margin-top: 20px;">
                <p style="margin: 0; font-size: 15px; color: #92400E;">
                  <strong>Feedback from our team:</strong><br>
                  ${adminReview || "No specific feedback provided. Please review our submission guidelines."}
                </p>
              </div>

              <div style="margin-top: 24px;">
                <a href="${baseUrl}/dashboard/create/product?Id=${id}/edit"
                  style="display: inline-block; background-color: #F59E0B; color: #fff; padding: 12px 20px; border-radius: 6px; text-decoration: none; font-weight: 600;">
                  Edit & Resubmit
                </a>
              </div>
            `
              : `
              <!-- CTA Button (If Approved) -->
              <div style="text-align: center; margin: 32px 0;">
                <a href="${baseUrl}/products/${slug}" 
                   style="display: inline-block; background-color: #2563EB; color: #fff; padding: 14px 28px; border-radius: 8px; text-decoration: none; font-weight: bold;">
                  View Product Page
                </a>
              </div>
            `
          }

          <!-- Support Note -->
          <p style="font-size: 14px; color: #9CA3AF; margin-top: 40px;">
            Need help? Just reply to this email or reach out to our support team anytime.
          </p>
        </div>

        <!-- Footer -->
        <div style="background-color: #F3F4F6; text-align: center; padding: 20px;">
          <p style="font-size: 13px; color: #6B7280;">
            © ${new Date().getFullYear()} Rentprompts. All rights reserved.<br/>
            <span style="font-size: 12px; color: #9CA3AF;">
              This is an automated email. Please do not reply directly.
            </span>
          </p>
        </div>
      </div>
    </div>
  `;
};
