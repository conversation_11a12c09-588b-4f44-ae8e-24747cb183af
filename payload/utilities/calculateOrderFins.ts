
import { Price } from "@/server/payload-types";
import { toast } from "sonner";

export const calculateOrderFins = async ( pricePackage: Price) => {
    if (pricePackage) {
        const totalCoins = Math.round(pricePackage.numberOfCoins + (pricePackage.numberOfCoins * pricePackage.discount)/100);
        // const totalBasePrice = ((pricePackage?.rate) ? (pricePackage?.rate) : 1) * (totalCoins? totalCoins: 1)
        const totalBasePrice = pricePackage.numberOfCoins;
        const totalTax = (((pricePackage?.tax) ? (pricePackage?.tax) : 0) / 100) * totalBasePrice; // configure tax
        const paymentGatewayCharge = totalBasePrice * 0.02; 
        const total = Number(totalBasePrice) + Number(totalTax) + Number(paymentGatewayCharge);

        return { totalCoins, totalBasePrice, totalTax, total };
    } else {
      toast.error("User cart is empty")

        // throw new Error("User cart is empty");
    }
}