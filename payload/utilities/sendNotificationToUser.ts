import payload from "payload"; // Ensure this is the correct import path

export const sendNotificationToUser = async ({
  userId,
  message,
  link,
}: {
  userId: string;
  message: string;
  link?: string;
}) => {
  await payload.create({
    collection: "notifications", // ✅ Make sure the collection slug matches exactly
    data: {
      user: userId,
      message,
      link,
      read: false,
    },
  });
};
