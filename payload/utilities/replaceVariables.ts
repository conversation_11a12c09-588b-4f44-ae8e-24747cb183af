const escapeRegExp = (string: string): string =>
  string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape all special regex chars

const replaceVariables = (
  template: string,
  values: { [key: string]: string }
): string => {
  for (const key in values) {
    const prefixedKey = key.startsWith('$$') ? key : `$$${key}`;
    const escapedKey = escapeRegExp(prefixedKey); // Escape all special chars
    const regex = new RegExp(escapedKey, 'g');
    template = template.replace(regex, values[key]);
  }
  return template;
};

export default replaceVariables;



// const replaceVariables = (
//   template: string,
//   values: { [key: string]: string }
// ): string => {
//   for (const key in values) {
//     // Determine the actual key in the template
//     const prefixedKey = key.startsWith('$$') ? key : `$$${key}`;
//     const escapedKey = prefixedKey.replace(/[$]/g, '\\$'); // Escape $ for RegExp
//     const regex = new RegExp(`${escapedKey}`, 'g'); // Match the key exactly
//     template = template.replace(regex, values[key]); // Replace occurrences with the value
//   }
//   return template;
// };

// export default replaceVariables;

