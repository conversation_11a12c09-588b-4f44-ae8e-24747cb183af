import payload from "payload";
import { rewardJoulesEmailTemplate } from "../emailTemplates/rewardJoulesEmailTemplate";
import sendEmail from "../sendEmail";
import { sendNotificationToUser } from "../sendNotificationToUser";

// Type definitions
type FinalSubmission = {
  file: string;
  submittedAt: Date;
  notes?: string;
  status?: "pending" | "accepted" | "rejected";
  rejectionReason?: string;
};

type Applicant = {
  userId: string;
  userImage?: string;
  userName?: string;
  linkedin: string;
  phone: number;
  approach: string;
  relatedFile?: string[];
  canReapply?: boolean;
  applicantStatus?: "pending" | "approved" | "rejected";
  rejectionReason?: string;
  winner?: boolean;
  finalSubmissions?: FinalSubmission[];
  rewardJoules?: number;
  message?: string;
};

type BountyDocument = {
  title: string;
  slug: string;
  status?: string;
  user: string | { id: string };
  applicants?: Applicant[];
  // Add other fields as needed
};

export const handleApplicantNotifications = async ({
  doc,
  previousDoc,
  req,
}: {
  doc: BountyDocument;
  previousDoc: BountyDocument;
  req: any;
}) => {
  const logoUrl = `https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/RentpromptsIcon.png`;

  const currentApplicants = doc.applicants || [];
  const previousApplicants = previousDoc.applicants || [];

  // Create a map of previous applicants by userId for reliable comparison
  const previousApplicantsMap = new Map(
    previousApplicants.map((app) => [app.userId, app])
  );

  for (const currentApplicant of currentApplicants) {
    const previousApplicant = previousApplicantsMap.get(
      currentApplicant.userId
    );
    const applicantId = currentApplicant.userId;
    const applicantName = currentApplicant.userName || "Applicant";

    // 1. New final submission
    if (previousApplicant) {
      const hasNewSubmission =
        (currentApplicant.finalSubmissions?.length || 0) >
        (previousApplicant.finalSubmissions?.length || 0);

      if (hasNewSubmission) {
        await sendNotificationToUser({
          userId: typeof doc.user === "object" ? doc.user.id : doc.user,
          message: `You've received a submission for your bounty "${doc.title}" from ${applicantName}. Click to review their work.`,
          link: `/bounties/${doc.slug}`,
        });
      }
    }

    // 2. Final submission status changed
    if (previousApplicant) {
      const currentSubmissions = currentApplicant.finalSubmissions || [];
      const previousSubmissions = previousApplicant.finalSubmissions || [];

      for (let j = 0; j < currentSubmissions.length; j++) {
        const submission = currentSubmissions[j];
        const prevSubmission = previousSubmissions[j];

        if (
          prevSubmission &&
          submission.status &&
          submission.status !== prevSubmission.status
        ) {
          let msg = "";
          if (submission.status === "accepted") {
            msg = `Your submission for the bounty "${doc.title}" has been accepted. Great job!`;
          } else if (submission.status === "rejected") {
            msg = `Your submission for the bounty "${doc.title}" was not accepted. Thank you for your effort.`;
          }

          if (msg) {
            await sendNotificationToUser({
              userId: applicantId,
              message: msg,
              link: `/bounties/${doc.slug}`,
            });
          }
        }
      }
    }

    // 3. Reward joules notification
    if (previousApplicant) {
      const currentJoules = currentApplicant.rewardJoules || 0;
      const previousJoules = previousApplicant.rewardJoules || 0;

      if (currentJoules > 0 && currentJoules !== previousJoules) {
        // console.log(
        //   `Reward change detected for ${applicantId}: ${previousJoules} -> ${currentJoules}`
        // );

        // Send notification
        await sendNotificationToUser({
          userId: applicantId,
         message: `Great job! You've been rewarded ${currentJoules} Joules for your contribution to the bounty: "${doc.title}".`,

          // message: `You've received a reward of ${currentJoules} Joules for your work on the bounty: "${doc.title}"`,
          link: `/bounties/${doc.slug}`,
        });

        // Send email
        try {
          const user = await payload.findByID({
            collection: "users",
            id: applicantId,
          });

          if (user?.email) {
            const html = rewardJoulesEmailTemplate({
              userName: applicantName,
              bountyTitle: doc.title,
              bountySlug: doc.slug,
              rewardJoules: currentJoules,
              logoUrl,
            });

            await sendEmail({
              to: user.email as string,
              subject: `🎉 You've earned ${currentJoules} Joules!`,
              html,
            });
          }
        } catch (err) {
          console.error(`Failed to process reward for ${applicantId}`, err);
        }
      }
    }
  }
};
