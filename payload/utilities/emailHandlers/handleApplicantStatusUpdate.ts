import sendEmail from "../../utilities/sendEmail";
import { applicantStatusUpdateTemplate } from "../emailTemplates/applicantStatusUpdateTemplate";
export const handleApplicantStatusUpdate = async ({
  updatedApplicants,
  doc,
  req,
}: any) => {
  for (const updatedApp of updatedApplicants) {
    try {
      const applicant = await req.payload.findByID({
        collection: "users",
        id: updatedApp.userId,
      });

      if (!applicant?.email) continue;

      const subject =
        updatedApp.applicantStatus === "approved"
          ? `✅ Your Application for "${doc.title}" is Approved!`
          : `❌ Your Application for "${doc.title}" is Rejected`;

      const rejectionNote =
        updatedApp.applicantStatus === "rejected" && updatedApp.rejectionReason
          ? `<p><strong>Reason:</strong> ${updatedApp.rejectionReason}</p>`
          : "";

      const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL
      const logoUrl = `https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/RentpromptsIcon.png`;
      const html = applicantStatusUpdateTemplate({
        userName: updatedApp.userName || "Applicant",
        bountyTitle: doc.title,
        bountySlug: doc.slug,
        applicantStatus: updatedApp.applicantStatus,
        rejectionReason: updatedApp.rejectionReason,
        logoUrl,
        baseUrl,
      });

      try {
        await req.payload.create({
          collection: "notifications",
         
          data: {
            user: updatedApp.userId,
            message: `Your application for the bounty "${doc.title}" has been ${updatedApp.applicantStatus.toUpperCase()}. ${
              updatedApp.applicantStatus === "rejected"
                ? `Reason: ${updatedApp.rejectionReason || "No specific reason provided."}`
                : `Congratulations! You can now chat with the bounty owner.`
            }`,
            link: `/bounties/${doc.slug || ""}`,
          },
        });
      } catch (error) {
        console.log("notification is not created :", error);
      }
      await sendEmail({
        to: applicant.email,
        subject,
        html,
      });

      

      // console.log(
      //   `✅ Email sent to applicant (${updatedApp.applicantStatus}): ${applicant.email}`
      // );
    } catch (e) {
      console.error("❌ Failed to send email to applicant", e);
    }
  }
};
