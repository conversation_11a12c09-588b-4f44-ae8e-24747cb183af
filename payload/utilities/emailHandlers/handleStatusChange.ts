import sendEmail from "../../utilities/sendEmail";
// import { getIO } from "../../../server/socketServer";
import { getBountyStatusEmailHTML } from "../emailTemplates/bountyStatusUpdate";

export const handleStatusChange = async ({
  doc,
  userEmail,
  userName,
  req,
  previousStatus,
}: any) => {
  // const isNewSubmission = previousStatus === undefined || previousStatus === null;
  // const isResubmission = previousStatus && previousStatus !== "pending" && doc.status === "pending";

  // let message = "";
  // let subject = "";
  // if (doc.status === "pending") {
  //   if (isNewSubmission) {
  //     message = `Your bounty "${doc.title}" has been submitted successfully. Please wait for admin review.`;
  //     subject = "Your Bounty Has Been Submitted!";
  //   } else if (isResubmission) {
  //     message = `Your bounty "${doc.title}" has been updated and is pending admin review.`;
  //     subject = "Your Bounty Has Been Updated!";
  //   }
  // } else {
  //   message = `Your bounty "${doc.title}" has been ${doc.status.toUpperCase()}. ${
  //     doc.status === "denied" ? doc.adminReview || "" : `You can now view it live.`
  //   }`;
  //   subject = `Your Bounty Has Been ${doc.status === "denied" ? "Rejected" : "Approved"}!`;
  // }
  const logoUrl = `https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/RentpromptsIcon.png`;
  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL;

  const htmlContent = getBountyStatusEmailHTML({
    userName: userName || "User",
    title: doc.title || "Untitled Bounty",
    status: doc.status,
    price: doc?.estimatedPrice,
    adminReview: doc.adminReview,
    slug: doc?.slug || "",
    logoUrl,
    baseUrl,
  });

  try {
    await req.payload.create({
      collection: "notifications",
      data: {
        user: doc.user,
        message: `Your bounty "${doc?.title}" has been ${doc?.status.toUpperCase()}. ${
          doc?.status === "denied"
            ? `Your bounty joules ${doc?.estimatedPrice} has been refunded to you. Reason: ${doc?.adminReview}`
            : `You can now view it live.`
        }`,
        link: `/bounties/${doc?.slug || ""}`,
      },
    });
  } catch (error) {
    console.log("notification is not created :", error);
  }

  await sendEmail({
    to: userEmail,
    subject: `Your Bounty Has Been ${doc?.status === "denied" ? "Rejected" : "Approved"}!`,
    html: htmlContent,
  });

  //   try {
  //     const io = getIO();
  //     io.emit("new-notification", {
  //       userId: doc.user,
  //       message: "Notification added",
  //     });
  //   } catch (err) {
  //     console.log("Socket emit failed", err);
  //   }

  // console.log(
  //   `✅ Status change email and notification sent to ${userEmail || "[no email]"}`
  // );
};
