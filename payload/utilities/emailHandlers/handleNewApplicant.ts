import sendEmail from "../../utilities/sendEmail";
import { newApplicantTemplate } from "../emailTemplates/newApplicantTemplate";
import {Logo} from "../../components/graphics/Logo"
export const handleNewApplicant = async ({
  newlyAdded,
  userEmail,
  userName,
  doc,
  req,
}: any) => {
  if (!userEmail) return;
// console.log("doc newapplicant", doc);
  try {
    await req.payload.create({
      collection: "notifications",
      data: {
        user: doc.user,
        message: `A new applicant "${newlyAdded?.userName || newlyAdded?.email}" has applied to your bounty "${doc.title}". Review their application now.`,
        // message: `Your bounty "${doc.title}" has been ${doc.status.toUpperCase()}. ${
        //   doc.status === "denied"
        //     ? doc.adminReview || ""
        //     : `You can now view it live.`
        // }`,
        link: `/bounties/${doc.slug || ""}`,
      },
    });
  } catch (error) {
    console.log("notification is not created :", error);
  }
  const logoUrl = `https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/RentpromptsIcon.png`;
      const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL

  const html = newApplicantTemplate({
    userName,
    bountyTitle: doc.title,
    bountySlug: doc.slug,
    applicant: newlyAdded,
    logoUrl,
    baseUrl,
  });

  await sendEmail({
    to: userEmail,
    subject: `New applicant on your bounty: ${doc.title}`,
    // html: `
    //   <p>Hello ${userName},</p>
    //   <p><strong>${newlyAdded.userName || "Someone"}</strong> has applied to your bounty <a href="/bounties/${doc.slug || ""}"><strong>${doc.title}</strong></a>.</p>
    //   <p><strong>LinkedIn:</strong> ${newlyAdded.linkedin || "Not Available"}</p>
    //   <p><strong>Phone:</strong> ${newlyAdded.phone || "Not Available"}</p>
    //   <p><strong>Approach:</strong> ${newlyAdded.approach || "Not Available"}</p>
    // `,
    html,
  });

  // console.log(`✅ Applicant email sent to ${userEmail}`);
};
