import { ChatOllama } from "@langchain/community/chat_models/ollama";
import { StringOutputParser } from "@langchain/core/output_parsers";

async function modelCallFunction(input: string) {
  const model = new ChatOllama({
    baseUrl: "http://localhost:11434", // Default value
    model: "llama3", // Default value
  });

  const stream = await model.pipe(new StringOutputParser()).stream(input);

  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  const result = chunks.join("");
  return result;
}
export default modelCallFunction;
