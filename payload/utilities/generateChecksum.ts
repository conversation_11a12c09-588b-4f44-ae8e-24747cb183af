import crypto from "crypto";
import sha256 from "sha256"
import * as CryptoJS from 'crypto-js';
// Utility function to generate the X-Verify header
export const generateChecksum = (data:any) => {

    // const payloadMain = Buffer.from(payload).toString("base64");
    // const _payload = Buffer.from(JSON.stringify(payload), "utf8");
    // const payloadMain = _payload.toString("base64");
    // const keyIndex = 1;
    // const string = payloadMain + "/pg/v1/pay" + "abece734-ba82-4d71-9ee3-0c31fab3789e";
    // // const _sha256 = crypto.createHash("sha256").update(string).digest("hex");
    // const _sha256 = sha256(string);
    // const checksum = _sha256 + "###" + keyIndex;
    const payload = JSON.stringify(data);
    const payloadMain = Buffer.from(payload).toString('base64');
    const key = process.env.SALT;
    // console.log('salt/apikey ', process.env.SALT)
    const keyIndex = 1;
    const string = payloadMain + '/pg/v1/pay' + key;
    const sha256 = CryptoJS.SHA256(string).toString(CryptoJS.enc.Hex);
    // const generateChecksum = (string: string): string => {
    //     const sha256 = CryptoJS.SHA256(string);
    //     return sha256.toString(CryptoJS.enc.Hex);
    // };
    const checksum = sha256 + '###' + keyIndex;

    return { checksum, payloadMain };
};
