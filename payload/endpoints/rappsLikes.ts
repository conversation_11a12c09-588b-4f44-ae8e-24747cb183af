import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";

export const rappsLikes: PayloadHandler = async (req: PayloadRequest, res) => {
  const { rentproductId } = req.params;
  const { payload, user } = req;
  const loggedInUserId = user?.id;

  if (!user) {
    return res.status(401).json({ error: "User not authenticated" });
  }

  try {
    const rentproduct = await payload.findByID({
      collection: "rapps",
      id: rentproductId,
      depth: 2, // Ensure this is necessary
    });
    if (!rentproduct) {
      return res.status(404).json({ error: "Rent product not found" });
    }

    const allLikes = rentproduct?.likes
    ? (rentproduct.likes as { user: { id: string } }[])
        .filter(like => like && like.user && like.user.id) // Filter out null or undefined likes and user IDs
        .map(like => like.user.id)
    : [];
  
    const hasLiked = allLikes.includes(loggedInUserId);
    
    // Create updated like IDs, filtering out the logged-in user if already liked
    const updatedLikeIds = hasLiked
      ? allLikes.filter(id => id !== loggedInUserId) // Exclude the logged-in user if already liked
      : [...allLikes, loggedInUserId]; // Add the logged-in user ID if not already liked
    
    // Map the updated like IDs to the desired format, ensuring no null or undefined IDs
    const updatedLikesArray = updatedLikeIds
      .filter(id => id != null) // Filter out any null or undefined IDs
      .map(id => ({
        user: id
      }));
  

    const updatedrentproduct: any = await payload.update({
      collection: "rapps",
      id: rentproductId,
      data: { likes: updatedLikesArray },
    });

    const likeCount = updatedLikesArray.length;
    const isLikeUpdated = !hasLiked;

    return res.status(201).json({ success: true, likeCount, isLikeUpdated });
  } catch (error) {
    console.error("Error in rappsLikes handler:", error);
    return res
      .status(500)
      .json({ message: "An error occurred while updating likes." });
  }
};
