import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";
import { ProductFile, User } from "../../server/payload-types";

export const bountyDetail: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  try {
    const { bountyId } = req.params;
    const { payload, user } = req;
    const loginUserId = user?.id;

    if (!loginUserId) {
      return res
        .status(404)
        .json({ success: false, message: "You have to loggedIn first" });
    }

    const bounty = await payload.findByID({
      collection: "bounties",
      id: bountyId,
      depth: 1,
    });

    const bountyDetails = {
    title: bounty?.title,
    content: bounty?.content,
    completionDate: bounty.completionDate,
    applyExpireDate: bounty.applyExpireDate,
    applicantsLimit: bounty.applicantsLimit,
    estimatedPrice: bounty?.estimatedPrice,
    updatedEstimatedPrice: bounty?.updatedEstimatedPrice,
    product_files: bounty?.product_files,
    termsAccepted: bounty?.termsAccepted,
    slug: bounty?.slug,
    needsApproval: bounty?.needsApproval,
    useCases: bounty.useCases,
    bountyType: bounty?.bountyType,
    status: bounty?.status,
     };
  
    return res.status(200).json( {success: true, data: bountyDetails });

  } catch (error) {
    console.error("[InvalidAction]", error);
    return res.status(500).json({
      success: false,
      error: true,
      msg: `Error fetching bounty`,
    });
  }
};
