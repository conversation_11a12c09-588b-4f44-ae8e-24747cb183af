
import { PayloadRequest } from "payload/types";
import { Payload<PERSON>and<PERSON> } from "payload/config";

export const pdfImport = async (req: PayloadRequest, res) => {

   
  

};
// import { PayloadRequest } from 'payload/types';
// import { getPayloadClient } from '../../server/get-payload';
// import { Purchase, User} from '../../server/payload-types';
// import { CharacterTextSplitter } from "@langchain/textsplitters";
// import { InMemoryStore } from "@langchain/core/stores";
// import { BaseMessage , HumanMessage } from "@langchain/core/messages";
// import { OpenAI } from "@langchain/openai";
// import {pdfParse} from 'pdf-parse';
// import fs from 'fs';
// import { spawn } from 'child_process';
// import path from 'path';

// const { ConversationalRetrievalQAChain } = require('langchain/chains');

// export const pdfimport = async (req: PayloadRequest, res: any) => {

//   const {  user}: {user: User} = req;
//   const userId = user?.id;
//   const pdfFile = req.files.file; // Assuming you're using a file upload middleware
//   const query = req.body.query;

//   const kvStore = new InMemoryStore<BaseMessage>();

// //Step 1: Extract Text from PDF
// async function extractTextFromPDF(filePath) {
//   const dataBuffer = fs.readFileSync(filePath);
//   const data = await pdfParse(dataBuffer);
//   return data.text; // Extracted raw text from the PDF
// }

// //Step 3: Split Summarized Text into Chunks
//  async function splitTextIntoChunks(text) {
//   const textSplitter = new CharacterTextSplitter({
//     chunkSize: 100,
//     chunkOverlap: 0,
//   });
//   return await textSplitter.splitText(text);
// }

// //Step 4: Store Chunks in a Vector Store (for similarity search
//   // Function to extract text and metadata from a PDF
//   async function extractPdfData(pdfFile) {
//     const pdfBuffer = fs.readFileSync(pdfFile); // Read PDF file as a buffer
//     const pdfData = await pdfParse(pdfBuffer); // Parse the PDF content
  
//     return {
//       text: pdfData.text, // Full text content of the PDF
//       metadata: pdfData.info, // Metadata (author, title, etc.)
//     };
//   }
//   // Initialize an InMemoryStore
// const kvStoreForPDF = new InMemoryStore();

// // Extract data from the PDF
// const { text, metadata } = await extractPdfData(pdfFile);

// // Store the extracted data in the InMemoryStore
// await kvStoreForPDF.mset([
//   ["pdf:content", new HumanMessage(text)], // Store the text content
//   ["pdf:metadata", new HumanMessage(JSON.stringify(metadata))], // Store the metadata as a JSON string
// ]);

// // Retrieve and iterate over keys
// const yieldedKeys = [];
// for await (const key of kvStoreForPDF.yieldKeys("pdf:")) {
//   yieldedKeys.push(key);
// }

// // Fetch data from the store
// await kvStore.mget(["pdf:content", "pdf:metadata"]);

// // Log results
// console.log("Yielded Keys:", yieldedKeys);

// //Example Usage
// (async () => {
//   const pdfText = await extractTextFromPDF('pdfFile');
//   const chunks = splitTextIntoChunks(pdfText);
//   const vectorStore = extractPdfData(chunks);
//   const qaChain = ConversationalRetrievalQAChain.fromLLM(new OpenAI({ temperature: 0.7 }), vectorStore);
//   const response = await qaChain.call({ input: query });
// })();


//     // Save the uploaded PDF file to a temporary location
//      const tempPath = path.resolve(`./uploads/${pdfFile.name}`);
//     await pdfFile.mv(tempPath);
//     const pythonScriptPath = path.resolve('./scripts/process_pdf.py');
//     const pythonProcess = spawn('python', [pythonScriptPath, tempPath, query]);
//     let result = '';
//     pythonProcess.stdout.on('data', (data) => {
//       result += data.toString();
//     });

//     pythonProcess.stderr.on('data', (data) => {
//       console.error('Python Error:', data.toString());
//     });

//     pythonProcess.on('close', (code) => {
//       if (code === 0) {
//         res.json({ answer: result.trim() });
//       } else {
//         res.status(500).json({ error: 'Failed to process the PDF.' });
//       }
//     });
// //  });
// // };

//   }