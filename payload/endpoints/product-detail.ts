import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";
import { Media, User } from "../../server/payload-types";

export const productDetail: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  try {
    const { productId } = req.params;
    const { payload, user } = req;
    const loginUserId = user?.id;

    if (!loginUserId) {
      return res
        .status(404)
        .json({ success: false, message: "You have to loggedIn first" });
    }

    const product = await payload.findByID({
      collection: "products",
      id: productId,
      depth: 1,
    });
    const productDetails = {
      slug: product.slug,
      name: product.name,
      description: product.description,
      generationType: product.generationType,
      generationModel: product.generationModel,
      price: product.price,
      images: product.images,
      affiliated_with: product.affiliated_with,
      needsApproval: product.needsApproval,
      productCategory:product.productCategory,
      listingCategory: product.listingCategory,
      product_files: product.product_files,
      approvedForSale:product.approvedForSale || " ",
     };
  
    return res.status(200).json( {success: true, data: productDetails });

  } catch (error) {
    console.error("[InvalidAction]", error);
    return res.status(500).json({
      success: false,
      error: true,
      msg: `Error fetching product`,
    });
  }
};
