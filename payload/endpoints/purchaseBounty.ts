import { PayloadRequest } from "payload/types";
import { getPayloadClient } from "../../server/get-payload";
import { Bounty, Purchase } from "@/server/payload-types";
import CustomError from "../access/customError";
import { toast } from "sonner";

export const purchaseBounty: any = async ({
  req,
  doc,
  res,
  operation,
}: {
  req: PayloadRequest;
  doc: Bounty;
  res: any;
  operation: string;
}): Promise<Bounty> => {
  const { user } = req;
  const userId = user?.id;

  const bounty: any = doc as Bounty;
  let payload: any;
  if (req.method === "POST" && operation === "create") {
    try {
      payload = await getPayloadClient();

      const user: any = await payload.findByID({
        collection: "users",
        id: userId,
      });

      if (!user) {
        throw new CustomError("You must be logged in to create a bounty.", 403);
      }

      // Check if user has enough balance
      if (user.coinBalance < bounty.estimatedPrice) {
        throw new CustomError("Insufficient balance Please recharge", 403);
      }

      const updatedCoinBalance = user.coinBalance - bounty.estimatedPrice;

      // Create a purchase record
      const purchase = await payload.create({
        collection: "purchases",
        data: {
          user: userId,
          bounty: bounty.id,
          purchaseType: "bounty",
        },
      });

      // Deduct the balance and update the user's purchases
      const purchaseIds = user.purchases
      ? user.purchases
          .filter((purchase: Purchase) => purchase && purchase.id != null) // Filter out null or undefined purchases
          .map((purchase: Purchase) => purchase.id) // Map to purchase IDs
      : [];

      const OldBounties = user.bounties
      ? user.bounties
          .filter((bounty: any) => bounty && bounty.id != null) // Filter out null or undefined bounties
          .map((bounty: any) => bounty.id) // Map to bounty IDs
      : [];

      

      try {
        const updatedUser = await payload.update({
          collection: "users",
          id: userId,
          data: {
            coinBalance: updatedCoinBalance,
            bounties: user.bounties ? [...OldBounties, bounty.id] : [bounty.id],
            purchases: user.purchases ? [...purchaseIds, purchase.id] : [purchase.id],
          },
        });

        return res;
      } catch (error) {
        // delete the existing bounty from the user account
        await payload.delete({
          collection: "bounties",
          id: bounty.id,
        });

        throw new CustomError(
          "Bounty Creation Failed, while updating the user account",
          403
        );
      }
    } catch (error) {
      console.log("error:", error)
      return res.status(500).json({ message: "Internal server error" });
    }
  } else {
    if (req.method === "PATCH" && operation === "update") {
        if (bounty.status === "denied") {
          // Fetch the user document
          const userDoc: any = await req.payload.findByID({
            collection: "users",
            id: userId,
          });
      
          if (!userDoc) {
            throw new CustomError("User not found.");
          }
      
          // Add the bounty's estimated coin value to the user's coinBalance
          const updatedCoinBalance = (userDoc.coinBalance || 0) + bounty.estimatedPrice;
      
          // Update the user's coin balance in the database
          try {
            await req.payload.update({
              collection: "users",
              id: userId,
              data: {
                coinBalance: updatedCoinBalance,
              },
            });
          } catch (error) {
            throw new CustomError("Error updating user document.");
           }
      
      
          }
          
        else{
          return bounty;
        }
  
     
    } else {
      throw new CustomError("Bounty Creation Failed", 403);
    }
  }
};
