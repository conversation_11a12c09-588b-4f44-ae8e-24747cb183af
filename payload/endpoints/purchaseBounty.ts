import { PayloadRequest } from "payload/types";
import { getPayloadClient } from "../../server/get-payload";
import { Bounty, Purchase } from "@/server/payload-types";
import CustomError from "../access/customError";

export const purchaseBounty: any = async ({
  req,
  doc,
  res,
  operation,
}: {
  req: PayloadRequest;
  doc: Bounty;
  res: any;
  operation: string;
  originalDoc: any;
}): Promise<Bounty> => {

  const { user } = req;
  const userId = user?.id;

  const bounty: any = doc as Bounty;
  let payload: any;

  const isReleasingReward = bounty.rewardReleased === true;
    if (isReleasingReward) {
    return bounty;
  }
  
  // if (req.method === "POST" && operation === "create" || "update") {
    if ((req.method === "POST" && operation === "create") ||
    (req.method === "POST" && operation === "update")) {
    try {
      payload = await getPayloadClient();

      if (!req.user) {
        throw new CustomError("You must be logged in to create a bounty.", 403);
      }

      // Check if user has enough balance
      if (user.coinBalance < bounty.estimatedPrice) {
        throw new CustomError("Insufficient balance Please recharge", 403);
      }

      const updatedCoinBalance = user.coinBalance - bounty.estimatedPrice;

      // Create a purchase record
      const purchase = await payload.create({
        collection: "purchases",
        data: {
          user: userId,
          bounty: bounty.id,
          purchaseType: "bounty",
        },
      });

      // Deduct the balance and update the user's purchases
      const purchaseIds = user.purchases
      ? user.purchases
          .filter((purchase: Purchase) => purchase && purchase.id != null) // Filter out null or undefined purchases
          .map((purchase: Purchase) => purchase.id) // Map to purchase IDs
      : [];

      const OldBounties = user.bounties
      ? user.bounties
          .filter((bounty: any) => bounty && bounty.id != null) // Filter out null or undefined bounties
          .map((bounty: any) => bounty.id) // Map to bounty IDs
      : [];

      try {
        const updatedUser = await payload.update({
          collection: "users",
          id: userId,
          data: {
            coinBalance: updatedCoinBalance,
            bounties: user.bounties ? [...OldBounties, bounty.id] : [bounty.id],
            purchases: user.purchases ? [...purchaseIds, purchase.id] : [purchase.id],
          },
          overrideAccess: true,
        });

        return bounty;
      } catch (error) {
        console.error("purchaseBounty error:", error);

        await payload.delete({
          collection: "bounties",
          id: bounty.id,
        });

        throw new CustomError(
          "Bounty Creation Failed, while updating the user account",
          403
        );
      }
    } catch (error) {
      console.error("purchaseBounty error:", error)
      return res.status(500).json({ message: "Internal server error" });
    }
  }
};
