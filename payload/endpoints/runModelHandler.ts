import { Payload<PERSON>and<PERSON> } from "payload/config";

import { User } from "../../server/payload-types";
import getOutput from "../utilities/runClients";
import { PayloadRequest } from "payload/types";

export const runModelHandler: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  const {
    obj,
    url,
    settings,
  }: { obj: any; url: string | null; settings: any } = req.body;
  const { user }: { user: User } = req;

  const { prompt,text, key, negativePrompt, systemPrompt , promptText, promptImage } = obj;
  const payload = req.payload;
  const id = req.params.id;
  const referer = req.headers.referer || "";
  const isDashboard = referer.includes("/livepreview/models");
  const isStoreFront = referer.includes("/rent");

  if (!id) {
    return res.status(400).send({ success: false, message: "Missing id" });
  }

  if (!user) {
    return res
      .status(400)
      .json({ success: false, message: "Please login to run" });
  }

  // let image = url || null;
  let image = null;
  if (url) {
    image = url;
  }
  try {
    const model = await payload.findByID({
      collection: "models",
      id,
    });
    const cost = model.cost + model.commision;

    if (user.coinBalance < cost) {
      return res.status(403).send({
        success: false,
        message: "Insufficient credits, please recharge",
      });
    }

    let apiKey = "";
    let modelName = "";

    if (model.provider === "openai") {
      apiKey =
        (key === "prod"
          ? model.prodkeys?.openai?.apikey
          : model.testkeys?.openai?.apikey) ?? "";
      modelName =
        (key === "prod"
          ? model.prodkeys?.openai?.modelname
          : model.testkeys?.openai?.modelname) ?? "";
    } else if (model.provider === "groq") {
      apiKey =
        (key === "prod"
          ? model.prodkeys?.groq?.apikey
          : model.testkeys?.groq?.apikey) ?? "";
      modelName =
        (key === "prod"
          ? model.prodkeys?.groq?.modelname
          : model.testkeys?.groq?.modelname) ?? "";
    } else if (model.provider === "replicate") {
      apiKey =
        (key === "prod"
          ? model.prodkeys?.replicate?.apikey
          : model.testkeys?.replicate?.apikey) ?? "";
      modelName =
        (key === "prod"
          ? model.prodkeys?.replicate?.modelname
          : model.testkeys?.replicate?.modelname) ?? "";
    } else if (model.provider === "falai") {
      apiKey =
        (key === "prod"
          ? model.prodkeys?.falai?.apikey
          : model.testkeys?.falai?.apikey) ?? "";
      modelName =
        (key === "prod"
          ? model.prodkeys?.falai?.modelname
          : model.testkeys?.falai?.modelname) ?? "";
    }
    else if (model.provider === "runway") {
      apiKey =
        (key === "prod"
          ? model.prodkeys?.runway?.apikey
          : model.testkeys?.runway?.apikey) ?? "";
      modelName =
        (key === "prod"
          ? model.prodkeys?.runway?.modelname
          : model.testkeys?.runway?.modelname) ?? "";
    }



    const modelOutput = await getOutput({
      modelName,
      apiKey,
      provider: model.provider, 
      prompt,
      text,
      promptText,
      promptImage,
      negativePrompt,
      systemPrompt,
      settings: model.type === "text" || model.type === "vision" ? {} : settings,
      image,
    });

    // Return success with the model output
    res.status(200).send({
      success: true,
      output: modelOutput,
      message: "Model run successfully",
    });
  } catch (error) {
    console.error("Error in runModelHandler:", error);
    res.status(500).send({ success: false, message: error.message });
  }
};
