import { Payload<PERSON><PERSON><PERSON> } from "payload/config";
import { PayloadRequest } from "payload/types";
import crypto from "crypto";
import sha256 from "sha256"
import { checkStatusRazorPayIntent } from "../lib/razorpay";


export const verifyRazorPay: PayloadHandler = async (
    req: PayloadRequest,
    res
  ) => {
    try {
        // console.log("inside verify razorpay")
        const { user, body, payload } = req;
        // payload.logger.info(`request payment and order :: ${body.razorpayPaymentId}|${body.razorpayOrderId}|${body.orderCreationId}`)
        // console.log('access ::', process.env.RAZORPAY_SECRET)
        const shasum = crypto.createHmac("sha256", process.env.RAZORPAY_SECRET);
        
        shasum.update(body.razorpayOrderId + "|" + body.razorpayPaymentId);
       
        const digest = shasum.digest("hex");
        
        // console.log('signature :: ', req.headers["x-razorpay-signature"])
        // console.log('digest :: ', digest);

        if(process.env.PAYMENT_MODE === 'prod'){
            if (digest !== req.headers["x-razorpay-signature"]){
                console.log('request is not legit there')
                const order = await payload.update({
                    collection: "orders",
                    id: body.orderCreationId,
                    data: {
                        status: "pending",
                    }
                })
                // const _payload = { "server": digest, msg: "request not legit", razorpaysign: req.headers["x-razorpay-signature"]}
                await payload.update({
                    collection: "paymentdetails",
                    where: {
                        providerTxId: body.razorpayOrderId
                    },
                    data: {
                        status: "pending",
                        payload: req.body
                    }
                })
                // log this event in the database in a
                return res.status(401).json({ success: false, msg: "Request is not legit.", data: {OrderId: order.id, status: order.status, isPaid: order._isPaid, paymentMethod: order.paymentMethod, PurchasedCoins: order.totalCoins}});
                
            }
        }
            const verifyRes = await checkStatusRazorPayIntent(body.razorpayPaymentId)           
            // console.log('Response verify ::', verifyRes)
            if (verifyRes.status === 'captured') {
                const order:any = await payload.update({
                    collection: "orders",
                    id: body.orderCreationId,
                    data: {
                        _isPaid: true,
                        status: "completed",
                    }
                })
                // console.log('Order updated ::', JSON.stringify(order))
                await payload.update({
                    collection: "paymentdetails",
                    where: {
                        providerTxId: body.razorpayOrderId
                    },
                    data: {
                        status: "paid",
                        payload: verifyRes
                    }
                })
                const updatedCoinBalance = await payload.update({
                    collection: "users",
                    id: user.id,
                    data: {
                        coinBalance: user.coinBalance ? user.coinBalance + order.totalCoins : 0 + order.totalCoins
                    }
                })

                // console.log("updated coin balance : ", updatedCoinBalance)
                return res.status(200).json({ success: true, data: {OrderId: order.id, coinBalance: updatedCoinBalance.coinBalance, status: order.status, isPaid: order._isPaid, paymentMethod: order.paymentMethod, PurchasedCoins: order.totalCoins} });
            } else {
                const order = await payload.update({
                    collection: "orders",
                    id: body.orderCreationId,
                    data: {
                        status: "pending",
                    }
                })
                await payload.update({
                    collection: "paymentdetails",
                    where: {
                        providerTxId: body.razorpayOrderId
                    },
                    data: {
                        status: "pending",
                        payload: verifyRes
                    }
                })
                return res.status(201).json({ success: true, status: "pending", data: {OrderId: order.id, status: order.status, isPaid: order._isPaid, paymentMethod: order.paymentMethod, PurchasedCoins: order.totalCoins} });
            }     
        
        
        
    } catch (error) {
        console.log("[VERIFY_ERROR]", error);
        return res.status(500).json({ error: `Error creating order: ${error}` });
    }
}
