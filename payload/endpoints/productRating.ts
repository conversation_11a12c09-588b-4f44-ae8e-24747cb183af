import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";


export const productRating: PayloadHandler = async (
    req: PayloadRequest,
    res
) => {
    
        const { rate } = req.body;
        const { productId } = req.params;
        const { payload, user } = req;

        // Validate rating
        if (typeof rate !== "number" || rate < 1 || rate > 5) {
          return res
            .status(400)
            .json({ error: "Rating must be a number between 1 and 5." });
        }

        try {
          // Find the product by ID
          const product: any = await payload.findByID({
            collection: "products",
            id: productId,
          });

          if (!product) {
            return res.status(404).json({ error: "Product not found." });
          }

          // Get user IDs who have already rated the product
          const userIds =
          (
            product?.userRatings as { user: { id: string | null | undefined }; rating: number | null | undefined }[]
          )
            ?.map((ratingId: any) => {
             const userId = ratingId?.user?.id;
              const rating = ratingId?.rating;
        
              if (userId && rating !== undefined && rating !== null) {
                return {
                  user: userId,
                  rating: rating,
                };
              }
              return null;
            }).filter((entry) => entry !== null) ?? [];
        

          const isRated = userIds?.some((userId) => userId.user === user?.id);

          // Update or add the user's rating
          const updatedRatings = isRated
            ? userIds.map((rating) =>
                rating.user === user.id ? { ...rating, rating: rate } : rating
              )
            : [...userIds, { user: user.id, rating: rate }];

          // Calculate the new average rating
          const totalRating =
            updatedRatings.reduce(
              (sum, userRating) => sum + userRating.rating,
              0
            ) / updatedRatings.length;

          // Update the product with the new ratings and average
          await payload.update({
            collection: "products",
            id: productId,
            data: { userRatings: updatedRatings, rating: totalRating },
            depth: 2,
          });

          res.status(201).json({ success: true });
        } catch (error) {
          console.error("Error updating rating:", error);
          res.status(500).json({ error: "Failed to update rating." });
        }
    }



    export const getProductRating: PayloadHandler = async (
        req: PayloadRequest,
        res
    ) => {
            const { productId } = req.params;
            const { payload } = req;
        
            try {
              // Find the product by ID
              const product: any = await payload.findByID({
                collection: "products",
                id: productId,
              });
        
              if (!product) {
                return res.status(404).json({ error: "Product not found." });
              }
        
              // Return the average product rating (no user-based check here)
              res.json({
                rating: product?.rating || 0,
              });
            } catch (error) {
              console.error("Error fetching product rating:", error);
              res.status(500).json({ error: "Failed to fetch product rating." });
            }
    }