import { calculateOrderFins } from "../utilities/calculateOrderFins";
import { createRazorPayIntent } from "../lib/razorpay";
import { createPhonePePayIntent } from "../lib/phonepe";
import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";
import { toast } from "sonner";

export const buyCoins: PayloadHandler = async (req: PayloadRequest, res) => {
  try {
    // console.log('BUy Coins route entered :: ', req.user)
    const { user, body, payload } = req;
    // payload.logger.info(`request :: ${ JSON.stringify(req.body) } `)
    const paymentMethod = body.paymentMethod;
    const Coins = body.totalCoins;
    const pricePackageId = body.pricePackageId;
    // console.log("inside creating order")

    // payload.logger.info(`request query :: ${paymentMethod}`);
    // payload.logger.info(`request :: ${JSON.stringify(user)} `);

    ////////////////////// comment pr /////////
    // if (!user || !paymentMethod || !pricePackageId) {
    //   return res.status(400).json({
    //     success: false,
    //     error: true,
    //     message: "Bad request. Please specify all required fields.",
    //   });
    // }
    if (!user || !paymentMethod || (!pricePackageId && !Coins)) {
      return res.status(400).json({
        success: false,
        error: true,
        message: "Bad request. Please specify all required fields.",
      });
    }
    ////////////////////// comment pr /////////
    let pricePackage;
    let financialData;

    ////////////////////// comment pr /////////

    // const pricePackage: any = await payload.findByID({
    //   collection: "prices",
    //   id: pricePackageId,
    // });

    if (pricePackageId) {

      try {
        // Try fetching the price package from the database
        pricePackage = await payload.findByID({
          collection: "prices",
          id: pricePackageId,
        });

        if (pricePackage && pricePackage.active) {
          financialData = await calculateOrderFins(pricePackage);
        } else {
          // If price package is not active or not found, throw an error (no fallback here)
          throw new Error("Price package is inactive or not found.");
        }
      } catch (error) {
        const totalCoins = Coins;
        const taxRate = 0; // Default tax rate
        const totalBasePrice = Coins; // Use totalCoins as base price
        const totalTax =  (taxRate / 100) * totalBasePrice;
        const paymentGatewayCharge = totalBasePrice * 0.02;
        const total = Number(totalBasePrice) + Number(totalTax) + Number(paymentGatewayCharge); 

        financialData = {
          totalCoins,
          totalBasePrice,
          totalTax,
          total,
        };
      
      }
    }


    const { totalCoins, totalBasePrice, totalTax, total } = financialData;

    // creating payment entry with init status
    const initpayment = await payload.create({
      collection: "paymentdetails",
      data: {
        paymentMethod: paymentMethod,
        totalAmountPaid: total,
        tax: parseFloat(totalTax.toFixed(2)),
        status: "init",
        createdBy: user.id,
      },
    });
    // console.log("initpayment", initpayment);
    // create a draft or init order
    const order = await payload.create({
      collection: "orders",
      data: {
        _isPaid: false,
        paymentMethod: paymentMethod,
        status: "init",
        totalCoins: totalCoins,
        totalBasePrice: totalBasePrice,
        tax: parseFloat(totalTax.toFixed(2)),
        total: total, // add user cart total here
        user: user.id,
        paymentId: initpayment.id,
        createdBy: user.email,
      },
    });
    // payload.logger.info(`draft order :: ${JSON.stringify(order)}`)
    // create payment intent
    const currency = "INR";
    switch (paymentMethod) {
      case "RAZORPAY":
        const response = await createRazorPayIntent(total, currency, order.id);
        // update order with order id from razor pay
        // payload.logger.info(`razorpay order :: ${JSON.stringify(response)}`);
        await payload.update({
          collection: "orders",
          id: order.id,
          data: {
            razorPayOrderId: response.id,
          },
        });
        // payload.logger.info(`razorpay order id :: ${response.id}`);
        await payload.update({
          collection: "paymentdetails",
          id: initpayment.id,
          data: {
            providerTxId: response.id,
            payload: response,
          },
        });
        // payload.logger.info(`payment details updated`);
        return res
          .status(201)
          .json({ success: true, rentpromptOderId: order.id, data: response });
      // break

      case "PHONEPE":
        const phoneperes = await createPhonePePayIntent({
          total,
          orderId: order.id,
          user,
        });
        // update order with order id from razor pay

        // payload.logger.info(`phonepe order :: ${JSON.stringify(phoneperes)}`);
        if (phoneperes.success) {
          // update order and payment
          await payload.update({
            collection: "paymentdetails",
            id: initpayment.id,
            data: {
              providerTxId: order.id,
              payload: phoneperes,
            },
          });
          // console.log("phonepay responose:", phoneperes)
          // return res.redirect(phoneperes?.data?.instrumentResponse?.redirectInfo?.url);
          return res.status(201).json({
            success: true,
            data: {
              Url: phoneperes?.data?.instrumentResponse?.redirectInfo?.url,
              OrderId: order.id,
            },
          });
        } else {
          return res.status(500).json({ success: false, data: phoneperes });
        }
      default:
        return res.status(500).json({
          success: false,
          error: true,
          message: "Bad request. Unsupported Payment Method!",
        });
    }
  } catch (error) {
    console.log("[BUYCOINS_ERROR]", error);
    // payload.logger.info(`razorpay order id :: ${JSON.stringify(error)}`);
    toast.error("Error initiating payment, Please reach out to support");
    // TODO: Update the status of the document created in payment with status 'error'
    return res.status(500).json({
      success: false,
      error: true,
      msg: `Error initiating payment`,
    });
  }
};
