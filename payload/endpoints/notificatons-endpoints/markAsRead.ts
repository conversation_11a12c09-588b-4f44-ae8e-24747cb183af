// src/endpoints/notifications-endpoints/markAsRead.ts

import { Endpoint } from "payload/config";

export const markAsRead: Endpoint = {
  path: "/markRead/:id",
  method: "patch",
  handler: async (req, res) => {
    if (!req.user) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    try {
      const updated = await req.payload.update({
        collection: "notifications",
        id: req.params.id,
        data: { read: true },
      });

      return res.status(200).json({ success: true, updated });
    } catch (error) {
      console.error("Error:", error);
      return res.status(500).json({ message: "Something went wrong" });
    }
  },
};
