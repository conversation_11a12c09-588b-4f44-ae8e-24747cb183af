import { Endpoint } from "payload/config";

export const getUserNotifications: Endpoint = {
  path: "/my-notifications",
  method: "get",
  handler: async (req, res) => {
    if (!req.user) {
      return res.status(401).json({ message: "Unauthorized" });
    }
    const limit = Number(req.query.limit) || 10;
    const page = Number(req.query.page) || 1;
    const onlyUnread = req.query.unread === "true";
    const userId = req.user.id;
    const filter = req.query.filter;
    const where: any = {
      user: { equals: userId },
    };

    if (filter === "unread") {
      where.read = { equals: false };
    } else if (filter === "read") {
      where.read = { equals: true };
    }
    if (onlyUnread) {
      where.read = { equals: false };
    }

    try {
      const notifications = await req.payload.find({
        collection: "notifications",
        where,
        sort: "-createdAt",
        limit,
        page,
      });
      
      const unreadCount = await req.payload.count({
        collection: "notifications",
        where: {
          user: { equals: userId },
          read: { equals: false },
        },
      });

      const readCount = await req.payload.count({
        collection: "notifications",
        where: {
          user: { equals: userId },
          read: { equals: true },
        },
      });

      // 👉 prevent browser caching
      res.setHeader("Cache-Control", "no-store");
      return res.status(200).json({
        docs: notifications.docs,
        totalDocs: notifications.totalDocs,
        totalPages: notifications.totalPages,
        page: notifications.page,
        hasNextPage: notifications.hasNextPage,
        hasPrevPage: notifications.hasPrevPage,
        unreadCount, // ✅ Use this on your frontend bell icon
        readCount,
      });
    } catch (error) {
      console.error("Error fetching user notifications:", error);
      return res.status(500).json({ message: "Something went wrong" });
    }
  },
};
