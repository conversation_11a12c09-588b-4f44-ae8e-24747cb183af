import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";

export const likes: PayloadHandler = async (req: PayloadRequest, res) => {
  const { productId } = req.params;
  const { payload, user } = req;
  const loggedInUserId = user?.id;

  if (!user) {
    return res.status(401).json({ error: "User not authenticated" });
  }

  if (!loggedInUserId || !productId) {
    return res.status(400).json({ error: "Missing user or product ID" });
  }

  try {
    const product = await payload.findByID({
      collection: "products",
      id: productId,
      depth: 2,
    });
    if (!product) {
      return res.status(404).json({ error: "Product not found" });
    }

    // console.log("Product fetched:", product);

    // Normalized likes to an array of user IDs
    const allLikes = product?.likes
    ? (product.likes as Array<{ user?: { id: string }; id: string }>)
        .filter(like => like && (like.user?.id || like.id)) // Filter out null or undefined likes
        .map(like => like.user?.id || like.id) // Extract user.id or fallback to like.id
    : [];
  
  const hasLiked = allLikes.includes(loggedInUserId);
  
  // Create updated like IDs, filtering out the logged-in user if already liked
  const updatedLikeIds = hasLiked
    ? allLikes.filter(id => id !== loggedInUserId) // Exclude the logged-in user if already liked
    : [...allLikes, loggedInUserId]; // Add the logged-in user ID if not already liked
  
  // Map the updated like IDs to the desired format, ensuring no null or undefined IDs
  const updatedLikesArray = updatedLikeIds
    .filter(id => id != null) // Filter out any null or undefined IDs
    .map(id => ({ user: id }));
  

    const updatedProduct = await payload.update({
      collection: "products",
      id: productId,
      data: { likes: updatedLikesArray },
    });

    const likeCount = (updatedProduct?.likes as Array<{ user: { id: string } }>)?.length;
    const isLikeUpdated = !hasLiked;

    return res.status(200).json({ success: true, likeCount, isLikeUpdated });
  } catch (error) {
    console.error("Error updating likes:", error);
    return res.status(500).json({ message: error.message });
  }
};





