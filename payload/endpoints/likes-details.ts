import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";

export const likesDetails: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  try {

    const { userId } = req.params;
    const { payload, user } = req;
    const loggedInUserId = user?.id;

    if (!loggedInUserId) {
      return res
        .status(404)
        .json({ success: false, message: "You have to loggedIn first" });
    }
    
    const userPortfolio: any = await payload.findByID({
      collection: "users",
      id: userId,
      depth: 2,
    });

    const likesList = 
    (userPortfolio?.likes as { user?: { id: string } }[])
      ?.filter(likedUser => likedUser && likedUser.user?.id) // Filter out invalid liked users
      .map(likedUser => likedUser.user.id) ?? []; // Extract user.id or fallback to an empty array
  
    const isLike = likesList.includes(loggedInUserId);
    
    if (isLike) {
      const updatedLikes = likesList.filter((like) => like !== loggedInUserId);
      const updatedLikesArray = updatedLikes.map((id) => {
        return { user: id };
    });

      const updatedUserPortfolio: any = await payload.update({
        collection: "users",
        id: userId,
        data: { likes: updatedLikesArray },
        depth: 2,
      });

      const likes = (updatedUserPortfolio?.likes as string[])?.length;
      const isLikeUpdated = false;

      return res.status(201).json({ success: true, likes, isLikeUpdated });
    } else {
      const updatedLikes = [...likesList, loggedInUserId];
      const updatedLikesArray = updatedLikes?.map((id) => {
        return { user: id };
      });

      const updatedUserPortfolio: any = await payload.update({
        collection: "users",
        id: userId,
        data: { likes: updatedLikesArray },
        depth: 2,
      });

      const likes = (updatedUserPortfolio?.likes as string[])?.length;
      const isLikeUpdated = true;

      return res.status(201).json({ success: true, likes, isLikeUpdated });
    }
  } catch (error) {
    console.error("[INVALID_ERROR]", error);
    return res.status(500).json({
      success: false,
      error: true,
      msg: `Error liking user`,
    });
  }
};
