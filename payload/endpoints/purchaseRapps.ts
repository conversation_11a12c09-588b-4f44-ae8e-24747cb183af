import { PayloadRequest } from "payload/types";
import { User } from "../../server/payload-types";

export const purchaseRapps = async (req: PayloadRequest, res: any) => {
  const { user }: { user: User } = req;
  const userId = user?.id;

  const url = req.headers.referer || "";
  const isDashboard = url.includes("/admin/collections/rapps");

  const { RappsId } = req.body;

  try {
    // Get the user
    const user: any = await req.payload.findByID({
      collection: "users",
      id: userId,
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Get the rapp
    const rapps: any = await req.payload.findByID({
      collection: "rapps",
      id: RappsId,
    }); 

    const purchasesList = 
    (rapps?.purchases as { user?: { id: string } }[])
      ?.filter(purchaseUser => purchaseUser && purchaseUser.user?.id) // Filter out invalid liked users
      .map(purchaseUser => purchaseUser.user.id) ?? [];

    if(userId !== rapps?.creator?.id) {
      const updatedPurchases = [...purchasesList, userId];
      const updatedPurchasesArray = updatedPurchases?.map((id) => {
        return { user: id };
      });

      const updatedRappsPurchases: any = await req.payload.update({
        collection: "rapps",
        id: RappsId,
        data: { purchases: updatedPurchasesArray },
        depth: 2,
      });
    }

    //Get the wallet info
    const walletInfos: any = await req.payload.find({
      collection: "wallet",
      where: {
        commisionFrom: {
          equals: "rapps",
        },
      },
    });

    const walletInfo = walletInfos.docs[0];
    const computeCost = (walletInfo?.totalComputeCostReceived || 0) + (rapps?.model.cost || 0);

    //if rapp commission is undefined / null 
    const commission = (rapps.model.commision != null || rapps.model.commision != undefined )
     ?  rapps.model.commision
     : 0;

    const walletCommision = (walletInfo.totalCommisionReceived || 0) + commission;

    if ( isDashboard || user.id === rapps?.creator?.id ) 
    {
      const totalPrice = (rapps.model.cost || 0) + commission ;

      if (user.coinBalance < totalPrice) {
        return res
          .status(400)
          .json({ message: "Insufficient balance, Please recharge" });
      }

      const cost= user.coinBalance - rapps.totalCost;

      try {
        const updateRappsUser = await req.payload.update({
          collection: "users",
          id: user.id,
          data: {
            coinBalance: cost, 
            tokens: user.tokens + totalPrice,
          },
        });
      } catch (error) {
        console.log("getting error while updating the user/admin account", error);
      }

      // updating wallet
      try {
        const updateAdminWallet = await req.payload.update({
          collection: "wallet",
          id: walletInfo.id,
          data: {
            totalComputeCostReceived: computeCost.toFixed(2),
            totalCommisionReceived: walletCommision.toFixed(2),
          },
        });
        return res.status(200).json({ message: "rapp run successful" });
      } catch (error) {
        console.log("getting error while updating the wallet info");
      }
    }

    else if (user.role === "admin") {
      const totalPrice = (rapps.model.cost || 0) + commission ;

      if (user.coinBalance < totalPrice) {
        return res
          .status(400)
          .json({ message: "Insufficient balance, Please recharge" });
      }

      const cost= user.coinBalance - rapps.totalCost;

      try {
        const updateRappsUser = await req.payload.update({
          collection: "users",
          id: user.id,
          data: {
            coinBalance: cost, 
            tokens: user.tokens + totalPrice,
          },
        });
      } catch (error) {
        console.log("getting error while updating the user/admin account", error);
      }

      // updating rapps owner account
      try {
        const updateRappsUser = await req.payload.update({
          collection: "users",
          id: rapps?.creator?.id,
          data: {
            coinBalance: rapps?.creator?.coinBalance + rapps.price, // add the estimated price of rapps to rapp owner account
          },
        });
      } catch (error) {
        console.log("getting error while updating the rapps owner account");
      }

        // updating wallet
        try {
          const updateAdminWallet = await req.payload.update({
            collection: "wallet",
            id: walletInfo.id,
            data: {
              totalComputeCostReceived: computeCost.toFixed(2),
              totalCommisionReceived: walletCommision.toFixed(2),
            },
          });
          return res.status(200).json({ message: "rapp run successful" });
        } catch (error) {
          console.log("getting error while updating the wallet info");
        }
      
      
    }

     else {
      // Check if user has enough balance
      if (user.coinBalance < rapps.totalCost) {
        return res
          .status(400)
          .json({ message: "Insufficient balance, Please recharge" });
      }

      // Create a purchase record
      const rappsPurchase = await req.payload.create({
        collection: "rappsPurchases",
        data: {
          user: userId,
          rapps: RappsId,
        },
      });

      // Deduct the balance and update the user's purchases
      const rappsPurchaseIds = Array.isArray(user.rappsPurchases)
      ? user.rappsPurchases
          .filter((purchase: any) => purchase && purchase.id) // Filter out invalid purchases
          .map((purchase: any) => purchase.id) // Extract the id
      : [];
      
      const cost= rapps.totalCost + rapps.price;
      // updating buyer account
      try {
        const updatedUser = await req.payload.update({
          collection: "users",
          id: userId,
          data: {
            coinBalance: user.coinBalance - cost , // deduct the tatal price of rapps from buyer account
            tokens: user.tokens + rapps.totalCost,
            rappsPurchases: user.rappsPurchases
              ? [...rappsPurchaseIds, rappsPurchase.id]
              : [rappsPurchase.id],
          },
        });
      } catch (error) {
        console.log("getting  error while updating buyer account");
      }

      // updating rapps owner account
      try {
        const updateRappsUser = await req.payload.update({
          collection: "users",
          id: rapps?.creator?.id,
          data: {
            coinBalance: rapps?.creator?.coinBalance + rapps.price, // add the estimated price of rapps to rapp owner account
          },
        });
      } catch (error) {
        console.log("getting error while updating the rapps owner account");
      }

      //updating wallet
      try {
        const updateAdminWallet = await req.payload.update({
          collection: "wallet",
          id: walletInfo.id,
          data: {
            totalComputeCostReceived: computeCost.toFixed(2),
            totalCommisionReceived: walletCommision.toFixed(2),
          },
        });
      } catch (error) {
        console.log("getting error while updating the wallet info");
      }

      return res.status(200).json({ message: "rapp cycle run successfull" });
    }


  } catch (error) {
    console.error("Purchase error:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};
