import { Payload<PERSON>and<PERSON> } from "payload/config";
import getOutput from "../utilities/runClients";
import { Model } from "@/server/payload-types";
import replaceVariables from "../utilities/replaceVariables";

export const handler: PayloadHandler = async (req, res) => {
  const {
    obj,
    // url,
    promptValues,
    negativeValues,
    systemValues,
    url,
  }: {
    obj: any;
    // url: string | null;
    promptValues: any;
    negativeValues: any;
    systemValues: any;
    url: any;
  } = req.body;
  const payload = req.payload;
  const id = req.params.id;
  const urls = req.headers.referer || "";
  const isDashboard = urls.includes("/livepreview/rapps");
  const isStoreFront = urls.includes("/rent");

  if (!id) {
    return res.status(400).send({ success: false, message: "Missing id" });
  }
  // let image = null;
  // if (url) {
  //   image = url;
  // }

  try {
    const rapp = await payload.findByID({
      collection: "rapps",
      id: id,
    });

    const model = rapp.model as Model;
    const price = model.cost + (model.commision ?? 0) + (rapp.price ?? 0);
    if (isDashboard) {
      if (req.user.coinBalance < price) {
        return res
          .status(400)
          .json({ message: "Insufficient balance, Please recharge" });
      }
    }

    if (isStoreFront) {
      if (price > req.user.coinBalance) {
        return res
          .status(400)
          .json({ message: "Insufficient balance, Please recharge" });
      }
    }

    const prompt = replaceVariables(rapp.prompt ?? "", obj ?? {});
    const negativePrompt = replaceVariables(
      rapp.negativeprompt ?? "",
      negativeValues ?? {}
    );
    const systemPrompt = replaceVariables(
      rapp.systemprompt ?? "",
      systemValues ?? {}
    );

    let apiKey = "";
    let modelName = "";

    if (model.provider === "openai") {
      apiKey =
        (rapp?.key === "prod"
          ? model.prodkeys?.openai?.apikey
          : model.testkeys?.openai?.apikey) ?? "";
      modelName =
        (rapp?.key === "prod"
          ? model.prodkeys?.openai?.modelname
          : model.testkeys?.openai?.modelname) ?? "";
    } else if (model.provider === "groq") {
      apiKey =
        (rapp.key === "prod"
          ? model.prodkeys?.groq?.apikey
          : model.testkeys?.groq?.apikey) ?? "";
      modelName =
        (rapp.key === "prod"
          ? model.prodkeys?.groq?.modelname
          : model.testkeys?.groq?.modelname) ?? "";
    } else if (model.provider === "replicate") {
      apiKey =
        (rapp.key === "prod"
          ? model.prodkeys?.replicate?.apikey
          : model.testkeys?.replicate?.apikey) ?? "";
      modelName =
        (rapp.key === "prod"
          ? model.prodkeys?.replicate?.modelname
          : model.testkeys?.replicate?.modelname) ?? "";
    } else if (model.provider === "falai") {
      apiKey =
        (rapp.key === "prod"
          ? model.prodkeys?.falai?.apikey
          : model.testkeys?.falai?.apikey) ?? "";
      modelName =
        (rapp.key === "prod"
          ? model.prodkeys?.falai?.modelname
          : model.testkeys?.falai?.modelname) ?? "";
    }
    else if (model.provider === "runway") {
      apiKey =
        (rapp.key === "prod"
          ? model.prodkeys?.runway?.apikey
          : model.testkeys?.runway?.apikey) ?? "";
      modelName =
        (rapp.key === "prod"
          ? model.prodkeys?.runway?.modelname
          : model.testkeys?.runway?.modelname) ?? "";
    }

    const output = await getOutput({
      modelName: modelName,
      apiKey: apiKey,
      provider: model.provider,
      prompt: prompt,
      negativePrompt: negativePrompt,
      systemPrompt: systemPrompt,
      promptText: prompt,
      promptImage : url,
      settings: rapp.settings ?? {},
      image: url,
    });
// console.log("output.settings ==>",output.settings)
    res.status(200).send({
      success: true,
      output: output,
      message: "Model run successfully",
    });
  } catch (error) {
    console.log("Error in runModel", error);
    res.status(500).send({ success: false, message: error.message });
  }
};
