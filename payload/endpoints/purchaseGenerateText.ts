
import { PayloadRequest } from "payload/types";
import { User } from "../../server/payload-types";

const purchaseGenerateText = async (req: PayloadRequest, res: any) => {
    const { user }: { user: User } = req;
    const { modelId } = req.body;

    try{
        if(!user){
            return res.status(404).json({status: "false", message: "User Not Found"});
        }

        if(!modelId){
            return res.status(404).json({status: "false", message: "ModelId Not Found"});
        }

        const modelDetail:any = await req.payload.findByID({
            collection: "models",
            id: modelId
        })

        if(!modelDetail){
            return res.status(404).json({status: "false", message: "Model Detail Not Found"});
        }

         //Get the wallet info
        const walletInfos: any = await req.payload.find({
            collection: "wallet",
            where: {
            commisionFrom: {
                equals: "generate",
            },
            },
        });
  
        const walletInfo = walletInfos.docs[0];
        const computeCost = walletInfo.totalComputeCostReceived + modelDetail.cost;
        const commision = walletInfo.totalCommisionReceived + modelDetail.commision;

        const cost = modelDetail.cost + modelDetail.commision;

        if(user.coinBalance < cost){
            return res.status(400).json({status: "false", message: "Insufficient Balance, Please Recharge"});
        }

        try{
            const creditDeduction = await req.payload.update({
                collection: "users",
                id: user.id,
                data: {
                    coinBalance: user.coinBalance - cost
                }
            })
        }catch(error){
            return res.status(500).json({status: "false", message: "Error while deducting credit from user account"});
        }

        // updating wallet
        try {
            const updateAdminWallet = await req.payload.update({
            collection: "wallet",
            id: walletInfo.id,
            data: {
                totalComputeCostReceived: computeCost.toFixed(2),
                totalCommisionReceived: commision.toFixed(2),
            },
            });
        } catch (error) {
            console.log("getting error while updating the wallet info");
        }

        return res.status(200).json({status: "success", message: "Credits purchase successful"})
        
    }
    catch(error){
        return res.status(400).json({status: "false", message: "getting error in credits purchase"})
    }
}


export default purchaseGenerateText;