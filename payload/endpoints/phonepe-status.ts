


import { Payload<PERSON><PERSON><PERSON> } from "payload/config";
import { PayloadRequest } from "payload/types";
import sha256 from "sha256";
import axios from "axios";
import payload from "payload";
import { cookies } from "next/headers"
import { toast } from "sonner";

export const phonepeStatus: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  // const { user } = req;
  try {

    const transactionId = req.params.txnId;
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        error: true,
        message: "Order ID is missing.",
      });
    }

    const {docs:orders}:any = await payload.find({
      collection: "orders",
      where:{
        id:{
          equals: transactionId,
        }
      }
    })

    const order = orders[0];

    if(order._isPaid && order.status === "completed"){
      return res.status(201).json({ success: true, status: "completed", data: {OrderId: order.id, status: order.status, isPaid: order._isPaid, paymentMethod: order.paymentMethod, PurchasedCoins: order.totalCoins} });
    }

    // payload.logger.info(`Transaction ID: ${transactionId}`);
    const merchantId = process.env.PHONEPE_MERCHANT_ID;
    const phonepeHost = process.env.PHONEPE_HOST;
    const salt = process.env.SALT;


    // Verify environment variables
    if (!merchantId || !phonepeHost || !salt) {
      payload.logger.error("Missing required environment variables.");
      return res.status(500).json({
        success: false,
        error: true,
        message: "Server configuration error.",
      });
    }

    const keyIndex = 1;
    const string =`/pg/v1/status/${merchantId}/${transactionId}` + salt;
    const _sha256 = sha256(string);
    const checksum = _sha256 + "###" + keyIndex;

    const options = {
      method: "GET",
      url: `${phonepeHost}/pg/v1/status/${merchantId}/${transactionId}`,
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        "X-VERIFY": checksum,
        "X-MERCHANT-ID": merchantId,
      },
    };

    const response = await axios.request(options);

    const orderAmount = Math.round(order.total*100)/100;

    if (response.data.success && response.data.data.merchantId === merchantId && response.data.data.merchantTransactionId === transactionId && response.data.data.state === 'COMPLETED' && orderAmount === (response.data.data.amount)/100) {
      toast.loading("Verifying Your Payment, Please wait.")
      const order:any = await payload.update({
        collection: "orders",
        id: transactionId,
        data: {
          _isPaid: true,
          status: "completed",
        },
      });
      // payload.logger.info(`Order updated: ${JSON.stringify(order)}`);

      await payload.update({
        collection: "paymentdetails",
        where: {
          providerTxId: {
            equals: transactionId,
          },
        },
          data: {
            status: "paid",
            payload: response.data,
            providerTxId: response.data.data.transactionId,
          }
      })

      const updatedCoinBalance = await payload.update({
        collection: "users",
        id: order.user.id,
        data: {
          coinBalance: order.user.coinBalance ? order.user.coinBalance + order.totalCoins : order.totalCoins,
        },
      });
      toast.dismiss();

      return;
      // return res.redirect(`${process.env.NEXT_PUBLIC_SERVER_URL}/payment-success?orderId=${order.id}`);
    } else {
      payload.logger.info("Payment not successful");
      const order = await payload.update({
        collection: "orders",
        id: transactionId,
        data: {
            status: "pending",
        }
    })
    await payload.update({
        collection: "paymentdetails",
        where: {
          providerTxId: {
            equals: transactionId,
          },
        },
        data: {
            status: "pending",
            payload: response.data
        }
    })
    res.status(201).json({ success: true, status: "pending", data: {OrderId: order.id, status: order.status, isPaid: order._isPaid, paymentMethod: order.paymentMethod, PurchasedCoins: order.totalCoins} });
      return res.status(204).json({ message: "Payment is in pending" });
    }
  } catch (error:any) {
    console.error("[PAYMENT_ERROR]", error);
    return res.status(500).json({ error: `Error using phonepe API: ${error.message}` });
  }
};
