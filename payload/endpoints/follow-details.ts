import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";
import { User } from "../../server/payload-types";

export const followDetails: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  try {
    const { userId } = req.params;
    const { payload, user } = req;
    const loggedInUserId = user?.id;

    if (!loggedInUserId) {
      return res
        .status(404)
        .json({ success: false, message: "You have to loggedIn first" });
    }

    const loggedInUser = await payload.findByID({
      collection: "users",
      id: loggedInUserId,
      depth: 2,
    });

    const followingList = 
      (loggedInUser?.following as { user?: { id: string } }[])
      ?.filter(followedUser => followedUser && followedUser.user?.id) // Filter out invalid liked users
      ?.map(followedUser => followedUser?.user?.id) ?? [];

    const isFollowing = followingList?.includes(userId);

    if (isFollowing) {
      const updatedFollowing = followingList?.filter(
        (follow: any) => follow !== userId
      );
      const updatedFollowingArray = updatedFollowing?.map((id) => {
        return { user: id };
      });

      const updatedFollowingUser = await payload.update({
        collection: "users",
        id: loggedInUserId,
        data: { following: updatedFollowingArray },
        depth: 2,
      });

      const userPortfolio = await payload.findByID({
        collection: "users",
        id: userId,
        depth: 2,
      });

      const followerList =
        (userPortfolio?.followers as { user?: { id: string } }[])
        ?.filter(followedUser => followedUser && followedUser.user?.id) // Filter out invalid liked users
        ?.map(followedUser => followedUser?.user?.id) ?? [];

      const updatedFollower = followerList?.filter(
        (follow: any) => follow !== loggedInUserId
      );
      const updatedFollowerArray = updatedFollower?.map((id) => {
        return { user: id };
      });

      const updatedFollowerUser = await payload.update({
        collection: "users",
        id: userId,
        data: { followers: updatedFollowerArray },
        depth: 2,
      });

      const Followers = updatedFollowerUser?.followers?.length;
      const isFollowingUpdated = false;

      return res
        .status(201)
        .json({ success: true, Followers, isFollowingUpdated });
    } else {
      const updatedFollowing = [...followingList, userId];
      const updatedFollowingArray = updatedFollowing?.map((id) => {
        return { user: id };
      });

      const updatedFollowingUser = await payload.update({
        collection: "users",
        id: loggedInUserId,
        data: { following: updatedFollowingArray },
        depth: 2,
      });

      const userPortfolio = await payload.findByID({
        collection: "users",
        id: userId,
        depth: 2,
      });

      const followerList =
        (userPortfolio?.followers as { user?: { id: string } }[])
        ?.filter(followedUser => followedUser && followedUser.user?.id) // Filter out invalid liked users
        ?.map(followedUser => followedUser?.user?.id) ?? [];
        
      const updatedFollower = [...followerList, loggedInUserId];
      const updatedFollowerArray = updatedFollower?.map((id) => {
        return { user: id };
      });

      const updatedFollowerUser = await payload.update({
        collection: "users",
        id: userId,
        data: { followers: updatedFollowerArray },
        depth: 2,
      });

      const Followers = updatedFollowerUser?.followers?.length;
      const isFollowingUpdated = true;

      return res
        .status(201)
        .json({ success: true, Followers, isFollowingUpdated });
    }
  } catch (error) {
    console.error("[InvalidAction]", error);
    return res.status(500).json({
      success: false,
      error: true,
      msg: `Error following user`,
    });
  }
};
