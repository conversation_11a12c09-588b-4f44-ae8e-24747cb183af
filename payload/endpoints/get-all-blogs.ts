import { PayloadRequest } from "payload/types";
import { cacheQuery } from "../../lib/cache-manager";

export const getAllBlogs = async (req: PayloadRequest, res: any) => {
  const { page, limit }: any = req.query;
  const pageNumber = parseInt(page as string, 10) || 1;
  const pageSize = parseInt(limit as string, 10) || 10;

  try {
    // Use centralized cache
    const result: any = await cacheQuery("blogs", {
      where: { status: { equals: "approved" } },
      page: pageNumber,
      limit: pageSize,
      sort: "-updatedAt",
      depth: 1,
    });

    return res.status(200).json({
      success: true,
      data: result.docs,
      pagination: {
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        totalDocs: result.totalDocs,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    });
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return res.status(500).json({ error: "Error fetching blogs" });
  }
};