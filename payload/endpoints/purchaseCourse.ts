import { PayloadRequest } from 'payload/types';
import { getPayloadClient } from '../../server/get-payload';
import { Purchase, User} from '../../server/payload-types';

export const purchaseCourse = async (req: PayloadRequest, res: any) => {

  const {  user}: {user: User} = req;
    const userId = user?.id;


  if (req.method === 'POST') {
    const { courseId } = req.body;

    try {
      const payload = await getPayloadClient();

      // Get the user
      const user:any = await payload.findByID({
        collection: 'users',
        id: userId,
      });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get the course
      const course:any = await payload.findByID({
        collection: 'courses',
        id: courseId,
      });

      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }

      // Check if user has enough balance
      if ( user.coinBalance < course.cost ) {
        return res.status(400).json({ message: 'Insufficient balance, Please recharge' });
      }

      // Create a purchase record
      const purchase = await payload.create({
        collection: 'purchases',
        data: {
          user: userId,
          course: courseId,
          purchaseType: 'course',
        },
      });

      // Deduct the balance and update the user's purchases
      const purchaseIds = user.purchases
      ? user.purchases
          .filter((purchase: any) => purchase && purchase.id) // Filter out null or invalid purchases
          .map((purchase: any) => purchase.id)
      : [];

      // if owner itself will purchase
      if(user.id === course?.user?.id){
        try{
          const updatedUser = await payload.update({
            collection: 'users',
            id: userId,
            data: {
              purchases: user.purchases ? [...purchaseIds, purchase.id] : [purchase.id]
            },
          });
          
        }catch(error){
          console.log("getting  error while updating owner account")
        }
        return res.status(200).json({ message: 'Purchase successful' });
      }

      try{
        const updatedUser = await payload.update({
          collection: 'users',
          id: userId,
          data: {
            coinBalance: user.coinBalance -  course.cost, 
            purchases: user.purchases ? [...purchaseIds, purchase.id] : [purchase.id]
          },
        });
      }catch(error){
        console.log("getting  error while updating buyer account")
      }

      try{
        const updateCourseUser = await payload.update({
          collection: 'users',
          id: course?.user?.id,
          data: {
            coinBalance: course?.user?.coinBalance +  course.cost, 
          },
        });
      }catch(error){
        console.log("getting error while updating the course creator account")
      }
        
      return res.status(200).json({ message: 'Purchase successful' });
    } catch (error) {
      console.error('Purchase error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};






