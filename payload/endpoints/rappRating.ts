import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";


export const rappRating: PayloadHandler = async (
    req: PayloadRequest,
    res
) => {
        const { rate } = req.body;
        const { rappId } = req.params;
        const { payload, user } = req;

        if (typeof rate !== "number" || rate < 1 || rate > 5) {
          return res
            .status(400)
            .json({ error: "Rating must be a number between 1 and 5." });
        }

        try {
          const rapp: any = await payload.findByID({
            collection: "rapps",
            id: rappId,
          });

          if (!rapp) {
            return res.status(404).json({ error: "Rapp not found." });
          }

          const userIds =
          (
            rapp?.userRatings as { user: { id: string }; rating: number }[]
          )?.map((ratingId: any) => ({
            user: ratingId?.user?.id,
            rating: ratingId?.rating,
          })).filter((rating) => rating.user) ?? [];

          const isRated = userIds.some((userId) => userId.user === user.id);

          const updatedRatings = isRated
            ? userIds.map((ratedUser) =>
                ratedUser.user === user?.id
                  ? { ...ratedUser, rating: rate }
                  : ratedUser
              )
            : user?.id
            ? [...userIds, { user: user.id, rating: rate }]
            : userIds;

          const totalRating =
            updatedRatings.length > 0
              ? updatedRatings.reduce((sum, userRating) => sum + userRating.rating, 0) /
                updatedRatings.length
              : 0;

          if (user) {
            const updateRatingArray = await payload.update({
              collection: "rapps",
              id: rappId,
              data: { userRatings: updatedRatings, rating: totalRating },
              depth: 2,
            });
          }


          return res.status(201).json({ success: true });
        } catch (error) {
          console.error("Error updating rating:", error);
          return res
            .status(500)
            .json({ error: error.message || "Failed to update rating." });
        }
    }



    export const getRappRating: PayloadHandler = async (
        req: PayloadRequest,
        res
      ) => {
            const { rappId } = req.params;
            const { user } = req;
    
            try {
              const rapp: any = await req.payload.findByID({
                collection: "rapps",
                id: rappId,
              });
    
              if (!rapp) {
                return res.status(404).json({ error: "Rapp not found." });
              }
              const userRating = rapp.userRatings?.find(
                (rating) => rating.user.id === user.id
              );
    
              res.json({
                rating: userRating?.rating || 0,
              });
            } catch (error) {
              console.error("Error fetching user rating:", error);
              return res
                .status(500)
                .json({ error: "Failed to fetch user rating." });
            }
    }
