

import { PayloadRequest } from 'payload/types';
import { getPayloadClient } from '../../server/get-payload';
import { Purchase, User} from '../../server/payload-types';

export const purchaseProduct = async (req: PayloadRequest, res: any) => {

  const {  user}: {user: User} = req;
    const userId = user?.id;

  if (req.method === 'POST') {
    const { productId } = req.body;

    try {
      const payload = await getPayloadClient();

      const user:any = await payload.findByID({
        collection: 'users',
        id: userId,
      });

      const product:any = await payload.findByID({
        collection: 'products',
        id: productId,
      });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if user has enough balance
      if ( user.coinBalance < product.price ) {
        return res.status(400).json({ message: 'Insufficient balance, Please recharge' });
      }

      // Create a purchase record
      const purchase = await payload.create({
        collection: 'purchases',
        data: {
          user: userId,
          product: productId,
          purchaseType: 'assets',
        },
      });

      // Deduct the balance and update the user's purchases
      const purchaseIds = user.purchases
      ? user.purchases
          .filter((purchase: any) => purchase && purchase.id) // Filter out null or invalid purchases
          .map((purchase: any) => purchase.id)
      : [];

      // if owner itself will purchase
      if(user.id === product.user.id){
        try{
          const updatedUser = await payload.update({
            collection: 'users',
            id: userId,
            data: {
              purchases: user.purchases ? [...purchaseIds, purchase.id] : [purchase.id]
            },
          });
          
        }catch(error){
          console.log("getting  error while updating owner account")
        }
        return res.status(200).json({ message: 'Purchase successful', purchase });
      }


      try{
        const updatedUser = await payload.update({
          collection: 'users',
          id: userId,
          data: {
            coinBalance: user.coinBalance -  product.price,  // Ensure coinBalance is not null or undefined
            purchases: user.purchases ? [...purchaseIds, purchase.id] : [purchase.id]
          },
        });
      }catch(error){
        console.log("getting  error while updating buyer account")
      }

      try{
        const updateProductUser = await payload.update({
          collection: 'users',
          id: product?.user?.id,
          data: {
            coinBalance: product?.user?.coinBalance +  product.price, 
          },
        });
      }catch(error){
        console.log("getting error while updating the product owner account")
      }
        
      

      return res.status(200).json({ message: 'Purchase successful', purchase });
    } catch (error) {
      console.error('Purchase error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};






