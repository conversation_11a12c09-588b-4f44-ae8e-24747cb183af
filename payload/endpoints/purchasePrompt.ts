import { PayloadRequest } from 'payload/types';
import { getPayloadClient } from '../../server/get-payload';
import { Purchase, User} from '../../server/payload-types';

export const purchasePrompt = async (req: PayloadRequest, res: any) => {

  const {  user}: {user: User} = req;
  const userId = user?.id;

  if (req.method === 'POST') {
    const { rappId } = req.body;

    try {
      const payload = await getPayloadClient();

      // Get the user
      const user:any = await payload.findByID({
        collection: 'users',
        id: userId,
      });

      // Get the rapp
      const rapp:any = await payload.findByID({
        collection: 'rapps',
        id: rappId,
      });

      const promptpurchaseIds = rapp?.promptpurchase
      ? (rapp.promptpurchase as Array<{ user?: { id: string }; id: string }>)
          .filter(promptpurchase => promptpurchase && (promptpurchase.user?.id || promptpurchase.id)) // Filter out null or undefined likes
          .map(promptpurchase => promptpurchase.user?.id || promptpurchase.id) // Extract user.id or fallback to like.id
      : [];

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if user has enough balance
      if ( user.coinBalance < rapp.promptcost ) {
        return res.status(400).json({ message: 'Insufficient balance, Please recharge' });
      }
      try{
        const updatedUser = await payload.update({
          collection: 'users',
          id: userId,
          data: {
            coinBalance: user.coinBalance -  rapp.promptcost,  // Ensure coinBalance is not null or undefined
          },
        });
      }catch(error){
        console.log("getting  error while updating buyer account")
      }

      const haspromptpurchase =  promptpurchaseIds .includes(userId);
      // Create updated  IDs, filtering out the logged-in user if already purchased prompt
      const updatedpromptpurchaseIds = haspromptpurchase
        ?  promptpurchaseIds .filter(id => id !== userId) // Exclude the logged-in user if already purchased
        : [... promptpurchaseIds , userId]; // Add the  user ID if not already 
      
      // Map the updated IDs to the desired format, ensuring no null or undefined IDs
      const updatedpromptpurchaseArray = updatedpromptpurchaseIds
        .filter(id => id != null) // Filter out any null or undefined IDs
        .map(id => ({ user: id }));
    
        const updatedRapp = await payload.update({
          collection: "rapps",
          id: rappId,
          data: { promptpurchase : updatedpromptpurchaseArray },
        });

      try{
        const updatePromptUser = await payload.update({
          collection: 'users',
          id: rapp?.creator?.id,
          data: {
            coinBalance: rapp?.creator?.coinBalance + rapp.promptcost, 
          },
        });
      }catch(error){
        console.log("getting error while updating the product owner account")
      }
      return res.status(200).json({ message: 'Prompt purchase successful' });
    } catch (error) {
      console.error('Purchase error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};






