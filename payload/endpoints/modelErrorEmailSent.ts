import { PayloadRequest } from "payload/types";
import { Payload<PERSON>and<PERSON> } from "payload/config";
import nodemailer from "nodemailer";

export const modelErrorEmailSent: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {

  const { errorMessage } = req.body;
  let modelName = "Unknown Model";
  const match = errorMessage.match(/model [`'"]?([\w\-]+)[`'"]?/i);
  if (match && match[1]) {
    modelName = match[1];
  }
  try{
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.warn("EMAIL_USER or EMAIL_PASS missing.");
      return res.status(500).json({ message: "Email credentials missing" });
    }

    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: "<EMAIL>",
      subject: "❌ Model Execution Error",
      text: `An error occurred while running a ${modelName} Model:\n\n${errorMessage}`,
    };

    // Send mail but don't block on it
    await transporter.sendMail(mailOptions);

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error("Email send failed:", error);
    return res.status(500).json({ message: "Failed to send error email." });
  }
}