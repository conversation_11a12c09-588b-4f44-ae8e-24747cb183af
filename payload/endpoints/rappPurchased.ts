import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";

export const rappPurchased: PayloadHandler = async (req: PayloadRequest, res) => {
  const { rappId } = req.params;
  const { payload, user } = req;
  const loggedInUserId = user?.id;

  if (!user) {
    return res.status(401).json({ error: "User not authenticated" });
  }

  try {
    const rapp = await payload.findByID({
      collection: "rapps",
      id: rappId,
      depth: 2, // Ensure this is necessary
    });

    if (!rapp) {
      return res.status(404).json({ error: "Rent product not found" });
    }

    const purchasesList = 
    (rapp?.purchases as { user?: { id: string } }[])
      ?.filter(purchaseUser => purchaseUser && purchaseUser.user?.id)
      .map(purchaseUser => purchaseUser.user.id) ?? [];
      
    const isPurchased = purchasesList.includes(loggedInUserId);
    
    return res.status(201).json({ success: true, isPurchased });
  } catch (error) {
    console.error("Error in rappPurchased handler:", error);
    return res
      .status(500)
      .json({ message: "An error occurred while fetching purchase." });
  }
};
