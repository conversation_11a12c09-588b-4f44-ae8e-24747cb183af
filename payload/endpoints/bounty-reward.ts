import { PayloadRequest } from "payload/types";
import { PayloadHandler } from "payload/config";

export const bountyReward: PayloadHandler = async (
  req: PayloadRequest,
  res
) => {
  try {
    const { bountyId } = req.params;
    const { payload, user } = req;
    const loginUserId = user?.id;
    const { winners } = req.body;
    
    if (!loginUserId) {
      return res
        .status(404)
        .json({ success: false, message: "You have to loggedIn first" });
    }

    if (!Array.isArray(winners) || winners.length === 0) {
        return res
          .status(400)
          .json({ success: false, message: "No winners provided." });
      }
  
      for (const { id, amount } of winners) {
        const existingUser = await payload.findByID({
          collection: "users",
          id,
        });
  
        if (!existingUser) {
          console.warn(`User with ID ${id} not found.`);
          continue;
        }
  
        const currentBalance = existingUser.coinBalance || 0;
        const currentBountyHuntBalance = existingUser.bountyHuntBalance || 0;
  
        const updateduser = await payload.update({
          collection: "users",
          id,
          data: {
            coinBalance: currentBalance + amount,
            bountyHuntBalance: currentBountyHuntBalance + amount,
          },
        });
      }

      const winnerMap = new Map(winners.map(({ id, amount }) => [id, amount]));

      const bounty = await req.payload.findByID({
        collection: "bounties",
        id: bountyId,
      });
      
      // Mark winners in applicants array
      const updatedApplicants = bounty.applicants.map((applicant) => {
        const isWinner = winnerMap.has(applicant.userId);
        const rewardAmount = winnerMap.get(applicant.userId) || applicant.rewardJoules;

        return {
          userId: applicant.userId,
          userName: applicant.userName,
          linkedin: applicant.linkedin,
          phone: applicant.phone,
          approach: applicant.approach,
          canReapply: applicant.canReapply,
          applicantStatus: applicant.applicantStatus,
          rejectionReason: applicant.rejectionReason,
          winner: isWinner,
          rewardJoules: isWinner ? rewardAmount : applicant.rewardJoules,
          message: applicant.message,
          relatedFile: (applicant.relatedFile || []).map(file =>
            typeof file === "string" ? file : file.id
          ),
          finalSubmissions: (applicant.finalSubmissions || []).map((submission) => ({
            file:
              typeof submission.file === "string"
                ? submission.file
                : submission.file?.id,
            submittedAt: submission.submittedAt,
            notes: submission.notes,
            status: submission.status,
          })),
        };
      });
      
      // Update bounty
      const updatedWinner = await req.payload.update({
        collection: "bounties",
        id: bountyId,
        data: {
          rewardReleased: true,
          applicants: updatedApplicants,
          status: 'completed',
        },
        req,
      });

      return res.status(200).json({
        success: true,
        message: "Bounty rewards distributed successfully.",
      });

  } catch (error) {
    console.error("[InvalidAction]", error);
    return res.status(500).json({
      success: false,
      error: true,
      msg: `Error rewarding bounty winners.`,
    });
  }
};
