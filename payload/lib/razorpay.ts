const Razorpay = require('razorpay');

//console.log(process.env.RAZORPAY_KEY)

export const createRazorPayIntent = async (total:any, currency="INR", orderId:any): Promise<any> => {
    // console.log(" key env :: ", process.env.RAZORPAY_KEY)
    // console.log(" sec env :: ", process.env.RAZORPAY_SECRET)
    const razorpay = new Razorpay({ key_id: process.env.RAZORPAY_KEY, key_secret: process.env.RAZORPAY_SECRET })
    const options = {
        amount: (Math.round(total*100)),
        currency: currency,
        receipt: orderId,
    };
    const response = await razorpay.orders.create(options)
    // console.log(" response :: ", response)
    return response
}

export const checkStatusRazorPayIntent = async (paymentId:any): Promise<any> => {
    const razorpay = new Razorpay({ key_id: process.env.RAZORPAY_KEY, key_secret: process.env.RAZORPAY_SECRET })
    const response = await razorpay.payments.fetch(paymentId)
    return response
}
