
import axios from "axios";
// import CryptoJS from "crypto-js"
import { generateChecksum } from "../utilities/generateChecksum";

export const createPhonePePayIntent = async ({total, orderId, user}:any): Promise<any> => {
  // console.log("I'm inside the createPhonePayIntent")
  // console.log("M ID env :: ", process.env.PHONEPE_MERCHANT_ID)
  // console.log(" MUID env :: ", process.env.MUID)
  const data = {
    merchantId: process.env.PHONEPE_MERCHANT_ID,
    merchantTransactionId: orderId,
    merchantUserId: process.env.MUID,
    amount: Math.round(total * 100),
    redirectUrl: `${process.env.NEXT_PUBLIC_SERVER_URL}/payment-success?orderId=${orderId}`,
    callbackUrl: `${process.env.NEXT_PUBLIC_SERVER_URL}/api/phonepe-status/${orderId}`,
    redirectMode: "REDIRECT",
    paymentInstrument: {
      type: "PAY_PAGE",
    },
  };

  const { checksum, payloadMain } = generateChecksum(data);
  

  // console.log(`checksum, payload main :: ${checksum}, ${payloadMain}`);

  const URL = `${process.env.PHONEPE_HOST}/pg/v1/pay`;
  // console.log(`url :: ${URL}`);
  const options = {
    method: "POST",
    url: URL,
    headers: {
      accept: "application/json",
      "Content-Type": "application/json",
      // 'Access-Control-Allow-Origin': '*',
      // 'Access-Control-Allow-Methods': 'GET,PUT,POST,DELETE,PATCH,OPTIONS',
      "X-VERIFY": checksum,
    },
    data: {
      request: payloadMain,
    },
  };

  const response = await axios.request(options);
  // console.log("==============================================================")
  // console.log('final resp ::', response)
  // console.log("================================================================")
  return response.data;
};
