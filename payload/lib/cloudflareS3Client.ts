import dotenv from "dotenv";
const { S3Client } = require("@aws-sdk/client-s3");

export const getS3Client = () => {
    // console.log("Testing env vars ::", dotenv.config())
    // console.log("bucket access key :: ", process.env.PAYLOAD_PUBLIC_CLOUDFLARE_ACCESS_KEY)
    const s3Client = new S3Client({
        region: "auto",
        endpoint: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_ENDPOINT, // Replace with your Cloudflare R2 endpoint
        credentials: {
          accessKeyId: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_ACCESS_KEY,
          secretAccessKey: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_SECRET_KEY,
        },
    });

    return s3Client;
}
