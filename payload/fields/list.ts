import { Field, FieldHook } from "payload/types";
import slugify from "slugify";
import totalCost from "../collections/Rapps/component/totalcost";
//import totalCost from "../collections/Rapps/components/totalCost";

const addTotalCost: FieldHook = async ({ data }) => {
  data.totalCost = data.price;
};

const autoGenerateSlug: FieldHook = ({ data, value }) => {
  if (data?.name && !value) {
    return slugify(data.name, { lower: true, strict: true });
  }
  return value; // Return the current value if no name or value already exists
};

const generateUniqueSlug: FieldHook = async ({
  data,
  value,
  req,
  originalDoc,
}) => {
  let slug = value;

  if (slug) {
    slug = slugify(slug, { lower: true, strict: true });

    // Check if slug hasn't changed in case of updates
    if (originalDoc?.slug === slug) {
      return slug; // No change needed, return the current slug
    }

    // Check if a document with the same slug already exists
    const existingDoc = await req.payload.find({
      collection: "rapps", // or your specific collection
      where: { slug: { equals: slug } },
    });

    if (existingDoc.totalDocs > 0) {
      // Add a timestamp or unique identifier to ensure the slug is unique
      slug = `${slug}-${Date.now()}`;
    }
  }

  return slug; // Return the unique slug
};

export const listing: Field[] = [
  {
    type: "text",
    name: "name",
    label: "Ai Apps Name",
    admin: {
      description: "Title of your Rent application",
      placeholder: "ex: Funky Sticker Generator",
      position: "sidebar",
    },
    required: true,
    index: true,
  },
  {
    name: "slug",
    label: "Slug",
    type: "text",
    required: false,
    unique: true,
    hooks: {
      beforeValidate: [autoGenerateSlug], // Use the field-level hook here for real-time slug generation
      beforeChange: [generateUniqueSlug],
    },
    admin: {
      hidden: true,
      readOnly: true,
      position: "sidebar", // Make it read-only so users cannot change it manually
    },
  },
  {
    type: "textarea",
    name: "description",
    label: "Ai Apps Description",
    admin: {
      description: `Describe what your application does to a potential buyer.
      A more detailed description will increase your sales, also can use markup for increase readability.`,
      placeholder: "Generates amazing high quality stickers",
      position: "sidebar",
    },
    required: true,
    index: true,
  },
  // applicable or not (you want to sell this ?)
  {
    type: "checkbox",
    name: "priceapplicable",
    label: "You want to sell this ?",
    required: true,
    defaultValue: false,
  },
  // estimatedPrice
  {
    label: "Estimated Price",
    name: "price",
    type: "number",
    defaultValue: 0,
    admin: {
      // condition: (data) => data.priceapplicable,
      description:
        "What do you think the price of this application should as per Cycle",
      placeholder: "Mention estimated price in coins",
      position: "sidebar",
    },
    // required: true,
  },
  // totalCost (cost + commission) and estimated if applicable
  {
    type: "number",
    name: "totalCost",
    required: true,
    admin: {
      position: "sidebar",
      components: {
        Field: totalCost,
      },
    },
  },
  // {
  //   name: "private",
  //   defaultValue: false,
  //   required: true,
  //   label: "Private",
  //   type: "checkbox",
  //   admin: {
  //     description: "Make this Rapp private",
  //     position: "sidebar",
  //   },
  // },
  //get prompt checkbox for user
  {
    name: "getprompt",
    defaultValue: false,
    required: true,
    label: "Sell Prompt",
    type: "checkbox",
    admin: {
      description: "Do you want to sell this prompt. ",
      position: "sidebar",
    },
  },
  //field for revealing prompt cost
  {
    name: "promptcost",
    label: "Cost",
    type: "number",
    admin: {
      condition: (data) => data.getprompt === true, // Field only visible when "Sell Prompt" is checked
      position: "sidebar",
      description: "Mention the cost required to buy this prompt. ",
    },
    required: true, // Make it required when visible
    validate: (value, { data }) => {
      if (data.getprompt && !value) {
        return "Cost is required when selling this prompt.";
      }
      return true;
    },
  },
  //prompt purchases field
  {
    name: "promptpurchase",
    type: "array",
    admin: {
      hidden: true,
    },
    fields: [
      {
        name: "user",
        type: "relationship",
        relationTo: "users",
        required: true,
      },
    ],
  },

  {
    name: "images",
    type: "array",
    minRows: 1,
    maxRows: 4,
    labels: {
      singular: "Image",
      plural: "Images",
    },
    admin: {
      description: "Upload 3-4 examples generated by this application",
      position: "sidebar",
    },
    fields: [
      {
        name: "image",
        type: "upload",
        relationTo: "media",
        required: true,
        label: "Example/Sample Image",
      },
    ],
  },
  {
    name: "creator",
    label: "Created By",
    type: "relationship",
    relationTo: "users",
    required: true,
    defaultValue: ({ user }) => {
      if (user) return user.id;
      else return "";
    },
    hasMany: false,
    admin: {
      allowCreate: false,
      readOnly: true,
      position: "sidebar",
    },
  },
  // systemVariables
  {
    type: "array",
    name: "systemVariables",
    label: "Variables",
    // required: true,
    // defaultValue: [],
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
      },
      {
        name: "identifier",
        type: "text",
        required: true,
        label: "Identifier",
      },
      {
        name: "displayName",
        type: "text",
        required: true,
        label: "Display Name",
      },
      {
        name: "description",
        type: "text",
        // required: true,
        label: "Description",
      },
      {
        name: "placeholder",
        type: "text",
        label: "Placeholder",
      },
      {
        name: "type",
        label: "Type",
        type: "select",
        options: [
          {
            label: "String",
            value: "string",
          },
          {
            label: "Number",
            value: "number",
          },
          {
            label: "Boolean",
            value: "boolean",
          },
          {
            label: "Select",
            value: "select",
          },
        ],
        required: true,
      },
      {
        name: "allowMultiple",
        type: "checkbox",
        label: "Allow Multiple",
        required: true,
        defaultValue: false,
      },
      {
        name: "options",
        type: "text",
        hasMany: true,
      },
    ],
  },
  // promptVariables
  {
    type: "array",
    name: "promptVariables",
    label: "Variables",
    // required: true,
    // defaultValue: [],
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
      },
      {
        name: "identifier",
        type: "text",
        required: true,
        label: "Identifier",
      },
      {
        name: "displayName",
        type: "text",
        required: true,
        label: "Display Name",
      },
      {
        name: "description",
        type: "text",
        required: false,
        label: "Description",
      },
      {
        name: "placeholder",
        type: "text",
        label: "Placeholder",
      },
      {
        name: "type",
        label: "Type",
        type: "select",
        options: [
          {
            label: "String",
            value: "string",
          },
          {
            label: "Number",
            value: "number",
          },
          {
            label: "Boolean",
            value: "boolean",
          },
          {
            label: "Select",
            value: "select",
          },
        ],
        required: true,
      },
      {
        name: "allowMultiple",
        type: "checkbox",
        label: "Allow Multiple",
        required: true,
        defaultValue: false,
      },
      {
        name: "options",
        type: "text",
        hasMany: true,
      },
    ],
  },
  // negativeVariables
  {
    type: "array",
    name: "negativeVariables",
    label: "Variables",
    // required: true,
    // defaultValue: [],
    fields: [
      {
        name: "name",
        label: "Name",
        type: "text",
        required: true,
      },
      {
        name: "identifier",
        type: "text",
        required: true,
        label: "Identifier",
      },
      {
        name: "displayName",
        type: "text",
        required: true,
        label: "Display Name",
      },
      {
        name: "description",
        type: "text",
        // required: true,
        label: "Description",
      },
      {
        name: "placeholder",
        type: "text",
        label: "Placeholder",
      },
      {
        name: "type",
        label: "Type",
        type: "select",
        options: [
          {
            label: "String",
            value: "string",
          },
          {
            label: "Number",
            value: "number",
          },
          {
            label: "Boolean",
            value: "boolean",
          },
          {
            label: "Select",
            value: "select",
          },
        ],
        required: true,
      },
      {
        name: "allowMultiple",
        type: "checkbox",
        label: "Allow Multiple",
        required: true,
        defaultValue: false,
      },
      {
        name: "options",
        type: "text",
        hasMany: true,
      },
    ],
  },
  // settings
  {
    name: "settings",
    type: "json",
    label: "Settings",
    jsonSchema: {
      fileMatch: ["a://b/foo.json"],
      schema: {
        type: "object",
        additionalProperties: true,
        properties: {},
      },
      uri: "a://b/foo.json",
    },
  },
];
