import { Field } from "payload/types";

export const modelType: Field = {
  name: "modelType",
  type: "select",
  label: "Rent Application type",
  admin: {
    description: "Select the type of application you want to rent",
  },
  defaultValue: "text",
  options: [
    {
      label: "Text Generation Application",
      value: "text",
    },
    {
      label: "Image Generation Application",
      value: "image",
    },
    {
      label: "Audio Generation Application",
      value: "audio",
    },
    //video model 
    {
      label: "Video Generation Application",
      value: "video",
    },
    // {
    //   label: "3D Generation Application",
    //   value: "3d",
    // },
  ],
  required: true,
};
