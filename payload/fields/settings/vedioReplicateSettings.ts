import { Field } from "payload/types";
import { steps } from "./ModelVideoSettings/steps";
import { seed } from "./ModelVideoSettings/seed";
import { cfg } from "./ModelVideoSettings/cfg";
import { target_size } from "./ModelVideoSettings/target_size";
import { aspect_ratio } from "./ModelVideoSettings/aspect_ratio";

export const vedioReplicateSettings: Field = {
  type: "group",
  name: "vedioreplicatesettings",
  label: "Settings",
  fields: [
    seed,
    cfg,
   // length,
    target_size,
    aspect_ratio,
  ]
  ,
  admin: {
    condition: (data) => {
      return data.modelType === "video";
    },
  },
};


