@use 'sass:math';

/////////////////////////////
// BREAKPOINTS
/////////////////////////////

$breakpoint-xs-width: 400px !default;
$breakpoint-s-width: 768px !default;
$breakpoint-m-width: 1024px !default;
$breakpoint-l-width: 1440px !default;

//////////////////////////////
// BASELINE GRID
//////////////////////////////

$baseline-px: 25px !default;
$baseline-body-size: 13px !default;
$baseline: math.div($baseline-px, $baseline-body-size) + rem;

@function base($multiplier) {
  @return (math.div($baseline-px, $baseline-body-size) * $multiplier) + rem;
}

//////////////////////////////
// FONTS (DEPRECATED. DO NOT USE. PREFER CSS VARIABLES)
//////////////////////////////

$font-body: 'Suisse Intl' !default;
$font-mono: monospace !default;

//////////////////////////////
// COLORS (DEPRECATED. DO NOT USE. PREFER CSS VARIABLES)
//////////////////////////////

$color-dark-gray: #333333 !default;
$color-gray: #9a9a9a !default;
$color-light-gray: #dadada !default;
$color-background-gray: #f3f3f3 !default;
$color-red: #ff6f76 !default;
$color-yellow: #fdffa4 !default;
$color-green: #b2ffd6 !default;
$color-purple: #f3ddf3 !default;

//////////////////////////////
// STYLES
//////////////////////////////

$style-radius-s: 3px !default;
$style-radius-m: 4px !default;
$style-radius-l: 9px !default;
$style-stroke-width: 1px !default;

$style-stroke-width-s: 1px !default;
$style-stroke-width-m: 2px !default;

//////////////////////////////
// MISC
//////////////////////////////

$top-header-offset: calc(var(--base) - 1px);
$top-header-offset-m: calc(var(--base) * 3);
$focus-box-shadow: 0 0 0 $style-stroke-width-m var(--theme-success-500);

//////////////////////////////
// SHADOWS
//////////////////////////////

@mixin shadow-sm {
  box-shadow:
    0 2px 3px 0 rgba(0, 2, 4, 0.05),
    0 10px 4px -8px rgba(0, 2, 4, 0.02);
}

@mixin shadow-m {
  box-shadow:
    0 0 30px 0 rgb(0 2 4 / 12%),
    0 30px 25px -8px rgb(0 2 4 / 10%);
}

@mixin shadow-lg {
  box-shadow:
    0 20px 35px -10px rgba(0, 2, 4, 0.2),
    0 6px 4px -4px rgba(0, 2, 4, 0.02);
}

@mixin shadow-lg-top {
  box-shadow:
    0 -20px 35px -10px rgba(0, 2, 4, 0.2),
    0 -6px 4px -4px rgba(0, 2, 4, 0.02);
}

@mixin shadow {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

@mixin inputShadowActive {
  box-shadow:
    0 2px 3px 0 rgba(0, 2, 4, 0.16),
    0 6px 4px -4px rgba(0, 2, 4, 0.13);
}

@mixin inputShadow {
  @include shadow-sm;

  &:not(:disabled) {
    &:hover {
      box-shadow:
        0 2px 3px 0 rgba(0, 2, 4, 0.13),
        0 6px 4px -4px rgba(0, 2, 4, 0.1);
    }

    &:active,
    &:focus-within,
    &:focus {
      @include inputShadowActive;
    }
  }
}

@mixin soft-shadow-bottom {
  box-shadow: 0 7px 14px 0px rgb(0 0 0 / 5%);
}

//////////////////////////////
// STYLE MIXINS
//////////////////////////////

@mixin blur-bg($color: var(--theme-bg), $opacity: 0.75) {
  &:before,
  &:after {
    content: ' ';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  &:before {
    background: $color;
    opacity: $opacity;
  }

  &:after {
    backdrop-filter: blur(8px);
  }
}

@mixin blur-bg-light {
  @include blur-bg(var(--theme-bg), 0.3);
}

@mixin formInput() {
  @include inputShadow;
  font-family: var(--font-body);
  width: 100%;
  border: 1px solid var(--theme-elevation-150);
  background: var(--theme-input-bg);
  color: var(--theme-elevation-800);
  border-radius: 0;
  font-size: 1rem;
  height: base(2);
  line-height: base(1);
  padding: base(0.5) base(0.75);
  -webkit-appearance: none;

  &[data-rtl='true'] {
    direction: rtl;
  }

  &::-webkit-input-placeholder {
    color: var(--theme-elevation-400);
    font-weight: normal;
    font-size: 1rem;
  }

  &::-moz-placeholder {
    color: var(--theme-elevation-400);
    font-weight: normal;
    font-size: 1rem;
  }

  &:hover {
    border-color: var(--theme-elevation-250);
  }

  &:focus,
  &:focus-within,
  &:active {
    border-color: var(--theme-elevation-400);
    outline: 0;
  }

  &:disabled {
    background: var(--theme-elevation-200);
    color: var(--theme-elevation-450);

    &:hover {
      border-color: var(--theme-elevation-150);
    }
  }
}

@mixin lightInputError {
  background-color: var(--theme-error-50);
  border: 1px solid var(--theme-error-500);
}

@mixin darkInputError {
  background-color: var(--theme-error-100);
  border: 1px solid var(--theme-error-400);

  &:hover {
    border-color: var(--theme-error-500);
  }
}
