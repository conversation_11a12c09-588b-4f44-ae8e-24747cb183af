<svg width="112" height="76" viewBox="0 0 112 76" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2539_14244)">
<mask id="mask0_2539_14244" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="112" height="112">
<circle cx="56" cy="56" r="56" fill="url(#paint0_radial_2539_14244)"/>
</mask>
<g mask="url(#mask0_2539_14244)">
<circle cx="56" cy="56" r="56" fill="#FFE24D"/>
</g>
<g filter="url(#filter0_dd_2539_14244)">
<circle cx="56" cy="56" r="43.0625" fill="white"/>
<circle cx="56" cy="56" r="43.0625" stroke="white" stroke-width="12" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<circle opacity="0.2" cx="56" cy="56" r="43.0625" fill="#FFE24D"/>
<g opacity="0.6" filter="url(#filter1_f_2539_14244)">
<circle cx="56" cy="56" r="20" fill="#FFE24D"/>
</g>
<path d="M79.5525 60.9375H32.4475C32.4475 60.9375 34.7524 78.941 56 78.941C77.2477 78.941 79.5525 60.9375 79.5525 60.9375Z" fill="white"/>
<circle cx="56" cy="56" r="43.0625" stroke="#FFE24D" stroke-width="6" stroke-linecap="round"/>
<path d="M79.5525 60.9375H32.4475C32.4475 60.9375 34.7523 78.941 56 78.941C77.2476 78.941 79.5525 60.9375 79.5525 60.9375Z" stroke="#FFE24D" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M42.75 46.75V38.75" stroke="#FFE24D" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M69.25 46.75L66.5202 45.7573C63.9715 44.8305 62.6972 44.3672 62.3366 43.6892C62.0243 43.102 62.0243 42.398 62.3366 41.8108C62.6972 41.1328 63.9715 40.6695 66.5202 39.7427L69.25 38.75" stroke="#FFE24D" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<g style="mix-blend-mode:plus-darker" opacity="0.4">
<circle cx="56" cy="56" r="43.0625" stroke="black" stroke-width="6" stroke-linecap="round"/>
<path d="M79.5525 60.9375H32.4475C32.4475 60.9375 34.7523 78.941 56 78.941C77.2476 78.941 79.5525 60.9375 79.5525 60.9375Z" stroke="black" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M42.75 46.75V38.75" stroke="black" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M69.25 46.75L66.5202 45.7573C63.9715 44.8305 62.6972 44.3672 62.3366 43.6892C62.0243 43.102 62.0243 42.398 62.3366 41.8108C62.6972 41.1328 63.9715 40.6695 66.5202 39.7427L69.25 38.75" stroke="black" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter2_dd_2539_14244)">
<path d="M85.9001 30.4343C83.3 28.926 82 28.1719 82 27.4482C82 26.7246 83.3 25.9705 85.9001 24.4622C87.2668 23.6694 88.2211 22.715 89.0139 21.3484C90.5222 18.7483 91.2764 17.4482 92 17.4482C92.7236 17.4482 93.4778 18.7483 94.9861 21.3484C95.7788 22.715 96.7332 23.6694 98.0998 24.4622C100.7 25.9705 102 26.7246 102 27.4482C102 28.1719 100.7 28.926 98.0998 30.4343C96.7332 31.2271 95.7788 32.1814 94.9861 33.5481C93.4778 36.1482 92.7236 37.4482 92 37.4482C91.2764 37.4482 90.5222 36.1482 89.0139 33.5481C88.2211 32.1815 87.2668 31.2271 85.9001 30.4343Z" fill="#FCAD00"/>
<path d="M80.5 27.4482C80.5 28.107 80.8018 28.6189 81.089 28.9653C81.372 29.3067 81.7358 29.6034 82.0992 29.8648C82.8094 30.3756 83.8361 30.9711 85.0719 31.6879L85.1475 31.7318C86.2882 32.3935 87.0547 33.1601 87.7164 34.3008L87.7603 34.3764C88.4771 35.6122 89.0727 36.6389 89.5835 37.3491C89.8449 37.7125 90.1416 38.0763 90.4829 38.3593C90.8293 38.6464 91.3413 38.9482 92 38.9482C92.6587 38.9482 93.1707 38.6464 93.5171 38.3593C93.8584 38.0763 94.1551 37.7125 94.4165 37.3491C94.9273 36.6388 95.5229 35.6122 96.2397 34.3763L96.2836 34.3008C96.9453 33.1601 97.7118 32.3935 98.8525 31.7318L98.9281 31.6879C100.164 30.9711 101.191 30.3756 101.901 29.8648C102.264 29.6034 102.628 29.3067 102.911 28.9653C103.198 28.6189 103.5 28.107 103.5 27.4482C103.5 26.7895 103.198 26.2776 102.911 25.9312C102.628 25.5898 102.264 25.2931 101.901 25.0317C101.191 24.5209 100.164 23.9254 98.9281 23.2085L98.8525 23.1647C97.7118 22.503 96.9453 21.7364 96.2836 20.5957L96.2397 20.5201C95.5229 19.2843 94.9273 18.2576 94.4165 17.5474C94.1551 17.184 93.8584 16.8202 93.5171 16.5372C93.1707 16.2501 92.6587 15.9482 92 15.9482C91.3413 15.9482 90.8293 16.2501 90.4829 16.5372C90.1416 16.8202 89.8449 17.184 89.5835 17.5474C89.0727 18.2576 88.4771 19.2843 87.7603 20.5201L87.7164 20.5957C87.0547 21.7364 86.2882 22.503 85.1475 23.1647L85.0719 23.2085C83.8361 23.9254 82.8094 24.5209 82.0992 25.0317C81.7358 25.2931 81.372 25.5898 81.089 25.9312C80.8018 26.2776 80.5 26.7895 80.5 27.4482Z" stroke="white" stroke-width="3" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_dd_2539_14244" x="-1.0625" y="-1.0625" width="114.125" height="114.125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="color-burn" in2="BackgroundImageFix" result="effect1_dropShadow_2539_14244"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="plus-darker" in2="effect1_dropShadow_2539_14244" result="effect2_dropShadow_2539_14244"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2539_14244" result="shape"/>
</filter>
<filter id="filter1_f_2539_14244" x="12" y="12" width="88" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_2539_14244"/>
</filter>
<filter id="filter2_dd_2539_14244" x="71" y="6.44824" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="color-burn" in2="BackgroundImageFix" result="effect1_dropShadow_2539_14244"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="plus-darker" in2="effect1_dropShadow_2539_14244" result="effect2_dropShadow_2539_14244"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2539_14244" result="shape"/>
</filter>
<radialGradient id="paint0_radial_2539_14244" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(56 56) rotate(-90) scale(56 56)">
<stop/>
<stop offset="0.395" stop-opacity="0.4"/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_2539_14244">
<rect width="112" height="112" fill="white"/>
</clipPath>
</defs>
</svg>
