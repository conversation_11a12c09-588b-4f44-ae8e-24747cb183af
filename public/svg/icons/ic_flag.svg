<svg width="112" height="112" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2539_14100)">
<mask id="mask0_2539_14100" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="112" height="112">
<circle cx="56" cy="56" r="56" fill="url(#paint0_radial_2539_14100)"/>
</mask>
<g mask="url(#mask0_2539_14100)">
<circle cx="56" cy="56" r="56" fill="#FFAFB4"/>
</g>
<g filter="url(#filter0_dd_2539_14100)">
<path d="M21 19.0398C21 19.0398 42.6292 7.08357 59.0675 19.0398C69.0532 26.3029 81.2737 24.7418 89.329 22.3972C92.764 21.3973 94.4815 20.8974 95.4437 21.1668C96.3825 21.4296 96.9579 21.8614 97.4726 22.6893C98.0002 23.538 98.0002 25.1447 98.0002 28.3583V65.6753C98.0002 66.8635 98.0002 67.4576 97.783 68.045C97.6072 68.5206 97.2448 69.0616 96.872 69.4052C96.4115 69.8296 95.9224 70.0309 94.9443 70.4335C88.2416 73.1924 71.8294 78.3225 59.0675 69.0408C42.6292 57.0852 21 69.0408 21 69.0408V19.0398Z" fill="white"/>
<path d="M21 10.5V19.0398M21 101.5V69.0408M21 19.0398C21 19.0398 42.6292 7.08357 59.0675 19.0398C69.0532 26.3029 81.2737 24.7418 89.329 22.3972C92.764 21.3973 94.4815 20.8974 95.4437 21.1668C96.3825 21.4296 96.9579 21.8614 97.4726 22.6893C98.0002 23.538 98.0002 25.1447 98.0002 28.3583V65.6753C98.0002 66.8635 98.0002 67.4576 97.783 68.045C97.6072 68.5206 97.2448 69.0616 96.872 69.4052C96.4115 69.8296 95.9224 70.0309 94.9443 70.4335C88.2416 73.1924 71.8294 78.3225 59.0675 69.0408C42.6292 57.0852 21 69.0408 21 69.0408M21 19.0398V69.0408" stroke="white" stroke-width="12" stroke-miterlimit="16" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<mask id="mask1_2539_14100" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="21" y="13" width="78" height="62">
<path d="M21 19.0398C21 19.0398 42.6292 7.08357 59.0675 19.0398C69.0532 26.3029 81.2737 24.7418 89.329 22.3972C92.764 21.3973 94.4815 20.8974 95.4437 21.1668C96.3825 21.4296 96.9579 21.8614 97.4726 22.6893C98.0002 23.538 98.0002 25.1447 98.0002 28.3583V65.6753C98.0002 66.8635 98.0002 67.4576 97.783 68.045C97.6072 68.5206 97.2448 69.0616 96.872 69.4052C96.4115 69.8296 95.9224 70.0309 94.9443 70.4335C88.2416 73.1924 71.8294 78.3225 59.0675 69.0408C42.6292 57.0852 21 69.0408 21 69.0408V19.0398Z" fill="#F57575"/>
</mask>
<g mask="url(#mask1_2539_14100)">
<path opacity="0.4" d="M21 19.0398C21 19.0398 42.6292 7.08357 59.0675 19.0398C69.0532 26.3029 81.2737 24.7418 89.329 22.3972C92.764 21.3973 94.4815 20.8974 95.4437 21.1668C96.3825 21.4296 96.9579 21.8614 97.4726 22.6893C98.0002 23.538 98.0002 25.1447 98.0002 28.3583V65.6753C98.0002 66.8635 98.0002 67.4576 97.783 68.045C97.6072 68.5206 97.2448 69.0616 96.872 69.4052C96.4115 69.8296 95.9224 70.0309 94.9443 70.4335C88.2416 73.1924 71.8294 78.3225 59.0675 69.0408C42.6292 57.0852 21 69.0408 21 69.0408V19.0398Z" fill="#FFAFB4"/>
<g filter="url(#filter1_f_2539_14100)">
<path d="M45.6295 29.2624C45.6295 23.6281 45.6295 20.811 47.4758 19.6313C49.3221 18.4517 51.8785 19.6354 56.9912 22.003L61.7313 24.198C63.9845 25.2414 65.1111 25.7631 65.7404 26.748C66.3697 27.7329 66.3697 28.9744 66.3697 31.4574V56.3281C66.3697 61.9623 66.3697 64.7794 64.5234 65.9591C62.6771 67.1388 60.1207 65.955 55.008 63.5875L50.2679 61.3925C48.0147 60.3491 46.8881 59.8274 46.2588 58.8425C45.6295 57.8576 45.6295 56.6161 45.6295 54.133V29.2624Z" fill="#FFAFB4"/>
</g>
</g>
<path d="M21 10.5V19.0398M21 101.5V69.0408M21 19.0398C21 19.0398 42.6292 7.08357 59.0675 19.0398C69.0532 26.3029 81.2737 24.7418 89.329 22.3972C92.764 21.3973 94.4815 20.8974 95.4437 21.1668C96.3825 21.4296 96.9579 21.8614 97.4726 22.6893C98.0002 23.538 98.0002 25.1447 98.0002 28.3583V65.6753C98.0002 66.8635 98.0002 67.4576 97.783 68.045C97.6072 68.5206 97.2448 69.0616 96.872 69.4052C96.4115 69.8296 95.9224 70.0309 94.9443 70.4335C88.2416 73.1924 71.8294 78.3225 59.0675 69.0408C42.6292 57.0852 21 69.0408 21 69.0408M21 19.0398V69.0408" stroke="#FFAFB4" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M51.7919 46.9492C48.8563 45.581 47.3884 44.8969 47.3884 44.0813C47.3884 43.2657 48.8563 42.5816 51.7919 41.2134C54.4643 39.9679 55.9679 38.4643 57.2134 35.7919C58.5816 32.8563 59.2657 31.3884 60.0813 31.3884C60.8969 31.3884 61.581 32.8563 62.9492 35.7919C64.1946 38.4643 65.6983 39.9679 68.3707 41.2134C71.3063 42.5816 72.7742 43.2657 72.7742 44.0813C72.7742 44.8969 71.3063 45.581 68.3707 46.9492C65.6983 48.1946 64.1946 49.6983 62.9492 52.3707C61.581 55.3063 60.8969 56.7742 60.0813 56.7742C59.2657 56.7742 58.5816 55.3063 57.2134 52.3707C55.9679 49.6983 54.4643 48.1946 51.7919 46.9492Z" fill="#FFAFB4"/>
<g style="mix-blend-mode:plus-darker" opacity="0.4">
<path d="M21 10.5V19.0398M21 101.5V69.0408M21 19.0398C21 19.0398 42.6292 7.08357 59.0675 19.0398C69.0532 26.3029 81.2737 24.7418 89.329 22.3972C92.764 21.3973 94.4815 20.8974 95.4437 21.1668C96.3825 21.4296 96.9579 21.8614 97.4726 22.6893C98.0002 23.538 98.0002 25.1447 98.0002 28.3583V65.6753C98.0002 66.8635 98.0002 67.4576 97.783 68.045C97.6072 68.5206 97.2448 69.0616 96.872 69.4052C96.4115 69.8296 95.9224 70.0309 94.9443 70.4335C88.2416 73.1924 71.8294 78.3225 59.0675 69.0408C42.6292 57.0852 21 69.0408 21 69.0408M21 19.0398V69.0408" stroke="black" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M51.7919 46.9492C48.8563 45.581 47.3884 44.8969 47.3884 44.0813C47.3884 43.2657 48.8563 42.5816 51.7919 41.2134C54.4643 39.9679 55.9679 38.4643 57.2134 35.7919C58.5816 32.8563 59.2657 31.3884 60.0813 31.3884C60.8969 31.3884 61.581 32.8563 62.9492 35.7919C64.1946 38.4643 65.6983 39.9679 68.3707 41.2134C71.3063 42.5816 72.7742 43.2657 72.7742 44.0813C72.7742 44.8969 71.3063 45.581 68.3707 46.9492C65.6983 48.1946 64.1946 49.6983 62.9492 52.3707C61.581 55.3063 60.8969 56.7742 60.0813 56.7742C59.2657 56.7742 58.5816 55.3063 57.2134 52.3707C55.9679 49.6983 54.4643 48.1946 51.7919 46.9492Z" fill="black"/>
</g>
</g>
<defs>
<filter id="filter0_dd_2539_14100" x="7" y="-3.5" width="105.003" height="119" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="color-burn" in2="BackgroundImageFix" result="effect1_dropShadow_2539_14100"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="plus-darker" in2="effect1_dropShadow_2539_14100" result="effect2_dropShadow_2539_14100"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2539_14100" result="shape"/>
</filter>
<filter id="filter1_f_2539_14100" x="21.6295" y="-4.83057" width="68.7402" height="95.2516" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_2539_14100"/>
</filter>
<radialGradient id="paint0_radial_2539_14100" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(56 56) rotate(-90) scale(56 56)">
<stop/>
<stop offset="0.395" stop-opacity="0.4"/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_2539_14100">
<rect width="112" height="112" fill="white"/>
</clipPath>
</defs>
</svg>
