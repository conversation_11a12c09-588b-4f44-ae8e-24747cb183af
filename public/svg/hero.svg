<svg width="1200" height="1165" viewBox="0 0 1200 1165" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2020_544" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1200" height="1165">
<rect width="1200" height="1165" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_2020_544)">
<g filter="url(#filter0_f_2020_544)">
<circle cx="601.5" cy="692.5" r="284.5" fill="#622A9A"/>
</g>
<g opacity="0.3">
<circle opacity="0.1" cx="600" cy="711" r="515.5" stroke="white"/>
<circle opacity="0.15" cx="600" cy="711" r="399.373" stroke="white" stroke-dasharray="4 4"/>
<circle opacity="0.2" cx="600" cy="711" r="282.15" stroke="white"/>
<circle cx="261.478" cy="322.083" r="10.4554" stroke="#4D3763"/>
<circle cx="261.478" cy="322.083" r="3.88217" fill="#2A193C" stroke="#4D3763"/>
</g>
</g>
<defs>
<filter id="filter0_f_2020_544" x="93" y="184" width="1017" height="1017" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="112" result="effect1_foregroundBlur_2020_544"/>
</filter>
</defs>
</svg>
