'use client'
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { Send } from "lucide-react";

const BountyShareButton = () =>{

  const [copied, setCopied] = useState(false);
  const copyURLToClipboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);
      toast.message("URL copied")
      setTimeout(() => {
        setCopied(false);
      }, 500); 
    })
  };
  return (
   <> 
     <button 
      onClick={copyURLToClipboard}
      className="bg-white text-indigo-600 px-2 py-2 hover:bg-indigo-500 hover:text-white rounded"> 
      <Send size={20} />
      </button> 
   </>

  )
}

export default BountyShareButton