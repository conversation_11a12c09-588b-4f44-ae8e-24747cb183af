'use client'
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { Send } from "lucide-react";

const BountyShareButton = () =>{

  const [copied, setCopied] = useState(false);
  const copyURLToClipboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);
      toast.message("URL copied")
      setTimeout(() => {
        setCopied(false);
      }, 500); 
    })
  };
  return (
   <> 
     <button 
      onClick={copyURLToClipboard}
      className="bg-indigo-700 text-white px-2 py-2 hover:bg-white hover:text-indigo-900 rounded"> 
      <Send size={20} />
      </button> 
   </>

  )
}

export default BountyShareButton