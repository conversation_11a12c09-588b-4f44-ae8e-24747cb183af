"use client";
import React, { useState } from "react";
import DownloadButton from "@/app/(mainapp)/thank-you/DownloadButton";
import Loader from "@/components/Loader";
import { useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { Purchase } from "@/server/payload-types";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";

const PurchaseProduct = ({ user, purchases }: any) => {
  const [showAlldescription, setShowAllDescription] = useState(null);
  const scrollContainerRefTopFeatured = useRef(null);

  const scrollLeft = (ref) => {
    if (ref.current) {
      ref.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = (ref) => {
    if (ref.current) {
      ref.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  const handleCardClick = (purchase: Purchase) => {
    setShowAllDescription(purchase);
  };

  return (
    <>
      <Loader />
      <div className="mx-auto lg:px-6 pt-12">
        <h1 className="md:mt-10 text-4xl font-bold tracking-tight sm:text-5xl text-center">
          Product Purchases
        </h1>

        {!purchases ||
          (purchases.length === 0 ? (
            <div className="text-center mt-20 mb-20">
              No Purchases Available
            </div>
          ) : (
            <>
              <div className="relative px-4 md:px-8 mt-12 text-lg font-normal">
                <button
                  onClick={() => scrollLeft(scrollContainerRefTopFeatured)}
                  className="absolute left-5 md:-left-3 top-1/2 transform -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 transition-transform transition-colors duration-300 ease-in-out active:scale-90"
                  aria-label="Scroll left"
                >
                  <ChevronLeft className="h-4 w-4 text-indigo-700" />
                </button>
                <button
                  onClick={() => scrollRight(scrollContainerRefTopFeatured)}
                  className="absolute right-5 md:-right-3 top-1/2 transform -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 transition-transform transition-colors duration-300 ease-in-out active:scale-90"
                  aria-label="Scroll right"
                >
                  <ChevronRight className="h-4 w-4 text-indigo-700" />
                </button>
                <div
                  ref={scrollContainerRefTopFeatured}
                  className="overflow-x-auto no-scrollbar whitespace-nowrap py-6"
                >
                  {purchases.map((purchase: any) => (
                    <div key={purchase.id} className="inline-block mr-2 w-full sm:w-[25rem] transition-transform duration-500">
                      <div
                        className="bg-indigo-700 border border-gray-200 p-2 sm:p-6 rounded-lg shadow-lg cursor-pointer"
                        onClick={() => handleCardClick(purchase)}
                      >
                        <div className="space-y-4">
                          <p className="text-gray-200">
                            <span className="font-bold">Purchase ID:</span>{" "}
                            {purchase.id.length > 18
                            ? purchase.id.substring(0, 18) + "..."
                            : purchase.id}
                          </p>
                          <p className="text-gray-200">
                            <span className="font-bold">Product Name:</span>{" "}
                            {purchase?.product?.name?.length > 20
                              ? purchase.product.name.substring(0, 20) + "..."
                              : purchase.product.name}
                          </p>

                          <div className="w-max" onClick={(e) => e.stopPropagation()}>
                            <DownloadButton
                              productFiles={purchase.product.product_files}
                              product={purchase.product}
                              userId={user.id}
                              type="product"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-8 text-right px-4 md:px-8">
                <Link
                  href="/"
                  className="text-sm font-medium text-blue-500 hover:text-blue-600 transition"
                >
                  Continue Shopping →
                </Link>
              </div>
            </>
          ))}
      </div>

      {showAlldescription && (
        <SpaceModal
          title=""
          description=""
          isOpen={true}
          onClose={() => {
            setShowAllDescription(null); // Close the modal by setting the state to null
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">
              {showAlldescription?.product?.name}
            </p>
            <p className="text-lg">
              {showAlldescription?.product?.description}
            </p>
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAllDescription(null)}
              >
                Close
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}
    </>
  );
};

export default PurchaseProduct;
