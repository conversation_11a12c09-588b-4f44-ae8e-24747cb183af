"use client";
import React, { useEffect, useState } from "react";
import coinImage from "../public/img/coin-png.png";
import { Course, User } from "@/server/payload-types";
import { Download, Star } from "lucide-react";
import { Clock4 } from "lucide-react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { SpaceModal } from "@/components/SpaceModal";
import { SignalMedium } from "lucide-react";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import ReactMarkdown from "react-markdown";

interface AcademyCoursesModalsProps {
  user: User | null;
}

const AcademyCoursesModals = ({user}:AcademyCoursesModalsProps) => {
  //--------------------fetching data -----------------------------
  const [course, setcourse] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [showAlldescription, setShowAllDescription] = useState<Course | null>(
    null
  );

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const res = await fetch(`/api2/courses`);
        const getdata = await res.json();
        setcourse(getdata?.data);
        setLoading(false);
      } catch (err) {
        // setError(err.message);
        setLoading(false);
      }
    };
    fetchCourses();
  }, []);

  const handleBuyNow = (course: any, event: React.MouseEvent) => {
    event.stopPropagation();
    if (user) {
      if (user.coinBalance >= course.cost) {
        setSelectedCourse(course);
        setIsModalOpen(true);
      } else {
        toast.error("Insufficient Credits to buy the course");
      }
    } else {
      toast.error("Please login to Buy Course");
    }
  };

  const handleConfirmPurchase = async (course: any) => {
    try {
      toast.loading("Downloading...");
      setIsModalOpen(false);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/courses/purchaseCourse`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            courseId: course.id,
          }),
        }
      );
      const result = await response.json();
      if (response.ok) {
        setLoading(true);

        const pdfId =
          typeof course.pdf === "string" ? course.pdf : course.pdf?.id;
        // let fileName = course.pdf.filename;
        let fileName = course.pdf;
        const id = course.id;
        const type = "course";

        if (fileName === "undefined" || fileName === null) {
          try {
            const req = await fetch(
              `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files/${pdfId}`
            );
            const data = await req.json();
            fileName = data.filename;
          } catch (err) {
            console.log(err);
          }
        }

        if (
          !fileName ||
          fileName === "undefined" ||
          (fileName === null && !id) ||
          id === "undefined"
        ) {
          toast.error("Unable to downlaod file due to some reason");
          return;
        }

        fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api2/downloadFile/${type}/${id}/${fileName}/${user.id}`
        )
          .then((response) => response.blob())
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            toast.dismiss();
            toast.success(
              "Course Purchase Successful and Credits have been deducted."
            );
            setLoading(false);
          })
          .catch(() => {
            toast("Failed to download file"), setLoading(false);
          });
      } else {
        console.log(result.message);
        toast.error(result.message);

        // throw new Error(result.message);
      }
    } catch (error) {
      toast.error(`An error occurred while purchasing the Course`);
    }
  };

  const handleCardClick = (course: Course) => {
    setShowAllDescription(course);
  };

  return (
    <>
      <div className="w-11/12 mx-auto md:max-w-3xl lg:max-w-6xl justify-center">
        {/* <hr className="border border-white mb-7" /> */}
        {/* {/-------------------------------------------------table------------------------------------------------------/} */}

        {course.map((course: any, index: any) => (
          <div key={index}>
            <div
              className="px-0 mx-auto min-w-full py-4 rounded-lg pt-2 cursor-pointer"
              key={index}
              onClick={() => handleCardClick(course)}
            >
              <div
                className="from-indigo-600 to-indigo-700 text-white rounded-xl  
              hover:shadow-lg transition duration-200 shadow-input bg-gradient-to-br  border border-transparent hover:scale-[1.06]
              "
              >
                <div className="relative">
                  {course.isFeatured === true && (
                    <div className="absolute top-2 right-2 text-yellow-500 rounded text-xs">
                      <Star className="w-6 h-6 text-yellow-500 fill-current" />
                    </div>
                  )}
                </div>

                <div className=" flex flex-col justify-between p-4 md:p-6 gap-1">
                  <div className="flex justify-end "></div>
                  <div className="flex justify-between">
                    <div className="flex items-center">
                      <p className="text-5xl font-extrabold">
                        {course.cost === 0 ? (
                          <>
                            <div className="px-4 py-2 rounded-full bg-gradient-to-r  text-white text-2xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                              FREE
                            </div>
                          </>
                        ) : (
                          course.cost
                        )}
                      </p>
                      {course.cost === 0 ? (
                        ""
                      ) : (
                        <img
                          src={coinImage.src}
                          alt="Coin"
                          style={{ width: "44px", height: "50px" }}
                          className=""
                        />
                      )}
                    </div>
                    <div className="flex justify-end gap-5 items-center">
                      <div className="flex items-center gap-1">
                        <Clock4 size={16} />
                        <p className="text-sm">{course.time}</p>
                      </div>

                      <p className="flex gap-1 items-center">
                        <SignalMedium size={28} className="mb-2 -mr-2" />
                        {course.level}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-1 "></div>
                  <h4 className="text-3xl">{`${
                    course.title.length > 50
                      ? course.title.substring(0, 50) + "..."
                      : course.title
                  }`}</h4>
                  <div className="flex justify-between items-center gap-3">
                    <h4 className="text-md text-gray-300">
                      {`${
                        course.description.length > 100
                          ? course.description.substring(0, 120) + "..."
                          : course.description
                      }`}
                    </h4>
                    <div className=" flex justify-end gap-5 z-10">
                      {/* <p className="text-sm">3</p> */}
                      <button
                        className="bg-indigo-900 hover:bg-indigo-800 cursor-pointer px-4 rounded-lg py-1 items-center gap-1 hidden md:flex"
                        onClick={(e) => handleBuyNow(course, e)}
                      >
                        <Download /> Download
                      </button>
                    </div>
                  </div>
                  <div>
                    <button
                      className="bg-indigo-900 hover:bg-indigo-800 cursor-pointer px-4 rounded-lg py-1 flex items-center gap-1
                  md:hidden mt-2"
                      onClick={(e) => handleBuyNow(course, e)}
                    >
                      <Download /> Download
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        <h3 className=" text-center text-xl mt-24 py-2 font-bold">
          Learn and teach generative AI affordably with courses designed to
          enhance skills and share expertise.
        </h3>
        <h1 className="text-center py-4 text-xl font-bold">
          Join now to make AI education accessible to all!
        </h1>
      </div>

      {isModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">
              {selectedCourse.cost === 0 ? (
                "This course is free and No credits will be deducted for this course. Confirm your purchase"
              ) : (
                <div>
                  The Course Price {selectedCourse.cost}
                  <img
                    src={coinImage.src}
                    alt="Coin"
                    style={{
                      width: "25px",
                      height: "27px",
                      display: "inline",
                      margin: "0px 4px 4px 2px",
                    }}
                  />
                  Credits will be deducted from your account for this purchase.
                </div>
              )}
            </p>
            {errorMessage && <p className="text-red-500">{errorMessage}</p>}
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Close
              </Button>
              <Button
                variant="outline"
                onClick={() => handleConfirmPurchase(selectedCourse)}
              >
                Confirm
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}

      {showAlldescription && (
        <SpaceModal
          title=""
          description=""
          isOpen={true}
          onClose={() => {
            setShowAllDescription(null); // Close the modal by setting the state to null
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">{showAlldescription?.title}</p>
            <div className="text-lg max-h-72 overflow-y-auto">
              <ReactMarkdown
                components={{
                  a: ({ href, children }) => (
                    <a
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      {children}
                    </a>
                  ),
                }}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {showAlldescription?.description}
              </ReactMarkdown>
            </div>
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAllDescription(null)}
              >
                Close
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}
    </>
  );
};

export default AcademyCoursesModals;
