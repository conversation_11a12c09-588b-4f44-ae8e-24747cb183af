import React from "react";


interface props {
  price: number;
}
const Price = ({ price }: props) => {
  return (
    <div className="flex flex-row items-center ">
      <h3 className="text-2xl">{price}</h3>
      <img
        src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
        alt="Coin"
        style={{
          width: "26px",
          height: "27px",
          display: "inline",
          margin: "4px 4px 4px 2px",
        }}
         loading="lazy"
      />
    </div>
  );
};

export default Price;
