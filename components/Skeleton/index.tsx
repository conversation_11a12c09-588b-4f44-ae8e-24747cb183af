import React from "react";
import Image from "next/image";

interface props {
  //price: number
  image: any;
  user: any;
}
const Price = ({ image, user }: props) => {
  return (
    <div>
      <div className="flex items-center mt-4 mb-4">
        <Image
          width="500"
          height="500"
          src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/prompt6.png`}
          alt="User Avatar"
          className="w-10 h-10 rounded-full mr-2"
        />
        <span className="bg-white text-indigo-600 px-2 py-1 rounded">
          @{user?.user_name}
        </span>
      </div>
      <div className="flex overflow-x-scroll whitespace-nowrap space-x-4 mb-4 no-scrollbar">
        {/* <Image
                width="500"
                height="500"
                src={image.image.url}
                alt="Example 1"
                className="object-cover min-w-36 h-28"
              /> */}
        {image.slice(1).map((image, index) => (
          <Image
            key={index}
            width="500"
            height="500"
            src={image?.image?.url}
            alt="Example 1"
            className="object-cover min-w-36 h-28 cursor-pointer"
          />
        ))}
        {/* <Image
                width="500"
                height="500"
                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/6.png`}
                alt="Example 2"
                className="object-cover min-w-36 h-28"
              />
              <Image
                width="500"
                height="500"
                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/7.png`}
                alt="Example 3"
                className="object-cover min-w-36 h-28"
              /> */}
      </div>
    </div>
  );
};

export default Price;
