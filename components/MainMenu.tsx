'use client'
import { PRODUCT_CATEGORIES } from '@/constants'
import { useOnClickOutside } from '@/app/hooks/use-onclick-out';
import { useEffect, useRef, useState } from 'react'
import MenuItem from './MenuItem'

interface MenuItemProps {
  sidebarnav: boolean;
}
const MainMenu = ({ sidebarnav }: MenuItemProps) => {
    const [activeIndex, setActiveIndex] = useState<
      null | number
    >(null)
  
    useEffect(() => {
      const handler = (e: KeyboardEvent) => {
       if (e.key === 'Escape') {
          setActiveIndex(null)
        }
      }
  
      document.addEventListener('keydown', handler)
  
      return () => {
        document.removeEventListener('keydown', handler)
      }
    }, [])
  
    const isAnyOpen = activeIndex !== null
  
    const navRef = useRef<HTMLDivElement | null>(null)
  
    useOnClickOutside(navRef, () => setActiveIndex(null))
  
    return (
    <div  className={`font-bold z-10 ${
      sidebarnav 
        ? "block relative w-full"
        : "relative px-6 sm:px-12 py-1 items-center hidden lg:block justify-between w-full"
    }`}>
       <div
        className={`${sidebarnav  ? "block h-full" : "flex gap-4 h-full"}`}
        ref={navRef}
      >
        {PRODUCT_CATEGORIES.map((category, i) => {
          const handleOpen = () => {
            if (activeIndex === i) {
              setActiveIndex(null)
            } else {
              setActiveIndex(i)
            }
          }
  
          const close = () => setActiveIndex(null)
  
          const isOpen = i === activeIndex
  
          return (
            <MenuItem
              category={category}
              close={close}
              sidebarnav={true}
              handleOpen={handleOpen}
              isOpen={isOpen}
              key={category.value}
              isAnyOpen={isAnyOpen}
            />
          )
        })}
      </div>
    </div>  
    )
  }
  
  export default MainMenu