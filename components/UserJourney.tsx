"use client";
import React, { useState, useEffect } from "react";
import { SpaceModal } from "@/components/SpaceModal";
import { useForm, SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { User } from "@/server/payload-types";
import { useRouter } from "next/navigation";
import axios from "axios";
import debounce from "lodash.debounce";
import Link from "next/link";

const User_Interests = [
  {
    label: "Prompt Engineering",
    value: "prompt engineering" as const,
  },
  {
    label: "Content Creation",
    value: "content creation" as const,
  },
  {
    label: "Assets",
    value: "assets" as const,
  },
  {
    label: "AI Applications",
    value: "ai applications" as const,
  },
  {
    label: "Learning and Courses",
    value: "learning and courses" as const,
  },
  {
    label: "Blogs",
    value: "blogs" as const,
  },
  {
    label: "Bounties",
    value: "bounties" as const,
  },
  {
    label: "Community Collaboration",
    value: "community collaboration" as const,
  },
];

const Professions = [
  { value: "engineer", label: "Engineer" },
  { value: "artist", label: "Artist" },
  { value: "content creator", label: "Content Creator" },
  { value: "designer", label: "Designer" },
  { value: "developer", label: "Developer" },
  { value: "marketer", label: "Marketer" },
  { value: "researcher", label: "Researcher" },
  { value: "teacher", label: "Teacher" },
  { value: "writer", label: "Writer" },
  { value: "other", label: "Other" },
];

const EnterpriseStageThree = [
  {
    name: "For Create Bounty",
    link: "/bounties",
  },
  {
    name: "For Create Private or Public AI App",
    link: "/dashboard",
  },
  {
    name: "Affiliate with Community Members",
    link: "/community",
  },
  {
    name: "Visit Services for Custom AI Solutions",
    link: "/services#write-to-us",
  },
];

interface UserJourneyProps {
  isModalOpen: boolean;
  setIsModalOpen: any;
  user: User | null;
}

type FormData = {
  user_name: string;
  userType: string;
  profession: string;
  otherProfession?: string;
  interests: string[];
  over18: boolean;
};

const UserJourney = ({
  isModalOpen,
  setIsModalOpen,
  user,
}: UserJourneyProps) => {
  const [step, setStep] = useState(1);
  const [username, setUsername] = useState("");
  const [usernameExists, setUsernameExists] = useState(false);
  const [userTypeInput, setUserTypeInput] = useState<string | null>("individual");
  const [loading, setLoading] = useState<boolean>(false);
  const [redirectLink, setRedirectLink] = useState<string | null>(null);
  const [selectedUserType, setSelectedUserType] = useState("");
  const [selectedProfession, setSelectedProfession] = useState("");
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [selectedStep, setSelectedStep] = useState<number>();
  const [isUsernameDisabled, setIsUsernameDisabled] = useState(true);
  const [isUserTypeDisabled, setIsUserTypeDisabled] = useState(true);
  const [isNextDisabled1st, setIsNextDisabled1st] = useState(true);
  const [isNextDisabled2nd, setIsNextDisabled2nd] = useState(true);
  const [isInterestsDisabled, setIsInterestsDisabled] = useState(true);
  const [skip, setSkip] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    clearErrors,
  } = useForm<FormData>();

  const totalSteps = 3;
  const progressPercentage = (step / totalSteps) * 100;

  const checkUsernameExists = async (user_name: string) => {
    try {
      const { data }: { data: CustomEndPointResponse } = await axios.post(
        `/api2/user/check-username`,
        {
          user_name,
        }
      );
      if (data.success === false) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error checking username:", error);
      return false;
    }
  };

  const debouncedCheckUsernameExists = debounce(async (user_name) => {
    setLoading(true);
    try {
      const exists = await checkUsernameExists(user_name);
      setUsernameExists(exists); // Update the state based on response
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error checking username:", error);
    }
  }, 200);

  const handleOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUserTypeInput(event.target.value);
    setSelectedUserType(event.target.value);
    setIsUserTypeDisabled(false);
  };

  const handleCheckboxChange = (value: string) => {
    setSelectedInterests((prev) =>
      prev.includes(value)
        ? prev.filter((item) => item !== value)
        : [...prev, value]
    );
  };

  useEffect(() => {
    if (selectedInterests.length === 0) {
      setIsInterestsDisabled(true);
    } else {
      setIsInterestsDisabled(false);
    }
  }, [selectedInterests]);

  const handleNext = async () => {
    setStep(step + 1);
  };

  // const handleSkip = () => {
  //   setIsModalOpen(false);
  //   router.push("/");
  // };

  const onSubmit: SubmitHandler<FormData> = async (formData, event) => {
    if (!formData.interests || formData.interests.includes("false")) {
      formData.interests = [];
    }
    
    const profession =
      formData.profession === "other"
        ? formData.otherProfession
        : formData.profession;
    setLoading(true);
    try {
      const req = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${user?.id}`,
        {
          method: "PATCH",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_name: formData.user_name,
            userType: formData.userType,
            genInfo: {
              profession: profession,
              interests: formData.interests,
            },
            over18: formData.over18,
          }),
        }
      );
      const data = await req.json();
      if (data.message === "Updated successfully.") {
        if (step === 2 && userTypeInput === "enterprise") {
          toast.success("Submitted Successfully!");
          setStep(step + 1);
        } else {
          toast.success("Thanks for your time, Welcome to RentPrompts.");
          setLoading(false);
          router.push("/");
        }
      }
    } catch (err) {
      console.log(err);
      toast.error("User Update Failed, Please try again");
    }
  };

  const handlePrevious = () => {
    setStep(step - 1);
  };

  const watchProfession = watch("profession");

  return (
    <SpaceModal
      title="For best experience at Rentprompts"
      description="It won't take long"
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      component="UserJourney"
    >
      <div className="relative z-10 max-w-lg w-full ml-0 mr-2 md:mx-0">
        <div className="mb-4 h-2 bg-white rounded">
          <div
            className="h-full bg-indigo-400 rounded"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <form onSubmit={handleSubmit(onSubmit)} encType="multipart/form-data">
          {step === 1 && (
            <div className="text-white p-8 rounded-lg shadow-2xl transition-transform duration-300">
              <div className="text-lg font-semibold mb-4 text-white">
                Enter a username
              </div>
              <div className="flex gap-4 w-full">
                <input
                  className="w-full bg-transparent border border-indigo-400 p-2 rounded-lg placeholder:text-gray-200"
                  type="text"
                  placeholder="@username"
                  {...register("user_name", {
                    required: {
                      value: true,
                      message: "Please enter a username",
                    },
                    minLength: {
                      value: 4,
                      message: "Username must be at least 4 characters long",
                    },
                    validate: () => {
                      if (usernameExists) return "Username already exists";
                      return true;
                    },
                  })}
                  onChange={(e) => {
                    const enteredUsername = e.target.value;
                    setUsername(enteredUsername);
                    setIsUsernameDisabled(false)
                    if (
                      errors.user_name?.type === "required" &&
                      enteredUsername.length > 0
                    ) {
                      clearErrors("user_name");
                    }
                    debouncedCheckUsernameExists(enteredUsername);
                  }}
                  value={username}
                />
              </div>

              {/* This wrapper div ensures that space is reserved for the checking message */}
              <div className="min-h-[20px]">
                {loading && (
                  <p className="text-yellow-200 text-sm">
                    checking username...
                  </p>
                )}
                {errors.user_name && (
                  <p className="text-yellow-200 text-xs mt-1">
                    {errors.user_name.message}
                  </p>
                )}
                {!loading && usernameExists && (
                  <p className="text-yellow-200 text-xs mt-1">
                    Username already exists. Please choose another.
                  </p>
                )}
              </div>

              {/* <div className="mt-6">
                <div className="text-lg font-semibold mb-4 text-white">
                  🤔 Are you an Enterprise or Individual ?
                </div>
                <div className="flex gap-4 w-full"> */}
                  {/* <label
                    className={`w-full text-center border flex gap-1 items-center justify-center border-indigo-400 rounded-lg p-2 text-base font-semibold ${selectedUserType === "individual" ? "bg-indigo-600 text-indigo-200" : "bg-indigo-200 text-indigo-800"}`}
                  > */}
                    {/* <input
                      type="radio"
                      // hidden
                      name="userType"
                      value="individual"
                      {...register("userType", {
                        required: "You have to choose one",
                      })}
                      className="appearance-none"
                      onChange={handleOptionChange}
                    />
                    <span className={``}>Individual</span>
                  </label> */}

                  {/* <label
                    className={`w-full text-center border flex gap-1 items-center justify-center border-indigo-400 rounded-lg p-2 text-base font-semibold ${selectedUserType === "individual" ? "bg-indigo-600 text-indigo-200" : "bg-indigo-200 text-indigo-800"}`}
                  >
                    <span className={``}>If you are an enterprise, visit our AI Studio
                    </span>
                       <Link href={'https://rentprompts.ai/'}>
                       AI Studio</Link>
                  </label> */}

                  {/* <label
                    className={`w-full text-center border border-indigo-400 rounded-lg p-2 text-base font-semibold ${selectedUserType === "enterprise" ? "bg-indigo-600 text-indigo-200" : "bg-indigo-200 text-indigo-800"}`}
                  >
                    <input
                      type="radio"
                      // hidden
                      name="userType" // Same name to make it mutually exclusive
                      value="enterprise"
                      {...register("userType", {
                        required: "You have to choose one",
                      })}
                      className="appearance-none"
                      onChange={handleOptionChange}
                    />
                    <span>Enterprise</span>
                  </label> */}

                {/* </div>
                {errors.userType && (
                  <p className="text-yellow-200 text-xs mt-1">
                    {errors.userType.message}
                  </p>
                )}
              </div> */}

              <div className="flex justify-end mt-8">
                {/* <button
                  onClick={handleSkip}
                  className="py-2 rounded hover:border-gray-700/50 transition duration-300 transform hover:scale-105 text-gray-400"
                >
                  Skip
                </button> */}
                <button
                  onClick={handleSubmit(() => handleNext())} // Ensure validation runs before moving to the next step
                  className={`px-4 py-1 border rounded transition duration-300 transform hover:border-purple-400/50 hover:scale-105 shadow-lg ${isUsernameDisabled || isUserTypeDisabled ? "bg-gray-200 text-blue-300 border-gray-200" : "bg-white text-blue-700 border-white cursor-pointer"}`}
                  disabled={isUsernameDisabled }
                >
                  Next
                </button>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="text-white p-6 rounded-lg shadow-2xl transition-transform duration-300">
              {userTypeInput === "individual" ? (
                <>
                  <h2 className="text-2xl font-bold mb-6 text-white">
                    📝 What is your role?
                  </h2>

                  <div className="mb-6">
                    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 gap-4">
                      {Professions.map((option) => (
                        <div key={option.value}>
                          <label
                            className={`flex overflow-hidden whitespace-normal justify-center text-sm px-3 py-2 lg:py-4 rounded-lg transition-all duration-300 hover:bg-gray-400 cursor-pointer border border-indigo-600 ${selectedProfession === option.value ? "bg-indigo-400 text-indigo-800" : ""}`}
                          >
                            <input
                              type="radio"
                              hidden
                              value={option.value}
                              {...register("profession")}
                              onClick={() => {
                                setSelectedProfession(option.value)
                                setIsNextDisabled1st(false);
                              }}
                              className="text-sm"
                            />
                            <span className="font-medium lg:text-base">
                              {option.label}
                            </span>
                          </label>
                        </div>
                      ))}
                      {watchProfession === "other" && (
                        <div>
                          <label>Enter your Role</label>
                          <input
                            className="bg-transparent border border-indigo-400 rounded-lg p-4 mt-2"
                            type="text"
                            {...register("otherProfession")}
                          />
                          {errors.otherProfession && (
                            <p className="text-xs text-yellow-200 mt-1">
                              This field is required
                            </p>
                          )}
                        </div>
                      )}

                      {errors.profession && <p>Please select a profession</p>}
                    </div>
                    {errors.profession && (
                      <p className="text-yellow-200 text-xs mt-1">
                        {errors.profession.message}
                      </p>
                    )}
                  </div>

                  <div className="flex justify-between items-center">
                    <button
                      type="submit"
                      className="px-4 py-1 rounded text-gray-400 hover:border-gray-700/50 transition duration-300 transform hover:scale-105"
                    >
                      Skip
                    </button>
                    <button
                      onClick={handleSubmit(() => handleNext())} // Ensure validation runs before moving to the next step
                      className={`px-4 py-1 border rounded transition duration-300 transform hover:border-purple-400/50 hover:scale-105 shadow-lg ${isNextDisabled1st ? "bg-gray-200 text-blue-300 border-gray-200" : "bg-white text-blue-700 border-white cursor-pointer"}`}
                      disabled={isNextDisabled1st}
                    >
                      Next
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <h2 className="text-2xl font-bold mb-2 text-white">
                    Features for you
                  </h2>

                  <div className="mb-10 p-3 flex flex-col gap-8">
                    <div className="list-item">
                      Challenge our Community by giving them Bounties
                    </div>
                    <div className="list-item">
                      Empower your workforce, Increase efficiency by creating
                      Private or Public AI apps
                    </div>
                    <div className="list-item">
                      Affiliate with Community Members and Generate leads
                    </div>
                    <div className="list-item">
                      Get your own custom AI solutions by visiting Services
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <button
                      onClick={handlePrevious}
                      className="px-4 py-1 border border-gray-600 rounded bg-indigo-600 text-white hover:border-gray-700/50 transition duration-300 transform hover:scale-105"
                    >
                      Prev
                    </button>
                    <button
                      type="submit"
                      onClick={(e) => {
                        setSelectedStep(step);
                      }}
                      className="px-4 py-1 border border-white bg-white text-blue-700 rounded transition duration-300 transform hover:border-purple-400/50 hover:scale-105 shadow-lg"
                    >
                      Submit
                    </button>
                  </div>
                </>
              )}
            </div>
          )}

          {step === 3 && (
            <div className="text-white p-3 md:p-4 rounded-lg shadow-2xl transition-transform duration-300">
              {userTypeInput === "individual" ? (
                <>
                  <h2 className="text-2xl font-bold mb-6 text-white">
                    🎨 What is your interest?
                  </h2>

                  <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 gap-4">
                    {User_Interests.map((interest, i) => (
                      <label
                        key={i}
                        className={`flex overflow-hidden whitespace-normal justify-center text-sm p-0 px-2 py-2 md:px-3 md:py-4 rounded-lg transition-all duration-300 hover:bg-gray-400 cursor-pointer border border-indigo-600 ${selectedInterests.includes(interest.value) ? "bg-indigo-400 text-indigo-800" : ""}`}
                      >
                        <input
                          type="checkbox"
                          hidden
                          value={interest.value}
                          {...register("interests")}
                          onClick={() => {handleCheckboxChange(interest.value)
                            selectedInterests.length === 0 ? setIsInterestsDisabled(true) : setIsInterestsDisabled(false);
                          }}
                          className="mr-2"
                        />
                        <span className="font-medium lg:text-base">
                          {interest.label}
                        </span>
                      </label>
                    ))}
                  </div>

                  <div className="bg-gradient-to-r from-transparent via-neutral-300 dark:via-neutral-700 to-transparent mt-8 mb-4 h-[1px] w-full" />

                  <div className="pt-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        id="over18"
                        name="over18"
                        {...register("over18")}
                        onChange={() => setIsNextDisabled2nd(!isNextDisabled2nd)}
                        className="mr-2"
                      />
                      I confirm that I&apos;m 18 years or older
                    </label>
                  </div>

                  <div className="flex justify-between items-center mt-6 md:mt-6">
                    <button
                      type="submit"
                      className="px-4 py-2 text-gray-400 rounded hover:border-gray-700/50 transition duration-300 transform hover:scale-105 "
                    >
                      Skip
                    </button>
                    <button
                      type="submit"
                      className={`px-4 py-1 border rounded transition-all duration-300 transform hover:scale-105 shadow-lg hover:border-green-800 ${isNextDisabled2nd || isInterestsDisabled ? "bg-gray-200 text-blue-300 border-gray-200" : "bg-white text-blue-700 border-white cursor-pointer"}`}
                      disabled={isNextDisabled2nd || isInterestsDisabled}
                    >
                      Submit
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <h2 className="text-2xl font-bold mb-7 text-white">
                    Features you might want to explore
                  </h2>
                  <div className="flex flex-col gap-2">
                    {EnterpriseStageThree.map((item, index) => {
                      return (
                        <div
                          key={index}
                          className="flex justify-between items-center"
                        >
                          <div className="text-base font-bold">
                            - {item.name}
                          </div>
                          <a
                            href={item.link}
                            // onClick={(e) => {
                            //   setRedirectLink(item.link);
                            // }}
                            rel="noopener noreferrer"
                            target="_blank"
                            className="text-sm ml-3 w-max bg-gradient-to-br from-indigo-400 to-indigo-00 border border-indigo-800 px-2 py-[6px] rounded-lg hover:bg-indigo-700"
                          >
                            Explore now
                          </a>
                        </div>
                      );
                    })}
                  </div>
                  <div className="flex justify-between mt-6 md:mt-6">
                    <button
                      onClick={() => {
                        router.push("/");
                      }}
                      className="px-4 py-1 border border-indigo-500 bg-indigo-600 text-white rounded hover:border-gray-700/50 transition duration-300 transform hover:scale-105 "
                    >
                      Skip
                    </button>
                  </div>
                </>
              )}
            </div>
          )}
        </form>
      </div>
    </SpaceModal>
  );
};

export default UserJourney;
