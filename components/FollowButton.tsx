"use client";
import { useState, useEffect } from "react";
import { User } from "@/server/payload-types";
import { toast } from "sonner";
import Link from "next/link";

interface FollowButtonProps {
  userPortfolio: User;
  loginUser: User;
  followers: number;
  following: number;
}

const FollowButton: React.FC<FollowButtonProps> = ({
  userPortfolio,
  loginUser,
  followers,
  following,
}) => {
  const [loggedInUser, setLoggedInUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followersCount, setFollowersCount] = useState(followers);
  const [followingCount, setFollowingCount] = useState(following);

  const userId = userPortfolio?.id;
  const userName = userPortfolio?.user_name;
  const userEmail = userPortfolio?.email?.split("@")[0];

  const facebookLink = userPortfolio?.socialMediaLinks?.facebook
    ? userPortfolio.socialMediaLinks.facebook.startsWith("http")
      ? userPortfolio.socialMediaLinks.facebook
      : `https://${userPortfolio.socialMediaLinks.facebook}`
    : "";

  const instagramLink = userPortfolio?.socialMediaLinks?.instagram
    ? userPortfolio.socialMediaLinks.instagram.startsWith("http")
      ? userPortfolio.socialMediaLinks.instagram
      : `https://${userPortfolio.socialMediaLinks.instagram}`
    : "";

  const twitterLink = userPortfolio?.socialMediaLinks?.twitter
    ? userPortfolio.socialMediaLinks.twitter.startsWith("http")
      ? userPortfolio.socialMediaLinks.twitter
      : `https://${userPortfolio.socialMediaLinks.twitter}`
    : "";

  const githubLink = userPortfolio?.socialMediaLinks?.github
    ? userPortfolio.socialMediaLinks.github.startsWith("http")
      ? userPortfolio.socialMediaLinks.github
      : `https://${userPortfolio.socialMediaLinks.github}`
    : "";

  const discordLink = userPortfolio?.socialMediaLinks?.discord
    ? userPortfolio.socialMediaLinks.discord.startsWith("http")
      ? userPortfolio.socialMediaLinks.discord
      : `https://${userPortfolio.socialMediaLinks.discord}`
    : "";

  useEffect(() => {
    const fetchFollowers = () => {
      if (!loginUser) {
        setIsFollowing(false);
        setLoading(false);
      }

      const followers = userPortfolio?.followers?.map(
        (followedUser: any) => followedUser?.user?.id
      );
      setIsFollowing(followers?.includes(loginUser?.id));
      setLoading(false);
    };

    fetchFollowers();
  }, [userId]);

  const handleFollow = async () => {
    if (!loginUser) {
      toast.error("You are not logged in.");
      return;
    }

    setLoading(true);
    
    const result = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/follow-details/${userId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
        credentials: "include",
      }
    );

    if (!result.ok) {
      console.log("Server error. Are you online?");
      setLoading(false);
      return;
    }

    const body = await result.json();
    setIsFollowing(body.isFollowingUpdated);
    setFollowersCount(body.Followers);
    setLoading(false);
  };

  return (
    <div className="flex flex-col gap-3 sm:gap-4 justify-center sm:justify-start items-center sm:items-start ">
      <div className="flex gap-6 sm:gap-8 justify-center items-center">
        <div className="text-white text-3xl font-medium first-letter:uppercase">
          {userName ?? userEmail}
        </div>
        <button
          onClick={handleFollow}
          className="w-max h-max bg-muted-foreground cursor-pointer hover:bg-indigo-500 px-2 py-[1px] rounded-lg text-muted text-lg font-medium"
        >
          {loading ? (
            <div>{isFollowing ? "Unfollowing..." : "Following..."}</div>
          ) : (
            <div>{isFollowing ? "Unfollow" : "Follow"}</div>
          )}
        </button> 
      </div>
      <div className="flex gap-2">
        <div className="text-muted-foreground">{followersCount} Followers</div>
        <div className="text-muted-foreground"> | </div>
        <div className="text-muted-foreground">{followingCount} Following</div>
      </div>
      {/* <div className="flex gap-1 sm:gap-2">
        <div className="text-muted-foreground">{followersCount} Followers</div>
        <div className="text-muted-foreground"> . </div>
        <div className="text-muted-foreground">{followingCount} Following</div>
      </div> */}

      {/* Follow Button 
       <button
          onClick={handleFollow}
          className="h-max w-full bg-muted-foreground cursor-pointer hover:bg-indigo-500 px-2 py-[1px] rounded-lg text-muted 
          text-lg font-medium hover:border hover:border-cyan-500 "
        >
          {loading ? (
            <div>{isFollowing ? "Unfollowing..." : "Following..."}</div>
          ) : (
            <div>{isFollowing ? "Unfollow" : "Follow"}</div>
          )}
        </button>
        */} 

      <ul className="flex gap-6">
        {facebookLink ? (
          <li>
            <Link
              href={facebookLink}
              rel="noreferrer"
              target="_blank"
              className="text-primary transition hover:text-indigo-500/75"
            >
              <span className="sr-only">Facebook</span>
              <svg
                className="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
          </li>
        ) : null}

        {instagramLink ? (
          <li>
            <Link
              href={instagramLink}
              rel="noreferrer"
              target="_blank"
              className="text-primary transition hover:text-indigo-500/75"
            >
              <span className="sr-only">Instagram</span>
              <svg
                className="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
          </li>
        ) : null}

        {twitterLink ? (
          <li>
            <Link
              href={twitterLink}
              rel="noreferrer"
              target="_blank"
              className="text-primary transition hover:text-indigo-500/75"
            >
              <span className="sr-only">Twitter</span>
              <svg
                className="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
              </svg>
            </Link>
          </li>
        ) : null}

        {githubLink ? (
          <li>
            <Link
              href={githubLink}
              rel="noreferrer"
              target="_blank"
              className="text-primary transition hover:text-indigo-500/75"
            >
              <span className="sr-only">GitHub</span>
              <svg
                className="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
          </li>
        ) : null}

        {discordLink ? (
          <li>
            <Link
              href={discordLink}
              rel="noreferrer"
              target="_blank"
              className="text-primary transition hover:text-indigo-500/75"
            >
              <span className="sr-only">Discord</span>
              <svg
                className="w-6 h-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path d="M20.317 4.369a19.791 19.791 0 00-4.885-1.515.074.074 0 00-.079.037 13.9 13.9 0 00-.608 1.248 19.22 19.22 0 00-5.785 0 13.365 13.365 0 00-.617-************* 0 00-.079-.037c-1.692.295-3.343.82-4.885 1.515a.064.064 0 00-.029.025C.533 9.473-.32 14.444.099 19.378a.08.08 0 00.031.056 19.982 19.982 0 005.996 ************* 0 00.084-.028 13.452 13.452 0 001.144-************ 0 00-.041-.108 13.123 13.123 0 01-1.872-.903.078.078 0 01-.008-.13c.125-.094.25-.192.371-.291a.075.075 0 01.077-.01c3.927 1.832 8.18 1.832 12.061 0a.075.075 0 01.079.009c.122.1.247.198.372.291a.078.078 0 01-.006.13 12.62 12.62 0 01-1.873.903.077.077 0 00-.04.109c.362.654.765 1.288 1.144 1.87a.078.078 0 00.084.028 19.953 19.953 0 006.001-3.044.08.08 0 00.031-.056c.512-5.308-.883-10.258-4.452-14.984a.067.067 0 00-.03-.025zM8.02 15.331c-1.185 0-2.167-1.086-2.167-2.419 0-1.333.955-2.419 2.167-2.419 1.217 0 2.202 1.092 2.173 2.419 0 1.333-.955 2.419-2.173 2.419zm7.959 0c-1.185 0-2.167-1.086-2.167-2.419 0-1.333.955-2.419 2.167-2.419 1.217 0 2.202 1.092 2.173 2.419 0 1.333-.955 2.419-2.173 2.419z" />
              </svg>
            </Link>
          </li>
        ) : null}
      </ul>
    </div>
  );
};

export default FollowButton;
