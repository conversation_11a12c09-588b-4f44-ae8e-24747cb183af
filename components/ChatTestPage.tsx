"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { FaLock, <PERSON>aCom<PERSON>, FaPaperclip, FaImage } from "react-icons/fa";

interface User {
  id: string;
  name: string;
}

interface Chat {
  id: string;
  bounty: { title: string } | string;
  updatedAt: string;
}

interface Message {
  id: string;
  sender: { id: string; user_name: string } | string;
  text: string;
  media?: { id: string; url: string; filename: string };
  file?: { id: string; url: string; filename: string };
  createdAt: string;
}

const ChatTestPage = () => {
  const [user, setUser] = useState<User | null>(null);
  const [selectedChatId, setSelectedChatId] = useState("");
  const [message, setMessage] = useState("");
  const [chats, setChats] = useState<Chat[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [showAllChats, setShowAllChats] = useState(false);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,
          { method: "GET" }
        );

        if (!response.ok) throw new Error("Network response was not ok");

        const data = await response.json();
        setUser(data.data || data.user);
      } catch (error) {
        console.error("Error fetching user data:", error);
        setError("Failed to load user data");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Fetch chats when user is available
  useEffect(() => {
    if (user?.id) {
      fetchChats();
    }
  }, [user, showAllChats]);

  const fetchChats = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await axios.get(`/api/chatrooms/${user?.id}/chats`);
      setChats(response.data.docs);
  
      if (response.data.docs.length > 0 && !selectedChatId) {
        setSelectedChatId(response.data.docs[0].id);
      }
    } catch (err) {
      console.error("Failed to fetch chats:", err);
      setError(
        err.response?.data?.error || err.message || "Failed to load chats"
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch messages when chat is selected
  useEffect(() => {
    if (selectedChatId) {
      fetchMessages();
    }
  }, [selectedChatId]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await axios.get(
        `/api/chatrooms/${selectedChatId}/messages`,
        { params: { depth: 2 } }
      );
      setMessages(response.data.docs);
    } catch (err) {
      console.error("Failed to fetch messages:", err);
      setError(
        err.response?.data?.error || err.message || "Failed to load messages"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const isImage = file.type.startsWith("image/");
    const isFile = !isImage;

    try {
      setIsUploading(true);
      setError("");
      const formData = new FormData();
      formData.append("file", file);

      const uploadEndpoint = isImage
        ? `${process.env.NEXT_PUBLIC_SERVER_URL}/api/media`
        : `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: "POST",
        credentials: "include",
        body: formData,
      });

      const uploadData = await uploadResponse.json();

      if (!uploadResponse.ok) {
        throw new Error(uploadData.message || "File upload failed");
      }

      await sendMessage(
        "",
        isImage ? uploadData.doc.id : undefined,
        isImage ? undefined : uploadData.doc.id
      );
    } catch (err) {
      console.error("File upload error:", err);
      setError(err.message || "Failed to upload file");
    } finally {
      setIsUploading(false);
      e.target.value = "";
    }
  };

  const sendMessage = async (text = "", mediaId = "", fileId = "") => {
    if (!text.trim() && !mediaId && !fileId) return;
    if (!user?.id || !selectedChatId) return;

    try {
      setLoading(true);
      setError("");
      await axios.post(`/api/chatrooms/${selectedChatId}/send`, {
        text: text || undefined,
        senderId: user.id,
        ...(mediaId && { mediaId }),
        ...(fileId && { fileId }),
      });

      setMessage("");
      await fetchMessages();
    } catch (err) {
      console.error("Send message error:", err);
      setError(
        err.response?.data?.error || err.message || "Failed to send message"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = () => {
    sendMessage(message);
  };

  const getSenderName = (message: Message) => {
    if (typeof message.sender === "object") {
      return message.sender.user_name;
    }
    return message.sender === user?.id ? "You" : "Other User";
  };

  const isCurrentUser = (message: Message) => {
    const senderId =
      typeof message.sender === "object" ? message.sender.id : message.sender;
    return senderId === user?.id;
  };

  const isOldChat = (updatedAt: string) => {
    return (
      new Date(updatedAt) < new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    );
  };

  return (
    <div
      style={{
        padding: "30px",
        fontFamily: "Arial, sans-serif",
        backgroundColor: "#f4f4f4",
        minHeight: "100vh",
        color: "#000",
      }}
    >
      <h1 style={{ marginBottom: "20px" }}>💬 Chat System</h1>

      {loading && !isUploading && <p>⏳ Loading...</p>}
      {isUploading && <p>📤 Uploading image...</p>}
      {error && <p style={{ color: "red" }}>❌ Error: {error}</p>}

      {user && (
        <div style={{ marginBottom: "20px" }}>
          <p>
            Logged in as: <strong>{user.name}</strong> (ID: {user.id})
          </p>
        </div>
      )}

      {chats.length > 0 ? (
        <>
          <div style={{ marginBottom: "20px" }}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "10px",
                marginBottom: "8px",
              }}
            >
              <label style={{ display: "block" }}>📁 Select Chat:</label>
              {/* <button
                onClick={() => setShowAllChats(!showAllChats)}
                style={{
                  padding: "5px 10px",
                  backgroundColor: showAllChats ? "#6c757d" : "#17a2b8",
                  color: "#fff",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer",
                }}
              >
                {showAllChats ? "Show Active Only" : "Show All Chats"}
              </button> */}
            </div>
            <select
              value={selectedChatId}
              onChange={(e) => setSelectedChatId(e.target.value)}
              style={{
                padding: "10px",
                width: "300px",
                borderRadius: "5px",
                marginBottom: "10px",
                color: "#000",
                backgroundColor: "#fff",
                border: "1px solid #ccc",
              }}
            >
              <option value="">Select a chat</option>
              {chats.map((chat) => (
                <option key={chat.id} value={chat.id}>
                  {typeof chat.bounty === "object"
                    ? chat.bounty.title
                    : chat.bounty}
                  {isOldChat(chat.updatedAt) && " (Old)"}
                </option>
              ))}
            </select>
          </div>

          {selectedChatId && (
            <div>
              <h2>💬 Messages</h2>
              <div
                style={{
                  backgroundColor: "#fff",
                  border: "1px solid #ccc",
                  borderRadius: "10px",
                  padding: "15px",
                  height: "400px",
                  overflowY: "auto",
                  marginBottom: "15px",
                  display: "flex",
                  flexDirection: "column-reverse",
                }}
              >
                {messages.length === 0 ? (
                  <p
                    style={{
                      color: "#666",
                      textAlign: "center",
                      marginTop: "20px",
                    }}
                  >
                    No messages yet. Start the conversation!
                  </p>
                ) : (
                  messages.map((msg) => (
                    <div
                      key={msg.id}
                      style={{
                        marginBottom: "10px",
                        alignSelf: isCurrentUser(msg)
                          ? "flex-end"
                          : "flex-start",
                        maxWidth: "80%",
                      }}
                    >
                      <div
                        style={{
                          backgroundColor: isCurrentUser(msg)
                            ? "#007bff"
                            : "#e9ecef",
                          color: isCurrentUser(msg) ? "#fff" : "#000",
                          padding: "8px 12px",
                          borderRadius: "8px",
                          display: "inline-block",
                          wordBreak: "break-word",
                          maxWidth: "100%",
                        }}
                      >
                        <strong>{getSenderName(msg)}</strong>
                        <div style={{ marginTop: "4px" }}>
                          {msg.media && (
                            <div style={{ margin: "5px 0" }}>
                              <img
                                src={msg.media.url}
                                alt={msg.media.filename}
                                style={{
                                  maxWidth: "100%",
                                  maxHeight: "300px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                  marginBottom: "5px",
                                }}
                              />
                              <a
                                href={msg.media.url}
                                download={msg.media.filename}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  display: "inline-flex",
                                  alignItems: "center",
                                  padding: "5px 10px",
                                  backgroundColor: "#f0f0f0",
                                  borderRadius: "4px",
                                  textDecoration: "none",
                                  color: "#007bff",
                                  fontSize: "0.8em",
                                }}
                              >
                                <FaImage
                                  style={{ marginRight: "5px" }}
                                  size={12}
                                />
                                Download Image
                              </a>
                            </div>
                          )}
                          {msg.file && (
                            <div style={{ margin: "5px 0" }}>
                              <a
                                href={msg.file.url}
                                download={msg.file.filename}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                  display: "inline-flex",
                                  alignItems: "center",
                                  padding: "8px 12px",
                                  backgroundColor: "#f0f0f0",
                                  borderRadius: "4px",
                                  textDecoration: "none",
                                  color: "#007bff",
                                }}
                              >
                                <FaPaperclip style={{ marginRight: "8px" }} />
                                {msg.file.filename}
                              </a>
                            </div>
                          )}
                          {msg.text}
                        </div>
                        <div
                          style={{
                            fontSize: "0.8em",
                            marginTop: "4px",
                            textAlign: "right",
                            opacity: 0.7,
                          }}
                        >
                          {new Date(msg.createdAt).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div style={{ position: "relative" }}>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  rows={3}
                  style={{
                    width: "100%",
                    padding: "10px 50px 10px 10px",
                    borderRadius: "8px",
                    border: "1px solid #ccc",
                    marginBottom: "10px",
                    resize: "none",
                    backgroundColor: "#fff",
                    color: "#000",
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <div
                  style={{
                    position: "absolute",
                    right: "10px",
                    top: "10px",
                    display: "flex",
                    gap: "10px",
                  }}
                >
                  <label
                    htmlFor="file-upload"
                    style={{
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      padding: "5px",
                    }}
                  >
                    <FaPaperclip />
                    <input
                      id="file-upload"
                      type="file"
                      accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
                      onChange={handleFileChange}
                      style={{ display: "none" }}
                    />
                  </label>
                  <button
                    onClick={handleSendMessage}
                    disabled={(!message.trim() && !isUploading) || loading}
                    style={{
                      padding: "5px 15px",
                      backgroundColor: "#17a2b8",
                      color: "#fff",
                      border: "none",
                      borderRadius: "5px",
                      cursor: "pointer",
                      opacity:
                        (!message.trim() && !isUploading) || loading ? 0.5 : 1,
                    }}
                  >
                    Send
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "300px",
            textAlign: "center",
            backgroundColor: "#fff",
            borderRadius: "10px",
            padding: "20px",
            border: "1px solid #ddd",
          }}
        >
          <div
            style={{ fontSize: "48px", color: "#ccc", marginBottom: "20px" }}
          >
            <FaLock />
            <FaComments />
          </div>
          <h3 style={{ marginBottom: "10px" }}>No Chat Room Available</h3>
          <p style={{ color: "#666" }}>
            You don't have any active chat rooms yet. Start a discussion by
            applying to a bounty or accepting an applicant.
          </p>
        </div>
      )}
    </div>
  );
};

export default ChatTestPage;
