import { useTheme } from "next-themes";
import { Moon, Sun } from "lucide-react";

import React from 'react'
import { But<PERSON> } from "@/components/ui/button";

const ThemeToggle = () => {
const { theme, setTheme } = useTheme();
  return (
    <div>
        <Button
              variant="ghost"
              size="smicon"
              aria-label="Toggle Theme"
              className="fixed z-10 bg-gray-800/20 top-2 right-0"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle Theme</span>
      </Button> 
    </div>
  )
}

export default ThemeToggle;