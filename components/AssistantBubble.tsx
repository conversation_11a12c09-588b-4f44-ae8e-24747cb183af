'use client';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import Fuse from 'fuse.js';
import { knowledgeBase, friendlyResponses } from './KnowledgeBase'; 
import { useTranslation } from 'react-i18next';
import ReactMarkdown from 'react-markdown';

// --- Stopwords List ---
const stopwords = [
  'how', 'to', 'where', 'is','my', 'i', 'on', 'a', 'an', 'the', 'can', 'do', 'what', 'when', 'why',
  'am', 'are', 'has', 'had', 'have', 'it', 'of', 'for', 'me', 'you', 'your',
  'he', 'she', 'they', 'we', 'us', 'them', 'or', 'and', 'if', 'else', 'then', 'be', 'been',
  'from', 'at', 'by', 'with', 'about', 'into', 'up', 'down', 'out', 'not', 'no', 'so', 'just',
  'myself', 'our', 'ours', 'ourselves', 'yours', 'yourself', 'yourselves', 'him', 'his', 'himself',
  'her', 'hers', 'herself', 'its', 'itself', 'their', 'theirs', 'themselves', 'was', 'were',
  'being', 'through', 'during', 'before', 'after', 'above', 'below', 'again', 'further',
  'once', 'here', 'there', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some',
  'such', 'only', 'own', 'same', 'too', 'very', 's', 't', 'will', 'would', 'should', 'could',
];

const normalizeString = (str) => {
  if (typeof str !== 'string') return '';
  let normalized = str.toLowerCase();
  normalized = normalized.replace(/[$@#&*]/g, '');
  normalized = normalized.replace(/[?.!,]+$/, '').trim();
  normalized = normalized.trim().replace(/\s+/g, ' ');
  return normalized;
};

const normalizeAndRemoveStopwords = (str) => {
  if (typeof str !== 'string') return '';
  let normalized = str.toLowerCase();
  normalized = normalized.replace(/[$@#&*]/g, '');
  normalized = normalized.replace(/[?.!,]+$/, '').trim();
  normalized = normalized.trim().replace(/\s+/g, ' ');

  const words = normalized.split(' ');
  const filteredWords = words.filter(word => !stopwords.includes(word));
  return filteredWords.join(' ').trim();
};

const emboldenBulletCharacter = (text) => {
  if (typeof text !== 'string') {
    return text;
  }
  const lines = text.split('\n');
  const newLines = lines.map(line => {
    const bulletMatch = line.match(/^(\s*)•\s+(.*)/);
    if (bulletMatch) {
      const indentation = bulletMatch[1];
      const itemText = bulletMatch[2].trim(); 
      return `${indentation}- ${itemText}`;
    }
    return line;
  });
  return newLines.join('\n');
};

function Chatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeQuestion, setActiveQuestion] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [messages, setMessages] = useState([]);
  const [typing, setTyping] = useState(false);
  const chatRef = useRef(null);
  const { t } = useTranslation();

  const QUESTIONS_PER_PAGE = 4;

  const friendlyQuestionsKeys = Object.keys(friendlyResponses);
  const mainKnowledgeBaseKeys = Object.keys(knowledgeBase).filter(
    (q) => !friendlyQuestionsKeys.includes(q)
  );

  const searchableList = useMemo(() => {
    const list = [];
    for (const key in friendlyResponses) {
      list.push({
        canonicalKey: key,
        normalizedText: normalizeAndRemoveStopwords(key),
        type: 'friendly',
        answerText: friendlyResponses[key]
      });
    }
    for (const key in knowledgeBase) {
      const entry = knowledgeBase[key];
      list.push({
        canonicalKey: key,
        normalizedText: normalizeAndRemoveStopwords(key),
        type: 'kb',
        answerData: entry
      });
      if (entry.alternateQuestions && Array.isArray(entry.alternateQuestions)) {
        entry.alternateQuestions.forEach(altQ => {
          if (typeof altQ === 'string' && altQ.trim() !== '') {
            list.push({
              canonicalKey: key,
              normalizedText: normalizeAndRemoveStopwords(altQ),
              type: 'kb',
              answerData: entry
            });
          }
        });
      }
    }
    return list;
  }, [knowledgeBase, friendlyResponses]); 

  const fuseOptions = useMemo(() => ({
    keys: ['normalizedText'],
    includeScore: true,
    threshold: 0.35,
    ignoreLocation: true,
  }), []);

  const fuse = useMemo(() => {
    return new Fuse(searchableList, fuseOptions);
  }, [searchableList, fuseOptions]);


  useEffect(() => {
    const saved = localStorage.getItem('chat-history');
    if (saved) {
      setMessages(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('chat-history', JSON.stringify(messages));
    if (chatRef.current) chatRef.current.scrollTop = chatRef.current.scrollHeight;
  }, [messages]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
    setSearchTerm('');
  };

  const handleQuestionClick = (questionText, lookupKey = null) => {
    if (!questionText.trim()) return;
    setTyping(true);

    const keyToUse = lookupKey || normalizeString(questionText);

    // setActiveQuestion(keyToUse); // 

    setMessages(prev => [...prev, { type: 'user', text: questionText }]);

    setTimeout(() => {
      let botResponse = {
        text: t("Sorry, I couldn't find an answer to that. Please try rephrasing or contact support through our About Us page."),
        link: 'https://rentprompts.com/aboutus',
        linkText: t('Contact Us')
      };

      if (friendlyResponses.hasOwnProperty(keyToUse)) {
        botResponse = {
          text: friendlyResponses[keyToUse],
          link: '',
          linkText: ''
        };
      }
      else if (knowledgeBase.hasOwnProperty(keyToUse)) {
        const kbEntry = knowledgeBase[keyToUse];
        botResponse = {
            text: kbEntry.text,
            link: kbEntry.link,
            linkText: kbEntry.linkText
        };
      }

      setMessages(prev => [
        ...prev,
        {
          type: 'bot',
          text: botResponse.text,
          link: botResponse.link,
          linkText: botResponse.linkText,
        }
      ]);
      setTyping(false);
    }, 800);
  };
  const handleSearchSubmit = () => {
    if (!searchTerm.trim()) return;
    const originalUserInput = searchTerm.trim();
    const normalizedUserInputForFuse = normalizeAndRemoveStopwords(originalUserInput);

    if (!normalizedUserInputForFuse) {
      handleQuestionClick(originalUserInput, null);
      setSearchTerm('');
      return;
    }

    const results = fuse.search(normalizedUserInputForFuse);
    let resolvedCanonicalKey = null;

    if (results.length > 0) {
      const topResult = results[0];
      resolvedCanonicalKey = topResult.item.canonicalKey;
    }

    handleQuestionClick(originalUserInput, resolvedCanonicalKey);
    setSearchTerm('');
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  const exportChat = () => {
    const text = messages.map(m => `${m.type === 'user' ? 'You' : 'Bot'}: ${m.text}`).join('\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const linkElement = document.createElement('a');
    linkElement.href = URL.createObjectURL(blob);
    linkElement.download = 'chat-history.txt';
    document.body.appendChild(linkElement);
    linkElement.click();
    document.body.removeChild(linkElement);
  };

  let displayedQuestions = [];
  const questionsForSuggestions = mainKnowledgeBaseKeys;

  if (searchTerm.trim()) {
    const normalizedSearchTermForSuggestions = normalizeString(searchTerm);
    displayedQuestions = questionsForSuggestions
      .filter(q => normalizeString(q).includes(normalizedSearchTermForSuggestions))
      .slice(0, QUESTIONS_PER_PAGE);
  } else {
    displayedQuestions = questionsForSuggestions.slice(0, QUESTIONS_PER_PAGE);
  }

  const renderMessage = (msg, index) => {
  return (
    <div
      key={index}
     
      className={`flex items-start gap-2 my-2 ${msg.type === 'bot' ? 'justify-start' : 'justify-end'}`}
    >
      {msg.type === 'bot' && (
        <img
          src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/logo1.png`} 
          alt="Bot Logo"
          className="h-8 w-8 object-contain rounded-full flex-shrink-0"
        />
      )}

      <div
        className={`rounded-xl  px-3 py-2 max-w-[80%] text-sm shadow-md whitespace-pre-wrap ${
          msg.type === 'bot' ? 'bg-white text-gray-800' : 'bg-indigo-600 text-white'
        }`}
      >
        <ReactMarkdown
          components={{
            a: ({ node, ...props }) => (
              <a
                {...props}
                target="_blank"
                rel="noopener noreferrer"
                className="text-indigo-500 underline"
              />
            ),
            strong: ({ node, ...props }) => (
              <strong className="font-bold text-gray-900" {...props} />
            ),
            
            p: ({ node, ...props }) => (
              <p className="text-medium leading-snug mb-0 " {...props} /> 
            ),
           
            ul: ({ node, className, children }) => {
              
              return <ul className={`my-3  -mt-9 -mb-5 list-disc list-outside pl-3 ${className || ''}`.trim()}>{children}</ul>;
            },
            li: ({ node, className, children }) => (
              <li className={`text-xs leading-tight mt-1 -mb-4 ${className || ''}`}>
                {children}
              </li>
            ),
          }}
        >
          {emboldenBulletCharacter(msg.text || '')}
        </ReactMarkdown>

        {msg.type === 'bot' && msg.link && msg.linkText && (
          <a
            href={msg.link}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block mt-3 mb-0  text-xs text-white bg-indigo-500 hover:bg-indigo-600 px-2 py-1 rounded transition"
          >
            🔗 {msg.linkText}
          </a>
        )}
      </div>

      {msg.type === 'user' && <div className="text-xl self-end flex-shrink-0">🙋‍♂️</div>}
    </div>
  );
};
  return (
    <div className="fixed bottom-5 right-5 z-50 text-white">
      {!isOpen && (
        <div className="relative group w-12 h-12">
          <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-purple-500 via-indigo-500 to-blue-500 shadow-[inset_0_3px_6px_rgba(255,255,255,0.2),_0_8px_20px_rgba(80,0,200,0.5)] group-hover:scale-110 transition-transform duration-300 ease-out"></div>
          <button
            onClick={handleToggle}
            className="relative z-10 w-full h-full rounded-full bg-gradient-to-tr from-white via-white to-white shadow-[0_4px_10px_rgba(0,0,0,0.3),_inset_0_-4px_8px_rgba(255,255,255,0.5)] transform active:scale-95 focus:outline-none transition-all duration-300 hover:shadow-[0_6px_20px_rgba(0,0,0,2)]"
            aria-label={t('Open Chat')}
          >
            <img
              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/logo12.png`} 
              alt="Chat Assistant Logo"
              className="w-10 h-10 mx-auto my-auto object-contain drop-shadow-[0_2px_2px_rgba(0,0,0,0.25)]"
            />
          </button>
        </div>
      )}

      {isOpen && (
       <div className="rounded-2xl mt-4 bg-gradient-to-br from-indigo-800 to-purple-800 shadow-[0_0_25px_rgba(139,92,246,0.6)] w-[90vw] max-w-[340px] h-[80vh] max-h-[490px] overflow-hidden flex flex-col">
          <div className="px-2 pr-3 py-1 border-b border-indigo-600 flex items-center justify-between h-10 flex-shrink-0">
            <div className="flex items-center gap-0">
              <img src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/logo1.png`} alt="RentPrompts Assistant" className="h-10 object-contain" />
              <span className="text-white font-bold text-sm">RentPrompts Assistant</span>
            </div>
            <button
              onClick={() => setMessages([])}
              className="text-xs text-white hover:text-red-500 transition"
              title={t('Clear chat')}
            >
              🧹 {t('Clear')}
            </button>
          </div>

          <div
            ref={chatRef}
            aria-live="polite"
            className="flex-1 px-2 py-2 overflow-y-auto no-scrollbar text-sm space-y-2"
          >
            {messages.length === 0 && !typing && (
              <div className="text-center text-indigo-100 mt-10 px-5">
                <p className="animate-wave text-4xl">👋</p>
                <span className='text-lg font-semibold'><p className='text-xl'>{t('Welcome to RentPrompts.')}</p>
                 <span className='text-sm font-normal'>{t('I’m here to help you with any questions or information you need. Feel free to ask anything!')}</span> </span>
              </div>
            )}

            {messages.map(renderMessage)}

            {typing && (
              <div className="flex items-start gap-2 my-2 justify-start">
                <img
                  src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/logo1.png`}
                  alt="Bot Logo"
                  className="h-8 w-8 object-contain rounded-full flex-shrink-0"
                />
                <div className="rounded-xl px-3 py-1 max-w-xs text-sm shadow-md bg-white text-gray-800">
                  {t('Typing')}…
                </div>
              </div>
            )}
          </div>

          <div className="p-2 border-t border-indigo-600 flex-shrink-0">
            <div className="flex flex-wrap gap-1 mb-2">
              {displayedQuestions.map((q, i) => (
                <button
                  key={i}
                  onClick={() => {
                    handleQuestionClick(q, q);
                  }}
                  className="bg-white/90 text-indigo-900 rounded-md px-2 py-0.5 text-[11px] hover:bg-indigo-100 transition-colors shadow-sm"
                >
                  {q}
                </button>
              ))}
            </div>
            <div className="flex gap-2">
              <input
                type="text"
                placeholder={t('Ask a question…')}
                className="flex-1 p-2 rounded bg-indigo-600 text-white placeholder-indigo-200 text-sm focus:outline-none focus:ring-2 focus:ring-[#e2e1e1]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
              />
              <button
                onClick={handleSearchSubmit}
                className="px-4 py-2 bg-gradient-to-r from-[#5C27FE] to-[#AE48FF] text-white text-xs font-semibold rounded-lg shadow-md hover:from-[#AE48FF] hover:to-[#5C27FE] transition duration-300 focus:outline-none focus:ring-2 focus:ring-purple-400"
              >
                {t('Send')}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between px-3 py-1 border-t border-indigo-600 text-xs flex-shrink-0">
            <button onClick={exportChat} className="text-white hover:underline">
              📤 {t('Export Chat')}
            </button>
            <button
              onClick={handleToggle}
              className="text-red-400 text-lg hover:text-red-600 focus:outline-none"
              aria-label={t('Close Chat')}
            >
              ❌
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Chatbot;
