"use client";

import { Loader2, XCircle } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button, buttonVariants } from "./ui/button";
import { useEffect, useState } from "react";
import axios from "axios";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";

interface VerifyEmailProps {
  token: string;
}

const VerifyEmail = ({ token }: VerifyEmailProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [errorState, setError] = useState(false);
  const [data, setData] = useState<any>();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // const data = await axios.post(
        //   `${process.env.NEXT_PUBLIC_SERVER_URL}/api2/user/verify-email`,
        //   token
        // );
        const data = await axios.post(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api2/user/verify-email/${token}`,
        );
        setData(data.data);

        if(data.data.status){
          toast.success("Email Verified and 25 Credits added to your account")
          // router.push('/sign-in')
        }
      } catch (error: any) {
        setError(true);
        toast.error("Verification Failed, Please try again")
        console.error(error.message);
      }

      setLoading(false);
    };

    fetchData();
    
  }, []);

  const handleSignIn = async () => {
    const currentPath = window.location.pathname + window.location.search; // Include the search parameters
    // await signOut();
    router.push(`/sign-in?from=${encodeURIComponent(currentPath)}`);
  };



  if (loading) {
    return (
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="spin-animation h-8 w-8 text-zinc-300" />
        <h3 className="font-semibold text-xl">Verifying...</h3>
        <p className="text-muted-foreground text-sm">
          This won&apos;t take long.
        </p>
      </div>
    );
  }
  if (errorState) {
    return (
      <div className="flex flex-col items-center gap-2">
        <XCircle className="h-8 w-8 text-red-600" />
        <h3 className="font-semibold text-xl">There was a problem</h3>
        <p className="text-muted-foreground text-sm">
          This token is not valid or might be expired. Please try again.
        </p>
      </div>
    );
  }

  if (data?.status) {
    return (
      <div className="flex h-full flex-col items-center justify-center">
        <div className="relative mb-4 h-60 w-60 text-muted-foreground">
          <Image src="/mailly.png" fill alt="the email was sent" />
        </div>

        <h3 className="font-semibold text-2xl">You&apos;re all set!</h3>
        <p className="text-muted-foreground text-center mt-1">
          Thank you for verifying your email.
        </p>
        <Button className={buttonVariants({ className: "mt-4" })} onClick={handleSignIn}>
          Sign in
        </Button>
      </div>
    );
  }
};

export default VerifyEmail;
