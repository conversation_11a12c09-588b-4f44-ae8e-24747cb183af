"use client";

import { User } from "../server/payload-types";
import React, { useEffect, useState } from "react";
import { Button } from "./ui/button";
import Link from "next/link";

const GetStarted = () => {
  const [user, setUser] = useState<User | null>(null);
  useEffect(() => {

    // const fetchUserData = async () => {
    //   try {
    //     const res = await fetch(
    //       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`
    //     );
    //     const data = await res.json();
    //     if (data) {
    //       setUser(data?.user);
    //     }
    //   } catch (error) {
    //     console.error("Error fetching user data:", error);
    //   }
    // };

    //----------------- Fetching Logged inUser with custom endpoint -----------------
    const fetchUserData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,              
          {
            method: "GET",
          }
        );
        if (!response.ok) {
           throw new Error("Network response was not ok");
        }
        const data = await response.json();                                    
        setUser(data.data);
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };
    fetchUserData();
  }, []);

  return (
    <>
      <Link href={!user ? "/sign-in" : "/ai-apps"}>
        <Button
          size="sm"
          className=" py-7 px-5 text-xs lg:text-lg lg:mr-6 font-extrabold text-primary-muted bg-white text-indigo-600 rounded z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none"
        >
          GET STARTED
        </Button>
      </Link>
    </>
  );
};

export default GetStarted;
