"use client";

import { TQ<PERSON>yValidator } from "@/lib/validators/query-validator";
import { Product } from "@/server/payload-types";
import Link from "next/link";
import ProductListing from "./ProductListing";
import { BackgroundGradient } from "./ui/background-gradient";
import { useEffect, useState } from "react";
import axios from "axios";

interface ProductReelProps {
  title: string;
  subtitle?: string;
  href?: string;
  query: TQueryValidator;
}

const FALLBACK_LIMIT = 4;

const RentProduct = (props: ProductReelProps) => {
  const [data, setData] = useState<any>();
  const [isLoading, setIsLoading] = useState(true);

  const { title, subtitle, href, query } = props;

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const { data } = await axios.get(`/api2/products`);
        setData(data);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };
  
    fetchProducts();
  }, []);
  
  // const { data: queryResults, isLoading } =
  //   trpc.getInfiniteProducts.useInfiniteQuery(
  //     {
  //       limit: query.limit ?? FALLBACK_LIMIT,
  //       query,
  //     },
  //     {
  //       getNextPageParam: (lastPage) => lastPage.nextPage,
  //     }
  //   );

  const products = data?.pages.flatMap((page) => page.items);

  let map: (Product | null)[] = [];
  if (products && products.length) {
    map = products;
  } else if (isLoading) {
    map = new Array<null>(query.limit ?? FALLBACK_LIMIT).fill(null);
  }

  return (
    <section className="items-center justify-center">
      <div className="flex items-center justify-center mb-4">
        <div className="max-w-2xl lg:max-w-4xl lg:px-0">
          {title ? (
            <h1 className="text-2xl font-bold sm:text-3xl">{title}</h1>
          ) : null}
          {subtitle ? (
            <p className="mt-2 text-sm text-muted-foreground">{subtitle}</p>
          ) : null}
        </div>
      </div>

      <div className="relative flex items-center justify-center">
        <div className="mt-6 flex items-center justify-center w-full">
          <div className="w-full grid grid-cols-2 gap-x-4 gap-y-10 sm:gap-x-6 md:grid-cols-4 md:gap-y-10 ">
            {map.map((product, i) => (
              <ProductListing
                key={`product-${i}`}
                product={product}
                index={i}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default RentProduct;
