"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import axios from "axios";
import { useRouter } from "next/navigation";
import {
  ForgotPasswordValidator,
  TForgotPasswordValidator,
} from "@/lib/validators/forgot-password-validator";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

const ForgotPassForm = () => {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TForgotPasswordValidator>({
    resolver: zodResolver(ForgotPasswordValidator),
  });

  const onSubmit = async ({ email }: TForgotPasswordValidator) => {
    setIsLoading(true);
    try {
      const { data } = await axios.post("/api2/user/forgot-password", {
        email,
      });
      setIsLoading(false);
      if (data?.success) {
        toast.success("Password reset email sent. Please check your inbox.");
        // router.push("/sign-in");
      } else {
        toast.error("Failed to send password reset email. Please try again.");
      }
    } catch (e) {
      setIsLoading(false);
      toast.error("Something went wrong. Please try again.");
    }
  };

  return (
    <div className="grid gap-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1 py-2">
            <Label htmlFor="email">Email</Label>
            <Input
              {...register("email")}
              className={cn({
                "focus-visible:ring-red-500": errors.email,
              })}
              placeholder="<EMAIL>"
            />
            {errors?.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          <Button
            className="flex bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 w-full text-white rounded-md h-10 font-medium"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              "Send Reset Link"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ForgotPassForm;