import Link from 'next/link';
import { useState } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

const Modal = ({ isOpen, onClose, onAccept }: ModalProps) => {
  const [isChecked, setIsChecked] = useState(false);

  if (!isOpen) return null;

  const handleAccept = () => {
    if (isChecked) {
      onAccept();
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-indigo-800 p-6 rounded-lg shadow-lg">
        <h2 className="text-xl font-semibold mb-4">Terms and Conditions</h2>
        <p className="mb-4">
          Please read and accept our{' '}
          <Link href="/terms-and-conditions" target="_blank" className="text-muted-foreground underline">
            Terms and Conditions
          </Link>{' '}
          to continue.
        </p>
        <label className="flex items-center mb-4">
          <input
            type="checkbox"
            className="form-checkbox"
            checked={isChecked}
            onChange={() => setIsChecked(!isChecked)}
          />
          <span className="ml-2 text-sm text-white">I accept the terms and conditions</span>
        </label>
        <div className="flex justify-end space-x-4">
          <button
            className="px-4 py-2 bg-white rounded text-indigo-600"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 bg-indigo-600 text-white rounded"
            onClick={handleAccept}
            disabled={!isChecked}
          >
            Accept and Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default Modal;
