"use client";

import React, { useEffect, useState, useRef } from "react";
import dynamic from "next/dynamic";
import axios from "axios";
import { debounce } from "lodash";
import { TQueryValidator } from "@/lib/validators/query-validator";
import { Rapp, User } from "@/server/payload-types";
import { Loader2 } from "lucide-react";

const RentListing = dynamic(() => import("./ui/rentListing"), { ssr: false });

interface RentReelProps {
    href?: string;
    query: TQueryValidator;
    sort?: string;
    model?: string;
    category?: string;
    tags?: string;
    className?: string;
    user: User | null;
    rentReelFilterCriteria: any;
    paginate?: boolean;
    itemsPerPage?: number;
}

const RentReel: React.FC<RentReelProps> = ({
    query,
    sort,
    model,
    category,
    tags,
    className,
    user,
    rentReelFilterCriteria,
    paginate = false,
    itemsPerPage = 10,
}) => {
    const [isLoading, setIsLoading] = useState(true);
    const [data, setData] = useState<{ items: Rapp[] } | null>(null);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const observer = useRef<IntersectionObserver | null>(null);
    const lastItemRef = useRef<HTMLDivElement>(null);
    const [noMoreData, setNoMoreData] = useState(false);

    const fetchRapps = async (queryParams, pageToFetch, limit) => {
      
        setIsLoading(true);
    
        try {
            const { data } = await axios.post(`/api2/rapps`, {
                query: { ...queryParams, page: pageToFetch, limit: limit },
            });
    
            if (data && data.items) {
                setData((prevData) => {
                    if (pageToFetch === 1) {
                        return { items: data.items };
                    } else {
                        const existingItems = prevData?.items || [];
                        const newItems = data.items.filter(newItem =>
                            !existingItems.some(existingItem => existingItem.id === newItem.id)
                        );
                        return { items: [...existingItems, ...newItems] };
                    }
                });
    
                setHasMore(data.items.length === limit);
                setNoMoreData(data.items.length < limit);
            } else {
                setHasMore(false);
                setNoMoreData(true);
            }
        } catch (error) {
            console.error("Error fetching rental items:", error);
            setHasMore(false);
            setNoMoreData(true);
        } finally {
            setIsLoading(false);
        }
    };
    
    useEffect(() => {
        setData(null);
        setPage(1);
        setHasMore(true);
        setNoMoreData(false);
    }, [query, sort, model, category, tags, rentReelFilterCriteria]);

    useEffect(() => {
        const queryParams = {
            ...query,
            sort,
            model,
            category,
            tags,
            ...rentReelFilterCriteria,
            generationType: rentReelFilterCriteria.generationType?.length > 0
            ? rentReelFilterCriteria.generationType.join(",") // Convert array to string
            : undefined, // Exclude if empty
        };


        fetchRapps(queryParams, page, itemsPerPage);
    }, [query, sort, model, category, tags, page, rentReelFilterCriteria, itemsPerPage]);

    useEffect(() => {
        if (!paginate) return;

        if (isLoading) return;
        if (!hasMore) return;

        const debouncedSetPage = debounce((newPage) => {
            setPage(newPage);
            fetchRapps({ ...query, sort, model, category, tags, ...rentReelFilterCriteria }, newPage, itemsPerPage);
        }, 200);

        observer.current = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
                debouncedSetPage(page + 1);
            }
        });

        if (lastItemRef.current) {
            observer.current.observe(lastItemRef.current);
        }

        return () => {
            if (observer.current) {
                observer.current.disconnect();
            }
            debouncedSetPage.cancel();
        };
    }, [isLoading, hasMore, paginate, query, sort, model, category, tags, rentReelFilterCriteria, itemsPerPage, page]);

    const classes = className;
    const rentProducts = data?.items || [];

    if (isLoading && page === 1) {
        return (
            <div className="flex justify-center items-center min-h-[200px] ">
                <p>Loading...</p>
            </div>
        );
    }

    if (!rentProducts.length && !isLoading) {
        return (
            <div className="text-center text-white py-6">
                No items available, try another filter.
            </div>
        );
    }

    return (
        <>
            {rentProducts.map((rentProduct, i) => (
                <React.Suspense
                    key={`product-placeholder-${i}`}
                    fallback={<div className="bg-gray-200 animate-pulse rounded-md h-48 w-full lg:w-72 inline-block mr-2" />}
                >
                    <RentListing
                        key={`product-${i}`}
                        rentproduct={rentProduct}
                        index={i}
                        className={className}
                        user={user}
                    />
                </React.Suspense>
            ))}
            
            {paginate && hasMore && isLoading && (
               <div className="col-span-full"> 
               <div className="flex justify-center items-center py-4">
                 <Loader2 className="h-8 w-8 spin-animation font-normal" />
               </div>
             </div>
            )}
            {paginate && hasMore && !isLoading && !noMoreData && (
                <div ref={lastItemRef} className="h-1" />
            )}
            {paginate && noMoreData &&(
                <div className="col-span-full">
                <div className="flex justify-center items-center py-4">
                    <p>No more app to fetch.</p>
                </div>
                </div>
            )}
        </>
    );
};

export default React.memo(RentReel);