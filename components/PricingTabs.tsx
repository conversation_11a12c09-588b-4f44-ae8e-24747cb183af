"use client";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useRouter } from "next/navigation";
import { Icons } from "@/components/Icons";
import TiltCard from "@/components/TiltCard";

interface Plan {
  id: string;
  packageName: string;
  numberOfCoins: number;
  discount: number;
  benefits: string;
  priceInDollars: number;
  tax: number;
}
interface TopUp {
  id: string;
  packageName: string;
  numberOfCoins: number;
  discount: number;
  benefits: string;
  priceInDollars: number;
  tax: number;
}
declare global {
  interface Window {
    Razorpay: any; // Or a more specific type if available
  }
}

const PricingTabs: React.FC = () => {
  const [user, setUser] = useState<any>();
  const [loading, setLoading] = useState<boolean>(true);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [topUp, setTopUp] = useState<TopUp[]>([]);
  const router = useRouter();

  // ------------------- Fetching the plans and top-up details --------------
  useEffect(() => {
    const fetchPrices = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/prices`
        );
        const data = await response.json();

        if (!data) {
          setLoading(false);
        }

        const isPlans = data.docs.filter((card: any) => card.isTopUp === false);
        setPlans(isPlans);

        const isTopUp = data.docs.filter((card: any) => card.isTopUp === true);
        setTopUp(isTopUp);

        setLoading(false);
      } catch (error) {
        console.error("Error fetching prices:", error);
      } finally {
        setLoading(false); // Ensure loading is turned off regardless of success or failure
      }
    };

    fetchPrices();
  }, []);

  //----------------- Fetching Logged in User -----------------
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`
        );
        const data = await response.json();

        if (!data) {
          setLoading(false);
        }
        setUser(data?.user);
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchUserData();
  }, []);

  if (loading)
    return (
      <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50 ">
        <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
      </div>
    );

  const handlePayClick = (topUp) => {
    if (!user) {
      toast.error("Please login to make a payment");
    } else {
      const totalCoins = Math.round(
        topUp.numberOfCoins + (topUp.numberOfCoins * topUp.discount) / 100
      );
      const totalBasePrice = topUp.numberOfCoins;
      const totalTax = ((topUp?.tax ? topUp?.tax : 18) / 100) * totalBasePrice; // configure tax
      const total = totalBasePrice + totalTax;
      const receivedCoins = Math.round(
        topUp.numberOfCoins + (topUp.numberOfCoins * topUp.discount) / 100
      );

      const params = new URLSearchParams({
        packageId: topUp.id,
        packageName: topUp?.packageName,
        packageAmount: totalBasePrice.toString(),
        packageTax: totalTax.toFixed(2).toString(),
        totalPaidAmount: total.toFixed(2).toString(),
        receivedCoins: receivedCoins.toString(),
        userName: user.user_name,
        userEmail: user.email,
      });
      router.push(`/checkout?${params.toString()}`);
    }
  };

  return (
    <Tabs defaultValue="account" className="w-11/12 mx-auto md:w-full">
      <TabsList className="grid w-full md:w-[50%] mx-auto grid-cols-2 bg-indigo-500 text-white">
        <TabsTrigger value="account" className="font-bold">
          Plans
        </TabsTrigger>
        <TabsTrigger value="password">Top-ups (Rupees)</TabsTrigger>
      </TabsList>

      {/* // ---------------------- plan details ----------------- */}
      <TabsContent value="account">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full md:w-11/12 mx-auto place-items-center bg-gradient-to-br px-0 md:px-4 py-12 text-slate-900">
          {plans.map((plan) => (
            <TiltCard key={plan.id} plan={plan} user={user} />
          ))}
        </div>
      </TabsContent>

      {/* // ---------------------- Top-up details ----------------- */}
      <TabsContent value="password">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 w-full md:w-[80%] mx-auto my-12 border-white">
          {topUp.map((topUp, index) => (
            <div
              className="flex flex-col p-3 gap-2 px-4 rounded-lg bg-white text-black"
              key={index}
            >
              <div className={`flex justify-between items-center`}>
                <p>
                  <span className="text-4xl font-extrabold ">
                    ₹{topUp.numberOfCoins}
                  </span>
                </p>
                <p className="flex gap-1">
                  Get{" "}
                  <span className="font-bold flex gap-[2px] items-center">
                    {" "}
                    {topUp.numberOfCoins}{" "}
                    <img
                      src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                      alt="Coin"
                      style={{ width: "20px", height: "20px" }}
                    />
                  </span>
                </p>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handlePayClick(topUp)}
                  className="border bg-indigo-500 text-uration-200 hover:bg-indigo-700 py-1 px-16 rounded-md text-white font-bold w-full md:w-auto"
                >
                  Pay
                </button>
              </div>
            </div>
          ))}
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default PricingTabs;
