// components/NotificationItem.tsx
"use client";

import { Notification } from "../../app/hooks/useNotifications";
import Link from "next/link";

const NotificationList = ({
  notif,
  onClick,
  key,
}: {
  notif: Notification;
  onClick?: () => void;
  key?: string;
}) => { 
  return (
    <div
      key={notif?.id}
      onClick={onClick}
      className={`border-b px-4 py-3 hover:bg-indigo-600 hover:shadow-md cursor-pointer transition-all duration-200 ${
        notif?.read === true ? "bg-indigo-600 text-white" : "bg-indigo-700 text-white"
      }`}
    >
      <Link href={notif.link || "#"} className="flex items-start gap-3">
        <div className="flex-shrink-0 pt-1">
          {!notif.read && (
            <span className="h-3 w-3 block bg-green-400 rounded-full animate-pulse" />
          )}
        </div>
        <div className="flex-grow">
          <div className={`text-sm font-medium line-clamp-3 ${notif?.read ? "text-white" : "text-white"}`}>
            {notif.message}
          </div>
          <div className={`text-xs mt-1 ${notif?.read ? "text-indigo-600" : "text-indigo-200"}`}>
            {new Date(notif.createdAt).toLocaleString()}
          </div>
        </div>
      </Link>
    </div>
  );
};

export default NotificationList;