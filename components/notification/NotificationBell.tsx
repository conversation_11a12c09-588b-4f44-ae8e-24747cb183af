"use client";
import { useEffect, useState } from "react";
import <PERSON> from "next/link";
import Pusher from "pusher-js";
import NotificationList from "./notificationList";
import { BellRing } from "lucide-react"; // Import Lucide Bell icon
import { useNotifications } from "@/app/hooks/useNotifications";

interface Notification {
  id: string;
  message: string;
  link?: string;
  read: boolean;
  createdAt: string;
}

const mockNotifications: Notification[] = [
  {
    id: "1",
    message: "Your order has been shipped!",
    link: "/orders/123",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "2",
    message: "New comment on your post.",
    link: "/posts/456",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "1",
    message: "Your order has been shipped!",
    link: "/orders/123",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "2",
    message: "New comment on your post.",
    link: "/posts/456",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "1",
    message: "Your order has been shipped!",
    link: "/orders/123",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "2",
    message: "New comment on your post.",
    link: "/posts/456",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "1",
    message: "Your order has been shipped!",
    link: "/orders/123",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "2",
    message: "New comment on your post.",
    link: "/posts/456",
    read: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: "3",
    message: "Welcome to our platform!",
    read: true,
    createdAt: new Date().toISOString(),
  },
];

const NotificationBell = () => {
  const { unreadCount, setUnreadCount } = useNotifications();
  const [count, setCount] = useState(unreadCount);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [userId, setUserId] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const fetchNotifications = async () => {
    if (!userId) return;

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/notifications/my-notifications?limit=0`
      );
      if (!res.ok) throw new Error("Notification fetch failed");

      const data = await res.json();
      const docs = Array.isArray(data?.docs) ? data.docs : [];
      setNotifications(docs);
      setCount(docs.filter((n) => !n.read).length);
    } catch (err) {
      console.error("❌ Error fetching notifications:", err);
    }
  };
  const fetchUserData = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,
        {
          method: "GET",
        }
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      setUserId(data.data.id);
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  const markAsRead = async (id: string) => {
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/notifications/markRead/${id}?limit=0`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          // body: JSON.stringify({ read: true }),
          credentials: "include",
        }
      );

      if (!res.ok) throw new Error("Failed to mark as read");

      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, read: true } : n))
      );
      setCount((prev) => Math.max(prev - 1, 0));
    } catch (err) {
      console.error("❌ Error marking notification as read:", err);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);
  useEffect(() => {
    if (!userId) {
      console.warn("No userId found, skipping Pusher subscription");
      return;
    }
    fetchNotifications();
    const timeout = setTimeout(() => {
      // Check for required env vars
      if (
        !process.env.NEXT_PUBLIC_PUSHER_KEY ||
        !process.env.NEXT_PUBLIC_PUSHER_CLUSTER
      ) {
        console.error("Missing Pusher env vars");
        return;
      }

      // Log for debugging
      const channelName = `my-channel-${userId}`;
      // console.log("📡 Subscribing to:", channelName);

      // Initialize Pusher
      const pusher = new Pusher(process.env.NEXT_PUBLIC_PUSHER_KEY!, {
        cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
        enabledTransports: ["ws", "wss"],
      });

      // Subscribe to user-specific channel
      const channel = pusher.subscribe(channelName);

      // Bind to event
      channel.bind("new-notification", (data: any) => {
        // console.log("📨 Notification received:", data);
        setNotifications((prev) => [data, ...prev]);
        setCount((prev) => prev + 1);
      });

      // Cleanup
      return () => {
        channel.unbind_all();
        channel.unsubscribe();
      };
    }, 100);
    return () => clearTimeout(timeout);
  }, [userId]);

  return (
    <div
      className="relative"
      onMouseEnter={() => setDropdownOpen(true)}
      onMouseLeave={() => setDropdownOpen(false)}
    >
      <Link
        href="/notifications"
        className="relative flex items-center justify-center"
      >
        <div className="p-2 rounded-full text-white hover:bg-gray-100  hover:text-gray-900 transition-colors duration-200">
          <BellRing className="h-5 w-5 " strokeWidth={1.5} />
          {count > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white px-1.5 py-0.5 rounded-full text-xs font-medium min-w-[18px] flex items-center justify-center">
              {/* {count} */}
              {count >= 10 ? "10+" : count}
            </span>
          )}
        </div>
      </Link>

      {dropdownOpen && (
        <div className="absolute right-0 w-80 bg-white shadow-xl rounded-lg z-50 max-h-96 overflow-y-auto border border-gray-100">
          <div className="sticky top-0 bg-indigo-600 px-4 py-2 border-b border-gray-100 font-medium text-white z-10">
            Notifications
          </div>
          {notifications.map((notif) => (
            <NotificationList
              key={notif.id}
              notif={notif}
              onClick={() => markAsRead(notif.id)}
            />
          ))}
          {/* )} */}
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
