"use client";
import { cn } from "@/lib/utils";
import { Spacelist } from "@/app/(mainapp)/(profile)/data/spacelists";
import { useState , useEffect } from "react";
import { usePathname } from "next/navigation";
import { Button } from "./ui/button";
import Link from "next/link";
import { useAuth } from "@/app/hooks/use-auth";

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  playlists: Spacelist[];
}

export default function ProfileSidebar({ className, playlists }: SidebarProps) {
  const { signOut } = useAuth();
  const [showButtons, setShowButtons] = useState(false);
  const pathname = usePathname();
  const [color, setColor] = useState("");
  const colors = ["#6EACDA", "#E3A5C7", "#E2E2B6", "#088395", "#824D74"];

  const getRandomColor = () => {
    const randomIndex = Math.floor(Math.random() * colors.length);
    return colors[randomIndex];
  };
  useEffect(() => {
    setColor(getRandomColor());
  }, []);

  const toggleButtons = () => {
    setShowButtons(!showButtons);
  };
  return (
    <>
    {/* user profile sidebar */}
      <div
        className={cn(
          `pb-12  ${className} ${
            pathname === "/profile" ? "block " : " hidden lg:block"
          }`
        )}
      >
        <h1
          className={`text-xl mt-4 md:mt-14 mb-2 pl-4 font-semibold ${
            pathname === "/profile" ? "block lg:hidden" : ""
          } `}
        >
         {pathname === "/profile" ? "Profile" : "Dashboard"}
        </h1>

        <div
          className={`space-y-4 lg:block ${
            pathname === "profile" ? "block bg-card " : " "
          }`}
        >
          <div className="px-3 md:py-6"> 
            <div className="space-y-1">
            <Link href={"/profile"}>
              <Button variant="ghost" className="w-full justify-start hover:bg-popover">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 h-4 w-4"
                >
                  <circle cx="12" cy="12" r="10" />
                  <polygon points="10 8 16 12 10 16 10 8" />
                </svg>
                Profile
              </Button>
              </Link>

              {pathname === "/profile" ?
              <Link href="/dashboard">
              <Button variant="ghost" className="w-full justify-start my-2 hover:bg-popover">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 h-4 w-4"
                >
                  <rect width="7" height="7" x="3" y="3" rx="1" />
                  <rect width="7" height="7" x="14" y="3" rx="1" />
                  <rect width="7" height="7" x="14" y="14" rx="1" />
                  <rect width="7" height="7" x="3" y="14" rx="1" />
                </svg>
                Dashboard
              </Button>
              </Link> : ""}

              {pathname === "/dashboard" && (
  <>
    <Link href="/marketplace">
      <Button variant="ghost" className="w-full justify-start my-2 hover:bg-popover">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mr-2 h-4 w-4"
        >
          <rect width="7" height="7" x="3" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="14" rx="1" />
          <rect width="7" height="7" x="3" y="14" rx="1" />
        </svg>
        All Products
      </Button>
    </Link>
    <Link href="/bounties" target="_blank">
      <Button variant="ghost" className="w-full justify-start  hover:bg-popover">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mr-2 h-4 w-4"
        >
          <rect width="7" height="7" x="3" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="14" rx="1" />
          <rect width="7" height="7" x="3" y="14" rx="1" />
        </svg>
        All Bounties
      </Button>
    </Link>
    <Link href="/blogs" target="_blank">
      <Button variant="ghost" className="w-full justify-start my-2 hover:bg-popover">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mr-2 h-4 w-4"
        >
          <rect width="7" height="7" x="3" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="3" rx="1" />
          <rect width="7" height="7" x="14" y="14" rx="1" />
          <rect width="7" height="7" x="3" y="14" rx="1" />
        </svg>
        All Blogs
      </Button>
    </Link>
  </>
)}

              
              <Button variant="ghost" className="w-full justify-start hover:bg-popover" onClick={signOut}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 h-4 w-4"
                >
                  <path d="M4.9 19.1C1 15.2 1 8.8 4.9 4.9" />
                  <path d="M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5" />
                  <circle cx="12" cy="12" r="2" />
                  <path d="M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5" />
                  <path d="M19.1 4.9C23 8.8 23 15.1 19.1 19" />
                </svg>
                Log out
              </Button>
            </div>
          </div>

          {/* <div className="py-2">
            <h2 className="relative px-7 text-lg font-semibold tracking-tight">
              Activity
            </h2>
            <ScrollArea className="h-[300px] px-1">
              <div className="space-y-1 p-2">
                {playlists?.map((playlist, i) => (
                  <Button
                    key={`${playlist}-${i}`}
                    variant="ghost"
                    className="w-full justify-start font-normal"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2 h-4 w-4"
                    >
                      <path d="M21 15V6" />
                      <path d="M18.5 18a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z" />
                      <path d="M12 12H3" />
                      <path d="M16 6H3" />
                      <path d="M12 18H3" />
                    </svg>
                    {playlist}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </div> */}
        </div>
      </div>
    </>
  );
}
