"use client";
import React, { useRef } from "react";
import {motion, useMotionTemplate, useMotionValue, useSpring} from "framer-motion";
import { Check } from "lucide-react";
import Script from 'next/script';
import { toast } from 'sonner'
import coinImage from '../public/img/coin-png.png'
import { useRouter } from "next/navigation";



const ROTATION_RANGE = 32.5;
const HALF_ROTATION_RANGE = 32.5 / 2;

interface Plan {
    id: string;
    packageName: string;
    numberOfCoins: number;
    discount: number;
    benefits: string;
    priceInDollars: number;
    tax:number;
  }

const TiltCard = ({ plan, user }: { plan: Plan, user: string }) => {
  const ref = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const xSpring = useSpring(x);
  const ySpring = useSpring(y);

  const transform = useMotionTemplate`rotateX(${xSpring}deg) rotateY(${ySpring}deg)`;

  const handleMouseMove = (e: any) => {
    if (!ref.current) return [0, 0];

    const rect = ref.current.getBoundingClientRect();

    const width = rect.width;
    const height = rect.height;

    const mouseX = (e.clientX - rect.left) * ROTATION_RANGE;
    const mouseY = (e.clientY - rect.top) * ROTATION_RANGE;

    const rX = (mouseY / height - HALF_ROTATION_RANGE) * -1;
    const rY = mouseX / width - HALF_ROTATION_RANGE;

    x.set(rX);
    y.set(rY);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  const handlePayClick = () => {
    // if (!user) 
    // {
    //   toast.error("Please login to make a payment");
    // } 
    if (!user) {
      if (plan.packageName === 'Enterprise') {
        router.push('/services#write-to-us');
      } else {
        toast.error("Please login to make a payment");
      }
    }
    else {
      // Navigate to the checkout page with query parameters
      // if (plan.id === '6672b7713e006fe4c6411256') {
      //   router.push('/services#write-to-us');
      // }
      if (plan.packageName === 'Enterprise') {
        router.push('/services#write-to-us');
      }
      else{
        const totalCoins = Math.round(plan.numberOfCoins + (plan.numberOfCoins * plan.discount)/100);
        const totalBasePrice = plan.numberOfCoins;
        const totalTax = (((plan?.tax) ? (plan?.tax) : 18) / 100) * totalBasePrice; // configure tax
        const total = totalBasePrice + totalTax;
        const receivedCoins = Math.round(plan.numberOfCoins + (plan.numberOfCoins * plan.discount)/100)

      
        const params = new URLSearchParams({
          packageId: plan.id.toString(),
          packageName: plan.packageName,
          packageAmount: totalBasePrice.toString(),
          packageTax: totalTax.toFixed(2).toString(),
          totalPaidAmount: total.toFixed(2).toString(),
          receivedCoins: receivedCoins.toString(),
        });
        router.push(`/checkout?${params.toString()}`);
      }
      
    }
  };

  return (
    <>
      <Script
        id="razorpay-checkout-js"
        src="https://checkout.razorpay.com/v1/checkout.js"
      />
      <motion.div
        ref={ref}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        style={{
          transformStyle: "preserve-3d",
          transform,
        }}
        className="relative h-[520px] md:h-[490px] w-full md:w-96 rounded-xl bg-gradient-to-br from-indigo-300 to-violet-300"
      >
        <div
          style={{
            transform: "translateZ(75px)",
            transformStyle: "preserve-3d",
          }}
          className="absolute inset-4 grid p-6 rounded-xl bg-white shadow-lg"
        >
          <div
            style={{
              transform: "translateZ(50px)",
            }}
            className="relative"
          >
            <p className="text-2xl font-bold mb-3">{plan.packageName}</p>
            <div className={`flex justify-between items-center ${plan.packageName === 'Enterprise' ? 'hidden' : ''}`}>
              <h2 className={`text-gray-700`}>
                <span className={`text-5xl font-extrabold text-black`}>₹{plan.numberOfCoins}</span> 
                {/* ${plan.priceInDollars} */}
              </h2>
              {plan.discount === 0 ? ( <div></div> ) : (
                <div className="bg-indigo-500 text-white p-2 rounded-full text-center text-sm">
                  <p>{plan.discount}% Extra</p>
                </div>
              )}
            </div>
            <div className={`flex gap-1 items-center ${plan.packageName === 'Enterprise' ? 'hidden' : ''}`}>
              <p>Get {Math.round(plan.numberOfCoins + (plan.numberOfCoins * plan.discount)/100)}</p>
              <img src={coinImage.src} alt="Coin" style={{ width: '20px', height: '20px' }}  loading="lazy"/>
            </div>
            <div className={`md:mt-8 ${plan.packageName === 'Enterprise' ? 'mt-12' : 'mt-4'}`}>
              <div className="flex flex-col gap-1">
                {plan.benefits
                  .split('",')
                  .map((benefit, index) => benefit && (
                    <div key={index} className="flex items-start gap-3">
                      <Check size={20} className="text-green-500 w-5 h-5" />
                      <p className="text-left">{benefit}</p>
                    </div>
                  ))}
              </div>
            </div>
            {/* <button
              onClick={() => 
                {
                  user ? (displayRazorpay({ pricePackageId: plan.id }))
                  : toast.error("Please login to make a payment")
                }
              }
              className={`border absolute bottom-11 bg-indigo-500 text-uration-200 hover:bg-indigo-700 w-full py-2 rounded-md text-white font-bold ${plan.packageName === 'Enterprise' ? 'hidden' : ''}`}
            >
              Pay with Razorpay
            </button>
            <button
              onClick={() => 
                {
                  user ? (handlePhonePe({ pricePackageId: plan.id }))
                  : toast.error("Please login to make a payment")
                }
              }
              className="border absolute bottom-0 bg-indigo-500 text-uration-200 hover:bg-indigo-700 w-full py-2 rounded-md text-white font-bold"
            >
              {plan.packageName === 'Enterprise' ? "Contact sales" : "Pay with PhonePe"}
            </button> */}

            <button
              onClick={handlePayClick}
              className="border absolute bottom-0 bg-indigo-500 text-uration-200 hover:bg-indigo-700 w-full py-2 rounded-md text-white font-bold"
            >
              {plan.packageName === 'Enterprise' ? "Contact Sales" : "Pay"}
            </button>
          </div>
        </div>
      </motion.div>

    </>
  );
};

export default TiltCard;