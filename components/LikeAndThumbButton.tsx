"use client";
import { useState, useEffect } from "react";
import { Heart } from "lucide-react";
import { User } from "@/server/payload-types";
import { toast } from "sonner";

interface LikeAndThumbButtonProps {
  userPortfolio: User;
  likes: number;
  loginUser: User;
}

const LikeAndThumbButton: React.FC<LikeAndThumbButtonProps> = ({
  userPortfolio,
  likes,
  loginUser,
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [isLike, setIsLike] = useState(false);
  const [likesCount, setLikesCount] = useState(likes);

  useEffect(() => {
    const fetchIsLike = () => {
      if (!loginUser) {
        setIsLike(false);
        setLoading(false);
      }
      const likeUsers = userPortfolio?.likes?.map((likeUser: any) => likeUser?.user?.id) ?? [];
      setIsLike(likeUsers?.includes(loginUser?.id));
      setLoading(false);
    };

    fetchIsLike();
  }, [userPortfolio?.id]);

  useEffect(() => {}, [isLike]);

  const handleLike = async () => {
    if (!loginUser) {
      toast.error("Please login to like this user");
      return;
    }

    setLoading(true);
    
    try {
      const result = await fetch(
        `/api/users/likes-details/${userPortfolio?.id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
        }
      );

      if (!result.ok) {
        console.log("Server error. Are you online?");
        setLoading(false);
        return;
      }
      const body = await result.json();
      
      setIsLike(body.isLikeUpdated);
      setLikesCount(body.likes);
      setLoading(false);
    } catch (error) {
      console.error("Error during like request:", error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="absolute top-[120px] sm:top-[110px] -right-0 backdrop-blur-3xl bg-white/30 flex items-center gap-2 px-2 py-1 rounded-xl flex ">
      <div className="flex gap-1 justify-center items-center">
        <Heart
          className={`cursor-pointer ${
            isLike ? "fill-red-600 text-red-600 " : "text-white"
          } ${loading ? "animate-pulse text-red-500 fill-red-500" : ""}`}
          onClick={handleLike}
        />
        <div className="text-muted">{likesCount}</div>
      </div>
    </div>
  );
};

export default LikeAndThumbButton;