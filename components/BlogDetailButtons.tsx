"use client";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
  } from "@/components/ui/dialog";
  import { X } from "lucide-react";
  import { User } from "@/server/payload-types";
  import { toast } from "sonner";
  import { useSearchParams, useRouter } from "next/navigation";

interface BlogDetailButtonsProps {
    user: User;
    userId: string;
    blogId: string;
}

const BlogDetailButtons = ({ user, userId, blogId}:BlogDetailButtonsProps) => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const [isDialogOpen, setISDialogOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    async function handleDelete(Id: string, collectionName: string) {
        try {
            setLoading(true)
          const response = await fetch(`/api/${collectionName}/${Id}`, {
            method: "DELETE",
          });
      
          if (response.ok) {
            toast.success(`Blog deleted successfully.`);
            setLoading(false);
            setISDialogOpen(false)
            router.push("/dashboard");
            // Optionally, you can refresh or update the product list here
            // window.location.reload(); // Reload the page or re-fetch products
          } else {
            toast.error(`Failed to delete the blog. Please try again.`);
          }
        } catch (error) {
          console.error(`Error deleting blog:`, error);
          toast.error(`Failed to delete the blog. Please try again.`);
        }
      }
    
  return (
    <div>
      {user && user.id === userId && (
        <div className="flex gap-1">
          <Link href={`/dashboard/create/blog?Id=${blogId}`}>
            <Button
              variant="outline"
              className=" flex bg-indigo-700 px-2  hover:bg-white hover:text-indigo-900 "
            >
              Edit
            </Button>
          </Link>
          <Dialog open={isDialogOpen} onOpenChange={setISDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="bg-indigo-900 px-2 hover:bg-white hover:text-indigo-900 "
              >
                Delete
              </Button>
            </DialogTrigger>
            <DialogContent>
              <div>
                <button
                  onClick={() => setISDialogOpen(false)}
                  className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100"
                >
                  <X size={15} />
                </button>
                <h1 className="text-center text-bold py-2 text-lg md:text-2xl">
                  {" "}
                  Are you sure want to delete blog ?{" "}
                </h1>
                <div className="flex justify-center gap-2 mt-3">
                  <Button
                    onClick={() => setISDialogOpen(false)}
                    variant="outline"
                    className="bg-transparent  text-white px-4 py-1 hover:bg-indigo-800"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={(e) => {
                        handleDelete(blogId, "blogs");
                    }}
                    // disabled={loading}
                    variant="outline"
                    className="bg-white text-indigo-700 px-4 py-1 hover:bg-indigo-800"
                  >
                    {loading ? "Deleting..." : "Delete"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
};

export default BlogDetailButtons;
