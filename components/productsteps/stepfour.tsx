"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import CustomDropdown from "../ui/dashboard/customdropdown";
import axios from "axios";
import Link from "next/link";
import Image from "next/image";


export const Step4 = ({ prevStep, formData, setFormData }) => {
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams(); // Use this to get query parameters
  const id = searchParams.get("Id");

  const [productCategories, setProductCategories] = useState([]);

  useEffect(() => {
    if (!formData.productCategory || productCategories.length === 0) return;

    const selectedCategory = productCategories.find(
      (cat) => cat.id === formData.productCategory
    );
    if (selectedCategory) {
      setFormData((prev) => ({
        ...prev,
        productCategoryLabel: selectedCategory.label || "Unknown Category",
      }));
    }
  }, [formData.productCategory, productCategories]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const save = async () => {
    const price = parseFloat(formData.price);
    setLoading(true);

    try {
      const productimg = formData.images
        .map((item) => {
          if (item.image && item.image.id) {
            return { image: item.image.id };
          } else if (item.id) {
            return { image: item.id };
          }
          return null;
        })
        .filter(Boolean);

      const productFile_id = formData.product_files?.id;
      const productCategory_id = formData.productCategory?.id
      const dataToSend = {
        ...formData,
        price,
        product_files: productFile_id,
        images: productimg,
        productCategory: productCategory_id,
      };

      let response;
      let isUpdate = !!id;
      if (isUpdate) {
        // dataToSend.productCategory = formData.productCategory?.id;
        dataToSend.needsApproval = formData.needsApproval;
      }
      // console.log("dataToSend save",dataToSend)
      if (isUpdate) {
        response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/products/${id}?depth=0`,
          {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(dataToSend),
          }
        );
      } else {
        response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/products?depth=0`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(dataToSend),
          }
        );
      }

      if (!response.ok) throw new Error("Failed to save data");

      await response.json();

      if (isUpdate) {
        toast.success("Product Updated Successfully!");
      } else {
        toast.success("Product Created Successfully!");
      }
      localStorage.removeItem("productFormData");
      localStorage.removeItem("productFormStep");

      router.push("/dashboard/products");
      
    } catch (error) {
      console.error("api error", error);
      toast.error("An error occurred while saving data.");
    } finally {
      setLoading(false);
    }
  };

  const SubmitData = async () => {
    setIsSubmitting(true);
    setFormData((prev) => ({
      ...prev,
      needsApproval: true,
    }));

    try {
      const productimg = formData.images
        .map((item) => {
          if (item.image && item.image.id) {
            return { image: item.image.id };
          } else if (item.id) {
            return { image: item.id };
          }
          return null;
        })
        .filter(Boolean);

      const productFile_id = formData.product_files?.id;
      const productCategory_id = formData.productCategory?.id

      const dataToSend = {
        ...formData,
        price: parseFloat(formData.price),
        images: productimg,
        product_files: productFile_id,
        needsApproval: true,
        productCategory: productCategory_id,
        
      };

      let response;
      let isUpdate = !!id;
      // if (isUpdate) {
      //   dataToSend.productCategory = formData.productCategory?.id;
      // }
      // console.log("dataToSend submit",dataToSend)

      if (isUpdate) {
        response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/products/${id}?depth=0`,
          {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(dataToSend),
          }
        );
      } else {
        response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/products?depth=0`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(dataToSend),
          }
        );
      }

      if (!response.ok) throw new Error("Failed to submit data");

      await response.json();

      if (isUpdate) {
        toast.success("Product Updated Successfully and Sent for Approval!");
      } else {
        toast.success("Product Created Successfully and Sent for Approval");
      }
      localStorage.removeItem("productFormData");
      localStorage.removeItem("productFormStep");

      router.push("/dashboard/products");
    } catch (error) {
      toast.error("An error occurred during submission.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDropdownChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      listingCategory: value,
    }));
  };
// console.log("formdata.productfile step 4",formData)
  const handleListingCategorySelect = (value: string) => {
    setFormData((prevData) => ({
      ...prevData,
      listingCategory: value,
    }));
  };

  return (
    <div className="p-8 mx-auto text-white">
      {/* Existing fields */}
      <div className="mb-6">
        <CustomDropdown
          label="Product Type"
          options={["Prompts", "Assets", "Datasets", "Models", "Automation"]}
          selectedValue={formData.listingCategory}
          onSelect={handleDropdownChange}
          error={
            !formData.listingCategory ? "Please select a product type." : undefined
          }
          disabled
        />
      </div>
      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
        {formData.listingCategory === "prompt" ? 'Generation Type' : 'Asset Type'}</label>

        <input
          type="text"
          name="name"
          value={formData.generationType}
          onChange={handleChange}
          readOnly
          aria-disabled="true"
          placeholder="Enter your name"
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        />
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">Product Title</label>

        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          readOnly
          aria-disabled="true"
          placeholder="Enter your name"
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        />
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
          Product Description
        </label>
        <textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          readOnly
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        />
      </div>

      {formData.listingCategory === "prompt" && (
        <div className="mb-6">
          <label className="block text-sm font-medium mb-1">
            Generation Model Type
          </label>
          <input
            type="text"
            name="name"
            value={formData.generationModel}
            onChange={handleChange}
            readOnly
            aria-disabled="true"
            placeholder="Enter model type"
            className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
          />
        </div>
      )}

      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">Product Price</label>
        <input
          type="text"
          name="price"
          value={formData.price}
          onChange={handleChange}
          readOnly
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        />
      </div>
      {/* <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
          Affiliated With
        </label>
        <input
          type="text"
          name="affiliated_with"
          value={formData.affiliated_with}
          onChange={handleChange}
          readOnly
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        />
      </div>*/}

      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
          Product Category
        </label>
        <input
          type="text"
          value={
            formData.productCategory?.label || formData.productCategoryLabel
          }
          readOnly
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        />
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
          Uploaded Images
        </label>
        {formData.images && formData.images.length > 0 ? (
          <div className="flex flex-wrap gap-4">
            {formData.images.map((image, index) => (
              <img
                key={index}
                src={image?.image?.url ? image?.image?.url : image?.image}
                alt="Product Image"
                className="w-32 h-32 object-cover rounded-lg"
              />
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No images uploaded.</p>
        )}
      </div>

      {/* New Fields: listingCategory and product_files */}
      {/* <div className="mb-6">
        <CustomDropdown
          options={["Asset", "Prompt"]}
          selectedValue={formData.listingCategory}
          onSelect={handleListingCategorySelect}
          label="Listing Category"
          disabled
        />
      </div> */}

      <div className="mb-6">
        <label className="block text-sm font-medium mb-1">
          Product File
        </label>
        {/* <input
          type="file"
          name="product_files"
          onChange={(e) => {
            const file = e.target.files[0];
            if (file) {
              setFormData((prev) => ({
                ...prev,
                product_files: file,
              }));
            }
          }}
          disabled
          className="w-full p-3 border border-gray-300 rounded-lg text-white focus:outline-none cursor-not-allowed opacity-50 bg-[#0b1434]"
        /> */}
        {formData?.product_files && (
          <div className="flex gap-2 items-center mt-3">
            <div className="relative">
              <Link href={formData?.product_files?.url} target="_blank">
                <Image alt="pdf" src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/PDF-icon.svg.webp`} width={30} height={40} />
              </Link>
            </div>
            <Link href={formData?.product_files?.url} target="_blank">
              <p className="text-gray-500">
                {formData.product_files?.filename
                  ? formData.product_files?.filename
                  : formData.product_files?.name}
              </p>
              Click here to view the PDF file.
            </Link>
          </div>
        )}
        {/* {formData.product_files && (
          <p className="mt-2 text-gray-500">
            File:{" "}
            {formData.product_files?.filename
              ? formData.product_files?.filename
              : formData.product_files?.name}
          </p>
        )} */}
      </div>

      {/* Submit Section */}
      <div className="flex justify-between gap-4">
        <button
          onClick={prevStep}
          className="bg-white text-indigo-800 font-semibold py-2 px-3 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
        >
          Previous
        </button>
        <div className="flex gap-3">
          <button
            onClick={save}
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
            disabled={loading || isSubmitting}
          >
            {loading
              ? id
                ? "Updating..."
                : "Saving..."
              : id
                ? "Update"
                : "Save"}
          </button>
          {!formData.needsApproval && (
            <button
              onClick={SubmitData}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
              disabled={isSubmitting || loading}
            >
              {isSubmitting ? "Publishing..." : "Publish"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};