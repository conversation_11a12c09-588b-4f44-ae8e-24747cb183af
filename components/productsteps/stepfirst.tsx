"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import ConfirmationModal from "../ui/dashboard/confirmdelete";
import { TbPrompt, TbBoxModel, TbSettingsAutomation } from "react-icons/tb";
import { LuCassetteTape } from "react-icons/lu";
import { MdOutlineDatasetLinked } from "react-icons/md";

export const Step1 = ({ nextStep, setFormData, formData }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedBox, setSelectedBox] = useState(null);
  const [error, setError] = useState("");
  const router = useRouter();
  const boxDetails = [
    {
      name: "Prompt",
      values: "prompt",
      icon: Tb<PERSON>rompt,
      text: "",
      description: "(AI-ready prompts for text, image, code, and chatbots.)"
    },
    {
      name: "Asset",
      values: "asset",
      icon: LuCassetteTape,
      text: "",
      description: "(Stock visuals, UI/UX kits, 3D models, video templates, and audio assets.)"
    },
    {
      name: "Datasets (Coming Soon)",
      values: "datasets",
      icon: MdOutlineDatasetLinked,
      text: "Coming Soon",
      description: "(AI-ready labeled data for ML, research, web scraping, and model training.)"
    },
    {
      name: "Models (Coming Soon)",
      values: "models",
      icon: TbBoxModel,
      text: "Coming Soon",
      description: "(Pre-trained AI models for text, image, speech, chatbots, and computer vision.)"
    },
    {
      name: "Automation (Coming Soon)",
      values: "automation",
      icon: TbSettingsAutomation,
      text: "Coming Soon",
      description: "(No-code workflows, AI bots, Python scripts, and RPA for streamlining tasks.)"
    },
  ];

  useEffect(() => {
    const initialSelection = boxDetails.findIndex(
      (box) => box.values === formData.listingCategory
    );
    if (initialSelection !== -1) {
      setSelectedBox(initialSelection);
    }
  }, [formData]);

  const handleBoxClick = (index) => {
    // console.log("index>>>>>>>>>>>.", index);
    setSelectedBox(index);
    setError("");
    setFormData((prevData) => ({
      ...prevData,
      listingCategory: boxDetails[index].values,
    }));
  };

  const handleNextStep = () => {
    if (selectedBox === null || [2, 3, 4].includes(selectedBox)) {
      setError("Please select a valid option.");
    } else {
      nextStep();
    }
  };
  const confirmDelete = () => {
    localStorage.removeItem("productFormData");
    router.push("/dashboard");
  };

  const closeModal = () => setIsModalOpen(false);
  return (
    <>
      <div className="p-8 rounded-lg max-w-4xl mx-auto text-white">
        <div className=" mb-3">
          <label className="block text-sm font-medium mb-1">
          Please choose the type of Product{" "}
            <span className="text-red-500">*</span>
          </label>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 mb-6">
          {boxDetails.map((box, index) => (
            <div
              key={index}
              className={`relative flex flex-col items-center justify-center p-5 rounded-xl transition-all duration-300 ${
                index === 0 || index === 1
                ? "cursor-pointer hover:scale-105 hover:shadow-lg"
                : "cursor-not-allowed opacity-50"
            } ${
              selectedBox === index
                ? "bg-indigo-800 border-violet-500 shadow-white border-b-2 border-l-2 border-t-0 border-r-0 shadow-sm scale-105"
                : "bg-indigo-800 border-violet-400 border-t-2 border-r-2"
            }`}
              onClick={() => handleBoxClick(index)}
            >
              {/* <Image
                src={box.imgSrc}
                alt={box.name}
                width={64}
                height={64}
                className="mb-4 object-contain"
                loading="lazy"
              /> */}
              <box.icon size={60} />
              <div className="text-lg font-semibold capitalize">{box.name}</div>
              <div className="text-sm capitalize text-wrap text-center mt-2">{box.description}</div>
              {selectedBox === index && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-white/5"></div>
              )}
            </div>
          ))}
        </div>

        {error && (
          <p className="text-red-400 text-center text-lg font-medium mb-4">
            {error}
          </p>
        )}

        <div className="flex justify-between">
          <button
            onClick={()=>setIsModalOpen(true)}
            className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
          >
            Cancel
          </button>
          <button
            onClick={handleNextStep}
            className="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-10 rounded-lg font-semibold shadow-md transition-all duration-300"
          >
            Save & Next
          </button>
        </div>
      </div>
      <ConfirmationModal
      component="prod1st"
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Cancel"
        message={`Are you sure you want to cancel?`}
      />
      {/* {isModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
          }}
        >
          <div className="space-y-3 py-2 pb-2 bg-[#111C44] p-6 rounded shadow-lg max-w-sm w-full">
            <p className="text-2xl font-bold">
              Are you sure you want to cancel? This will erase all your filled data.
            </p>
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Close
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                Confirm
              </Button>
            </div>
          </div>
        </SpaceModal>
      )} */}
    </>
  );
};