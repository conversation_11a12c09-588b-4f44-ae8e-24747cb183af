"use client";
import Link from "next/link";
import { log } from "node:console";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Input } from "../ui/input";
import CustomDropdown from "../ui/dashboard/customdropdown";
import { useRouter } from "next/navigation";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import ConfirmationModal from "../ui/dashboard/confirmdelete";
import Select from "react-select";


import Image from "next/image";

import axios from "axios";
import {
  handleRemoveFile,
  handleUploadFile,
} from "@/app/utils/fileuploadhelper";
type ValidationErrors = {
  images?: string;
  affiliated_with?: string;
  listingCategory?: string;
  category?: string;
  product_files?: string;
};

export const Step3 = ({ nextStep, prevStep, formData, setFormData }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [images, setImages] = useState(formData.images || []);
  const [affiliated_with, setaffiliated_with] = useState(
    formData.affiliated_with || ""
  );
  const [loading, setLoading] = useState(false);
  const [fileLoading, setFileLoading] = useState(false);

  const [category, setCategory] = useState(formData.category || "");
  // const [approval, setApproval] = useState(formData.needsApproval || false);
  const [listingCategory, setListingCategory] = useState(
    formData.listingCategory || "asset"
  );
  const [productFile, setProductFile] = useState(
    formData?.product_files || null
  );
  const [errors, setErrors] = useState<ValidationErrors>({});
  const fileInputRef = useRef(null);
  const ImageRef = useRef(null);

  const router = useRouter();

  const [productCategory, setProductCategory] = useState([]);
  const [productCategories, setProductCategories] = useState([]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get("/api/category?limit=0");
        const categories = response.data.docs.map((cat) => ({
          id: cat.id,
          label: cat.label,
          value: cat.value,
        }));

        setProductCategories(categories);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  const handleProductChange = (selectedOption) => {
    setProductCategory(selectedOption || null);

    setFormData((prev) => ({
      ...prev,
      productCategory: selectedOption,
      productCategoryLabel: selectedOption ? selectedOption?.label : "",
    }));
  };

  useEffect(() => {
    if (formData.productCategory && productCategories.length > 0) {
      const selectedCategory = productCategories.find(
        (cat) =>
          cat.id ===
          (typeof formData.productCategory === "object"
            ? formData.productCategory.id
            : formData.productCategory)
      );

      if (selectedCategory) {
        setProductCategory(selectedCategory);
      }
    }
  }, [formData.productCategory, productCategories]);

  const validateFields = () => {
    const newErrors: ValidationErrors = {};

    if (formData.images.length === 0) {
      newErrors.images = "Please upload at least one image.";
    }

    if (!formData.listingCategory) {
      newErrors.listingCategory = "Please select a listing category.";
    }

    if (!productFile) {
      newErrors.product_files = "Please upload a product file.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (!files || files.length === 0) {
      setErrors({ images: "No files selected." });
      return;
    }
    if (formData?.images?.length >= 4) {
      setErrors({ images: "You have already uploaded 4 images." });
      e.target.value = ""; // Reset file input
      return;
    }
    if (formData?.images?.length + files.length > 4) {
      setErrors({ images: "You can only upload up to 4 images in total." });
      e.target.value = "";
      return;
    }

    const validFiles = Array.from(files);

    if (validFiles.length > 0) {
      setLoading(true);
      let uploadedImages: { id: string; image: string; filename: string }[] =
        [];
      // console.log("Uploading images", uploadedImages)
      try {
        for (const file of validFiles) {
          const fileFormData = new FormData();
          fileFormData.append("file", file);

          const mediaRes = await fetch(
            `${process.env.NEXT_PUBLIC_SERVER_URL}/api/Media?depth=0`,
            {
              method: "POST",
              credentials: "include",
              body: fileFormData,
            }
          );

          const mediaData = await mediaRes.json();
          if (mediaRes.ok && mediaData?.doc?.id) {
            uploadedImages.push({
              id: mediaData?.doc?.id,
              image: mediaData?.doc?.url,
              filename: mediaData?.doc?.filename,
            });
          } else {
            throw new Error("Failed to upload an image.");
          }
        }

        if (uploadedImages.length > 0) {
          setFormData((prevData) => ({
            ...prevData,
            images: [...(prevData.images || []), ...uploadedImages],
          })); // Replace old images
          setErrors({ images: "" });
        } else {
          setErrors({ images: "No images were uploaded." });
        }
      } catch (error) {
        console.error("Image upload error:", error);
        setErrors({ images: "Media upload failed." });
      } finally {
        setLoading(false);
        e.target.value = ""; // Reset file input
      }
    }
  };

  const removeImages = (indexToRemove: number) => {
    setFormData((prevData) => ({
      ...prevData,
      images: prevData.images.filter((_, index) => index !== indexToRemove),
    }));
  };

  // console.log("after upload mulitple images", formData.images);

  // const handleProductFileChange = async (
  //   e: React.ChangeEvent<HTMLInputElement>
  // ) => {
  //   const file = e.target.files?.[0];
  //   if (!file) {
  //     setErrors((prev) => ({ ...prev, product_files: "No file selected." }));
  //     return;
  //   }
  //   setFileLoading(true);

  //   try {
  //     const fileFormData = new FormData();
  //     fileFormData.append("file", file);

  //     const mediaRes = await fetch(
  //       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files`,
  //       {
  //         method: "POST",
  //         credentials: "include",
  //         body: fileFormData,
  //       }
  //     );

  //     const mediaData = await mediaRes.json();

  //     if (mediaRes.ok) {
  //       const uploadedFile = {
  //         id: mediaData.doc.id,
  //         filename: mediaData.doc.filename,
  //         url: mediaData?.doc?.url,
  //       };
  //       setProductFile(uploadedFile);
  //       setFileLoading(false); // Stop the loader
  //       setFormData((prevData) => ({
  //         ...prevData,
  //         product_files: uploadedFile,
  //       }));
  //       setErrors((prev) => ({ ...prev, product_files: "" }));
  //     } else {
  //       setErrors((prev) => ({
  //         ...prev,
  //         product_files: "File upload failed.",
  //       }));
  //     }

  //     const uploadedFilesIds = Array.isArray(mediaData.doc)
  //       ? mediaData.doc.map((file: { id: string; filename: string }) => ({
  //           id: file.id,
  //           filename: file.filename,
  //           url: mediaData?.doc?.url,
  //         }))
  //       : mediaData.doc
  //         ? {
  //             id: mediaData?.doc?.id,
  //             filename: mediaData?.doc?.filename,
  //             url: mediaData?.doc?.url,
  //           }
  //         : [];
  //     // Update the product file state
  //     setProductFile(uploadedFilesIds);
  //     setFormData((prevData) => ({
  //       ...prevData,
  //       product_files: uploadedFilesIds,
  //     }));
  //   } catch (error) {
  //     setErrors((prev) => ({ ...prev, product_files: "File upload failed." }));
  //     toast.error("Media upload failed. Please try again.");
  //   }
  // };

  useEffect(() => {
    return () => {
      images.forEach((image) => URL.revokeObjectURL(image));
    };
  }, [images]);

  const handleNext = () => {
    if (validateFields()) {
      setFormData((prevData) => ({
        ...prevData,
        // affiliated_with,
        // needsApproval: approval,
        listingCategory,
        product_files: productFile,
      }));
      nextStep();
    }
  };
  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData((prev) => {
      const updated = { ...prev, [name]: value };

      return updated;
    });

    // Validate the field, and if the field is now valid, remove the error
    if (value.trim() !== "") {
      setErrors((prevErrors) => ({ ...prevErrors, [name]: "" }));
    }
  };
  const handleListingCategoryChange = (value: string) => {
    setListingCategory(value);
    setFormData((prevData) => ({
      ...prevData,
      listingCategory: value,
    }));
    if (value.trim() !== "") {
      setErrors((prevErrors) => ({ ...prevErrors, listingCategory: "" }));
    }
  };

  const handleCategoryChange = (value: string) => {
    setCategory(value);
    setFormData((prevData) => ({
      ...prevData,
      category: value,
    }));
    if (value.trim() !== "") {
      setErrors((prevErrors) => ({ ...prevErrors, category: "" }));
    }
  };
  const confirmDelete = () => {
    localStorage.removeItem("productFormData");
    localStorage.removeItem("productFormStep");
    router.push("/dashboard");
  };

  const closeModal = () => setIsModalOpen(false);

  // const handleRemovePdf = () => {
  //   setProductFile(null);
  //   setFormData((prev) => ({ ...prev, product_files: null }));
  // };

  // console.log("create create formData.images", formData);

  // console.log("productFile", productFile);

  // const handleProductFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   handleUploadFile(e, setProductFile, setFileLoading, setFormData, setErrors);
  // };

  // const handleRemovePdf = () => {
  //   handleRemoveFile(setProductFile, setFormData);
  // };
  // console.log("productCategory", productCategory);
  // console.log("formData.productCategory", formData);
  return (
    <>
      <div className="p-8 mx-auto text-white">
      <div className="mb-5">
          <label
            htmlFor="productCategory"
            className="block text-sm font-medium mb-1 text-white"
          >
            Product Categories
          </label>

          <Select
            options={productCategories}
            className="w-full"
            getOptionLabel={(e) => e.label}
            getOptionValue={(e) => String(e.id)}
            value={productCategory}
            onChange={handleProductChange}
            isLoading={productCategories.length === 0}
            styles={{
              control: (base) => ({
                ...base,
                backgroundColor: "#0B1437", // Indigo-800
                color: "#e2e8f0", // Gray-600
              }),
              singleValue: (base) => ({
                ...base,
                color: "#e2e8f0", // Gray-600
              }),
              multiValueLabel: (base) => ({
                ...base,
                color: "#e2e8f0", // Gray-600
              }),
              placeholder: (base) => ({
                ...base,
                color: "#757575", // Gray-400
              }),
              menu: (base) => ({
                ...base,
                backgroundColor: "#0B1437", // Indigo-800
              }),
              option: (base, state) => ({
                ...base,
                backgroundColor: state.isFocused ? "#4b5563" : "#0B1437",
                color: "#ffffff", // White
              }),
            }}
          />
        </div>
        
        <div className="mb-5">
          <label htmlFor="images" className="block text-sm font-medium mb-1">
            Product Thumbnail Images <span className="text-red-500">*</span>
          </label>
          <Input
            type="text"
            className="cursor-pointer w-full mx-auto border p-2 text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
            // className="w-full mx-auto border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
            value={
              formData.images.length === 1
                ? formData.images[0]?.filename
                  ? formData.images[0]?.filename
                  : formData.images[0]?.image?.filename || "1 image selected"
                : formData.images.length > 1
                  ? `${formData.images.length} images selected`
                  : "Click here to upload images"
            }
            readOnly
            onClick={() => ImageRef.current?.click()} // Clicking will trigger file input
          />
          <input
            type="file"
            id="images"
            ref={ImageRef}
            accept="image/*"
            multiple
            onChange={handleFileChange}
            className="hidden w-full border border-gray-300 rounded-lg bg-dash text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
          />
          {errors.images && (
            <p className="mt-1 text-sm text-red-400">{errors.images}</p>
          )}

          <div className="flex items-center mt-2">
            {loading && (
              <span className="loader border-2 border-t-indigo-500 border-gray-300 rounded-full w-5 h-5 animate-spin"></span>
            )}
            {!loading && formData.images.length > 0 && (
              <span className="text-green-500 font-medium">
                ✅ Image uploaded!
              </span>
            )}
          </div>

          {/* Image Preview */}
          {formData.images.length > 0 && (
            <div className="mt-3 grid grid-cols-4 gap-2">
              {formData.images.map((img, index) => (
                <div key={index} className="relative">
                  <img
                    src={img?.image?.url || img?.image}
                    alt="Uploaded"
                    className="w-full h-20 object-cover rounded-lg"
                  />
                  <button
                    onClick={() => removeImages(index)}
                    className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full text-xs hover:scale-110 transition-transform duration-200 hover:bg-red-700"
                  >
                    ✖
                  </button>
                </div>
              ))}
            </div>
          )}
          <p className="text-sm text-gray-400 ">
            You can upload up to 4 images max. Your first image will be your thumbnail image and else your output image.
          </p>
        </div>

        {/* <div className="mb-5">
          <label
            htmlFor="affiliated_with"
            className="block text-sm font-medium mb-1"
          >
            Affiliated With
          </label>
          <Input
            type="text"
            id="affiliated_with"
            name="affiliated_with"
            value={formData.affiliated_with}
            onChange={handleChange}
            placeholder="Enter affiliation link"
            className="w-full p-3 border border-gray-300 bg-dash rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
          />
          <p className="text-sm text-gray-400 mt-1 ms-1">
            Please specify the affiliation, if any.
          </p>
        </div> */}

        {/* <div className="mb-5">
          <CustomDropdown
            label={<>Listing Category</>}
            options={["asset", "prompt"]}
            selectedValue={listingCategory}
            onSelect={handleListingCategoryChange}
            error={errors.listingCategory}
          />
          <p className="text-sm text-gray-400 mt-1 ms-1 break-words whitespace-pre-wrap">
            If you are selling digital media(images, audio, or video), please
            select Assest. If you are selling a prompt, please select Prompt.
          </p>
        </div> */}

        

        <div className="mb-5">
          <label
            htmlFor="product_files"
            className="block text-sm font-medium mb-1"
          >
            Product File <span className="text-red-500">*</span>
          </label>
          <Input
            type="text"
            className="cursor-pointer w-full mx-auto border p-2 text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
            // className="w-full mx-auto border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
            value={
              productFile
                ? productFile.filename
                  ? productFile?.filename
                  : productFile?.name
                : "Click here to upload a file"
            }
            readOnly
            onClick={() => fileInputRef.current?.click()} // Clicking will trigger file input
          />
          <Input
            type="file"
            id="product_files"
            ref={fileInputRef}
            accept="application/pdf, application/zip, application/octet-stream"
            // onChange={handleProductFileChange}
            onChange={(e) =>
              handleUploadFile({
                event: e,
                updateFileState: setProductFile,
                setLoading: setFileLoading,
                updateFormData: setFormData,
                updateErrors: setErrors,
                // allowedFileTypes: ["application/pdf, application/zip, application/octet-stream"], // Only PDF allowed
              })
            }
            className="hidden w-full border border-gray-300 bg-dash rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-white"
          />
          {errors.product_files && (
            <p className="mt-2 text-sm text-red-400">{errors.product_files}</p>
          )}
          <div className="flex items-center mt-2">
            {fileLoading && (
              <span className="loader border-2 border-t-indigo-500 border-gray-300 rounded-full w-5 h-5 animate-spin me-1"></span>
            )}
            {!fileLoading && productFile ? (
              <span className="text-green-500 font-medium">
                ✅ File uploaded!
              </span>
            ) : (
              <p className="text-sm text-gray-400 mt-1 ms-1">
                Please upload your {formData.listingCategory === "prompt" ? "Prompt" : "Asset or any Related File"} here.
              </p>
            )}
          </div>

          {formData?.product_files?.url && (
            <div className="flex gap-2 items-center mt-2">
              <div className="relative">
                <Link href={formData?.product_files?.url} target="_blank">
                  <Image alt="pdf" src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/PDF-icon.svg.webp`} width={30} height={40} />
                </Link>

                <button
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"
                  // onClick={() => handleRemovePdf()} // Replace with your function
                  onClick={() =>
                    handleRemoveFile({
                      updateFileState: setProductFile,
                      updateFormData: setFormData,
                    })
                  }
                >
                  ✖
                </button>
              </div>
              <Link href={formData?.product_files?.url} target="_blank">
                <p className="text-gray-500">
                  {formData.product_files?.filename
                    ? formData.product_files?.filename
                    : formData.product_files?.name}
                </p>
                Click here to view the PDF file.
              </Link>
            </div>
          )}

          <div className="flex justify-between mt-6 items-start sm:items-center flex-col sm:flex-row gap-3">
            <div className="flex gap-3">
              <button
                onClick={prevStep}
                className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-gray-400 hover:shadow-lg transition-all duration-300"
              >
                Previous
              </button>
              <button
                onClick={() => setIsModalOpen(true)}
                className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
              >
                Cancel
              </button>
            </div>

            <button
              onClick={handleNext}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center"
            >
              Save & Next
            </button>
          </div>
        </div>
      </div>
      <ConfirmationModal
        component="prod3rd"
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Cancel"
        message={`Are you sure want to cancel?`}
      />
    </>
  );
};
