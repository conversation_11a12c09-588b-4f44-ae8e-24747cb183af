"use client";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { Textarea } from "../ui/textarea";
import { Input } from "../ui/input";
import coinImage from "../../public/img/coin-png.png";
import { useRouter } from "next/navigation";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import ConfirmationModal from "../ui/dashboard/confirmdelete";
import CustomBountyDropdown from "../ui/dashboard/bountydropdown";
import { Model } from "@/server/payload-types";
import Select from "react-select";

const generationTypeOptions = [
  { value: "text", label: "Text" },
  { value: "image", label: "Image" },
  { value: "video", label: "Video" },
  { value: "music", label: "Music" },
  { value: "3d", label: "3D" },
];

interface FormDataType {
  name: string;
  description: string;
  price: string;
  generationType: string;
  generationModel: string;
}
export const Step2 = ({ nextStep, prevStep, formData, setFormData }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [errors, setErrors] = useState<Partial<FormDataType>>({});
  const router = useRouter();
  const textareaRef = useRef(null);
  const [height, setHeight] = useState("auto");
  const [generationModel, setGenerationModel] = useState<any>(null);
  const [generationModels, setGenerationModels] = useState([]);

  // Adjust the height of the textarea based on its content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
      setHeight(`${textarea.scrollHeight}px`);
    }
  };

  // Adjust height on initial render (for pre-filled content)
  useEffect(() => {
    adjustTextareaHeight();
  }, [formData.description]);

  const handleTypeClick = (value) => {
    setFormData((prevData) => ({
      ...prevData,
      generationType: value,
    }));

    setErrors((prevErrors) => ({
      ...prevErrors,
      generationType: "", // Clear error message for bountyType
    }));
  };

  const handleModelChange = (selectedOption) => {
    setGenerationModel(selectedOption || null);

    setFormData((prev) => ({
      ...prev,
      generationModel: selectedOption.name,
    }));

    setErrors((prevErrors) => ({
      ...prevErrors,
      generationModel: "", // Clear error message for bountyType
    }));
  };

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await fetch(`/api/models/get-model-name`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
        });
        if (response.ok) {
          const models = await response.json();
          const modelsArr = models.modelsArr;
  
          setGenerationModels(modelsArr);
  
         
          const selected = modelsArr.find(model => model === formData.generationModel);
          if (selected) {
            setGenerationModel({ name: selected, id: modelsArr.indexOf(selected) });
          }
        } else {
          console.error("Failed to fetch models.");
        }
      } catch (error) {
        console.error("Error fetching models:", error);
      }
    };

    fetchModels();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "price") {
      // Ensure the input doesn't allow negative values or more than one decimal point
      if (!/^\d*\.?\d{0,1}$/.test(value)) {
        return;
      }

      // Prevent negative values programmatically
      if (parseFloat(value) < 0) {
        return;
      }
    }

    setFormData((prev) => ({ ...prev, [name]: value }));

    if (value.trim() !== "") {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleNextStep = (productType) => {
    const newErrors: Partial<FormDataType> = {};

    // Validation logic for required fields
    if (!formData.generationType || formData.generationType.trim() === "") {
      newErrors.generationType = "Generation Type is required.";
    }

    if (!formData.name || formData.name.trim() === "") {
      newErrors.name = "Product Title is required.";
    }

    if (!formData.description || formData.description.trim() === "") {
      newErrors.description = "Product Description is required.";
    }

    if(productType === "prompt") {
      if (!formData.generationModel || formData.generationModel.trim() === "") {
        newErrors.generationModel = "Generation Model is required.";
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
    } else {
      nextStep(); // Proceed to the next step if no errors
    }
  };

  const confirmDelete = () => {
    localStorage.removeItem("productFormData");
    localStorage.removeItem("productFormStep");
    router.push("/dashboard");
  };

  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <div className="p-8 rounded-lg mx-auto text-gray-100">
        <div className="mb-5">
          <label htmlFor="" className="block text-sm font-medium mb-1">
            {formData.listingCategory === "prompt" ? 'Choose Generation Type' : 'Choose Asset Type'}
            <span className="text-red-500">*</span>
          </label>
          <div className="flex flex-wrap gap-3 mt-2">
            {generationTypeOptions.map((option, index) => (
              <div
                key={index}
                className={`cursor-pointer px-4 py-2 rounded-full ${
                  formData.generationType === option.value
                    ? "bg-indigo-900 text-white"
                    : "bg-indigo-700 text-white"
                }`}
                onClick={() => handleTypeClick(option.value)}
              >
                {option.label}
              </div>
            ))}
          </div>
          {errors.generationType && (
            <p className="text-sm text-red-500 mt-1">{errors.generationType}</p>
          )}
          <p className="text-sm text-gray-400 mt-1 ms-1 break-words whitespace-normal max-w-full">
          {formData.listingCategory === "prompt" ? 'Please select a tag which type of response your product will generate.' : 'Please select a tag which type of Asset you want to sell.'}
          </p>
        </div>
        <div className="mb-5">
          <label htmlFor="" className="mt-4 block text-sm font-medium mb-1">
            Product Title <span className="text-red-500">*</span>{" "}
          </label>
          <Input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Product Name"
            className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name}</p>
          )}
          <p className="text-sm text-gray-400 mt-1 ms-1">
            Please enter the product title.
          </p>
        </div>
        <div className="mb-5">
          <label htmlFor="" className="mt-4 block text-sm font-medium mb-1">
            Please add product description{" "}
            <span className="text-red-500">*</span>{" "}
          </label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            onInput={(e) => {
              const textarea = e.target as HTMLTextAreaElement;
              textarea.style.height = "auto";
              textarea.style.height = `${textarea.scrollHeight}px`;
            }}
            style={{ height }}
            ref={textareaRef}
            placeholder="Product Description"
            className={`w-full p-3 border border-gray-300 rounded-lg bg-dash text-white focus:outline-none focus:ring-2 focus:ring-purple-400  no-scrollbar`}
          ></Textarea>
          {errors.description && (
            <p className="text-sm text-red-500 mt-1">{errors.description}</p>
          )}
          <p className="text-sm text-gray-400 mt-1 ms-1 break-words whitespace-normal max-w-full">
            Please highlight what makes your product unique and valuable to
            potential buyers. A thorough and engaging description can help boost
            your sales.
          </p>
        </div>

        {formData.listingCategory === "prompt" && 
        <div className="mb-5">
          <label htmlFor="" className="mt-4 block text-sm font-medium mb-1">
            Generation Model <span className="text-red-500">*</span>{" "}
          </label>
          <Select
            options={generationModels.map((model, index) => ({ id: index, name: model }))}
            className="w-full"
            getOptionLabel={(e) => e.name}
            getOptionValue={(e) => String(e.id)}
            value={generationModel}
            onChange={handleModelChange}
            isLoading={generationModels?.length === 0}
            styles={{
              control: (base) => ({
                ...base,
                backgroundColor: "#0B1437", // Indigo-800
                color: "#e2e8f0", // Gray-600
              }),
              singleValue: (base) => ({
                ...base,
                color: "#e2e8f0", // Gray-600
              }),
              multiValueLabel: (base) => ({
                ...base,
                color: "#e2e8f0", // Gray-600
              }),
              placeholder: (base) => ({
                ...base,
                color: "#757575", // Gray-500
              }),
              menu: (base) => ({
                ...base,
                backgroundColor: "#0B1437", // Indigo-800
              }),
              option: (base, state) => ({
                ...base,
                backgroundColor: state.isFocused ? "#4b5563" : "#0B1437",
                color: "#ffffff", // White
              }),
            }}
          />
          {errors.generationModel && (
            <p className="text-sm text-red-500 mt-1">{errors.generationModel}</p>
          )}
          <p className="text-sm text-gray-400 mt-1 ms-1">
            Please choose a model, on which model you want to generate your product.
          </p>
        </div>}

        <div className="mb-5">
          <label htmlFor="" className="mt-4 mb-1 text-sm block  font-medium ">
            Please provide product price{" "}
          </label>
          <Input
            type="number"
            name="price"
            value={formData.price}
            onChange={handleChange}
            placeholder="Price (in joules)"
            className="w-full p-3 border border-gray-300 rounded-lg bg-dash text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
            autoComplete="off"
          />
          {errors.price && (
            <p className="text-sm text-red-500 mt-1">{errors.price}</p>
          )}
          <p className="text-sm text-gray-400 mt-1 ms-1  items-center break-words whitespace-pre-wrap">
            Please enter the product price in credits. (1
            <span>
              <img
                src={coinImage.src}
                alt="coin"
                className="h-5 w-5 inline mx-1"
              />
            </span>
            = 1 INR ~ 0.012 USD)
          </p>
        </div>

        <div className="flex flex-col sm:flex-row justify-between mt-8 gap-3">
          <div className="flex gap-3">
            <button
              onClick={prevStep}
              className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-gray-400 hover:shadow-lg transition-all duration-300"
            >
              Previous
            </button>
            <button
              onClick={() => setIsModalOpen(true)}
              className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
            >
              Cancel
            </button>
          </div>

          <button
            onClick={() => handleNextStep(formData.listingCategory)}
            className="bg-indigo-600 w-fit text-white py-2 px-6 rounded-lg font-semibold shadow-md hover:bg-indigo-700 transition-all duration-300"
          >
            Save & Next
          </button>
        </div>
      </div>
      <ConfirmationModal
        component="prod2nd"
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Cancel"
        message={`Are you sure you want to cancel?`}
      />
    </>
  );
};