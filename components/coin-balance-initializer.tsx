"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useSetCoinBalance } from "@/lib/userStore";

interface CoinBalanceContextType {
  coinBalance: number;
  isLoading: boolean;
  error: string | null;
  updateBalance: () => Promise<void>;
  setBalance: (amount: number) => void;
  incrementBalance: (amount: number) => void;
  decrementBalance: (amount: number) => void;
}

const CoinBalanceContext = createContext<CoinBalanceContextType | undefined>(undefined);

export function CoinBalanceProvider({
  initialBalance,
  children,
}: {
  initialBalance: number;
  children: ReactNode;
}) {
  const [coinBalance, setCoinBalance] = useState(initialBalance);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const setZustandBalance = useSetCoinBalance();

  // Initialize with server-side balance
  useEffect(() => {
    setCoinBalance(initialBalance);
    setZustandBalance(initialBalance);
  }, [initialBalance, setZustandBalance]);

  // Manual balance update function - सिर्फ जब जरूरत हो तभी call करें
  const updateBalance = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch balance');
      }

      const data = await response.json();
      const newBalance = data.data.coinBalance;

      setCoinBalance(newBalance);
      setZustandBalance(newBalance);

      console.log('✅ Balance updated manually:', newBalance);
    } catch (err: any) {
      setError(err.message);
      console.error('❌ Error updating balance:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Set balance directly (for immediate UI updates)
  const setBalance = (amount: number) => {
    setCoinBalance(amount);
    setZustandBalance(amount);
    console.log('💰 Balance set directly:', amount);
  };

  // Increment balance (for recharge scenarios)
  const incrementBalance = (amount: number) => {
    const newBalance = coinBalance + amount;
    setCoinBalance(newBalance);
    setZustandBalance(newBalance);
    console.log('📈 Balance incremented by:', amount, 'New balance:', newBalance);
  };

  // Decrement balance (for purchase scenarios)
  const decrementBalance = (amount: number) => {
    const newBalance = Math.max(0, coinBalance - amount);
    setCoinBalance(newBalance);
    setZustandBalance(newBalance);
    console.log('📉 Balance decremented by:', amount, 'New balance:', newBalance);
  };

  const contextValue: CoinBalanceContextType = {
    coinBalance,
    isLoading,
    error,
    updateBalance,
    setBalance,
    incrementBalance,
    decrementBalance,
  };

  return (
    <CoinBalanceContext.Provider value={contextValue}>
      {children}
    </CoinBalanceContext.Provider>
  );
}

// Custom hook to use the context
export function useCoinBalanceContext() {
  const context = useContext(CoinBalanceContext);
  if (context === undefined) {
    throw new Error('useCoinBalanceContext must be used within a CoinBalanceProvider');
  }
  return context;
}