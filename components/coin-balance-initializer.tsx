"use client";

import { useEffect } from "react";
import { useSetCoinBalance, useCoinBalance } from "@/lib/userStore";
import { useRouter } from "next/navigation";

export function CoinBalanceProvider({ 
  initialBalance,
  children,
}: {
  initialBalance: number;
  children: React.ReactNode;
}) {
  const setCoinBalance = useSetCoinBalance();
  const currentBalance = useCoinBalance();
  const router = useRouter();

  // Initialize with server-side balance
  useEffect(() => {
    setCoinBalance(initialBalance);
  }, [initialBalance, setCoinBalance]);

  // Refresh balance periodically
  useEffect(() => {
    const interval = setInterval(() => {
      fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`, {
        credentials: 'include'
      })
        .then(res => res.json())
        .then(data => {
          if (data.data.coinBalance !== currentBalance) {
            setCoinBalance(data.data.coinBalance);
            router.refresh();
          }
        })
        .catch(console.error);
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [currentBalance, setCoinBalance, router]);

  return <>{children}</>;
}