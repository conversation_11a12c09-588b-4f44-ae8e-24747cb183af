"use client";
import { Spotlight } from "@/components/ui/Spotlight";
import { HoverBorderGradient } from "@/components/ui/hover-border-gradient";
import Image from "next/image";
import Link from "next/link";
import Graphic from "../public/svg/Graphic_Element.svg"
const CommunityHeroSec = () => {
  return (
    <>
      <div className=" md:h-full relative w-full flex flex-col items-center justify-center rounded-md pt-24 md:pt-12 my-8">
        <div className="w-11/12 mx-auto rounded-md flex md:items-center md:justify-center antialiased bg-grid-white/[0.02] relative">
          <div className="gap-2 sm:p-4 max-w-7xl  mx-auto relative z-10  w-full flex flex-col justify-center items-center">
            <h1 className="relative z-10 text-2xl md:text-4xl  bg-clip-text text-transparent bg-gradient-to-b from-neutral-200 to-neutral-200  text-center font-sans font-bold">
              Be The Part of Biggest{" "}<br/>
              <span className="bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent text-5xl md:text-6xl">Generative AI Community</span>
            </h1>
            <div className="text-sm sm:text-base text-center mt-4 max-w-lg">
              Immerse yourself in a dynamic environment of innovation and
              collaboration. Our community is focused on advancing generative AI
              through knowledge sharing, skill building, and pioneering
              projects. Whether you&apos;re experienced or just starting, join
              us to shape the future of AI. future of generative AI.
            </div>
            <div className="mx-4 mb-4 mt-10 flex justify-center text-center">
              <HoverBorderGradient
                containerClassName="rounded-xl"
                as="button"
                className="
                 text-xl md:text-2xl text-white flex items-center space-x-2 hover:bg-blue-500"
              >
                <Link href={"https://discord.gg/kPkYbzMvN3"} target="_blank" className="bg-gradient-to-br from-amber-500 to-pink-500 transform hover:scale-105 duration-200  px-6 md:px-10 py-1 md:py-3 rounded-xl font-black">
                  JOIN US
                </Link>
              </HoverBorderGradient>
            </div>
          </div>
        </div>
        <Image src={Graphic} alt="community" className="" />
      </div>
    </>
  );
};

export default CommunityHeroSec;
