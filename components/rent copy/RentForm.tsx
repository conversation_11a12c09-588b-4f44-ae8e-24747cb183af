"use client";

import { extractVariablesInBrackets } from "@/app/utils/extractVariables";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import axios from "axios";
import { useState } from "react";

interface RentFormProps {
  rapp: any;
}

const RentForm = ({ rapp }: RentFormProps) => {
  const [output, setOutput] = useState<string | undefined>();

  const prompt = extractVariablesInBrackets(rapp.prompt).map((variable, i) => (
    <div key={i} className="mb-4">
      <Label className="block text-md font-medium ml-1">{variable}:</Label>
      <Input
        type="text"
        name={variable}
        className="w-full p-2 bg-indigo-600 rounded-lg"
        placeholder="Enter keyword"
      />
    </div>
  ));

  return (
    <div>
      <form
        className="flex flex-col"
        onSubmit={async (e: any) => {
          e.preventDefault();
          const formData = new FormData(e.target);
          const formObject = Object.fromEntries(formData.entries());
          try {
            const res = await axios.post(
              `${process.env.NEXT_PUBLIC_SERVER_URL}/api/run/${rapp.id}`,
              formObject
            );
            setOutput(res.data);
          } catch (e: any) {
            console.log(e);
          }
        }}
      >
        <p className="mb-2 text-lg">Choose a theme related to vision</p>
        {prompt}
        <button
          type="submit"
          className="bg-gradient-to-br from-indigo-600 to-indigo-700 text-white p-3 rounded mt-4 flex items-center justify-center focus:ring-offset-2 text-center dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none"
        >
          Submit
        </button>
      </form>
      {output && (
        <div className="bg-indigo-800 mt-4 h-fit border-2 border-indigo-600 text-white px-6 py-4 rounded-lg shadow-lg">
          <h2 className="text-lg mb-2">Output Section:</h2>{" "}
          {rapp.modelType === "text" ? (
            <div className="bg-indigo-500 px-4 py-3 rounded-lg">{output}</div>
          ) : null}
          {rapp.modelType === "image" ? (
            <div className="bg-indigo-500 px-4 py-3 rounded-lg">
              <img src={output} alt="preview" loading="lazy" />
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};
export default RentForm;
