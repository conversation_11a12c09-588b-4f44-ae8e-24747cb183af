"use client";

import axios from "axios";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface PaymentStatusProps {
  orderEmail: string;
  orderId: string;
  isPaid: boolean;
}

const PaymentStatus = ({ orderEmail, orderId, isPaid }: PaymentStatusProps) => {
  const router = useRouter();
  const [paid, setpaid] = useState();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data } = await axios.post(
          `/api2/orderStatus`,
          orderId
        );
        setpaid(data?.isPaid);
      } catch (error: any) {
        console.error(error.message);
      }
    };
    const num = setInterval(fetchData, 1000);
    if (!paid) clearInterval(num);
    if (paid) router.refresh();
  }, [paid, router]);

  return (
    <div className="mt-16 grid grid-cols-2 gap-x-4 text-sm text-gray-600">
      <div>
        <p className="font-medium text-gray-900">Shipping To</p>
        <p>{orderEmail}</p>
      </div>

      <div>
        <p className="font-medium text-gray-900">Order Status</p>
        <p>{isPaid ? "Payment successful" : "Pending payment"}</p>
      </div>
    </div>
  );
};

export default PaymentStatus;
