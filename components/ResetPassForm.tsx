"use client";

import { useState } from "react";
import axios from "axios";
import { useSearchParams, useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

const ResetPassForm = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const token = searchParams.get("token");

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!token) {
      setError("Token is missing. Please try again.");
      return;
    }

    // console.log("token:", token);
    // console.log("password:", password);

    setIsLoading(true);
    try {
      const response = await axios.post("/api2/user/reset-password", {
        token,
        password,
      });

      if (response.data?.success) {
        toast.success("Password reset successfully. Please sign in");
        router.push("/sign-in");
      } else {
        toast.error("Failed to reset password. Please try again");
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="grid gap-6">
      <div className="grid gap-2">
        <div className="grid gap-1 py-2">
          <label
            htmlFor="password"
            className="block text-sm font-medium text-white"
          >
            New Password
          </label>
          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="New Password"
              className="border border-gray-300 rounded-md p-2 w-full text-black"
            />
            <button
              type="button"
              className="absolute inset-y-0 top-1 right-0 pr-3 flex items-center text-sm leading-5"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-indigo-400" />
              ) : (
                <Eye className="h-5 w-5 text-indigo-400" />
              )}
            </button>
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}
        </div>

        <Button
          className="flex bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 w-full text-white rounded-md h-10 font-medium"
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            "Reset Password"
          )}
        </Button>
      </div>
    </form>
  );
};

export default ResetPassForm;