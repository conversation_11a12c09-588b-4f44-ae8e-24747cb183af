import Link from 'next/link'
import React from 'react'



const CatergorySection = () => {
    return (
        <article
            className="hover:animate-background rounded-xl bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 p-0.5 shadow-xl transition hover:bg-[length:400%_400%] hover:shadow-sm hover:[animation-duration:_4s]"
        >
            <div className="rounded-[10px] bg-white/5 p-4 !pt-20 sm:p-6">
                <time dateTime="2022-10-10" className="block text-xs text-gray-500"> 10th Oct 2022 </time>

                <Link href="#">
                    <h3 className="mt-0.5 text-lg font-medium text-gray-900">
                        How to center an element using JavaScript and jQuery
                    </h3>
                </Link>

            </div>
        </article>
    )
}


export default CatergorySection