import React, { Fragment } from 'react'
import escapeHTML from 'escape-html'
import { Text } from 'slate'
import Link from 'next/link'

// eslint-disable-next-line no-use-before-define
type Children = Leaf[]

type Leaf = {
  type: string
  value?: {
    url: string
    alt: string
  }
  children?: Children
  url?: string
  [key: string]: unknown
}

const serialize = (children: Children): React.ReactNode[] => {

  const serializedChildren = [];

  for (let i = 0; i < (children || []).length; i++) {
    const node = children[i];

    if (Text.isText(node)) {
      let text = <span dangerouslySetInnerHTML={{ __html: escapeHTML(node.text) }} />

      if (node.bold) {
        text = <strong key={i}>{text}</strong>
      }

      if (node.code) {
        text = <code key={i}>{text}</code>
      }

      if (node.italic) {
        text = <em key={i}>{ text }</em>
      }

      if (node.underline) {
        text = (
          <span style={{ textDecoration: 'underline' }} key={i}>
            {text}
          </span>
        )
      }

      if (node.strikethrough) {
        text = (
          <span style={{ textDecoration: 'line-through' }} key={i}>
            {text}
          </span>
        )
      }

      serializedChildren.push(<Fragment key={i}>{text}</Fragment>);
    } else if (node) {
      switch (node.type) {
        case 'upload':
          serializedChildren.push(<img key={i} src={node.value?.url} alt="image" className='py-6' loading="lazy" />);
          break;
        case 'h1':
          serializedChildren.push(<h1 className='text-4xl mb-4' key={i}>{serialize(node.children || [])}</h1>);
          break;
        case 'heading-1':
            serializedChildren.push(<h1 className='text-4xl font-bold text-white/80 mb-4' key={i}>{serialize(node.children || [])}</h1>);
            break;
        case 'heading-2':
            serializedChildren.push(<h1 className='text-2xl font-bold text-white/80 mb-4' key={i}>{serialize(node.children || [])}</h1>);
            break;
        case 'heading-3':
            serializedChildren.push(<h1 className='text-xl font-bold text-white/80  mb-4' key={i}>{serialize(node.children || [])}</h1>);
            break;
        case 'heading-4':
            serializedChildren.push(<h1 className='text-lg font-bold text-white/80  mb-4' key={i}>{serialize(node.children || [])}</h1>);
            break;
        case 'heading-5':
            serializedChildren.push(<h1 className='text-sm font-bold text-white/80  mb-4' key={i}>{serialize(node.children || [])}</h1>);
            break;
        case 'heading-6':
            serializedChildren.push(<h1 className='text-sm font-bold text-white/80  mb-4' key={i}>{serialize(node.children || [])}</h1>);
            break;
        case 'paragraph':
              serializedChildren.push(<h1 className='text-lg text-white/80  mb-4' key={i}>{serialize(node.children || [])}</h1>);
              break;
        case 'h2':
          serializedChildren.push(<h2 className='text-2xl text-white/80  mb-3' key={i}>{serialize(node.children || [])}</h2>);
          break;
        case 'h3':
          serializedChildren.push(<h3 className='text-xl text-white/80  mb-3' key={i}>{serialize(node.children || [])}</h3>);
          break;
        case 'h4':
          serializedChildren.push(<h4 className='text-xl text-white/80  mb-2' key={i}>{serialize(node.children || [])}</h4>);
          break;
        case 'h5':
          serializedChildren.push(<h5 className='text-xl text-white/80  mb-2' key={i}>{serialize(node.children || [])}</h5>);
          break;
        case 'h6':
          serializedChildren.push(<h6 className='text-xl text-white/80  mb-2' key={i}>{serialize(node.children || [])}</h6>);
          break;
        case 'blockquote':
          serializedChildren.push(<blockquote className="mb-4" key={i}>{serialize(node.children || [])}</blockquote>);
          break;
        case 'ul':
          serializedChildren.push(<ul key={i} className='list-disc text-white/80  list-inside mb-4'>{serialize(node.children || [])}</ul>);
          break;
        case 'ol':
          serializedChildren.push(<ol key={i} className='list-decimal text-white/80  list-inside mb-4'>{serialize(node.children || [])}</ol>);
          break;
        case 'li':
          serializedChildren.push(<li className="mb-2" key={i}>{serialize(node.children || [])}</li>);
          break;

        case 'ordered-list': // Ordered list
          serializedChildren.push(
            <ol key={i} className="list-decimal list-inside mb-4 text-lg text-white/80 ">
              {serialize(node.children || [])}
            </ol>
          );
          break;
  
        case 'list-item': // List item
          serializedChildren.push(
            <li className="mb-2 text-lg text-white/80" key={i}>
              {serialize(node.children || [])}
            </li>
          );
          break;

        case 'link':
          serializedChildren.push(
            <Link href={escapeHTML(node.url)} key={i} className='italic underline font-bold '>
              {serialize(node.children || [])}
            </Link>
          );
          break;

        case 'enter':  
          //  serializedChildren.push(<br key={i} />)
          // break;
          serializedChildren.push(<div key={i} className='mb-8'><br /></div>);
          break;
        case '':
          // serializedChildren.push(<br key={i} />);
          serializedChildren.push(<div key={i} className='mb-8'><br /></div>);
          break;
        //default:
          // serializedChildren.push(<div className="mb-4 text-xl" key={i}>{serialize(node.children || [])}</div>);
          //break
             // Handle text formatting
             default: {
              // Handle text formatting
              const textNode = node.text || '';
              const styles = [];
              if (node.bold) styles.push('font-bold');
              if (node.italic) styles.push('italic');
              if (node.underline) styles.push('underline');
          
              serializedChildren.push(<div className="mb-4 text-xl" key={i}>{serialize(node.children || [])}</div>);
              break;
            }


      }
    }
  }

  return serializedChildren;
}
export default serialize
