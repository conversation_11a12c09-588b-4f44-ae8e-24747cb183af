"use client";
import React, { useEffect, useRef, useState } from "react";
import axios from "axios";
import ReactMarkdown from "react-markdown";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { toast } from "sonner";
import { Copy, Download, Volume2 } from "lucide-react";

interface Model {
  id: string;
  name: string;
  description: string;
  type: string;
  cost: number;
  video?: { url: string };
  imageinput: string;
  commision: number;
  enablePrompt: boolean;
  provider: {
    text: string;
    image: string;
    video: string;
    vision: string;
    audio: string;
  };
  examples: { example: string; id: string }[];
  settings: {
    groqtextsettings: string[];
    openaitextsettings: string[];
    replicatetextsettings: string[];
    replicateimagesettings: string[];
    falvideosettings: string[];
    falaudiosettings: string[];
    ModelImageReplicateSettings: {
      seed: number;
      cfg: number;
      steps: number;
      prompt_strength: number;
      output_format: string;
      aspect_ratio: string;
      output_quality: number;
      face_upsample: boolean;
      background_enhance: boolean;
      upscale: number;
      codeformer_fidelity: number;
    };
    ModelVideoFalSettings: {
      seconds_start: number;
      seconds_total: number;
      steps: number;
    };
    ModelAudioFalSettings: {
      seconds_start: number;
      seconds_total: number;
      steps: number;
    };
  };
}

type VideoOutput = {
  video: {
    url: string;
    content_type: string;
    file_name: string;
    file_size: number;
  };
};

type OutputType = string | VideoOutput | any;

const SerializeModel = (model: Model, user: any, id: string) => {
  const [output, setOutput] = useState<OutputType | undefined>();
  const [spinLoading, setSpinLoading] = useState<boolean>(false);
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const [audioSrc, setAudioSrc] = useState("");

  const [modelSettings, setModelSettings] = useState<
    Record<string, number | string>
  >({});
  const [localSettings, setLocalSettings] = useState(
    filterRelevantSettings(
      model.settings.ModelImageReplicateSettings,
      model.settings.replicateimagesettings
    )
  );
  const onSettingsChange = (
    updatedSettings: Record<string, number | string>
  ) => {
    setModelSettings(updatedSettings);
    // console.log("modelset", modelSettings);
  };

  function filterRelevantSettings(
    allSettings: Record<string, any>,
    allowedKeys?: string[]
  ) {
    if (!allowedKeys) return {};
    return Object.fromEntries(
      Object.entries(allSettings).filter(([key]) => allowedKeys.includes(key))
    );
  }
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    const updatedSettings = { ...localSettings, [name]: value };
    setLocalSettings(updatedSettings);
    onSettingsChange(updatedSettings);
  };

  useEffect(() => {
    const relevantSettings = filterRelevantSettings(
      model.settings.ModelImageReplicateSettings,
      model.settings.replicateimagesettings
    );
    setLocalSettings(relevantSettings);
  }, [model.settings]);

  // const [sliderValues, setSliderValues] = useState({
  //   seed: 5,
  //   cfg: 0,
  //   steps: 0,
  //   prompt_strength: 0,
  //   output_quality: 0,
  // });
  // const [outputFormat, setOutputFormat] = useState(
  //   model.settings.ModelImageReplicateSettings.output_format
  // );
  // const [aspectRatio, setAspectRatio] = useState(
  //   model.settings.ModelImageReplicateSettings.aspect_ratio
  // );
  // const handleOutputFormatChange = (e) => setOutputFormat(e.target.value);
  // const handleAspectRatioChange = (e) => setAspectRatio(e.target.value);

  const handleSliderChange = (setting: string, value: number) => {
    const updatedSettings = { ...localSettings, [setting]: value };
    setLocalSettings(updatedSettings);
    onSettingsChange(updatedSettings);
  };

  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleSeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = parseFloat(e.target.value);
    setCurrentTime(time);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioDownload = () => {
    if (output?.audio_file?.url) {
      // Create an anchor element
      const downloadLink = document.createElement("a");
      downloadLink.href = output.audio_file.url;
      downloadLink.download = output.audio_file.file_name || "audio.wav";
      downloadLink.target = "_blank";

      // Append to document, click, and remove
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      toast.success("Download started");
    } else {
      toast.error("No audio file available to download");
    }
  };

  // {video controls}
  const videoUrl = output?.video?.url;

  if (!model) {
    return null;
  }

  const settings = (
    <>
      {model.settings.openaitextsettings.length > 0 && (
        <div>
          <h4>OpenAI Text Settings:</h4>
          {model.settings.openaitextsettings.map((setting, i) => (
            <div key={i} className="mb-4">
              <Label className="block text-lg font-medium ml-1 mb-1">
                {setting}:
              </Label>
              <Input
                type="text"
                name={setting}
                className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
                placeholder="Enter value"
                required={true}
              />
            </div>
          ))}
        </div>
      )}
      {model.type === "text" && model.settings.groqtextsettings.length > 0 && (
        <div>
          <h4>Groq Text Settings:</h4>
          {model.settings.groqtextsettings.map((setting, i) => (
            <div key={i} className="mb-4">
              <Label className="block text-lg font-medium ml-1 mb-1">
                {setting}:
              </Label>
              <Input
                type="text"
                name={setting}
                className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
                placeholder="Enter value"
                required={true}
              />
            </div>
          ))}
        </div>
      )}
      {model.type === "text" &&
        model.settings.replicatetextsettings.length > 0 && (
          <div>
            <h4>Replicate Text Settings:</h4>
            {model.settings.replicatetextsettings.map((setting, i) => (
              <div key={i} className="mb-4">
                <Label className="block text-lg font-medium ml-1 mb-1">
                  {setting}:
                </Label>
                <Input
                  type="text"
                  name={setting}
                  className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
                  placeholder="Enter value"
                  required={true}
                />
              </div>
            ))}
          </div>
        )}
      {model.type === "image" && model.settings.ModelImageReplicateSettings && (
        <div>
          <h4 className="text-2xl font-bold mb-4">Replicate Image Settings:</h4>
          {model.settings.replicateimagesettings.map((setting, i) => (
            <div key={i} className="mb-4">
              <Label className="block text-lg font-medium ml-1 mb-1">
                {setting.replace(/_/g, " ")}:
              </Label>

              {/* Conditionally render input fields */}
              {setting === "output_format" ? (
                <select
                  name={setting}
                  value={localSettings.output_format || ""}
                  onChange={handleSelectChange}
                  className="w-full p-2 bg-indigo-700 text-white rounded-lg shadow-lg"
                >
                  <option value="jpg">JPG</option>
                  <option value="png">PNG</option>
                  <option value="webp">WEBP</option>
                </select>
              ) : setting === "aspect_ratio" ? (
                <select
                  name={setting}
                  value={localSettings[setting] || "2_3"}
                  onChange={handleSelectChange}
                  className="w-full p-2 bg-indigo-700 text-white rounded-lg shadow-lg"
                >
                  <option value="1_1">1:1</option>
                  <option value="16_9">16:9</option>
                  <option value="21_9">21:9</option>
                  <option value="2_3">2:3</option>
                  <option value="3_2">3:2</option>
                  <option value="4_5">4:5</option>
                  <option value="5_4">5:4</option>
                  <option value="9_16">9:16</option>
                  <option value="9_21">9:21</option>
                </select>
              ) : [
                  "seed",
                  "cfg",
                  "steps",
                  "prompt_strength",
                  "output_quality",
                  "upscale",
                  "codeformer_fidelity",
                ].includes(setting) ? (
                <div className="flex items-center gap-4">
                  <input
                    type="range"
                    name={setting}
                    min="0"
                    max="100"
                    value={localSettings[setting] || 0}
                    onChange={(e) =>
                      handleSliderChange(setting, Number(e.target.value))
                    }
                    className="w-full h-2 rounded-lg appearance-none cursor-pointer bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 focus:outline-none"
                    style={{
                      backgroundSize: `${localSettings[setting] || 0}% 100%`,
                      backgroundRepeat: "no-repeat",
                    }}
                  />

                  <span className="text-xl font-medium text-white mr-5">
                    {localSettings[setting] || 0}
                  </span>
                </div>
              ) : null}
            </div>
          ))}
        </div>
      )}
      {model.type === "video" && model.settings.ModelVideoFalSettings && (
        <div>
          <h4 className="text-2xl font-bold mb-4">Model Video Fal Settings:</h4>
          {model.settings.falvideosettings.map((setting, i) => (
            <div key={i} className="mb-4">
              <Label className="block text-lg font-medium ml-1 mb-1">
                {setting.replace(/_/g, " ")}:
              </Label>

              {/* Render sliders for specific settings */}
              {["seconds_start", "seconds_total", "steps"].includes(
                setting
              ) && (
                <div className="flex items-center gap-4">
                  <input
                    type="range"
                    name={setting}
                    min={setting === "steps" ? "1" : "0"}
                    max={setting === "steps" ? "200" : "120"}
                    value={localSettings[setting] || 0}
                    onChange={(e) =>
                      handleSliderChange(setting, Number(e.target.value))
                    }
                    className="w-full h-2 rounded-lg appearance-none cursor-pointer"
                  />
                  <span className="text-xl font-medium text-white mr-5">
                    {localSettings[setting] || 0}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {model.type === "audio" && model.settings.ModelAudioFalSettings && (
        <div>
          <h4 className="text-2xl font-bold mb-4">Model Audio Fal Settings:</h4>
          {model.settings.falaudiosettings.map((setting, i) => (
            <div key={i} className="mb-4">
              <Label className="block text-lg font-medium ml-1 mb-1">
                {setting.replace(/_/g, " ")}:
              </Label>

              {/* Render sliders for specific settings */}
              {["seconds_start", "seconds_total", "steps"].includes(
                setting
              ) && (
                <div className="flex items-center gap-4">
                  <input
                    type="range"
                    name={setting}
                    min={setting === "steps" ? "1" : "0"}
                    max={setting === "steps" ? "200" : "120"}
                    value={localSettings[setting] || 0}
                    onChange={(e) =>
                      handleSliderChange(setting, Number(e.target.value))
                    }
                    className="w-full h-2 rounded-lg appearance-none cursor-pointer"
                  />
                  <span className="text-xl font-medium text-white mr-5">
                    {localSettings[setting] || 0}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );

  const handleOnSubmit = async (e) => {
    e.preventDefault();

    if (!user) {
      toast.error("Please login to use this space");
      return;
    }

    if (user.coinBalance <= model.cost) {
      toast.error("Insufficient coin balance to run this model");
      return;
    }

    try {
      setSpinLoading(true);
      toast.loading("Running...");

      const formData = new FormData(e.target);
      const formObject = Object.fromEntries(formData.entries());

      const { data: runResponse } = await axios.post(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/runModel/${id}`,
        {
          obj: formObject,
          settings: modelSettings,
          url: base64Image,
        }
      );
      // console.log("obj", formObject);

      if (runResponse.success === false) {
        ``;
        toast.error(runResponse.message);
        return;
      }
      if (runResponse.output?.audio_file?.url) {
        setAudioSrc(runResponse.output.audio_file.url);
      }

      toast.dismiss();
      setOutput(runResponse.output);
    } catch (error) {
      let errorMessage = "An error occurred. Please try again.";

      if (error?.response) {
        if (error?.response?.status === 413) {
          ("Request too large. Try reducing the input size.");
        } else if (error?.response?.data?.message) {
          errorMessage;
        } else {
          errorMessage =
            error?.response?.data?.message ||
            "An error occurred. Please try again.";
        }
      }

      toast.error(errorMessage);
      console.error("Error:", error);
      toast.dismiss();
    } finally {
      setSpinLoading(false);
      toast.dismiss();
    }
  };

  const handleCopy = () => {
    if (output) {
      navigator.clipboard.writeText(output).then(
        () => {
          toast.success("Output copied to clipboard");
        },
        (err) => {
          toast.error("Failed to copy output");
          console.error("Could not copy text: ", err);
        }
      );
    }
  };

  const handleDownload = async () => {
    if (output) {
      try {
        const response = await axios.get(output, {
          responseType: "blob",
        });
        const blob = new Blob([response.data], { type: "image/png" });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "output.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Download failed: ", error);
      }
    }
  };

  const downloadAudio = () => {
    const link = document.createElement('a');
    link.href = audioSrc; 
    link.setAttribute('download', 'audio-file.wav'); 
    document.body.appendChild(link);
    link.click(); // Trigger the download
    document.body.removeChild(link); // Clean up
  };

  const handleImageUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null; // Get the selected file

    if (file) {
      // Convert the file to a base64 string if needed
      const reader = new FileReader();
      reader.onloadend = () => {
        setBase64Image(reader.result as string); // Save base64 encoded image
        // console.log("base64Image front", reader.result as string);
      };
      reader.readAsDataURL(file); // Read the file as Data URL
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-5 text-white p-6">
      <div className="bg-indigo-600 h-fit min-h-96 border-4 border-muted-foreground text-white py-4 rounded-lg shadow-lg">
        <div className="flex justify-between items-center mb-2 px-4">
          <h2 className="text-xl font-bold">{model.name}</h2>
          <span className="text-sm">Cost: {model.cost} coins</span>
          <span className="text-sm">
            Commission: {model.commision} commission
          </span>
        </div>

        <div className="px-4 mb-4">
          <p>{model.description}</p>
          <p>
            <strong>Model Type:</strong> {model.type}
          </p>
          <p>
            <strong>Provider:</strong>{" "}
            {model.type === "video"
              ? model.provider.video
              : model.type === "text"
                ? model.provider.text
                : model.type === "vision"
                  ? model.provider.vision
                  : model.type === "audio"
                    ? model.provider.audio
                    : model.provider.image}
          </p>
        </div>
        {/* Display Settings */}
        {/* <div className="px-4 mb-4">
          {model.settings.openaitextsettings.length > 0 && (
            <div>
              <h4>OpenAI Text Settings:</h4>
              {model.settings.openaitextsettings.map((setting, i) => (
                <div key={i} className="mb-4">
                  <Label className="block text-lg font-medium ml-1 mb-1">
                    {setting}:
                  </Label>
                  <Input
                    type="text"
                    name={setting}
                    className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
                    placeholder="Enter value"
                    required={true}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Display Examples */}
        {model.examples.length > 0 && (
          <div className="px-4 mb-4">
            <h4>Examples:</h4>
            {model.examples.map((example, index) => (
              <div
                key={example.id}
                className="mb-2 max-h-20 overflow-hidden overflow-ellipsis p-2 bg-gray-700 rounded-lg"
              >
                <p className="whitespace-pre-wrap break-words">
                  <strong>Example {index + 1}:</strong> {example.example}
                </p>
              </div>
            ))}
          </div>
        )}

        {settings}
      </div>

      <form className="flex flex-col" onSubmit={handleOnSubmit}>
        {model.enablePrompt && (
          <div className="mb-4">
            <label className="block text-lg font-medium mb-1">
              Enter Prompt:
            </label>
            <input
              type="text"
              name="prompt"
              className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
              placeholder="Enter your prompt"
              required
            />
          </div>
        )}

        {/* {model.type === "image" && (
          <div className="mb-4">
            <label className="block text-lg font-medium mb-1">
              Upload Image:
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageFileChange}
              className="file:rounded-lg file:bg-indigo-600 file:border-none file:text-white"
              required
            />
          </div>
        )} */}

        {model.imageinput ? (
          <div className="mb-4 px-4">
            <Label className="block text-lg font-medium ml-1 mb-1">
              {"Image"}:
            </Label>

            <Input
              type="file"
              name={"image"}
              required={true}
              accept="image/*"
              onChange={handleImageUrlChange}
              className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
              placeholder="Add image"
            />
          </div>
        ) : null}

        <button
          type="submit"
          disabled={spinLoading}
          className="bg-indigo-700 py-2 px-4 rounded-lg text-lg font-semibold text-white hover:bg-indigo-600"
        >
          {spinLoading ? "Running..." : "Run Model"}
        </button>

        <div className="">
          {output ? (
            <div>
              {model.type === "text" ? (
                <div className="text-sm rounded-lg h-fit relative">
                  <div className="bg-indigo-600 z-10 w-full absolute">
                    <h2 className="text-lg mt-1 absolute">Output Section:</h2>
                    <div
                      className="absolute mx-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                      onClick={handleCopy}
                    >
                      {spinLoading ? (
                        <div className="loader"></div>
                      ) : (
                        <Copy className="" />
                      )}
                    </div>
                  </div>
                  <div className="pt-12">
                    <div className="overflow-y-scroll max-h-80 custom-scrollbar">
                      <ReactMarkdown
                        rehypePlugins={[rehypeSanitize]}
                        remarkPlugins={[remarkGfm]}
                      >
                        {output}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              ) : null}
              {model.type === "image" ? (
                <div className="rounded-lg relative h-fit">
                  <div
                    className="absolute m-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                    onClick={handleDownload}
                  >
                    {spinLoading ? (
                      <div className="loader"></div>
                    ) : (
                      <Download className="" />
                    )}
                  </div>
                  <img src={output} alt="preview" className="rounded-lg" loading="lazy"/>
                </div>
              ) : null}
              {model.type === "video" ? (
                <div className="rounded-lg relative h-fit">
                  <div
                    className="absolute m-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                    onClick={handleDownload}
                  >
                    {spinLoading ? (
                      <div className="loader"></div>
                    ) : (
                      <Download className="" />
                    )}
                  </div>

                  {/* Video Player */}
                  <video controls className="rounded-lg w-full max-h-96">
                    <source src={videoUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              ) : null}
              {model.type === "vision" ? (
                <div className="text-sm rounded-lg h-fit relative">
                  <div className="bg-indigo-600 z-10 w-full absolute">
                    <h2 className="text-lg mt-1 absolute">Output Section:</h2>
                    <div
                      className="absolute mx-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                      onClick={handleCopy}
                    >
                      {spinLoading ? (
                        <div className="loader"></div>
                      ) : (
                        <Copy className="" />
                      )}
                    </div>
                  </div>
                  <div className="pt-12">
                    <div className="overflow-y-scroll max-h-80 custom-scrollbar">
                      <ReactMarkdown
                        rehypePlugins={[rehypeSanitize]}
                        remarkPlugins={[remarkGfm]}
                      >
                        {output}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              ) : null}
              {model.type === "audio" ? (
                <div className="bg-transparent shadow-lg rounded-lg p-6">
                  <h2 className="text-lg font-semibold mb-4 text-center text-white">
                    Audio Output
                  </h2>
                  <audio
                    controls
                    className="w-full border border-gray-300 rounded-md bg-gray-100"
                  >
                    <source src={audioSrc} type="audio/wav" />
                    Your browser does not support the audio element.
                  </audio>
                </div>
              ) : null}
            </div>
          ) : (
            <p className="text-2xl font-bold text-center mt-28 animate-pulse">
              no output yet
            </p>
          )}
        </div>
      </form>
    </div>
  );
};

export default SerializeModel;
