"use client";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { extractVariablesInBrackets } from "../../app/utils/extractVariables";
import Description from "../Description";
import Images from "../Images";
import ReactMarkdown from "react-markdown";
import Model from "../Model";
import ModelType from "../ModelType";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import Name from "../Name";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { toast } from "sonner";

import { Copy, Download, Play } from "lucide-react";
import Skeleton from "../Skeleton/index";
import DescriptionToggle from "../DescriptionToggle";


interface Rapp {
  id: string;
  name: string;
  description: string;
  price: number;
  images: Object[];
  modelType: string;
  prompt: string;
  systemprompt: string;
  negativeprompt: string;
  model: { name: string } | null;
  imageinput: boolean;
  totalCost: number;
  creator: any;
  computationcost: number;
  commission: number;
}

const SerializeRapp = (
  children: Rapp,
  user: any,
  id: string
): React.ReactNode => {
  const [output, setOutput] = useState<string | undefined>();
  const [spinLoading, setSpinLoading] = useState<boolean>(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [audioKey, setAudioKey] = useState(0);
  const [audioFile, setAudioFile] = useState<string | null>(null);

  if (!children) {
    return null;
  }
  const systemPrompt = extractVariablesInBrackets(children.systemprompt).map(
    (variable, i) => (
      <div key={i} className="mb-4">
        <Label className="block text-lg font-medium ml-1 mb-1">
          {variable}:
        </Label>
        <Input
          type="text"
          name={variable}
          className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
          placeholder="Enter keyword"
          required={true}
        />
      </div>
    )
  );
  const userPrompt = extractVariablesInBrackets(children.prompt).map(
    (variable, i) => (
      <div key={i} className="mb-4">
        <Label className="block text-lg font-medium ml-1 mb-1">
          {variable}:
        </Label>
        <Input
          type="text"
          name={variable}
          className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
          placeholder="Enter keyword"
          required={true}
        />
      </div>
    )
  );
  const negativePrompt = extractVariablesInBrackets(
    children.negativeprompt
  ).map((variable, i) => (
    <div key={i} className="mb-4">
      <Label className="block text-lg font-medium ml-1 mb-1">{variable}:</Label>
      <Input
        type="text"
        name={variable}
        className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
        placeholder="Enter keyword"
        required={true}
      />
    </div>
  ));

  const handleOnSubmit = async (e: any) => {
    e.preventDefault();

    if (!user) {
      toast.error("Please login to use this space");
      return;
    }

    if (user.coinBalance <= children.totalCost) {
      toast.error("Insufficient coin balance to Run this rapps");
      return;
    }

    try {
      setSpinLoading(true);
      toast.loading("Running...");
      let imageUrl = null;
      if (children.imageinput) {
        const res = await axios.post(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/media?depth=0`,
          {
            file: imageFile,
          },
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        imageUrl = res.data.doc.url;

        // if (!imageFile) {
        //   toast.error("Unable to upload image. Please retry.");
        //   return;
        // }
        if (!imageUrl) {
          toast.error("Unable to upload image. Please retry.");
          return;
        }
      }

      const formData = new FormData(e.target);
      const formObject: Record<string, any> = Object.fromEntries(
        formData.entries()
      );

      const { data: runResponse } = await axios.post(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/run/${id}`,
        {
          obj: formObject,
          url: imageUrl,
        }
      );

      if (runResponse.success === false) {
        toast.error(runResponse.message);
        // console.log("runresponse", runResponse);
        return;
      }

      const { output } = runResponse;
      const newAudioFile = output?.audio_file?.url;

      setAudioFile(null);
      setAudioFile(newAudioFile);
      setAudioKey((prevKey) => prevKey + 1); // Increment the key to force a re-render


      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rappsPurchase/purchaseRapps`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            RappsId: id,
          }),
        }
      );

      const result = await response.json();
      toast.dismiss();

      if (response.ok) {
        toast.success("Rapp Run Successful and Credits have been deducted.");
      } else {
        // console.log("second", result);
        toast.error(result.message);
      }

      setOutput(runResponse.output.result);
    } catch (error) {
      console.log("Error caught in catch block:", error); // Check if it enters here

      if (error.response) {
        toast.error(error.response.data.message);
      } else {
        console.error("Error during request:", error);
        toast.error("An error occurred. Please try again.");
      }
    } finally {
      setSpinLoading(false);
      toast.dismiss();
    }
  };

  const handleCopy = () => {
    setSpinLoading(true);
    if (output) {
      navigator.clipboard.writeText(output).then(
        () => {
          toast.success("Output copied to clipboard");
          setSpinLoading(false);
        },
        (err) => {
          toast.error("Failed to copy output");
          console.error("Could not copy text: ", err);
          setSpinLoading(false);
        }
      );
    }
  };

  const handleDownload = async () => {
    setSpinLoading(true);
    if (output) {
      try {
        const response = await axios.get(output, {
          responseType: "blob",
        });
        const blob = new Blob([response.data], { type: "image/png" });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "output.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        setSpinLoading(false);
      } catch (error) {
        console.error("Download failed: ", error);
        setSpinLoading(false);
      }
    }
  };
  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageFile(e.target.files?.[0] || null);
  };

  //const totalcost = {children.commission + children.computationcost} ; 
  // console.log("computation cost : " , children.computationcost);
  // console.log("commission cost ",children.commission);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-5 text-white p-6">
      <div className="bg-indigo-800 h-fit border-2 border-indigo-600 text-white px-6 py-4 rounded-lg shadow-lg">
        <div className="flex flex-col mb-4">
          {children.images && <Images image={children.images[0]} />}
          <Name name={children.name} />
          <DescriptionToggle description={children.description} />
        </div>
        <div className="flex items-center mb-4">
          <ModelType modelType={children.modelType} />
          <Model model={children.model?.name || "Model Name"} />
        </div>
        {children.images && (
          <Skeleton image={children.images} user={children.creator} />
        )}
      </div>
      <div>
        <div className="bg-indigo-600 h-fit min-h-96 border-4 border-muted-foreground text-white py-4 rounded-lg shadow-lg">
          <div className="flex justify-between items-center mb-2 px-4">
            <h2 className="text-xl font-bold">Try this Rapp</h2>
          </div>
          <form className="flex flex-col" onSubmit={handleOnSubmit}>
            <div className="bg-indigo-600 px-4">{systemPrompt}</div>
            <div className="bg-indigo-600 px-4">{userPrompt}</div>
            <div className="bg-indigo-600 px-4">{negativePrompt}</div>

            {children.imageinput ? (
              <div className="mb-4 px-4">
                <Label className="block text-lg font-medium ml-1 mb-1">
                  {"Image"}:
                </Label>

                <Input
                  type="file"
                  name={"image"}
                  required={true}
                  accept="image/*"
                  onChange={handleImageFileChange}
                  className="w-full p-2 bg-indigo-600 rounded-lg text-lg"
                  placeholder="Add image"
                />
              </div>
            ) : null}

            <div className="bg-indigo-800 px-4 py-4 min-h-80 h-fit rounded">
              <div className="">
                {output ? (
                  <div>
                    {children.modelType === "text" ? (
                      <div className="text-sm rounded-lg h-fit relative">
                        <div className="bg-indigo-600 z-10 w-full absolute">
                          <h2 className="text-lg mt-1 absolute">
                            Output Section:
                          </h2>
                          <div
                            className="absolute mx-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                            onClick={handleCopy}
                          >
                            {spinLoading ? (
                              <div className="loader"></div>
                            ) : (
                              <Copy className="" />
                            )}
                          </div>
                        </div>
                        <div className="pt-12">
                          <div >
                            <ReactMarkdown
                              rehypePlugins={[rehypeSanitize]}
                              remarkPlugins={[remarkGfm]}
                            >
                              {output}
                            </ReactMarkdown>
                          </div>
                        </div>
                      </div>
                    ) : null}
                    {children.modelType === "image" ? (
                      <div className="rounded-lg relative h-fit">
                        <div
                          className="absolute m-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                          onClick={handleDownload}
                        >
                          {spinLoading ? (
                            <div className="loader"></div>
                          ) : (
                            <Download className="" />
                          )}
                        </div>
                        <img src={output} alt="preview" className="rounded-lg" loading="lazy" />
                      </div>
                    ) : null}
                    {audioFile ? (
                <div className="relative flex flex-col justify-between h-full">
                  <div className="relative flex-1">
                    <h2 className="text-lg font-semibold text-yellow-400 mb-4">
                      Audio Output:
                    </h2>
                    <audio key={audioKey} controls className="w-full">
                      <source src={audioFile} type="audio/mpeg" />
                      Your browser does not support the audio tag.
                    </audio>
                  </div>
                </div>
              ) : null
              // (
              //   <p className="text-center text-lg text-yellow-400 bg-indigo-700 flex items-center justify-center h-full">
              //     No output generated yet.
              //   </p>
              // )
              }
                  </div>
                ) : (
                  <p className="text-2xl font-bold text-center mt-28 animate-pulse">
                    Running this rapp will deduct {"  "}
                    {(children.commission != null || children.commission != undefined) ? (
                        children.computationcost + children.commission
                      ) : (
                        children.computationcost
                      )}
                    <img
                      src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                      alt="Coin"
                      style={{
                        width: "25px",
                        height: "27px",
                        display: "inline",
                        margin: "0px 4px 4px 2px",
                      }}
                       loading="lazy"
                    />
                    from your account.
                  </p>
                )}
              </div>
            </div>
            <div className="w-full text-center">
              <button
                type="submit"
                className="bg-gradient-to-br from-indigo-700 to-indigo-800 text-white py-4 px-8 rounded mt-4 focus:ring-offset-2 text-center transition-all hover:shadow-lg hover:shadow-blue-gray-500/20"
              >
                {spinLoading ? <div className="loader"></div> : <div>Run</div>}
              </button>
            </div>
          </form>
        </div>
        <p className="-mb-5 mt-3 text-center">Need help? <a href="https://discordapp.com/channels/1179720072370065428/1280188340108525661" target="_blank" className="ml-1 underline text-green-400">Contact Support Team</a></p>
      </div>
    </div>
  );
};

export default SerializeRapp;
