import React from "react";
import SerializeRapp from "./serializeRapp";
import serializeModel from "./serializeModel";
import SerializeBlog from "./serializeBlog";

export const RenderRapp: React.FC<{ content: any; user: any; id: string }> = ({
  content,
  user,
  id,
}) => {
  if (!content) {
    return null;
  }
  return <div id="root">{SerializeRapp(content, user, id)}</div>;
};
export const RenderModel: React.FC<{ content: any, user: any, id: string }> = ({ content ,user, id}) => {
  if (!content) {
    return null;
  }
  return <div>{serializeModel(content, user, id)}</div>;
};
export const RenderBlog: React.FC<{ content: any; id }> = ({ content, id }) => {
  if (!content) {
    return null;
  }
  return <div id="root">{SerializeBlog(content, id)}</div>;
  //return <RichText content={content?.richText}  className='px-12' />
};
