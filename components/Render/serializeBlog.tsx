import React from "react";
import { Blog } from "@/server/payload-types";
import RichText from "../RichText";
import { Media } from "@/server/payload-types";
import BlogImages from "../BlogImages";

 //------------------------------for image render---------------------------------------
 const renderImage = (image: string | Media | undefined) => {
  if (typeof image === "string") {
    return <img src={image ?? undefined} alt="Image"/>;
  } else if (image && image.sizes && image.sizes.thumbnail) {
    return <img src={image.sizes.thumbnail.url ?? undefined} alt="Image"/>;
  }
  return null;
};


const SerializeBlog = (children: Blog, id: string): React.ReactNode => {

  interface Blog {
    id: string;
    title: string;
    content: string;
    status?: string
    time: string;
    tags: string;
    //image: { id: string; url: string; alt: string }[];
    images:object[];
  }

  if (!children) {
    return null;
  }

  return (
 <>

  <div className="w-9/12 mx-auto justify-center pt-8 md:mt-20">

    <div className="md:flex md:flex-wrap justify-between items-center">
            {/* <button className="text-indigo-200 px-2 bg-indigo-700 py-2 my-4 rounded-lg shadow shadow-indigo-900">
              <div className="flex items-center text-md font-bold">
                <ArrowLeft />
                <p className="">
                 Back to Blogs
                </p>
              </div>
            </button> */}

          <div className="flex gap-2 text-sm md:text-lg text-indigo-200 font-bold">
              <p>
                 {children.time} min read
              </p>
              <span> | </span>
              <p>
                {new Date(children.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                })}
              </p>
            </div>
          </div> 
          
          <h1 className="text-xl md:text-5xl font-bold text-white mb-4 pt-12 ">
            {children.title}
          </h1>
          <div className="py-4">

             <BlogImages image={children.images[0]} />

            </div>
          <div className="text-white">
            {/* <p>{blog.content}</p> */}
            <RichText content={children?.richText} />
          </div>

    
  </div>
 </>
  );
};

export default SerializeBlog;
