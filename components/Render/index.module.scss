.container {
  display: flex;
  flex-direction: column;
  border-radius: 0.5rem;
  max-width: 28rem;
  width: 100%;
  padding: 1rem;
  color: white;
  margin: 1.5rem auto;
  background-color: #4F46E5F1;
}

.wrap {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #3730a3;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.topSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  background-color: #4f46e5;
  border-radius: 0.5rem;
  width: 100%;
}

.middleSection {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-top: 1rem;
  gap: 1rem;
  background-color: #3730A3;
  border-radius: 0.5rem;
  padding: 1rem;
  width: 100%;
}

.price {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #4f46e5;
  border-radius: 0.5rem;
  width: 100%;
  margin-top: 1rem;
}

.formcontainer {
  background-color: #4f46e5;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-width: 28rem;
  margin: 1.5rem auto;
  color: white;
  min-height: 30rem;
}

.formheading {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: left;
}

.formgroup {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formlabel {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: white;
}

.forminput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: #3730a3;
  color: white;

  &:focus {
    outline: none;
    border-color: #8e9db5;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
  }
}

.button {
  background-color: #4338ca;
  padding: 0.75rem;
  border-radius: 0.25rem;
  font-size: 1rem;
  margin-top: 1rem;
  border: none;
  cursor: pointer;
  font-weight: 600;
  color: white;
  text-align: center;
  transition: background-color 0.2s;

  &:hover {
    background-color: #3730a3;
  }

  &:focus {
    outline: none;
    background-color: #3730a3;
  }

  &:active {
    background-color: #3730a3;
  }
}

.form {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  width: 100%;
}

.input {
  color: white;
}

.output {
  background-color: #4f46e5;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin: 1.5rem;
  color: white;
  min-height: 30rem;
}

.field {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0rem;
  width: 100%;
  margin-bottom: 1rem;
  background: #4f46e5;
  border-radius: 0.25rem;
  padding: 0.5rem;
  color: white;
  box-sizing: border-box;
}

.textInput {
  background: #3730a3;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.75rem;
  width: 100%;
  color: white;
  box-sizing: border-box;

  ::placeholder {
    color: white;
  }
}

/* Media query for medium and larger screens */
@media (min-width: 768px) {
  .container, .formcontainer {
    max-width: none;
    margin: 1.5rem;
  }
  
  .layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}
