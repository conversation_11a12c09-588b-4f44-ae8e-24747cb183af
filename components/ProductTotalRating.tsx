import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Star } from "lucide-react";

const ProductTotalRating = ({ productId, user, onRatingFetched }) => {
  const [totalProductRating, setTotalProductRating] = useState(0);

  useEffect(() => {
    const fetchTotalProductRating = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/products/rating/${productId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            credentials: "include", // Include cookies (optional)
          }
        );

        const data = await response.json();
        if (response.ok) {
          setTotalProductRating(data.rating); // Set the total product rating
          if (onRatingFetched) {
            onRatingFetched(data.rating); // Pass the rating to the parent via callback
          }
        } else {
          toast.error(data.error || "Failed to fetch product rating.");
        }
      } catch (error) {
        console.error("Error fetching product rating:", error);
        toast.error("An error occurred while fetching the product rating.");
      }
    };

    fetchTotalProductRating();
  }, [productId]);

  // Calculate the width for the filled star based on the rating (1 star = 20%).
  const filledWidth = (totalProductRating / 5) * 100;


  if (totalProductRating === 0) {
    return null; 
  }

  
  return (
    <div className="flex items-center">
      <div className="flex items-center">
        <div
          style={{
            position: "relative",
            width: "1rem",
            height: "1rem",
            marginRight: "0.4rem",
          }}
        >
          {/* Background star (empty) */}
          <Star
            style={{
              position: "absolute",
              color: "#FFFFFF", // Background color for empty part
              width: "100%",
              height: "100%",
            }}
          />

          {/* Foreground star (filled) */}
          <Star
            style={{
              position: "absolute",
              color: "#FACC15", // Filled color for rating part
              width: "100%",
              height: "100%",
              clipPath: `inset(0 ${100 - filledWidth}% 0 0)`, // Fill based on rating percentage
            }}
            className="fill-yellow-400"
          />
        </div>
        {/* <p className="text-white font-semibold ml-2">{totalProductRating.toFixed(1)}</p> */}
        <div className=" text-white text-sm font-semibold -ml-1">
          {totalProductRating === 0 ? 0 : totalProductRating.toFixed(1)}
        </div>
      </div>
    </div>
  );
};

export default ProductTotalRating;
