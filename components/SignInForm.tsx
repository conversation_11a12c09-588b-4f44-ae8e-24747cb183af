"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight, Loader2 } from "lucide-react";
import Link from "next/link";
import { useForm } from "react-hook-form";
import {
  AuthCredentialsValidator,
  TAuthCredentialsValidator,
} from "@/lib/validators/account-credentials-validator.ts";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { Icons } from "@/components/Icons";
import { buttonVariants } from "@/components/ui/button";

const SignInForm = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const isSeller = searchParams.get("as") === "seller";
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const redirectTo = searchParams.get("from") || "/";

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TAuthCredentialsValidator>({
    resolver: zodResolver(AuthCredentialsValidator),
  });

  const onSubmit = async ({ email, password }: TAuthCredentialsValidator) => {
    setIsLoading(true);
    try {
      const { data }: { data: CustomEndPointResponse } = await axios.post(
        `/api2/user/signin`,
        {
          email,
          password,
        }
      );
      setIsLoading(false);
      if (data.success === false) {
        toast.error("Invalid email or password.");
        return;
      }
      toast.success("Signed in Successfully");

      //NOTE refresh then push
      router.refresh();

      if (redirectTo === "/verify-email") {
        router.push("/");
      } else {
        router.push(redirectTo);
      }
    } catch (e) {
      setIsLoading(false);
      console.log(e.message);
      toast.error(e.response.data.message);
    }
  };

  const BottomGradient = () => {
    return (
      <>
        <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
        <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
      </>
    );
  };

  return (
    <div className="container relative flex pt-20 md:mt-5 flex-col items-center justify-center lg:px-0">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col items-center space-y-2 text-center">
          <Icons.logo className="h-20 w-20 fill-muted-foreground" />
          <h1 className="text-2xl font-semibold tracking-tight">
            Sign in to your {isSeller ? "seller" : ""} account
          </h1>

          <Link
            className={buttonVariants({
              variant: "link",
              className: "gap-1.5",
            })}
            href="/sign-up"
          >
            Don&apos;t have an account?
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
        <div className="grid gap-6">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-2">
              <div className="grid gap-1 py-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  {...register("email")}
                  className={cn({
                    "focus-visible:ring-red-500": errors?.email,
                  })}
                  placeholder="<EMAIL>"
                />
                {errors?.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              <div className="grid gap-1 py-2 relative">
                <Label htmlFor="password">Password</Label>
                <div className="relative ">
                  <Input
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    className={cn({
                      "focus-visible:ring-red-500": errors?.password,
                    })}
                    placeholder="Password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0  top-1 right-0  pr-3 flex items-center text-sm leading-5"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-indigo-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-indigo-400" />
                    )}
                  </button>
                </div>
                {errors?.password && (
                  <p className="text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
              </div>
              <div className="text-right">
                <Link
                  href="/forgot-password"
                  className="text-sm text-indigo-500 hover:underline"
                >
                  Forgot Password?
                </Link>
              </div>

              <Button
                className="flex bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 w-full text-white rounded-md h-10 font-medium"
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign in
                <BottomGradient />
              </Button>
            </div>
          </form>

          <div className="relative">
            <div
              aria-hidden="true"
              className="absolute inset-0 flex items-center"
            >
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                or
              </span>
            </div>
          </div>
          {isSeller ? (
            <Button
              className="bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 block w-full text-white rounded-md h-10 font-medium"
              onClick={() => {
                router.replace("/sign-in", undefined);
              }}
              variant="secondary"
              disabled={isLoading}
            >
              Continue as customer
              <BottomGradient />
            </Button>
          ) : (
            <Button
              className="bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 block w-full text-white rounded-md h-10 font-medium"
              onClick={() => {
                router.push("?as=seller");
              }}
              variant="secondary"
              disabled={isLoading}
            >
              Continue as seller
              <BottomGradient />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default SignInForm;