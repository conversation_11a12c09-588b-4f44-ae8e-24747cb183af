"use client";

import { useRouter } from "next/navigation";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import ShinnyButton from "./ui/ShinnyButton";

const NavLoginButton = () => {
  const router = useRouter();

  //after signin,user returns to the previous path
  const handleLoginClick = () => {
    const currentPath = window.location.pathname + window.location.search;
    if (currentPath === "/sign-up") {
      router.push("/sign-in");
    }else
    router.push(`/sign-in?from=${encodeURIComponent(currentPath)}`);
  };

  return (
    <ShinnyButton
      onClick={handleLoginClick}
      className=" px-3 mr-2"
    >
      <p className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">Login</p>
    </ShinnyButton>
  );
};

export default NavLoginButton;
