"use client";

import { cn } from "@/lib/utils";
import {
    GlowingStarsBackground,
    Illustration,
  } from "@/components/ui/glowing-stars";
import { User } from "@/server/payload-types";
import Link from "next/link";
import Image from "next/image";

interface CommunityMembersProps {
    sortedUsers: User[];
  }

const CommunityMembers = ({sortedUsers}:CommunityMembersProps) => {
  return (
    <div className="relative">
      <GlowingStarsBackground className="absolute -z-10 inset-x-0 -top-[140px] lg-top-[calc(100%-32rem)]" />
      <div className="flex flex-col w-11/12 mx-auto mt-28">
      <div className="text-3xl md:text-4xl text-white font-medium">
        Top Builder Profiles
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-none lg:flex relative z-10 mt-20 mx-auto w-full lg:w-auto">
      {sortedUsers.map((user, index) => (
        <Feature key={user.id} user={user} index={index}/>
      ))}
    </div>
    </div>
    <Illustration mouseEnter={false} />
    </div>
   
  );
};

export default CommunityMembers;

const Feature = ({user, index}) => {
  return (
      <div
      className={cn(
        "flex flex-col border-r lg:border-r  py-10 relative group/feature dark:border-neutral-800 border-white/[0.2] flex justify-center items-center",
        "border-l",
        (index === 0) && "border-l lg:border-l dark:border-neutral-800",
        index < 7 && "border-b lg:border-b dark:border-neutral-800"
      )}
    >
      {index < 7 && (
        <div className="opacity-0 group-hover/feature:opacity-50 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      {/* {index >= 4 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )} */}
      <Link href={`/users/${user.id}`} className="px-8">
          <Image
            height={100}
            width={100}
            src={user?.profileImage?.url ?? "/mailly.png"}
            alt={user.id}
            className={`px-6 object-cover !m-0 !p-0 object-top rounded-full sm:h-32 sm:w-32 h-32 w-32 border-2 group-hover:scale-105 group-hover:z-10 border-white  relative transition duration-500`}
          />
          </Link>
      <div className="text-2xl font-bold mb-2 relative z-10 mt-4 w-full text-center">
        <div className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover/feature:bg-blue-500 transition-all duration-200 origin-center" />
        <span className="group-hover/feature:translate-x-2 transition duration-200 inline-block">
          {user?.user_name ?? user?.email?.split("@")[0]}
        </span>
      </div>
      {/* <p className="text-xl text-zinc-400 dark:text-neutral-300 max-w-xs relative z-10 px-10">
        {user?.generalInformation?.profession ?? user?.role}
      </p> */}
    </div>
   
    
  );
};

// "use client";

// import {
//   GlowingStarsBackground,
//   Illustration,
// } from "@/components/ui/glowing-stars";
// import { AnimatedTooltip } from "@/components/ui/animated-tooltip";
// import { User } from "@/server/payload-types";

// interface CommunityMembersProps {
//   sortedUsers: User[];
// }

// const CommunityMembers = ({sortedUsers} : CommunityMembersProps) => {
//   return (
//     <div className="relative">
//       <GlowingStarsBackground className="absolute -z-10 inset-x-0 -top-[140px] lg-top-[calc(100%-32rem)]" />
//       <div className="flex flex-col w-11/12 mx-auto mt-28">
//         <div className="text-3xl md:text-4xl text-white font-medium">
//           Top Builder Profiles
//         </div>
//         <div className="flex flex-row items-center justify-center w-full mt-20">
//           <AnimatedTooltip items={sortedUsers} component={'communitypage'} />
//         </div>
//       </div>
//       <Illustration mouseEnter={false} />
//     </div>
//   );
// };

// export default CommunityMembers;



// 63 px-16
