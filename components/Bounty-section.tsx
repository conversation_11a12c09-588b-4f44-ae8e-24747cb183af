"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";

import { EvervaultCard, Icon } from "@/components/ui/evervault-card";

export const BountySection = () => {
  const [bounties, setBounties] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBounties = async () => {
      try {
        const response = await fetch("/api2/bounties");
        const data = await response.json();
        setBounties(data.data);
      } catch (error) {
        console.error("Error fetching bounties:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchBounties();
  }, []);

  const calculateDaysLeft = (completionDate: string) => {
    const completionDateObj = new Date(completionDate);
    const currentDate = new Date();
    const timeDiff = completionDateObj.getTime() - currentDate.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    return daysLeft;
  };

  return (
    <div className="pb-12 md:pb-24 w-11/12 mx-auto">
      <div className="flex items-center justify-between mb-10">
          <div className="text-white text-3xl md:text-4xl font-medium">
          Bounties
          </div>
          <Link href={"/bounties"}>
            <button className="bg-gradient-to-r from-amber-500 to-pink-500 text-white font-semibold py-2 px-4 rounded-full text-sm md:text-md">
              READ MORE →
            </button>
          </Link>
        </div>

      {loading ? (
        <div className="flex justify-center items-center">
          <p className="text-gray-200">Loading bounties...</p>
        </div>
      ) : bounties?.length > 0 ? (
        <div className="w-full grid grid-cols-1 gap-6 md:grid-cols-2">
          {bounties
            ?.filter((bounty) => bounty.isFeatured) // Get only featured bounties
            .slice(0, 4) // Show only the first 4
            .map((bounty, i) => {
              const daysLeft = calculateDaysLeft(bounty.completionDate);
              const isExpired = daysLeft < 0;
              const isCompleted = bounty.status === "completed";

              return (
                <Link key={bounty.slug} href={`/bounties/${bounty.slug}`}>
                  <div className="flex w-full">
                    <div className="bg-indigo-700 w-full p-4 rounded-xl shadow-md border border-slate-300 hover:shadow-lg hover:border-indigo-400 transition-all transform hover:-translate-y-1">
                      <div className="flex justify-between items-center mb-4">
                        {/* Price Section */}
                        {bounty.isFeatured === true && (
                          <div className="absolute top-[-10px] -left-5 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                            <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                              FEATURED
                              <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                            </div>
                          </div>
                        )}

                        <div className="text-white text-4xl flex items-center font-bold">
                          {bounty.estimatedPrice === 0 ? (
                            <div className="px-3 py-1 rounded-full bg-gradient-to-r  text-white text-xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                              FREE
                            </div>
                          ) : (
                            bounty.estimatedPrice
                          )}
                          {bounty.estimatedPrice === 0 ? (
                            ""
                          ) : (
                            <img
                              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                              style={{ width: "35px", height: "39px" }}
                              alt="coin"
                              loading="lazy"
                            />
                          )}
                        </div>
                        <button className="bg-gray-800 text-white text-sm rounded-full px-3 py-1 hover:bg-gray-600">
                        {isCompleted ? (
                          <p className="text-green-500 text-sm">Completed</p>
                        ) : isExpired ? (
                          <p className="text-red-500 text-sm">Expired</p>
                        ) : (
                          <p className="text-yellow-500 text-sm">
                            {daysLeft} day{daysLeft === 1 ? "" : "s"} left
                          </p>
                        )}
                        </button>
                      </div>

                      {/* Bounty Title */}
                      <h3 className="text-lg sm:text-xl font-semibold text-white mb-2 leading-tight line-clamp-2 capitalize">
                        {bounty.title.length > 25
                          ? `${bounty.title.substring(0, 30)}...`
                          : bounty.title}
                      </h3>

                      {/* Content Preview */}
                      <p className="text-gray-300 mb-4 line-clamp-1 text-md sm:text-lg first-letter:uppercase">
                        {bounty.content.length > 30
                          ? `${bounty.content.substring(0, 50)}...`
                          : bounty.content}
                      </p>

                      {/* Bounty Type and Applicants */}
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-indigo-200 ">
                          Type: {bounty.type}
                        </p>
                        <p className="text-white text-sm">
                          {bounty?.applicants} Applicants
                        </p>
                      </div>
                      
                    </div>
                  </div>
                </Link>
              );
            })}
        </div>
      ) : (
        <p className="text-gray-200">No Bounties yet.</p>
      )}
    </div>
  );
};