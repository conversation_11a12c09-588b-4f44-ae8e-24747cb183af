"use client";

import StepOne from "@/components/bountysteps/stepone";

import <PERSON>Three from "@/components/bountysteps/stepthree";

import StepTwo from "@/components/bountysteps/steptwo";

import TopHeader from "@/components/ui/topheader";

import Link from "next/link";

import { useRouter } from "next/navigation";

import { useSearchParams } from "next/navigation";

import React, { useEffect, useState } from "react";

import { toast } from "sonner";

import { SpaceModal } from "@/components/SpaceModal";

import { Button } from "@/components/ui/button";

import StepNavigation from "../ui/dashboard/StepNavigation";

import ConfirmationModal from "../ui/dashboard/confirmdelete";

// import { useDebounce } from "@/app/hooks/use-debounce";

export default function CreateBountyForm({ user }) {
  const router = useRouter();

  const searchParams = useSearchParams();

  const id = searchParams.get("Id");

  const [currentStep, setCurrentStep] = useState(1);

  const [step, setStep] = useState(1);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: "",

    content: "",

    user: "",

    completionDate: "",

    estimatedPrice: "",

    product_files: null,

    termsAccepted: "",

    needsApproval: "",

    tags: [],

    slug: "",

    bountyType: "",
    status: "pending",
  });

  const [clickedButton, setClickedButton] = useState(null);

  const [errors, setErrors] = useState({
    title: "",

    content: "",

    tags: "",

    estimatedPrice: "",

    completionDate: "",

    bountyType: "",

    product_files: "",
  });

  // Separate storage keys for create and edit modes
  const getStorageKey = () => (id ? "editFormData" : "createFormData");
  const getStepKey = () => (id ? "editFormStep" : "createFormStep");

  const validateStepOne = () => {
    let valid = true;

    const newErrors = { ...errors };

    if (!formData.title) {
      newErrors.title = "Bounty Title is required";

      valid = false;
    }

    if (!formData.content) {
      newErrors.content = "Bounty Description is required";

      valid = false;
    }

    setErrors(newErrors);

    return valid;
  };

  const validateStepTwo = () => {
    let valid = true;

    const newErrors = { ...errors };

    if (formData.estimatedPrice === "") {
      newErrors.estimatedPrice = "Estimated Price is required";

      valid = false;
    }

    if (!formData.completionDate) {
      newErrors.completionDate = "Please select a completion date.";

      valid = false;
    }

    if (!formData.bountyType) {
      newErrors.bountyType = "Please select a bounty type.";

      valid = false;
    }

    setErrors(newErrors);

    return valid;
  };

  const validateStepThree = () => {
    let valid = true;

    const newErrors = { ...errors };

    setErrors(newErrors);

    return valid;
  };

  useEffect(() => {
    if (!user) {
      router.push("/sign-in");
    }
  }, [user]);

  const totalSteps = 3;

  const nextStep = () => {
    let isValid = true;

    if (currentStep === 1) {
      isValid = validateStepOne();
    } else if (currentStep === 2) {
      isValid = validateStepTwo();
    }

    if (isValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep((prev) => prev - 1);
  };

  useEffect(() => {
    if (id) {
      const fetchProduct = async () => {
        try {
          const response = await fetch(`/api/bounties/bounty-detail/${id}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            credentials: "include",
          });
          if (response.ok) {
            const bountyData = await response.json();
            const bounty = bountyData.data;

            const completionDate =
              bounty?.completionDate &&
              new Date(bounty.completionDate).toISOString().split("T")[0];
            
            setFormData({
              title: bounty?.title || "",
              content: bounty?.content || "",
              user: bounty?.product_files?.user || "",
              completionDate: completionDate || "",
              estimatedPrice:
                bounty?.estimatedPrice === 0 ? 0 : bounty?.estimatedPrice,
              product_files: bounty?.product_files || "",
              termsAccepted: "",
              slug: bounty?.slug || "",
              needsApproval: bounty?.needsApproval || "",
              tags: bounty?.tags || [],
              bountyType: bounty.bountyType,
              status: bounty?.status || "",
            });
          } else {
            console.error("Failed to fetch product data.");
          }
        } catch (error) {
          console.error("Error fetching product:", error);
        }
      };

      fetchProduct();
    }
  }, [id]);

  const handleFormSubmit = async (needsApprovalValue) => {
    setIsLoading(true);

    if (!validateStepThree()) {
      setIsLoading(false);

      return;
    }

    try {
      const userCoinBalance = user?.coinBalance ?? 0;

      const estimatedPrice = parseFloat(formData.estimatedPrice);

      if (userCoinBalance < estimatedPrice) {
        toast.error("Insufficient balance. Please recharge your account.");

        setIsLoading(false);

        return;
      }
      const blogStatus =
      formData?.status === "denied" ? "pending" : formData?.status;
      // Prepare FormData for bounty creation

      const formDataToSend = new FormData();

      formDataToSend.append("title", formData.title);

      formDataToSend.append("content", formData.content);

      formDataToSend.append("completionDate", formData.completionDate);

      formDataToSend.append("estimatedPrice", formData.estimatedPrice);

      formDataToSend.append("bountyType", formData.bountyType);

      formDataToSend.append("user", user?.id);
      formDataToSend.append("status", blogStatus);
      formDataToSend.append("termsAccepted", formData.termsAccepted.toString());

      // Use the passed `needsApprovalValue` instead of formData.needsApproval

      formDataToSend.append(
        "needsApproval",

        needsApprovalValue ? "true" : "false"
      );
      // console.log("formData?.product_files", formData?.product_files);
      formDataToSend.append("product_files", formData?.product_files?.id || "");

      formData.tags.forEach((tag) => {
        formDataToSend.append("tags", tag);
      });

      // Submit bounty data

      let bountyResponse;

      if (id) {
        bountyResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${id}?depth=0`,

          {
            method: "PATCH",

            credentials: "include",

            body: formDataToSend,
          }
        );
      } else {
        bountyResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties?depth=0`,

          {
            method: "POST",

            credentials: "include",

            body: formDataToSend,
          }
        );
      }

      if (bountyResponse.ok) {
        toast.success(
          id ? "Bounty Updated Successfully!" : "Bounty Created Successfully!"
        );
        localStorage.removeItem(getStepKey());
        localStorage.removeItem(getStorageKey());
        router.push("/dashboard/bounties");
      } else {
        const error = await bountyResponse.json();
        console.error("Failed to create bounty:", error);

        toast.error(
          error.message || "Failed to create bounty. Please try again."
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);

      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitWrapper = (needsApprovalValue) => {
    if (clickedButton !== null) return; // Prevent multiple clicks

    setClickedButton(needsApprovalValue ? "publish" : "submit"); // Track which button was clicked

    setIsLoading(true);

    handleFormSubmit(needsApprovalValue).finally(() => {
      setIsLoading(false);

      setClickedButton(null); // Reset clicked button after request completes
    });
  };

  useEffect(() => {
    const savedStep = localStorage.getItem(getStepKey());
    const savedData = localStorage.getItem(getStorageKey());
    if (savedStep && !id) {
      // Only load step if not editing
      setCurrentStep(parseInt(savedStep, 10));
    }
    if (savedData && !id) {
      // Only load data if not editing (unless no saved data for edit)
      try {
        const parsedData = JSON.parse(savedData);

        setFormData((prev) => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error("Error parsing saved form data:", error);
      }
    }
  }, [id]);

  useEffect(() => {
    if (formData.title) {
      localStorage.setItem(getStepKey(), currentStep.toString());
      localStorage.setItem(getStorageKey(), JSON.stringify(formData));
    }
  }, [currentStep, formData, id]);

  const confirmDelete = () => {
    localStorage.removeItem(getStepKey());
    localStorage.removeItem(getStorageKey());
    router.push("/dashboard");
  };

  const closeModal = () => setIsModalOpen(false);

  const renderStep = () => {
    if (!formData) return <p>Loading...</p>;

    switch (currentStep) {
      case 1:
        return (
          <StepOne
            formData={formData}
            setFormData={setFormData}
            errors={errors}
            setErrors={setErrors}
            validateStepOne={validateStepOne}
          />
        );

      case 2:
        return (
          <StepTwo
            formData={formData}
            setFormData={setFormData}
            errors={errors}
            setErrors={setErrors}
            id={id}
          />
        );

      case 3:
        return (
          <StepThree
            formData={formData}
            setFormData={setFormData}
            errors={errors}
            setErrors={setErrors}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      <TopHeader
        user={user}
        title="Create Bounty"
        buttonName="Bounties"

        // buttonLink="/dashboard/bounties"
      />

      <div className="flex-col flex items-center py-2 bg-dash-foreground rounded-lg md:m-6">
        <div className="max-w-3xl w-full">
          <div className="space-y-4 py-4 mx-4">
            <StepNavigation step={currentStep} totalSteps={totalSteps} />

            <div>{renderStep()}</div>

            <div className="flex justify-between pt-4">
              <div className="flex gap-4">
                {currentStep !== 1 && (
                  <button
                    onClick={prevStep}
                    className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
                  >
                    Previous
                  </button>
                )}

                {/* Cancel Button (Only on Step 1) */}
                {currentStep === 1 && (
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
                  >
                    Cancel
                  </button>
                )}

                {currentStep === 2 && (
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
                  >
                    Cancel
                  </button>
                )}

                {currentStep === 3 && (
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="bg-white text-indigo-800 font-semibold py-2 px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
                  >
                    Cancel
                  </button>
                )}
              </div>

              {currentStep === 3 ? (
                <div className="flex gap-4">
                  {/* Publish Button (Without Approval) */}

                  <button
                    onClick={() => handleSubmitWrapper(false)}
                    className={`${
                      formData.termsAccepted
                        ? "bg-green-600 hover:bg-green-700"
                        : "bg-gray-400 cursor-not-allowed"
                    } text-white py-2 px-10 rounded-lg font-semibold shadow-md transition-all duration-300`}
                    disabled={!formData.termsAccepted || clickedButton !== null}
                  >
                    {isLoading && clickedButton === "submit"
                      ? "Submitting..."
                      : "Submit"}
                  </button>

                  <button
                    onClick={() => handleSubmitWrapper(true)}
                    className={`${
                      formData.termsAccepted
                        ? "bg-indigo-600 hover:bg-indigo-700"
                        : "bg-gray-400 cursor-not-allowed"
                    } text-white py-2 px-10 rounded-lg font-semibold shadow-md transition-all duration-300`}
                    disabled={!formData.termsAccepted || clickedButton !== null}
                  >
                    {isLoading && clickedButton === "publish"
                      ? "Publishing..."
                      : "Publish"}
                  </button>
                </div>
              ) : (
                <button
                  onClick={nextStep}
                  className="bg-indigo-600 text-white py-2 px-10 rounded-lg font-semibold shadow-md hover:bg-indigo-700 transition-all duration-300"
                >
                  Save & Next
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <ConfirmationModal
        component="bountyForm"
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Cancel"
        message={`Are you sure you want to cancel?`}
      />

      {/* {isModalOpen && (

        <SpaceModal

          title=""

          description=""

          isOpen={isModalOpen}

          onClose={() => {

            setIsModalOpen(false);

          }}

        >

          <div className="space-y-3 py-2 pb-2">

            <p className="text-2xl font-bold">

              Are you sure you want to cancel? This will erase all your filled

              data.

            </p>

            <div className="pt-6 space-x-2 flex items-center justify-end">

              <Button variant="outline" onClick={() => setIsModalOpen(false)}>

                Close

              </Button>

              <Button variant="outline" onClick={handleCancel}>

                Confirm

              </Button>

            </div>

          </div>

        </SpaceModal>

      )} */}
    </>
  );
}
