"use client";
import StepOne from "@/components/bountysteps/stepone";
import StepTwo from "@/components/bountysteps/steptwo";
import TopHeader from "@/components/ui/topheader";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import StepNavigation from "../ui/dashboard/StepNavigation";
import ConfirmationModal from "../ui/dashboard/confirmdelete";
import { CustomDialogWithDash } from "./CustomDialog";

export default function CreateBountyForm({ user }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("Id");
  const formRef = useRef<HTMLFormElement>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitConfirmModal, setSubmitConfirmModal] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    user: "",
    completionDate: "",
    applyExpireDate: "",
    applicantsLimit: "",
    estimatedPrice: "",
    updatedEstimatedPrice: "",
    product_files: null,
    termsAccepted: "",
    needsApproval: "",
    tags: [],
    slug: "",
    useCases: [],
    bountyType: "",
    status: "",
  });

  const [clickedButton, setClickedButton] = useState(null);
  const [errors, setErrors] = useState({
    title: "",
    content: "",
    bountyType: "",
    tags: "",
    estimatedPrice: "",
    updatedEstimatedPrice: "",
    completionDate: "",
    applyExpireDate: "",
    applicantsLimit: "",
    useCases: "",
    product_files: "",
  });

  // Separate storage keys for create and edit modes
  const getStorageKey = () => (id ? "editFormData" : "createFormData");
  const getStepKey = () => (id ? "editFormStep" : "createFormStep");

  const validateStepOne = () => {
    let valid = true;
    const newErrors: any = { ...errors };

    if (!formData.title?.trim()) {
      newErrors.title = "Bounty Title is required";
      valid = false;
    }

    if (!formData.content?.trim()) {
      newErrors.content = "Bounty Description is required";
      valid = false;
    }

    if (!formData.useCases || formData.useCases.length === 0) {
      newErrors.useCases = "Bounty Type is required";
      valid = false;
    }

    if (!formData.bountyType || formData.bountyType === "") {
      newErrors.bountyType = "Bounty Type is required";
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const validateStepTwo = () => {
    let valid = true;
    const newErrors = { ...errors };

    if (formData.estimatedPrice === "") {
      newErrors.estimatedPrice = "Estimated Price is required";
      valid = false;
    }

    if (!formData.completionDate) {
      newErrors.completionDate = "Please select a completion date.";
      valid = false;
    }

    if (!formData.applyExpireDate) {
      newErrors.applyExpireDate = "Please select an Expiry date.";
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  useEffect(() => {
    if (!user) {
      router.push("/sign-in");
    }
  }, [user]);

  const totalSteps = 2;

  const nextStep = () => {
    let isValid = true;

    if (currentStep === 1) {
      isValid = validateStepOne();
    } else if (currentStep === 2) {
      isValid = validateStepTwo();
    }

    if (isValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep((prev) => prev - 1);
  };

  useEffect(() => {
    if (id) {
      const fetchProduct = async () => {
        try {
          const response = await fetch(`/api/bounties/bounty-detail/${id}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            credentials: "include",
          });
          if (response.ok) {
            const bountyData = await response.json();
            const bounty = bountyData.data;
            const completionDate =
              bounty?.completionDate &&
              new Date(bounty.completionDate).toISOString().split("T")[0];

            const applyExpireDate =
              bounty?.applyExpireDate &&
              new Date(bounty.applyExpireDate).toISOString().split("T")[0];
            setFormData({
              title: bounty?.title || "",
              content: bounty?.content || "",
              user: bounty?.product_files?.user || "",
              completionDate: completionDate || "",
              applyExpireDate: applyExpireDate || "",
              applicantsLimit: bounty.applicantsLimit,
              estimatedPrice:
                bounty?.estimatedPrice === 0 ? 0 : bounty?.estimatedPrice,
              updatedEstimatedPrice:
                bounty?.updatedEstimatedPrice === undefined
                  ? 0
                  : bounty?.updatedEstimatedPrice,
              product_files: bounty?.product_files || "",
              termsAccepted: bounty?.termsAccepted || false,
              slug: bounty?.slug || "",
              needsApproval: bounty?.needsApproval || "",
              tags: bounty?.tags || [],
              status: bounty?.status,
              bountyType: bounty?.bountyType,
              useCases: bounty?.useCases?.map((useCase) => useCase.id) || [],
            });
          } else {
            console.error("Failed to fetch product data.");
          }
        } catch (error) {
          console.error("Error fetching product:", error);
        }
      };

      fetchProduct();
    }
  }, [id]);

  const handleFormSubmit = async (needsApprovalValue) => {
    setIsLoading(true);

    try {
      const BountyStatus =
        formData?.status === "denied"
          ? "pending"
          : formData?.status || "pending";

      const updatedEstimatedPriceStatus =
        formData?.updatedEstimatedPrice === null ||
        formData?.updatedEstimatedPrice === undefined
          ? "0"
          : formData?.updatedEstimatedPrice;

      const formDataToSend = new FormData();
      formDataToSend.append("title", formData.title);
      formDataToSend.append("content", formData.content);
      formDataToSend.append("completionDate", formData.completionDate);
      formDataToSend.append("applyExpireDate", formData.applyExpireDate);
      formDataToSend.append("applicantsLimit", formData.applicantsLimit);
      formDataToSend.append("estimatedPrice", formData.estimatedPrice);
      formDataToSend.append(
        "updatedEstimatedPrice",
        updatedEstimatedPriceStatus
      );
      formDataToSend.append("bountyType", formData.bountyType);
      formData.useCases.forEach((useCaseId) => {
        formDataToSend.append("useCases", useCaseId);
      });

      formDataToSend.append("user", user?.id);
      formDataToSend.append("status", BountyStatus);
      // formDataToSend.append("termsAccepted", formData.termsAccepted.toString());
      if (id) {
        formDataToSend.append("slug", formData?.slug);
      }
      formDataToSend.append(
        "termsAccepted",
        (formData.termsAccepted as any) ? "true" : "false"
      );

      // Use the passed `needsApprovalValue` instead of formData.needsApproval
      formDataToSend.append(
        "needsApproval",
        needsApprovalValue ? "true" : "false"
      );
      formDataToSend.append("product_files", formData?.product_files?.id || "");
      formData.tags.forEach((tag) => {
        formDataToSend.append("tags", tag);
      });

      let bountyResponse;

      if (id) {
        bountyResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${id}?depth=0`,
          {
            method: "PATCH",
            credentials: "include",
            body: formDataToSend,
          }
        );
      } else {
        bountyResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties?depth=0`,
          {
            method: "POST",
            credentials: "include",
            body: formDataToSend,
          }
        );
      }

      if (bountyResponse.ok) {
        toast.success(
          id ? "Bounty Updated Successfully!" : "Bounty Created Successfully!"
        );
        localStorage.removeItem(getStepKey());
        localStorage.removeItem(getStorageKey());
        router.push("/dashboard/bounties");
      } else {
        const error = await bountyResponse.json();
        console.error("Failed to create bounty:", error);
        toast.error(
          error.message || "Failed to create bounty. Please try again."
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitWrapper = (buttonType) => {
    setClickedButton(buttonType);
    if (!buttonType) return;

    const hasErrors = Object.values(errors).some(
      (value) => value && value.trim() !== ""
    );
    if (hasErrors) {
      toast.error("Please check the form. Some info is missing or incorrect.");
      return;
    }

    const needsApprovalValue =
      buttonType === "publish" ? "publish" : formData.needsApproval;

    setIsLoading(true);

    handleFormSubmit(needsApprovalValue).finally(() => {
      setIsLoading(false);
      setClickedButton(null); // Reset clicked button after request completes
    });
  };

  useEffect(() => {
    const savedStep = localStorage.getItem(getStepKey());
    const savedData = localStorage.getItem(getStorageKey());
    if (savedStep && !id) {
      // Only load step if not editing
      setCurrentStep(parseInt(savedStep, 10));
    }
    if (savedData && !id) {
      // Only load data if not editing (unless no saved data for edit)
      try {
        const parsedData = JSON.parse(savedData);
        setFormData((prev) => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error("Error parsing saved form data:", error);
      }
    }
  }, [id]);

  useEffect(() => {
    if (formData.title) {
      localStorage.setItem(getStepKey(), currentStep.toString());
      localStorage.setItem(getStorageKey(), JSON.stringify(formData));
    }
  }, [currentStep, formData, id]);

  const confirmDelete = () => {
    localStorage.removeItem(getStepKey());
    localStorage.removeItem(getStorageKey());
    router.push("/dashboard");
  };

  const closeModal = () => setIsModalOpen(false);

  const renderStep = () => {
    if (!formData) return <p>Loading...</p>;

    switch (currentStep) {
      case 1:
        return (
          <StepOne
            formData={formData}
            setFormData={setFormData}
            errors={errors}
            setErrors={setErrors}
            validateStepOne={validateStepOne}
            id={id}
          />
        );

      case 2:
        return (
          <StepTwo
            user={user}
            formData={formData}
            setFormData={setFormData}
            errors={errors}
            setErrors={setErrors}
            id={id}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      <TopHeader user={user} title="Create Bounty" buttonName="Bounties" />

      <div className="flex-col flex items-center py-2 bg-dash-foreground rounded-lg md:m-6">
        <div className="max-w-6xl w-full">
          <div className="space-y-4 py-4 mx-4">
            <StepNavigation step={currentStep} totalSteps={totalSteps} />

            <div>{renderStep()}</div>

            {/* Updated Button Container with responsive button labels */}
            <div className="flex flex-row justify-between pt-4 gap-2 sm:gap-4">
              {/* Left Side Buttons (Cancel/Previous) */}
              <div className="flex gap-2 sm:gap-4">
                {currentStep !== 1 && (
                  <button
                    onClick={prevStep}
                    className="bg-white text-indigo-800 font-semibold py-2 px-3 sm:px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
                  >
                    <span className="hidden sm:inline">Previous</span>
                    <span className="sm:hidden">Previous</span>
                  </button>
                )}

                {/* Cancel Button (Shown on all steps) */}
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="bg-white text-indigo-800 font-semibold py-2 px-3 sm:px-6 rounded-lg shadow-md hover:bg-slate-200 hover:shadow-lg transition-all duration-300"
                >
                  <span className="hidden sm:inline">Cancel</span>
                  <span className="sm:hidden">Cancel</span>
                </button>
              </div>

              {/* Right Side Buttons (Save & Next or Submit/Publish) */}
              {currentStep === 2 ? (
                <div className="flex gap-2 sm:gap-4">
                  {/* Submit Button */}
                  <button
                    onClick={() => {
                      if (formData.status === "approved") {
                        handleSubmitWrapper("submit");
                      } else {
                        setClickedButton("submit");
                        setSubmitConfirmModal(true);
                      }
                    }}
                    className={`${
                      !formData.termsAccepted ||
                      !formData.estimatedPrice ||
                      !formData.applicantsLimit ||
                      !formData.completionDate ||
                      !formData.applyExpireDate ||
                      clickedButton === "publish"
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-green-600 hover:bg-green-700"
                    } text-white py-2 px-3 sm:px-6 rounded-lg font-semibold shadow-md transition-all duration-300`}
                    disabled={
                      !formData.termsAccepted ||
                      !formData.estimatedPrice ||
                      !formData.applicantsLimit ||
                      !formData.completionDate ||
                      !formData.applyExpireDate ||
                      clickedButton === "publish"
                    }
                  >
                    {isLoading && clickedButton === "submit" ? (
                      "Submitting..."
                    ) : (
                      <>
                        <span className="hidden sm:inline">Submit</span>
                        <span className="sm:hidden">Submit</span>
                      </>
                    )}
                  </button>
                  {!formData.needsApproval && (
                    <>
                      {/* Publish Button */}
                      <button
                        onClick={() => {
                          if (formData.status === "approved") {
                            handleSubmitWrapper("publish");
                          } else {
                            setClickedButton("publish");
                            setSubmitConfirmModal(true);
                          }
                        }}
                        className={`${
                          !formData.termsAccepted ||
                          !formData.estimatedPrice ||
                          !formData.applicantsLimit ||
                          !formData.completionDate ||
                          !formData.applyExpireDate ||
                          clickedButton === "submit"
                            ? "bg-gray-400 cursor-not-allowed"
                            : "bg-indigo-600 hover:bg-indigo-700"
                        } text-white py-2 px-3 sm:px-6 rounded-lg font-semibold shadow-md transition-all duration-300`}
                        disabled={
                          !formData.termsAccepted ||
                          !formData.estimatedPrice ||
                          !formData.applicantsLimit ||
                          !formData.completionDate ||
                          !formData.applyExpireDate ||
                          clickedButton === "submit"
                        }
                      >
                        {isLoading && clickedButton === "publish" ? (
                          "Publishing..."
                        ) : (
                          <>
                            <span className="hidden sm:inline">Publish</span>
                            <span className="sm:hidden">Publish</span>
                          </>
                        )}
                      </button>
                    </>
                  )}
                </div>
              ) : (
                <button
                  onClick={nextStep}
                  className="bg-indigo-600 text-white py-2 px-3 sm:px-10 rounded-lg font-semibold shadow-md hover:bg-indigo-700 transition-all duration-300"
                >
                  <span className="hidden sm:inline">Save & Next</span>
                  <span className="sm:hidden">Next</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <ConfirmationModal
        component="bountyForm"
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Cancel"
        message={`Are you sure you want to cancel?`}
      />

      <CustomDialogWithDash
        isOpen={submitConfirmModal}
        onClose={() => {
          setSubmitConfirmModal(false);
          setClickedButton("");
        }}
        onConfirm={() => handleSubmitWrapper(clickedButton)}
      />
    </>
  );
}
