"use client";

import {
  ArrowLeft,
  File,
  Maximize,
  MessageCircle,
  MessageCircleIcon,
  Minimize,
  Paperclip,
  Send,
  User as UserIcon,
  X,
  RefreshCw,
  Loader2,
  AlertCircle,
  DownloadCloudIcon,
  Lock,
  EditIcon,
  XIcon,
  EyeIcon,
  Download,
  Eye,
  TableOfContents,
  List,
} from "lucide-react";
import React, {
  ReactNode,
  useState,
  useEffect,
  useRef,
  useCallback,
} from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import ApplicantProposalCard from "./applicantPropsalCard";

import BountyReward from "./bountyReward";
import axios from "axios";
import { toast } from "sonner";
import Link from "next/link";
import Image from "next/image";
import ImageEditor from "@toast-ui/react-image-editor";
import { Button } from "../ui/button";
import downloadFile from "@/app/utils/downloadFile";
interface BountyProps {
  bounty: any;
  user: any | null;
}

interface Message {
  id: string;
  sender: string;
  content: string;
  timestamp: Date;
  media?: {
    id: string;
    url: string;
    filename: string;
  };
  file?: {
    id: string;
    url: string;
    filename: string;
  };
}

interface Applicant {
  id: string;
  userId: string;
  userName: string;
  userImage?: string;
  approach: string;
  applicantStatus: "pending" | "approved" | "rejected";
  finalSubmissions?: Array<{
    file: any; // ID of product_files document
    submittedAt: string;
    notes?: string;
    status?: string;
    filename?: string; // For UI display
    url?: string; // For UI display
    rejectionReason?: string; // Reason for rejection
  }>;
  relatedFile?: string[]; // Array of product_files IDs
}

const ApplicationTabs: React.FC<BountyProps> = ({ bounty, user }) => {
  // Validate user and bounty IDs
  const isValidId = (id: any): boolean =>
    typeof id === "string" && /^[0-9a-fA-F]{24}$/.test(id);

  const isOwner =
    user &&
    isValidId(user.id) &&
    bounty?.user?.id &&
    user.id === bounty.user.id;
  const [activeTab, setActiveTab] = useState<"description" | "applicants">(
    "description"
  );
  const [selectedApplicant, setSelectedApplicant] = useState<Applicant | null>(
    null
  );
  const [chatOpen, setChatOpen] = useState(false);
  const [viewChat, setViewChat] = useState(false);
  const [maximized, setMaximized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [chatRoomId, setChatRoomId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const finalSubmissionInputRef = useRef<HTMLInputElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [filePreview, setFilePreview] = useState<{
    type: "image" | "file";
    url: string;
    file: File;
    filename: string;
  } | null>(null);
  const [finalSubmissionPreview, setFinalSubmissionPreview] = useState<{
    status: any;
    url: string;
    file: File;
    filename: string;
    notes?: string;
  } | null>(null);
  const [submissionNotes, setSubmissionNotes] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalPages: 1,
    hasMore: true,
  });
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadingType, setLoadingType] = useState<
    null | "accepted" | "rejected"
  >(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [showFinalSubmission, setShowFinalSubmission] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [showRejectionPopup, setShowRejectionPopup] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");

  // Utility to sanitize relatedFile
  const sanitizeRelatedFile = (relatedFile: any): string[] => {
    if (!relatedFile || !Array.isArray(relatedFile)) return [];
    return relatedFile.filter((id) => isValidId(id));
  };
  // Scroll handling
  useEffect(() => {
    if (!chatContainerRef.current) return;

    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }

    if (!initialLoadComplete && messages.length > 0) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
      setInitialLoadComplete(true);
    } else if (messages.length > 0 && !isLoadingMore) {
      const container = chatContainerRef.current;
      const isNearBottom =
        container.scrollHeight - container.scrollTop <=
        container.clientHeight + 100;
      if (isNearBottom) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [messages, initialLoadComplete, isLoadingMore]);
  // Scroll event for pagination
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container || !chatRoomId || !pagination.hasMore) return;

    const handleScroll = () => {
      if (container.scrollTop < 100 && !isLoadingMore && pagination.hasMore) {
        loadMoreMessages();
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [chatRoomId, isLoadingMore, pagination.hasMore]);

  // Fetch chat room
  useEffect(() => {
    if (!selectedApplicant) {
      return;
    }

    if (!user) {
      setError("User not authenticated");
      return;
    }

    if (!isValidId(selectedApplicant.userId)) {
      setError("Invalid applicant ID format");
      return;
    }

    const fetchChatRoom = async () => {
      try {
        setLoading(true);
        setError(null);
        setInitialLoadComplete(false);
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/chatrooms?where[bounty][equals]=${bounty.id}&where[applicant][equals]=${selectedApplicant.userId}&depth=1`,
          { credentials: "include" }
        );

        if (!response.ok) throw new Error("Failed to fetch chat room");

        const data = await response.json();
        if (data.docs && data.docs.length > 0) {
          setChatRoomId(data.docs[0].id);
          fetchMessages(data.docs[0].id, 1, true);
        } else {
          setChatRoomId(null);
          setMessages([]);
          setPagination({
            page: 1,
            limit: 20,
            totalPages: 1,
            hasMore: false,
          });
        }
      } catch (err) {
        console.error("Error fetching chat room:", err);
        setError("Failed to load chat room");
      } finally {
        setLoading(false);
      }
    };

    fetchChatRoom();
  }, [selectedApplicant, bounty.id, user]);

  // Fetch messages with pagination
  const fetchMessages = async (
    chatId: string,
    page: number,
    initialLoad = false
  ) => {
    try {
      if (initialLoad) {
        setLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      setError(null);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/chatrooms/${chatId}/messages?page=${page}&limit=${pagination.limit}&depth=2`,
        { method: "GET", credentials: "include" }
      );

      if (!response.ok) throw new Error("Failed to fetch messages");
      const data = await response.json();

      const formattedMessages: Message[] = data.docs.map((msg: any) => ({
        id: msg.id,
        sender: typeof msg.sender === "object" ? msg.sender.id : msg.sender,
        content: msg.text || "",
        timestamp: new Date(msg.createdAt),
        media: msg.media,
        file: msg.file,
      }));

      setPagination({
        page: data.page,
        limit: data.limit,
        totalPages: data.totalPages,
        hasMore: data.hasNextPage,
      });

      if (initialLoad) {
        setMessages(
          formattedMessages.sort(
            (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
          )
        );
      } else {
        const previousScrollHeight =
          chatContainerRef.current?.scrollHeight || 0;

        setMessages((prev) => {
          const newMessages = formattedMessages.filter(
            (newMsg) =>
              !prev.some((existingMsg) => existingMsg.id === newMsg.id)
          );

          const merged = [...newMessages, ...prev];
          return merged.sort(
            (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
          );
        });

        requestAnimationFrame(() => {
          if (chatContainerRef.current) {
            const newScrollHeight = chatContainerRef.current.scrollHeight;
            chatContainerRef.current.scrollTop =
              newScrollHeight - previousScrollHeight;
          }
        });
      }
    } catch (err) {
      console.error("Error fetching messages:", err);
      setError("Failed to load messages");
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  };

  const loadMoreMessages = useCallback(() => {
    if (!chatRoomId || isLoadingMore || !pagination.hasMore) return;

    const nextPage = pagination.page + 1;
    fetchMessages(chatRoomId, nextPage);
  }, [chatRoomId, isLoadingMore, pagination]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const isImage = file.type.startsWith("image/");

    if (isImage) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setFilePreview({
          type: "image",
          url: event.target?.result as string,
          file,
          filename: file.name,
        });
      };
      reader.readAsDataURL(file);
    } else {
      setFilePreview({
        type: "file",
        url: URL.createObjectURL(file),
        file,
        filename: file.name,
      });
    }
  };

  const handleFinalSubmissionSelect = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    setFinalSubmissionPreview({
      url: URL.createObjectURL(file),
      file,
      filename: file.name,
      notes: submissionNotes,
      status: "pending",
    });
  };

  const clearFilePreview = () => {
    setFilePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const clearFinalSubmissionPreview = () => {
    setFinalSubmissionPreview(null);
    setSubmissionNotes("");
    if (finalSubmissionInputRef.current) {
      finalSubmissionInputRef.current.value = "";
    }
  };

  const handleFileUpload = async (textToSend?: string) => {
    if (!filePreview || !chatRoomId || !user || !isValidId(user.id)) {
      setError("Invalid user ID or chat room");
      return;
    }

    let uploadData;
    try {
      setIsUploading(true);
      setError(null);
      const formData = new FormData();
      formData.append("file", filePreview.file);

      const uploadEndpoint =
        filePreview.type === "image"
          ? `${process.env.NEXT_PUBLIC_SERVER_URL}/api/media`
          : `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: "POST",
        credentials: "include",
        body: formData,
      });
      uploadData = await uploadResponse.json();

      if (!uploadResponse.ok) {
        console.error("Upload failed:", uploadData);
        throw new Error(uploadData.message || "File upload failed");
      }

      const messagePayload: any = {
        senderId: user.id,
        ...(filePreview.type === "image" && { mediaId: uploadData.doc.id }),
        ...(filePreview.type === "file" && { fileId: uploadData.doc.id }),
      };

      if (textToSend && textToSend.trim()) {
        messagePayload.text = textToSend;
      }

      const messageResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/chatrooms/${chatRoomId}/send`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(messagePayload),
          credentials: "include",
        }
      );

      if (!messageResponse.ok) {
        throw new Error("Failed to send message");
      }

      await fetchMessages(chatRoomId, 1, true);
      clearFilePreview();
      if (textToSend) {
        setNewMessage("");
      }
    } catch (err) {
      console.error("Error uploading file:", err);
      setError(err instanceof Error ? err.message : "Failed to upload file");
    } finally {
      setIsUploading(false);
    }
  };

  function getFileId(fileReference: any): string {
    if (!fileReference) return "";

    if (typeof fileReference === "string") {
      return fileReference;
    }

    if (fileReference.id) {
      return fileReference.id;
    }

    if (fileReference._id) {
      return fileReference._id;
    }

    console.warn("Unrecognized file reference format:", fileReference);
    return "";
  }

  const handleSendFinalSubmission = async () => {
    if (
      !finalSubmissionPreview ||
      !selectedApplicant ||
      !user ||
      !isValidId(user.id)
    ) {
      setError("Invalid user or applicant");
      return;
    }

    try {
      setIsUploading(true);
      setError(null);

      // Check submission limit
      const currentSubmissions =
        selectedApplicant.finalSubmissions?.length || 0;
      if (currentSubmissions >= 3) {
        throw new Error(
          "You have reached the maximum number of submissions (3)"
        );
      }

      // Upload file
      const formData = new FormData();
      formData.append("file", finalSubmissionPreview.file);

      const uploadResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files`,
        {
          method: "POST",
          credentials: "include",
          body: formData,
        }
      );

      const uploadData = await uploadResponse.json();

      if (!uploadResponse.ok)
        throw new Error(uploadData.message || "File upload failed");

      // Prepare new submission
      const newSubmission = {
        file: uploadData.doc.id,
        submittedAt: new Date().toISOString(),
        notes: finalSubmissionPreview.notes || "",
        status: finalSubmissionPreview.status,
      };

      // Update bounty with sanitized submissions
      const updatedApplicants = (bounty.applicants || []).map(
        (app: Applicant) => {
          const sanitizedSubmissions = (app.finalSubmissions || []).map(
            (sub) => ({
              ...sub,
              file: getFileId(sub.file), // Using our helper function
            })
          );
          const sanitizedRelatedFiles = (app.relatedFile || []).map(getFileId);
          if (app.userId === selectedApplicant.userId) {
            return {
              ...app,
              relatedFile: sanitizedRelatedFiles,
              finalSubmissions: [...sanitizedSubmissions, newSubmission],
            };
          }
          return {
            ...app,
            relatedFile: sanitizedRelatedFiles,
            finalSubmissions: sanitizedSubmissions,
          };
        }
      );
      const updateResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ applicants: updatedApplicants }),
          credentials: "include",
        }
      );
      const updateData = await updateResponse.json();
      if (!updateResponse.ok)
        throw new Error(updateData.message || "Update failed");

      // Update local state
      setSelectedApplicant((prev) =>
        prev
          ? {
              ...prev,
              finalSubmissions: [
                ...(prev.finalSubmissions || []),
                {
                  file: uploadData.doc.id,
                  submittedAt: newSubmission.submittedAt,
                  notes: newSubmission.notes,
                  status: newSubmission.status,
                  filename: finalSubmissionPreview.filename,
                  url: uploadData.doc.url,
                },
              ],
            }
          : null
      );

      clearFinalSubmissionPreview();
    } catch (err) {
      console.error("Error submitting final work:", err);
      setError(
        err instanceof Error ? err.message : "Failed to submit final work"
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleSendMessage = async () => {
    if (
      (!newMessage.trim() && !filePreview) ||
      !chatRoomId ||
      !user ||
      !isValidId(user.id)
    ) {
      setError("Invalid message or user ID");
      return;
    }

    try {
      setError(null);

      if (filePreview) {
        await handleFileUpload(newMessage.trim());
      } else {
        await sendTextMessage(newMessage);
        setNewMessage("");
      }
    } catch (err) {
      console.error("Error sending message:", err);
      setError("Failed to send message");
    }
  };

  const sendTextMessage = async (text: string) => {
    if (!chatRoomId || !user || !isValidId(user.id)) {
      setError("Invalid user ID or chat room");
      return;
    }

    setIsUploading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/chatrooms/${chatRoomId}/send`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            text,
            senderId: user.id,
          }),
          credentials: "include",
        }
      );
      if (!response.ok) throw new Error("Failed to send message");
      await fetchMessages(chatRoomId, 1, true);
    } finally {
      setIsUploading(false);
    }
  };

  const refreshChat = async () => {
    if (!chatRoomId) return;
    try {
      setLoading(true);
      await fetchMessages(chatRoomId, 1, true);
    } catch (err) {
      console.error("Error refreshing chat:", err);
      setError("Failed to refresh chat");
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    setSelectedApplicant(null);
    setChatOpen(false);
    setViewChat(false);
    setMessages([]);
    setChatRoomId(null);
    setError(null);
    clearFilePreview();
    clearFinalSubmissionPreview();
    setInitialLoadComplete(false);
    setShowFinalSubmission(false);
  };

  const downloadImage = (url: string, filename: string) => {
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename || "image.jpg");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const isApprovedApplicant = (bounty.applicants || []).some(
    (app: Applicant) =>
      isValidId(app.userId) &&
      app.userId === user?.id &&
      app.applicantStatus === "approved"
  );

  const getChatHeaderName = () => {
    if (!selectedApplicant) return "";
    return isOwner
      ? selectedApplicant.userName
      : bounty.user?.user_name || "Bounty Owner";
  };

  const closePreview = (e: React.MouseEvent) => {
    e.stopPropagation();
    // console.log("closePreview");

    setPreviewImage(null);
  };
  const renderFileContent = (message: Message) => {
    const openPreview = (e: React.MouseEvent, url: string) => {
      e.stopPropagation();
      // console.log("openPreview : ", url);
      setPreviewImage(url);
    };

    if (message.media) {
      return (
        <>
          <div className="max-w-xs mb-2">
            <div className="relative group overflow-hidden rounded-md">
              <img
                src={message.media.url}
                alt={message.media.filename}
                className="max-w-full h-auto rounded-md max-h-64 object-contain bg-indigo-900/40 p-1 transition-transform group-hover:scale-[1.02]"
                onError={(e) => {
                  e.currentTarget.src = "/fallback-image.png";
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end gap-2 p-2">
                <button
                  onClick={(e) => openPreview(e, message.media.url)}
                  className="inline-flex items-center text-black text-xs rounded-md transition-colors shadow-sm p-1 bg-black/30 hover:bg-black/50"
                >
                  <Eye size={16} className="text-white" />
                </button>
                {/* <button
                  className="inline-flex items-center text-black text-xs rounded-md transition-colors shadow-sm p-1 bg-black/30 hover:bg-black/50"
                  onClick={() =>
                    downloadFile(message.media.filename, message.media.filename)
                  }
                >
                  <Download size={16} className="text-white" />
                </button> */}
              </div>
            </div>
          </div>

          {/* Image Preview Popup */}
        </>
      );
    } else if (message?.file) {
      return (
        <div className="flex items-center gap-2 p-2 mb-2 bg-indigo-800/70 rounded-md shadow-sm border border-indigo-600/30 hover:bg-indigo-700/70 transition-colors">
          <File size={16} className="flex-shrink-0 text-indigo-300" />
          <span
            onClick={() =>
              downloadFile(
                message.file.filename, // This can be a filename or fileId depending on your API design
                message.file.filename
              )
            }
            rel="noopener noreferrer"
            className="text-indigo-100 text-sm hover:text-white transition-colors truncate max-w-[180px]"
          >
            {message.file.filename}
          </span>
        </div>
      );
    }
    return null;
  };

  const renderFinalSubmissions = () => {
    if (!selectedApplicant?.finalSubmissions?.length) {
      return (
        <div className="p-4 text-center text-indigo-200">
          No final submissions yet
        </div>
      );
    }

    return (
      <div className="space-y-3 p-4">
        {selectedApplicant?.finalSubmissions.map((submission, index) => (
          <>
            <div
              key={`${submission.file}-${index}`}
              className="flex items-center gap-3 p-3 bg-indigo-700/30 rounded-lg"
            >
              <File size={16} className="flex-shrink-0 text-indigo-300" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {submission.filename
                    ? submission.filename
                    : submission.file?.filename || `Submission ${index + 1}`}
                </p>
                <p className="text-xs text-indigo-300">
                  Submitted on{" "}
                  {new Date(submission.submittedAt).toLocaleString()}
                </p>
                {submission.notes && (
                  <p className="text-xs text-indigo-200 mt-1">
                    Notes: {submission.notes}
                  </p>
                )}
                <p className="text-xs text-white p-1 rounded-lg bg-indigo-600 mt-1 w-max">
                  {submission.status}
                </p>
              </div>
              {submission.status !== "rejected" && (
                <Button
                  onClick={() =>
                    downloadFile(
                      submission.filename
                        ? submission.filename
                        : submission.file?.filename ||
                            `Submission ${index + 1}`, // This can be a filename or fileId depending on your API design
                      submission.filename
                        ? submission.filename
                        : submission.file?.filename || `Submission ${index + 1}`
                    )
                  }
                  className="px-3 py-1 bg-indigo-600 text-white text-xs rounded hover:bg-indigo-500 transition-colors"
                >
                  Download
                </Button>
              )}
            </div>
            {submission.status === "rejected" &&
              submission?.rejectionReason && (
                <div className="p-3 rounded-md text-sm bg-red-100 text-red-700 border border-red-200">
                  <div className="flex flex-wrap items-center gap-1">
                    <span className="font-semibold">
                      Rejection Reason: {submission.rejectionReason}
                    </span>
                  </div>
                </div>
              )}
          </>
        ))}

        {isOwner &&
          selectedApplicant.finalSubmissions?.find(
            (sub) => sub.status === "pending"
          )?.status && (
            <div className="flex gap-2 justify-end">
              <button
                onClick={() => handleStatusChange("accepted")}
                className="bg-green-600 px-2 py-1 rounded-lg"
                disabled={
                  loadingType === "accepted" || loadingType === "rejected"
                }
              >
                {loadingType === "accepted" ? "Accepting..." : "Accept"}
              </button>
              <button
                onClick={() => setShowRejectionPopup(true)}
                className="bg-red-600 px-2 py-1 rounded-lg"
                disabled={
                  loadingType === "accepted" || loadingType === "rejected"
                }
              >
                {loadingType === "rejected" ? "Rejecting..." : "Reject"}
              </button>
            </div>
          )}
        {showRejectionPopup && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-indigo-700 rounded-2xl shadow-lg p-6 w-96 relative">
              <button
                onClick={closeModal}
                className="absolute top-3 right-3 text-white text-xl hover:bg-red-700 rounded-lg"
              >
                <X />
              </button>
              <h2 className="text-lg font-semibold mb-4">
                Enter Rejection Reason
              </h2>
              <textarea
                className={`w-full h-32 p-2 border border-gray-300 rounded-lg mb-4 resize-none bg-transparent placeholder-gray-400 ${
                  rejectionReason.trim() ? "text-white" : "text-black"
                }`}
                placeholder="Please enter the rejection reason..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
              ></textarea>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setShowRejectionPopup(false);
                    setRejectionReason("");
                  }}
                  className="px-4 py-2 border border-purple-300 text-purple-200 rounded-xl hover:bg-purple-600/30 transition-all duration-200 text-sm sm:text-base focus:outline-none focus:ring-1 focus:ring-purple-500"
                >
                  Cancel
                </button>

                <button
                  onClick={confirmRejection}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 transition-all duration-200 text-white rounded-xl text-sm sm:text-base disabled:opacity-50 focus:outline-none focus:ring-1 focus:ring-red-400"
                  disabled={loading || !rejectionReason.trim()}
                >
                  {loadingType === "rejected" ? "Rejecting..." : "Reject"}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const closeModal = () => {
    setShowRejectionPopup(false);
    setRejectionReason("");
  };

  const confirmRejection = async () => {
    try {
      setLoading(true);
      setShowRejectionPopup(false);
      await handleStatusChange("rejected", rejectionReason);
      closeModal();
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (
    status: "accepted" | "rejected",
    rejectionReason?: string
  ) => {
    try {
      setLoadingType(status === "accepted" ? "accepted" : "rejected");
      const { data: existingBounty } = await axios.get(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`
      );

      const updatedSubmissionStatus = existingBounty.applicants.map(
        (applicant) => {
          const isTargetApplicant =
            applicant.userId === selectedApplicant.userId;
          return {
            ...applicant,
            finalSubmissions:
              isTargetApplicant && Array.isArray(applicant.finalSubmissions)
                ? applicant.finalSubmissions.map((submission) => ({
                    ...submission,
                    status: status,
                    ...(status === "rejected" &&
                      rejectionReason && {
                        rejectionReason: rejectionReason,
                      }),
                  }))
                : applicant.finalSubmissions,
          };
        }
      );

      await axios.patch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`,
        { applicants: updatedSubmissionStatus }
      );

      toast.success(`Final submission ${status} successfully!`);
      setLoadingType(null);
      window.location.reload();
    } catch (err) {
      console.error(err);
      toast.error("Failed to update status.");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div
      className={`border border-indigo-500 rounded-xl shadow-lg w-full bg-gradient-to-br from-indigo-800 to-indigo-700 ${
        maximized ? "fixed inset-0 z-50 h-full w-full" : ""
      } flex flex-col overflow-hidden`}
    >
      {/* Tabs */}
      <div className="flex bg-indigo-900">
        <button
          onClick={() => {
            setActiveTab("description");
            goBack();
          }}
          className={`flex-1 py-2 px-4 transition-colors duration-200 ${
            activeTab === "description"
              ? "bg-indigo-600 font-semibold shadow-inner"
              : "bg-indigo-800 hover:bg-indigo-500"
          }`}
        >
          Description
        </button>
        <button
          onClick={() => {
            if (!user) return;

            setActiveTab("applicants");
            goBack();
          }}
          className={`flex-1 py-2 px-4 flex items-center justify-center gap-2 transition-colors duration-200 ${
            activeTab === "applicants"
              ? "bg-indigo-600 font-semibold shadow-inner"
              : "bg-indigo-800 hover:bg-indigo-500"
          }${!user ? "cursor-not-allowed opacity-70" : ""}`}
        >
          {isOwner ? (
            <>
              Applicants
              {!user && <Lock size={16} className="ml-1" />}
            </>
          ) : (
            <>
              Discussion
              {!user && <Lock size={16} className="ml-1" />}
            </>
          )}

          {isOwner && (
            <span className="flex items-center justify-center text-sm bg-indigo-500 rounded-full h-6 w-6 font-medium">
              {(bounty.applicants || []).length}
            </span>
          )}
        </button>
      </div>
      {/* Tab Content */}
      <div
        className={`bg-indigo-800/50 rounded-lg ${maximized ? "h-[calc(100vh-100px)]" : "h-[500px]"}`}
      >
        {activeTab === "description" && (
          <div
            className={`relative overflow-y-auto whitespace-nowrap no-scrollbar ${maximized ? "h-[calc(100vh-100px)]" : "h-[490px]"}`}
          >
            <div className="sticky top-0 bg-indigo-600 py-2 px-4 text-white w-full flex justify-between items-center z-10">
              <h3 className="font-medium">Requirements</h3>
              <button
                className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                onClick={() => setMaximized(!maximized)}
              >
                {maximized ? <Minimize size={18} /> : <Maximize size={18} />}
              </button>
            </div>
            <div className="relative p-4">
              <div
                className="text-white/90 pt-1 prose prose-invert max-w-none"
                style={{
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                  whiteSpace: "pre-wrap",
                }}
              >
                <ReactMarkdown
                  components={{
                    a: ({ href, children }) => (
                      <a
                        href={href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:underline"
                      >
                        {children as ReactNode}
                      </a>
                    ),
                  }}
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                >
                  {typeof bounty?.content === "string"
                    ? bounty.content
                    : "No content available"}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        )}

        {activeTab === "applicants" && (
          <div className="relative h-full">
            <div className="sticky top-0 bg-indigo-600 py-2 px-4 text-white w-full flex justify-between items-center z-10 ">
              <div>
                {selectedApplicant || viewChat || chatOpen ? (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={goBack}
                      className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                    >
                      <ArrowLeft size={18} />
                    </button>
                    <div className="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-500 text-white font-semibold">
                      <Link href={`/users/${getChatHeaderName()}`}>
                        {isOwner ? (
                          selectedApplicant?.userImage ? (
                            <Image
                              alt="User Avatar"
                              width={28} // 7 * 4 = 28px
                              height={28}
                              src={selectedApplicant?.userImage}
                              className="rounded-full w-7 h-7 object-cover"
                            />
                          ) : (
                            selectedApplicant?.userName?.charAt(0)
                          )
                        ) : bounty.user?.profileImage?.url ? (
                          <Image
                            alt="User Avatar"
                            width={40} // 10 * 4 = 40px
                            height={40}
                            src={bounty.user?.profileImage?.url}
                            className="rounded-full w-7 h-7 object-cover"
                          />
                        ) : (
                          bounty.user?.user_name?.charAt(0)
                        )}

                        {/* {getChatHeaderName()?.charAt(0) || "U"} */}
                      </Link>
                    </div>
                    <Link href={`/users/${getChatHeaderName()}`}>
                      <span className="font-medium">{getChatHeaderName()}</span>
                    </Link>
                  </div>
                ) : (
                  <h3 className="font-medium">
                    {isOwner ? "Applicants" : "Discussion"}
                  </h3>
                )}
              </div>
              <div className="flex items-center gap-2">
                {viewChat && chatRoomId && (
                  <button
                    onClick={refreshChat}
                    className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                    disabled={loading}
                  >
                    <RefreshCw
                      size={18}
                      className={loading ? "animate-spin" : ""}
                    />
                  </button>
                )}
                <button
                  className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                  onClick={() => setMaximized(!maximized)}
                >
                  {maximized ? <Minimize size={18} /> : <Maximize size={18} />}
                </button>
              </div>
            </div>

            {error && (
              <div className="p-4 text-red-400 bg-red-900/20 rounded-lg mx-4 mt-2">
                {error}
              </div>
            )}

            {!selectedApplicant ? (
              // Applicant List
              <div className="h-[460px] overflow-y-auto ">
                {isOwner ? (
                  <div>
                    <ul className="divide-y divide-indigo-700">
                      {(bounty.applicants || []).map((applicant: Applicant) => (
                        <li
                          key={applicant.userId}
                          className="p-3 hover:bg-indigo-700 cursor-pointer flex items-center gap-3 transition-colors"
                          onClick={() => {
                            if (!isValidId(applicant.userId)) {
                              setError("Invalid applicant user ID");
                              return;
                            }
                            setSelectedApplicant(applicant);
                            setViewChat(true);
                            setChatOpen(
                              applicant.applicantStatus === "approved"
                            );
                          }}
                        >
                          <div className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-600 text-white font-medium">
                            <Link href={`/users/${applicant?.userName}`}>
                              {applicant?.userImage ? (
                                <Image
                                  alt="User Avatar"
                                  width={10}
                                  height={10}
                                  src={applicant?.userImage}
                                  className="rounded-full w-10 h-10 object-cover"
                                />
                              ) : (
                                applicant?.userName?.charAt(0)
                              )}
                            </Link>
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">
                              <Link href={`/users/${applicant?.userName}`}>
                                {applicant?.userName}
                              </Link>
                            </p>
                            <p className="text-sm text-indigo-200 truncate">
                              {applicant.approach?.length > 100
                                ? `${applicant.approach.slice(0, 100)}...`
                                : applicant.approach}
                            </p>
                          </div>
                          <div className="flex items-center gap-1">
                            {applicant.applicantStatus === "approved" && (
                              <>
                                <span className="bg-indigo-800 text-indigo-100 hover:text-indigo-300 p-1 rounded h-7 ">
                                  <List
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedApplicant(applicant);
                                      setViewChat(false);
                                      setChatOpen(false);
                                    }}
                                    className="cursor-pointer h-full"
                                  />
                                </span>
                              </>
                            )}
                            <span
                              className={`text-xs px-2 py-1 rounded font-semibold capitalize h-7 ${
                                applicant.applicantStatus === "approved"
                                  ? "bg-green-600"
                                  : applicant.applicantStatus === "rejected"
                                    ? "bg-red-600"
                                    : "bg-yellow-600"
                              }`}
                            >
                              {applicant.applicantStatus}
                            </span>
                            {/* {applicant.applicantStatus !== "rejected" && (
                              <MessageCircleIcon />
                            )} */}
                          </div>
                        </li>
                      ))}
                    </ul>
                    <BountyReward bounty={bounty} />
                  </div>
                ) : isApprovedApplicant ? (
                  <ul className="divide-y divide-indigo-700">
                    {(bounty.applicants || [])
                      .filter((app: Applicant) => app.userId === user?.id)
                      .map((applicant: Applicant) => (
                        <li
                          key={applicant.userId}
                          className="p-3 hover:bg-indigo-700 cursor-pointer flex items-center gap-3 transition-colors"
                          onClick={() => {
                            if (!isValidId(applicant.userId)) {
                              setError("Invalid applicant user ID");
                              return;
                            }
                            setSelectedApplicant(applicant);
                            setViewChat(true);
                            setChatOpen(
                              applicant.applicantStatus === "approved"
                            );
                          }}
                        >
                          <div className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-600 text-white font-medium">
                            <Link href={`/users/${bounty.user?.user_name}`}>
                              {/* {bounty.user?.user_name?.charAt(0) || "O"} */}
                              {bounty.user?.profileImage?.url ? (
                                <Image
                                  alt="User Avatar"
                                  width={10}
                                  height={10}
                                  src={bounty.user?.profileImage?.url}
                                  className="rounded-full w-10 h-10 object-cover"
                                />
                              ) : (
                                bounty.user?.user_name?.charAt(0)
                              )}
                            </Link>
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">
                              <Link href={`/users/${bounty.user?.user_name}`}>
                                {bounty.user?.user_name || "Bounty Owner"}
                              </Link>
                            </p>
                            {/* <p className="text-sm text-indigo-200 truncate">
                              Your approved application
                            </p> */}
                          </div>
                          <MessageCircleIcon />
                        </li>
                      ))}
                  </ul>
                ) : (
                  <div className="p-6 text-center flex flex-col items-center justify-center h-full">
                    <UserIcon size={40} className="text-indigo-400 mb-4" />
                    <p className="text-indigo-200 text-lg mb-2">
                      {(bounty.applicants || []).length > 0
                        ? "Only approved applicants can discuss"
                        : "No applicants yet"}
                    </p>
                    <p className="text-indigo-300 text-sm">
                      {(bounty.applicants || []).length > 0
                        ? "Apply to join the discussion"
                        : ""}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              // Chat View
              <div className={`relative ${maximized ? "h-full" : "h-[460px]"}`}>
                {loading && !isUploading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-indigo-800/50 z-10">
                    <div className="flex justify-center items-center py-4">
                      <Loader2 className="h-8 w-8 spin-animation font-normal" />
                    </div>
                  </div>
                )}

                {(chatOpen ||
                  (selectedApplicant.applicantStatus === "approved" &&
                    selectedApplicant.userId === user?.id)) && (
                  <>
                    <div className="flex-1 bg-indigo-800/30 rounded h-full">
                      {/* Final Submission Toggle */}
                      <div className="flex border-b sticky top-0 z-10 border-indigo-700">
                        <button
                          onClick={() => setShowFinalSubmission(false)}
                          className={`flex-1 py-2 text-center ${
                            !showFinalSubmission
                              ? "bg-indigo-700 text-white"
                              : "text-indigo-300 hover:bg-indigo-800"
                          }`}
                        >
                          Chat
                        </button>
                        <button
                          onClick={() => setShowFinalSubmission(true)}
                          className={`flex-1 py-2 text-center ${
                            showFinalSubmission
                              ? "bg-indigo-700 text-white"
                              : "text-indigo-300 hover:bg-indigo-800"
                          }`}
                        >
                          Final Submission
                          {selectedApplicant.finalSubmissions?.length ? (
                            <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold bg-indigo-500 rounded-full">
                              {selectedApplicant.finalSubmissions.length}
                            </span>
                          ) : null}
                        </button>
                      </div>
                      <div
                        ref={chatContainerRef}
                        className={`chat-container space-y-3 py-3 overflow-auto no-scrollbar ${
                          maximized ? "h-[calc(100vh-160px)]" : "h-[405px]"
                        } pb-7`}
                      >
                        {showFinalSubmission ? (
                          <div className="h-full flex flex-col">
                            <div className="flex-1 overflow-y-auto">
                              {renderFinalSubmissions()}
                            </div>

                            {/* Final Submission Input */}
                            {!isOwner && (
                              <div className="p-3 absolute bottom-0 w-full bg-indigo-900/80 border-t border-indigo-700">
                                {finalSubmissionPreview && (
                                  <div className="flex items-center gap-2 bg-indigo-700/50 p-2 rounded-lg mb-2">
                                    <button
                                      onClick={clearFinalSubmissionPreview}
                                      className="text-white p-1 hover:bg-indigo-600 rounded-full transition-colors"
                                    >
                                      <X size={16} />
                                    </button>
                                    <div className="flex items-center gap-2">
                                      <File
                                        size={16}
                                        className="text-indigo-300"
                                      />
                                      <span className="text-sm text-white truncate max-w-xs">
                                        {finalSubmissionPreview?.filename}
                                      </span>
                                    </div>
                                  </div>
                                )}

                                <div className="flex gap-2">
                                  <button
                                    onClick={() =>
                                      finalSubmissionInputRef.current?.click()
                                    }
                                    className="cursor-pointer p-2 rounded-lg transition-colors flex items-center justify-center hover:bg-indigo-600"
                                    disabled={loading || isUploading}
                                  >
                                    <Paperclip
                                      size={18}
                                      className="text-white"
                                    />
                                    <input
                                      id="final-submission-upload"
                                      type="file"
                                      className="hidden"
                                      onChange={handleFinalSubmissionSelect}
                                      ref={finalSubmissionInputRef}
                                      accept=".pdf,.txt,.doc,.docx,.zip"
                                      disabled={
                                        selectedApplicant?.finalSubmissions
                                          ?.length >= 1 ||
                                        bounty.status === "completed"
                                      }
                                    />
                                  </button>
                                  <div className="flex-1 flex items-center justify-between">
                                    <div className="text-xs text-indigo-300">
                                      {selectedApplicant.finalSubmissions
                                        ?.length || 0}
                                      /1 submissions
                                    </div>
                                    <button
                                      onClick={handleSendFinalSubmission}
                                      className="px-8 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors flex items-center gap-2 disabled:opacity-50"
                                      disabled={
                                        loading ||
                                        isUploading ||
                                        !finalSubmissionPreview ||
                                        (selectedApplicant.finalSubmissions
                                          ?.length || 0) >= 1
                                      }
                                    >
                                      {isUploading ? (
                                        <div className="flex space-x-1">
                                          <div
                                            className="w-2 h-2 bg-white rounded-full animate-pulse"
                                            style={{ animationDelay: "0ms" }}
                                          ></div>
                                          <div
                                            className="w-2 h-2 bg-white rounded-full animate-pulse"
                                            style={{ animationDelay: "150ms" }}
                                          ></div>
                                          <div
                                            className="w-2 h-2 bg-white rounded-full animate-pulse"
                                            style={{ animationDelay: "300ms" }}
                                          ></div>
                                        </div>
                                      ) : (
                                        <div className="relative group">
                                          <button className="flex justify-center align-center gap-2">
                                            Submit
                                            <span className="mt-1">
                                              {(selectedApplicant
                                                .finalSubmissions?.length ||
                                                0) >= 1 && (
                                                <AlertCircle
                                                  size={16}
                                                  className="text-yellow-300"
                                                />
                                              )}
                                            </span>
                                          </button>

                                          {/* Tooltip */}
                                          <div className="absolute hidden group-hover:block bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs bg-gray-800 text-white rounded whitespace-nowrap">
                                            1 submissions allowed
                                          </div>
                                        </div>
                                      )}
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <>
                            <div className="flex-1 bg-indigo-800/30 px-6 rounded h-full">
                              <div
                                ref={chatContainerRef}
                                className={`chat-container space-y-3 py-3 pb-16 ${maximized ? "h-[calc(100vh-160px)] pb-16" : "h-[405px]"} overflow-auto no-scrollbar`}
                              >
                                {isLoadingMore && (
                                  <div className="flex justify-center py-2">
                                    <Loader2 className="animate-spin h-5 w-5 text-indigo-300" />
                                  </div>
                                )}

                                <div className="mb-4 text-sm bg-indigo-600 w-full px-4 py-3 rounded-lg shadow-md max-w-[70%]">
                                  <p className="font-medium mb-1">
                                    {isOwner
                                      ? `${selectedApplicant?.userName}'s proposal:`
                                      : "Your proposal:"}
                                  </p>
                                  <p className="text-indigo-100">
                                    &quot;{selectedApplicant?.approach}&quot;
                                  </p>
                                </div>

                                {messages.map((message) => {
                                  const isOwnMessage =
                                    message.sender === user?.id;
                                  return (
                                    <div
                                      key={message.id}
                                      className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} mb-3`}
                                    >
                                      <div className="flex items-end gap-1 group">
                                        {/* Timestamp */}
                                        {isOwnMessage && (
                                          <div className="text-xs text-indigo-300 opacity-0 group-hover:opacity-100 transition-opacity duration-200 mr-2">
                                            {message.timestamp.toLocaleTimeString(
                                              [],
                                              {
                                                hour: "2-digit",
                                                minute: "2-digit",
                                              }
                                            )}
                                          </div>
                                        )}

                                        {/* Message bubble */}
                                        <div
                                          className={`relative max-w-xs px-4 py-3 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg ${
                                            isOwnMessage
                                              ? "bg-gradient-to-r from-indigo-600 to-indigo-500 text-white border-r-4 border-indigo-400 hover:translate-x-[-2px]"
                                              : "bg-gradient-to-r from-indigo-500 to-indigo-400 text-white border-l-4 border-indigo-300 hover:translate-x-[2px]"
                                          }`}
                                        >
                                          {renderFileContent(message)}
                                          {message.content && (
                                            <div className="text-white/95 leading-relaxed">
                                              {message.content}
                                            </div>
                                          )}
                                        </div>

                                        {/* Timestamp for others */}
                                        {!isOwnMessage && (
                                          <div className="text-xs text-indigo-300 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-2">
                                            {message.timestamp.toLocaleTimeString(
                                              [],
                                              {
                                                hour: "2-digit",
                                                minute: "2-digit",
                                              }
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>

                            {/* Input area */}
                            <div className="absolute bottom-0 w-full p-3 bg-gradient-to-r from-indigo-700 to-indigo-600 backdrop-blur-sm border-t border-indigo-500/30">
                              {filePreview && (
                                <div className="flex items-center gap-2 bg-indigo-700/50 p-2 rounded-lg mb-2">
                                  <button
                                    onClick={clearFilePreview}
                                    className="text-white p-1 hover:bg-indigo-600 rounded-full transition-colors"
                                  >
                                    <X size={16} />
                                  </button>
                                  {filePreview.type === "image" ? (
                                    <img
                                      src={filePreview?.url}
                                      alt="Preview"
                                      className="h-12 w-12 object-cover rounded"
                                    />
                                  ) : (
                                    <div className="flex items-center gap-2">
                                      <File
                                        size={16}
                                        className="text-indigo-300"
                                      />
                                      <span className="text-sm text-white truncate max-w-xs">
                                        {filePreview.filename}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              )}

                              <div className="flex gap-2">
                                <button
                                  onClick={() => fileInputRef.current?.click()}
                                  className="cursor-pointer p-2 rounded-lg transition-all duration-200 flex items-center justify-center bg-indigo-800/80 hover:bg-indigo-900 hover:shadow-md"
                                  disabled={loading || isUploading}
                                >
                                  <Paperclip
                                    size={24}
                                    className="text-white hover:scale-110 transition-transform"
                                  />
                                  <input
                                    id="file-upload"
                                    type="file"
                                    className="hidden"
                                    onChange={handleFileSelect}
                                    ref={fileInputRef}
                                    accept="image/*,.pdf,.txt,.doc,.docx"
                                  />
                                </button>
                                <input
                                  type="text"
                                  value={newMessage}
                                  onChange={(e) =>
                                    setNewMessage(e.target.value)
                                  }
                                  placeholder="Type your message..."
                                  className="flex-1 p-2 border rounded-lg bg-indigo-800/80 border-indigo-500/50 focus:outline-none focus:ring-2 focus:ring-indigo-400 text-white placeholder-indigo-300/80 shadow-inner"
                                  onKeyPress={(e) =>
                                    e.key === "Enter" && handleSendMessage()
                                  }
                                  disabled={loading || isUploading}
                                />
                                <button
                                  onClick={handleSendMessage}
                                  className="px-3 py-2 bg-gradient-to-r from-amber-500 to-pink-500 font-bold text-white rounded-lg hover:from-amber-400 hover:to-pink-400 transition-all duration-200 flex items-center gap-2 disabled:opacity-50 shadow-md hover:shadow-lg hover:scale-105 active:scale-95"
                                  disabled={
                                    loading ||
                                    isUploading ||
                                    (!newMessage.trim() && !filePreview)
                                  }
                                >
                                  {isUploading ? (
                                    <div className="flex space-x-1">
                                      <div
                                        className="w-2 h-2 bg-white rounded-full animate-pulse"
                                        style={{ animationDelay: "0ms" }}
                                      ></div>
                                      <div
                                        className="w-2 h-2 bg-white rounded-full animate-pulse"
                                        style={{ animationDelay: "150ms" }}
                                      ></div>
                                      <div
                                        className="w-2 h-2 bg-white rounded-full animate-pulse"
                                        style={{ animationDelay: "300ms" }}
                                      ></div>
                                    </div>
                                  ) : (
                                    <Send size={24} />
                                  )}
                                </button>
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </>
                )}

                {!chatOpen &&
                  !(
                    selectedApplicant.applicantStatus === "approved" &&
                    (selectedApplicant.userId === user?.id)
                  ) && (
                    <ApplicantProposalCard
                      isOwner={isOwner}
                      selectedApplicant={selectedApplicant}
                      bounty={bounty}
                    />
                  )}
              </div>
            )}
          </div>
        )}
      </div>
      {previewImage && (
        <div
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={closePreview}
        >
          <div
            className="relative max-w-full max-h-full"
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={previewImage}
              alt="Preview"
              className="max-w-[90vw] max-h-[90vh] object-contain"
            />
            <button
              onClick={closePreview}
              className="absolute top-2 right-2 bg-red-400 text-white rounded-full p-2 z-10 shadow-sm hover:bg-white hover:text-red-400 transition-colors"
            >
              <XIcon size={20} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplicationTabs;
