"use client";

import { <PERSON><PERSON><PERSON><PERSON>, File, Maximize, Minimize, Paperclip, Send, User as UserIcon } from "lucide-react";
import React, { ReactNode, useState } from "react";
import { Checkbox } from "../ui/checkbox";
import ReactMarkdown from "react-markdown";
import { Bounty, User } from "@/server/payload-types";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";

interface BountyProps {
  bounty: any;
  user: User | null;
}

interface Message {
  id: string;
  sender: 'user' | 'applicant';
  content: string;
  timestamp: Date;
  file?: {
    name: string;
    type: string;
    size: number;
  };
}

const applicants = [
  {
    id: 1,
    name: "<PERSON>",
    proposal:
      "<PERSON>, I'd love to work on this project because I have experience in X.",
  },
  {
    id: 2,
    name: "<PERSON>",
    proposal:
      "I'm passionate about this field and have done similar work in the past.",
  },
  {
    id: 3,
    name: "<PERSON>",
    proposal: "Looking forward to contributing my skills to this team.",
  },
];

const ApplicationTabs: React.FC<BountyProps> = ({ bounty, user }) => {
  // Check if current user is the bounty owner
  const isOwner = user && user.id === bounty?.user.id;
  
  const [activeTab, setActiveTab] = useState<"description" | "applicants">(
    "description"
  );
  const [selectedApplicant, setSelectedApplicant] = useState<
    typeof applicants[0] | null
  >(null);
  const [chatOpen, setChatOpen] = useState(false);
  const [viewChat, setViewChat] = useState(false);
  const [maximized, setMaximized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  
  // Add a ref for the chat container
  const chatContainerRef = React.useRef<HTMLDivElement>(null);

  // Add useEffect to scroll to bottom when messages change
  React.useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a file message
      const fileMessage: Message = {
        id: Date.now().toString(),
        sender: 'user',
        content: `File: ${file.name}`,
        timestamp: new Date(),
        file: {
          name: file.name,
          type: file.type,
          size: file.size
        }
      };
      
      setMessages([...messages, fileMessage]);
      
      // Simulate applicant response
      setTimeout(() => {
        const applicantMessage: Message = {
          id: (Date.now() + 1).toString(),
          sender: 'applicant',
          content: `I received your file: ${file.name}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, applicantMessage]);
      }, 1000);
    }
  };

  const handleSendMessage = () => {
    if (newMessage.trim() && selectedApplicant) {
      const userMessage: Message = {
        id: Date.now().toString(),
        sender: 'user',
        content: newMessage,
        timestamp: new Date()
      };
      setMessages([...messages, userMessage]);
      setNewMessage('');
      
      // Simulate applicant response after 1 second
      setTimeout(() => {
        const applicantMessage: Message = {
          id: (Date.now() + 1).toString(),
          sender: 'applicant',
          content: `Reply to: ${newMessage}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, applicantMessage]);
      }, 1000);
    }
  };

  const goBack = () => {
    setSelectedApplicant(null);
    setChatOpen(false);
  };

  const applicant = bounty.applicants;

  return (
    <div
      className={`border border-indigo-500 rounded-xl shadow-lg w-full bg-gradient-to-br from-indigo-800 to-indigo-700 ${
        maximized ? "fixed inset-0 z-50 h-full w-full" : ""
      } flex flex-col overflow-hidden`}
    >
      {/* Tabs */}
      <div className="flex p-2 gap-2">
        <button
          onClick={() => {
            setActiveTab("description");
            goBack();
            setViewChat(false);
          }}
          className={`flex-1 py-2 px-4 rounded-lg transition-colors duration-200 ${
            activeTab === "description"
              ? "bg-indigo-900 font-semibold shadow-inner"
              : "bg-transparent hover:bg-indigo-500 "
          }`}
        >
          Description
        </button>
        <button
          onClick={() => {
            setActiveTab("applicants");
            goBack();
          }}
          className={`flex-1 py-2 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors duration-200 ${
            activeTab === "applicants"
              ? "bg-indigo-900 font-semibold shadow-inner"
              : "bg-transparent hover:bg-indigo-500 "
          }`}
        >
          {isOwner ? "Applicants" : "Discuss"}
          <span className="flex items-center justify-center text-sm bg-indigo-500 rounded-full h-6 w-6 font-medium">
            {(applicant as any[]).length !== 0
              ? `${(applicant as any[]).length as any}`
              : "0"}
          </span>
        </button>
      </div>

      {/* Tab Content */}
      <div className={`bg-indigo-800/50 rounded-lg ${maximized ? 'h-[calc(100vh-100px)]' : 'h-[500px]'}`}>
        {activeTab === "description" && (
          <div className={`relative mx-1 overflow-y-auto whitespace-nowrap no-scrollbar ${maximized ? 'h-[calc(100vh-100px)]' : 'h-[490px]'}`}>
            <div className="sticky top-0 bg-indigo-900 py-2 px-4 right-2 text-white w-full flex justify-between items-center z-10 rounded-lg">
              <h3 className="font-medium">Requirements</h3>
              <button
                className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                onClick={() => setMaximized(!maximized)}
              >
                {maximized ? <Minimize size={18} /> : <Maximize size={18} />}
              </button>
            </div>
            <div className="relative p-4">
              <div
                className="text-white/90 pt-1 prose prose-invert max-w-none"
                style={{
                  wordBreak: "break-word",
                  overflowWrap: "break-word",
                  whiteSpace: "pre-wrap",
                }}
              >
                <ReactMarkdown
                  components={{
                    a: ({
                      href,
                      children,
                    }: {
                      href?: string;
                      children?: React.ReactNode;
                    }) => (
                      <a
                        href={href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:underline"
                      >
                        {children as ReactNode}
                      </a>
                    ),
                  }}
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                >
                  {typeof bounty?.content === "string"
                    ? bounty.content
                    : "No content available"}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        )}

        {activeTab === "applicants" && (
          <div className="relative mx-1 h-full">
            <div className="sticky top-0 bg-indigo-900 py-2 px-4 right-2 text-white w-full flex justify-between items-center z-10 rounded-lg">
              <div>
                {viewChat || chatOpen ? (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => { 
                        goBack();
                        setViewChat(false);
                      }}
                      className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                    >
                      <ArrowLeft size={18} />
                    </button>
                    <div className="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-500 text-white font-semibold">
                      {selectedApplicant?.name.charAt(0)}
                    </div>
                    <span className="font-medium">
                      {selectedApplicant?.name}
                    </span>
                  </div>
                ) : (
                  <h3 className="font-medium">{isOwner ? "Applicants" : "Discussion"}</h3>
                )}
              </div>
              <button
                className="text-white p-1 hover:bg-indigo-700 rounded-md transition-colors"
                onClick={() => setMaximized(!maximized)}
              >
                {maximized ? <Minimize size={18} /> : <Maximize size={18} />}
              </button>
            </div>
            
            {!selectedApplicant ? (
              // Applicant List
              <div className="h-full">
                {isOwner ? (
                  <ul className="divide-y divide-indigo-700">
                    {applicants.map((applicant) => (
                      <li
                        key={applicant.id}
                        className="p-3 hover:bg-indigo-700 cursor-pointer flex items-center gap-3 transition-colors"
                        onClick={() => {
                          setSelectedApplicant(applicant);
                          setViewChat(true);
                          setChatOpen(false);
                        }}
                      >
                        <div className="w-8 h-8 flex items-center justify-center rounded-full bg-indigo-600 text-white font-medium">
                          {applicant.name.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{applicant.name}</p>
                          <p className="text-sm text-indigo-200 truncate">{applicant.proposal}</p>
                        </div>
                        <Checkbox className="rounded-md h-4 w-4" />
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="p-6 text-center flex flex-col items-center justify-center h-full">
                    <UserIcon size={40} className="text-indigo-400 mb-4" />
                    <p className="text-indigo-200 text-lg mb-2">This section is locked</p>
                    <p className="text-indigo-300 text-sm">Only the bounty owner can interact with applicants</p>
                  </div>
                )}
              </div>
            ) : (
              // Chat View
              <div className={`relative ${maximized ? 'h-full' : 'h-[460px]'}`}>
                {chatOpen ? (
                  <>
                    <div className="flex-1 bg-indigo-800/30 px-6 rounded h-full">
                      <div 
                        ref={chatContainerRef}
                        className={`chat-container space-y-3 py-3 ${maximized ? 'h-[calc(100vh-160px)] pb-16' : 'h-[405px]'} overflow-auto no-scrollbar`}
                      >
                        <div className="mb-4 text-sm bg-indigo-600 w-fit px-4 py-3 rounded-lg shadow-md">
                          <p className="font-medium mb-1">{selectedApplicant.name}&apos;s proposal:</p>
                          <p className="text-indigo-100">&apos;{selectedApplicant.proposal}&apos;</p>
                        </div>
                        
                        {messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs px-4 py-3 rounded-lg shadow-md ${
                                message.sender === 'user'
                                  ? 'bg-indigo-600 text-white'
                                  : 'bg-indigo-500 text-white'
                              }`}
                            >
                              {message.file ? (
                                <div className="flex items-center gap-2">
                                  <File size={16} className="flex-shrink-0" />
                                  <span className="truncate">{message.content}</span>
                                </div>
                              ) : (
                                message.content
                              )}
                              <div className="text-xs opacity-70 mt-1 text-right">
                                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {isOwner && (
                      <div className="absolute bottom-0 w-full p-3 flex gap-2">
                        <label htmlFor="file-upload" className="cursor-pointer p-2 rounded-lg  transition-colors flex items-center justify-center">
                          <Paperclip size={18} className="text-white" />
                          <input
                            id="file-upload"
                            type="file"
                            className="hidden"
                            onChange={handleFileUpload}
                          />
                        </label>
                        <input
                          type="text"
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          placeholder="Type your message..."
                          className="flex-1 p-2 border rounded-lg bg-indigo-700/50 border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        />
                        <button
                          onClick={handleSendMessage}
                          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors flex items-center gap-2"
                        >
                          <Send size={16} />
                          <span className="hidden sm:inline">Send</span>
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="px-6 py-4 flex flex-col h-full">
                    <div className="bg-indigo-800/30 p-4 rounded-lg shadow-md mb-6">
                      <h3 className="font-semibold text-lg mb-2">
                        {selectedApplicant.name}&apos;s Proposal
                      </h3>
                      <p className="text-gray-200 border-l-2 border-indigo-500 pl-3 italic">
                        &apos;{selectedApplicant.proposal}&apos;
                      </p>
                    </div>
                    
                    {isOwner && (
                      <div className="mt-auto flex gap-3 justify-end">
                        <button
                          className="px-5 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 transition-colors font-medium"
                          onClick={() => setChatOpen(true)}
                        >
                          Accept
                        </button>
                        <button
                          className="px-5 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors font-medium"
                          onClick={() => {
                            goBack();
                          }}
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationTabs;
