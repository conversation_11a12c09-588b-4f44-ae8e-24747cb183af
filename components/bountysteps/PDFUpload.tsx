import React, { useEffect, useRef, useState } from "react";
import {
  handleUploadFile,
  handleRemoveFile,
} from "../../app/utils/fileuploadhelper"; // adjust path as needed
import Link from "next/link";
import Image from "next/image";
import pdfimg from "../../public/PDF-icon.svg.webp";

interface PdfUploadProps {
  name: string;
  setFormdata: (value: any) => void;
  formdata: any;
  removeError?: (value: any) => void;
  className?: string;
  disabled?: boolean;
}

const PdfUpload: React.FC<PdfUploadProps> = ({
  name,
  setFormdata,
  formdata,
  removeError,
  className,
  disabled
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});

  // ✅ Show file from edit mode
  useEffect(() => {
    if (formdata?.product_files) {
      setUploadedFile(formdata.product_files);
    }
  }, [formdata?.product_files]);

  const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleUploadFile({
      event,
      updateFileState: setUploadedFile,
      setLoading: setIsLoading,
      updateFormData: setFormdata,
      updateErrors: setErrors,
      allowedFileTypes: ["application/pdf"],
      maxFileSize: 5 * 1024 * 1024, // 5 MB
    });
  };

  const onRemove = () => {
    handleRemoveFile({
      updateFileState: setUploadedFile,
      updateFormData: setFormdata,
    });
    if (removeError) removeError("");
  };

  return (
    <div>
      <div
        className={`cursor-pointer p-6 flex justify-center bg-dash border border-dashed border-gray-300 rounded-xl dark:bg-neutral-800 dark:border-neutral-600 ${className} ${
          disabled ? "opacity-50 cursor-not-allowed" : ""
        }`}
        onClick={() => {
          if (!disabled) inputRef.current?.click();
        }}
      >
        <div className="text-center">
          <span className="inline-flex justify-center items-center size-12 bg-gray-100 text-gray-800 rounded-full dark:bg-neutral-700 dark:text-neutral-200">
            <svg
              className="shrink-0 size-6"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#4F46E5"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="17 8 12 3 7 8"></polyline>
              <line x1="12" x2="12" y1="3" y2="15"></line>
            </svg>
          </span>
          <div className="mt-4 flex flex-wrap justify-center text-sm leading-6 text-gray-600">
            <span className="pe-1 font-medium text-white dark:text-neutral-200">
              Upload PDF file or
            </span>
            <span className="bg-white px-1 font-semibold text-indigo-600 hover:text-blue-700 rounded-lg decoration-2 hover:underline dark:bg-neutral-800 dark:text-blue-500 dark:hover:text-blue-600">
              browse
            </span>
          </div>
        </div>
      </div>

      <input
        ref={inputRef}
        type="file"
        accept="application/pdf"
        style={{ display: "none" }}
        onChange={onChange}
        disabled={disabled}
      />

      {errors?.product_files && (
        <p className="text-red-500 text-sm mt-2">{errors.product_files}</p>
      )}

      {isLoading && <p className="text-sm text-gray-400 mt-2">Uploading...</p>}

      {uploadedFile && uploadedFile.url && (
        <div className="mt-4 p-3 bg-dash rounded-xl dark:bg-neutral-800 dark:border-neutral-600">
          <div className="flex items-center gap-4">
            <Link href={uploadedFile.url} target="_blank">
              <Image alt="pdf" src={pdfimg} width={40} height={50} />
            </Link>
            <div className="flex-1">
              <p className="text-sm font-medium text-white truncate">
                {uploadedFile.filename || "PDF File"}
              </p>
              <p className="text-xs text-gray-400">
                <a
                  href={uploadedFile.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline text-blue-400"
                >
                  View PDF
                </a>
              </p>
            </div>
            {!disabled && (
            <button
              onClick={onRemove}
              className="text-red-500 hover:text-gray-800 dark:text-neutral-500 dark:hover:text-neutral-200 text-xl"
            >
              ❌
            </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PdfUpload;
