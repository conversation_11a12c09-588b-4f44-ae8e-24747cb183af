import React, { useState } from "react";
import axios from "axios";
import { toast } from "sonner";
import Link from "next/link";
import pdfimg from "../../public/PDF-icon.svg.webp";
import Image from "next/image";
import { Button } from "../ui/button";
import { X } from "lucide-react";
import downloadFile from "@/app/utils/downloadFile";

interface ProposalCardProps {
  // userName: string;
  // approach: string;
  isOwner: boolean;
  // applicantId: string;
  bounty: {
    id: string;
    applicantsLimit: number;
    applicants: any[];
  };
  selectedApplicant?: any;
}

const ApplicantProposalCard: React.FC<ProposalCardProps> = ({
  // userName,
  // approach,
  isOwner,
  // applicantId,
  bounty,
  selectedApplicant,
}) => {
  const [rejectionReason, setRejectionReason] = useState("");
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loadingType, setLoadingType] = useState<null | "approved" | "reject">(
    null
  );

  const handleStatusChange = async (
    status: "approved" | "rejected",
    reason?: string
  ) => {
    try {
      setLoadingType(status === "approved" ? "approved" : "reject");

      const { data: existingBounty } = await axios.get(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty?.id}?depth=0`
      );

      const updatedApplicants = existingBounty.applicants.map((applicant) => {
        const isTargetApplicant = applicant.id === selectedApplicant?.id;
        return {
          ...applicant,
          applicantStatus: isTargetApplicant
            ? status
            : applicant.applicantStatus,
          rejectionReason: isTargetApplicant
            ? status === "rejected"
              ? reason
              : applicant.rejectionReason // keep existing reason if not rejecting
            : applicant.rejectionReason,
          // relatedFile: applicant.relatedFile?.map((file) => file.id) || [],
        };
      });

      await axios.patch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty?.id}?depth=0`,
        { applicants: updatedApplicants }
      );

      toast.success(`Applicant ${status} successfully!`);
      setLoadingType(null);
      window.location.reload();
    } catch (err) {
      console.error(err);
      toast.error("Failed to update status.");
    } finally {
      setLoading(false);
    }
  };

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => {
    setIsModalOpen(false);
    setRejectionReason("");
  };

  const confirmRejection = async () => {
    try {
      setIsModalOpen(false);
      setLoading(true);
      await handleStatusChange("rejected", rejectionReason);
      closeModal();
    } finally {
      setLoading(false);
    }
  };

  const acceptApplicant = async () => {
    try {
      setLoading(true);
      await handleStatusChange("approved");
    } finally {
      setLoading(false);
    }
  };

  const approvedCount = bounty?.applicants?.filter(
    (app: any) => app.applicantStatus === "approved"
  ).length;
  const hasLimit =
    bounty?.applicantsLimit !== null &&
    bounty?.applicantsLimit !== undefined &&
    bounty?.applicantsLimit !== 0;

  const canAcceptMore = hasLimit && approvedCount < bounty.applicantsLimit;
  // console.log("canAcceptmore", canAcceptMore);
  return (
    <div className="px-6 py-4 flex flex-col relative max-h-full overflow-y-auto scrollbar-hide">
      <div className="bg-indigo-800/30 p-4 rounded-lg shadow-md mb-6 overflow-y-auto max-w-[85%] mb-4">
        <h3 className="font-semibold text-lg mb-2">
          {selectedApplicant?.userName}&apos;s Proposal
        </h3>
        {/* {applicants?.phone && (
            <p className="text-gray-200 border-l-2 border-indigo-500 pl-3 italic">
              <Link href={`tel:${applicants?.phone}`}>{applicants?.phone}</Link>
            </p>
          )} */}
        {selectedApplicant?.linkedin && (
          <p className="text-gray-200 border-l-2 border-indigo-500 pl-3 italic ">
            <Link href={`/${selectedApplicant?.linkedin}`} target="_blank">
              {selectedApplicant?.linkedin}
            </Link>
          </p>
        )}
        {selectedApplicant?.approach && (
          <p className="text-gray-200 border-l-2 border-indigo-500 pl-3 italic break-words whitespace-normal truncate ">
            &apos;{selectedApplicant?.approach}&apos;
          </p>
        )}
      </div>

      {selectedApplicant?.relatedFile[0]?.url && (
        <div className="bg-indigo-800/30 p-4 rounded-lg shadow-md mb-6 max-w-[85%]">
          <h3 className="font-semibold text-lg mb-2">Related file</h3>
          <div
            className="flex gap-2 items-center mt-2 border-l-2 border-indigo-500 pl-3 cursor-pointer"
            onClick={() =>
              downloadFile(
                selectedApplicant?.relatedFile[0].filename, // This can be a filename or fileId depending on your API design
                selectedApplicant?.relatedFile[0].filename
              )
            }
          >
            <div
            // href={applicant?.relatedFile[0].url}
            // target="_blank"
            >
              <Image alt="pdf" src={pdfimg} width={30} height={40} />
            </div>
            <div className="flex flex-col gap-1">
              <span>{selectedApplicant?.relatedFile[0].filename}</span>
              <span className="text-sm text-gray-500">
                {" "}
                Click here to download the proposal PDF.
              </span>
            </div>
          </div>
        </div>
      )}

      {selectedApplicant?.applicantStatus === "rejected" && (
        <div className="bg-red-100 text-red-800 p-4 rounded-lg shadow-md mb-6 max-w-[85%] border border-red-200">
          <h3 className="font-semibold text-lg mb-2">Rejection Reason</h3>
          <p className="pl-3 italic border-l-4 border-red-400 text-sm">
            {selectedApplicant?.rejectionReason}
          </p>
        </div>
      )}

      {isOwner && (selectedApplicant as any)?.applicantStatus === "pending" && (
        <div className="mt-auto flex flex-row justify-end items-end gap-3">
          {canAcceptMore && (
            <Button
              variant="green"
              className="px-5 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 transition-colors font-medium"
              onClick={() => handleStatusChange("approved")}
              disabled={loading}
            >
              {loadingType === "approved" ? "Processing..." : "Accept"}
              {/* {loading ? "Processing..." : "Accept"} */}
            </Button>
          )}
          {!canAcceptMore && (
            <p className="text-sm text-red-500">
              Applicant limit reached — can't accept more.
            </p>
          )}
          {selectedApplicant?.applicantStatus !== "rejected" && (
            <Button
              variant="red"
              className="px-5 py-2 bg-red-500 text-white rounded-lg hover:bg-red-400 transition-colors font-medium"
              onClick={openModal}
              disabled={loading}
            >
              {loadingType === "reject" ? "Submitting..." : "Reject"}
            </Button>
          )}
        </div>
      )}

      {/* Modal for Rejection */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-indigo-700 rounded-2xl shadow-lg p-6 w-96 relative">
            <button
              onClick={closeModal}
              className="absolute top-3 right-3 text-white text-xl hover:bg-red-700 rounded-lg"
            >
              <X />
            </button>
            <h2 className="text-lg font-semibold mb-4">
              Enter Rejection Reason
            </h2>
            <textarea
              className={`w-full h-32 p-2 border border-gray-300 rounded-lg mb-4 resize-none bg-transparent placeholder-gray-400 ${
                rejectionReason.trim() ? "text-white" : "text-black"
              }`}
              placeholder="Please enter the rejection reason..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
            ></textarea>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setIsModalOpen(false);
                  setRejectionReason("");
                }}
                className="px-4 py-2 border border-purple-300 text-purple-200 rounded-xl hover:bg-purple-600/30 transition-all duration-200 text-sm sm:text-base focus:outline-none focus:ring-1 focus:ring-purple-500"
              >
                Cancel
              </button>

              <button
                onClick={confirmRejection}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 transition-all duration-200 text-white rounded-xl text-sm sm:text-base disabled:opacity-50 focus:outline-none focus:ring-1 focus:ring-red-400"
                disabled={loading || !rejectionReason.trim()}
              >
                {loadingType === "reject" ? "Rejecting..." : "Reject"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplicantProposalCard;
