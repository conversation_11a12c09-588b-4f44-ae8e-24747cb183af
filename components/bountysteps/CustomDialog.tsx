import * as Dialog from "@radix-ui/react-dialog";
import { X, AlertCircle, CheckCircle2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CustomDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const CustomDialogWithDash = ({
  isOpen,
  onClose,
  onConfirm,
}: CustomDialogProps) => {
  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed z-50 left-1/2 top-1/2 w-[380px] -translate-x-1/2 -translate-y-1/2 rounded-xl bg-gray-900 border border-gray-700 shadow-2xl focus:outline-none overflow-hidden">
          
          {/* Header */}
          <div className="flex justify-between items-start p-4 border-b border-indigo-500/20">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-indigo-600/10 border border-indigo-500/20 mt-0.5">
                <AlertCircle className="w-5 h-5 text-indigo-400" />
              </div>
              <div>
                <Dialog.Title className="text-lg font-semibold text-white">
                  Submission Confirmation
                </Dialog.Title>
                <Dialog.Description className="text-sm text-gray-400 ">
                  Please review before submitting
                </Dialog.Description>
              </div>
            </div>
            <Dialog.Close asChild>
              <button className="text-gray-400 hover:text-white p-1 rounded-md hover:bg-gray-700/50 transition-colors">
                <X className="w-5 h-5" />
              </button>
            </Dialog.Close>
          </div>

          {/* Content with custom scrollbar */}
          <div className="p-4 space-y-5 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300/60 scrollbar-track-gray-800/50">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-full bg-red-500/10">
                  <X className="w-3.5 h-3.5 text-red-400" />
                </div>
                <h3 className="text-sm font-medium text-white">Locked After Approval</h3>
              </div>
              
              <div className="space-y-2">
                {[
                  { title: "Bounty Title", desc: "Cannot be changed once approved" },
                  { title: "Description", desc: "Content and requirements are locked" },
                  { title: "Bounty Type", desc: "Selected category cannot be changed" },
                  { title: "Uploaded Files", desc: "Cannot update or replace assets" },
                  { title: "Applicant Limit", desc: "Cannot change allowed applicants" }
                ].map((item, i) => (
                  <div key={i} className="flex items-start gap-3 p-3 rounded-lg bg-gray-800/50 border border-gray-700/30">
                    <div className="w-2 h-2 rounded-full bg-red-400 mt-1.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-white">{item.title}</p>
                      <p className="text-xs text-gray-400 mt-0.5">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-full bg-green-500/10">
                  <CheckCircle2 className="w-3.5 h-3.5 text-green-400" />
                </div>
                <h3 className="text-sm font-medium text-white">Editable After Approval</h3>
              </div>
              
              <div className="space-y-2">
                {[
                  { title: "Bounty Reward", desc: "Amount can be increased" },
                  { title: "Completion Date", desc: "Deadline can be extended" },
                  { title: "Application Deadline", desc: "Can extend application date" }
                ].map((item, i) => (
                  <div key={i} className="flex items-start gap-3 p-3 rounded-lg bg-gray-800/50 border border-gray-700/30">
                    <div className="w-2 h-2 rounded-full bg-green-400 mt-1.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-white">{item.title}</p>
                      <p className="text-xs text-gray-400 mt-0.5">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 p-4 border-t border-indigo-500/20 bg-gray-800/20">
            <Button 
              variant="outline" 
              onClick={onClose}
              className="px-4 h-9 text-sm font-medium text-gray-300 border-gray-600 hover:bg-gray-700/50 hover:text-white"
            >
              Cancel & Review
            </Button>
            <Button
              onClick={() => {
                onClose();
                onConfirm();
              }}
              className="px-4 h-9 text-sm font-medium bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              Confirm
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};