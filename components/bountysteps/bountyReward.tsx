import React, { useEffect, useState } from "react";
import { SpaceModal } from "../SpaceModal";
import { Button } from "../ui/button";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { CheckCircle, Award, AlertCircle, ArrowRight, Sparkles } from "lucide-react";

interface BountyRewardProps {
  bounty: any;
}

interface Applicant {
  id: string;
  userId: string;
  userName: string;
  approach: string;
  applicantStatus: "pending" | "approved" | "rejected";
  finalSubmissions?: Array<{
    file: string; // ID of product_files document
    submittedAt: string;
    notes?: string;
    status?: string;
    filename?: string; // For UI display
    url?: string; // For UI display
  }>;
  rewardJoules: number;
  relatedFile?: string[]; // Array of product_files IDs
}

const BountyReward = ({ bounty }: BountyRewardProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [acceptedSubmissionUsers, setAcceptedSubmissionUsers] = useState<
    Applicant[]
  >([]);
  const [selected, setSelected] = useState<string | null>(null);
  const [selectedAmounts, setSelectedAmounts] = useState<
    { id: string; amount: number }[]
  >([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const total = selectedAmounts.reduce(
      (sum, entry) => sum + (entry.amount || 0),
      0
    );
    setTotalAmount(total);
  }, [selectedAmounts]);

  const getSubmissionUsers = () => {
    try {
      setIsLoading(true);
      const acceptSubmissionUsers = (bounty.applicants || []).filter(
        (applicant: Applicant) =>
          applicant.finalSubmissions?.some(
            (submission) => submission.status === "accepted"
          )
      );
      setAcceptedSubmissionUsers(acceptSubmissionUsers);
    } catch (err) {
      console.error("Error getting accepting submission's applicants:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAmountChange = (id: string, amount: number) => {
    if (amount < 0) return;
    setSelectedAmounts((prev) => {
      const existing = prev.find((entry) => entry.id === id);
      if (existing) {
        return prev.map((entry) =>
          entry.id === id ? { ...entry, amount } : entry
        );
      } else {
        return [...prev, { id, amount }];
      }
    });
  };

  const isValidTotal = totalAmount === bounty.estimatedPrice;

  const handleReleaseJoules = () => {
    if (!isValidTotal) return;
    setIsLoading(true);

    const releaseJoules = async () => {
      try {
        const response = await fetch(
          `/api/bounties/bounty-reward/${bounty.id}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
            credentials: "include",
            body: JSON.stringify({
              winners: selectedAmounts,
            }),
          }
        );

        if (response.ok) {
          const result = await response.json();
          setIsLoading(false);
          toast.success("Released rewards successfully.");
          setIsModalOpen(false);
          window.location.reload();
        } else {
          toast.error("Failed to release joules.");
        }
      } catch (error) {
        toast.error("Error releasing joules:", error);
      }
    };

    releaseJoules();
  };
  
  return (
    <div>
      {!bounty.rewardReleased && bounty.status === 'completed' && (
        <Button
          variant="gradient"
          onClick={() => {
            getSubmissionUsers();
            setIsModalOpen(true);
          }}
          disabled={bounty.rewardReleased}
          className="absolute bottom-2 right-2 rounded-lg px-4 py-2 bg-gradient-to-r from-violet-600 to-indigo-600 text-white shadow-lg flex items-center gap-2 hover:opacity-90 transition-all duration-300 hover:shadow-xl"
        >
          <Award className="h-5 w-5" />
          {bounty.rewardReleased ? "Rewards Released" : "Release Joules"}
        </Button>
      )}
    
      <SpaceModal
        title="Release Bounty Rewards"
        description="Distribute joules to accepted submissions" 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      >
        <div className="space-y-6 py-4 max-h-[70vh] overflow-y-auto no-scrollbar">
          <div className="bg-gradient-to-r from-violet-600/10 via-indigo-500/10 to-blue-600/10 p-5 rounded-xl mb-6 border border-indigo-500/20">
            <h3 className="text-xl font-semibold mb-2 text-white flex items-center">
              <Sparkles className="h-5 w-5 mr-2 text-violet-400" />
              Reward Distribution
            </h3>
            <p className="text-sm text-gray-300">
              Allocate the total bounty amount of <span className="font-bold text-violet-400">{bounty.estimatedPrice} joules</span> among accepted submissions.
            </p>
          </div>
          
          {acceptedSubmissionUsers.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-12 text-center bg-black/20 backdrop-filter backdrop-blur-sm rounded-xl border border-gray-800"> 
              <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-300">No accepted submissions found</p>
            </div>
          ) : (
            <div className="grid gap-5">
              {acceptedSubmissionUsers.map((user) => (
                <motion.div 
                  key={user.userId} 
                  className="border border-indigo-600/20 bg-black/30 backdrop-filter backdrop-blur-sm p-6 rounded-xl shadow-lg overflow-hidden relative"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-violet-600/10 to-indigo-600/10 rounded-bl-full -z-10" />
                  
                  <div
                    className="cursor-pointer flex items-center gap-4 mb-3"
                    onClick={() =>
                      setSelected(selected === user.userId ? null : user.userId)
                    }
                  >
                    <div className="h-12 w-12 rounded-full bg-gradient-to-r from-violet-600 to-indigo-600 flex items-center justify-center text-white font-bold text-lg shadow-md">
                      {user.userName.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">{user.userName}</h3>
                      <p className="text-xs text-indigo-200">{user.finalSubmissions?.length || 0} accepted submission(s)</p>
                    </div>
                    <div className="ml-auto">
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="bg-violet-600 hover:bg-violet-700 border-violet-500/50 text-white shadow-md"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelected(selected === user.userId ? null : user.userId);
                        }}
                      >
                        {selected === user.userId ? "Hide" : "Allocate"}
                      </Button>
                    </div>
                  </div>

                  {selected === user.userId && (
                    <motion.div 
                      className="mt-5 border-t border-indigo-500/20 pt-5"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      transition={{ duration: 0.3 }}
                    >
                      <label className="block text-sm font-medium mb-2 text-indigo-200">
                        Reward Amount (in joules)
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="number"
                          className="border border-indigo-500/30 bg-black/40 px-4 py-2 rounded-lg w-full text-white focus:ring-2 focus:ring-violet-500 focus:border-transparent outline-none"
                          placeholder="Enter amount"
                          value={
                            selectedAmounts
                              .find((entry) => entry.id === user.userId)
                              ?.amount?.toString() || ""
                          }
                          onChange={(e) =>
                            handleAmountChange(
                              user.userId,
                              parseFloat(e.target.value)
                            )
                          }
                        />
                        <Button 
                          variant="outline"
                          className="bg-indigo-600 hover:bg-indigo-700 border-indigo-500/30 text-white shadow-md"
                          onClick={() => handleAmountChange(user.userId, bounty.estimatedPrice)}
                        >
                          Max
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
          
          <div className="sticky bottom-0 backdrop-filter backdrop-blur-md p-5 rounded-xl border border-indigo-600/20 mt-6 shadow-lg">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm text-indigo-200">Total Allocation:</span>
              <div className="flex items-center gap-2">
                <span className={`text-lg font-bold ${isValidTotal ? 'text-green-400' : 'text-amber-400'}`}>
                  {totalAmount}
                </span>
                <span className="text-gray-400">/ {bounty.estimatedPrice}</span>
                {isValidTotal && <CheckCircle className="h-5 w-5 text-green-400" />}
              </div>
            </div>
            
            {!isValidTotal && (
              <p className="text-amber-400 text-sm mb-4 flex items-center gap-2 bg-amber-400/10 p-2 rounded-lg">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>Total must equal {bounty.estimatedPrice} joules</span>
              </p>
            )}
            
            <Button 
              className="w-full bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 py-6 rounded-xl"
              onClick={handleReleaseJoules}
              disabled={!isValidTotal || isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : (
                <>
                  Release Rewards <ArrowRight className="h-5 w-5 ml-1" />
                </>
              )}
            </Button>
          </div>
        </div>
      </SpaceModal>
    </div>
  );
};

export default BountyReward;