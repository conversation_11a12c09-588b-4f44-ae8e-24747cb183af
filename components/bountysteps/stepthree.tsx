"use client";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Input } from "../ui/input";
import Link from "next/link";
import Image from "next/image";

import {
  handleRemoveFile,
  handleUploadFile,
} from "@/app/utils/fileuploadhelper";

const StepThree = ({ formData, setFormData, errors, setErrors }) => {
  const fileInputRef = useRef(null);
  const [file, setFile] = useState(formData?.product_files || null);
  const [loading, setLoading] = useState(false);
  const [fileUploaded, setFileUploaded] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  useEffect(() => {
    if (formData.product_files) {
      setFileUploaded(true);
    }
  }, [formData.product_files]);

  // const handleFileChange = async (e) => {
  //   const selectedFile = e.target.files?.[0];

  //   if (!selectedFile) {
  //     toast.error("No file selected. Please choose a file.");
  //     return;
  //   }
  //   setErrors((prevErrors) => ({
  //     ...prevErrors,
  //     product_files: "",
  //   }));
  //   setLoading(true);
  //   setFileUploaded(false);
  //   setFile(selectedFile);
  //   try {
  //     const fileFormData = new FormData();
  //     fileFormData.append("file", selectedFile);
  //     const response = await fetch(
  //       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files`,
  //       {
  //         method: "POST",
  //         credentials: "include",
  //         body: fileFormData,
  //       }
  //     );

  //     if (!response.ok) {
  //       throw new Error("Failed to upload the file");
  //     }

  //     const fileData = await response.json();

  //     // console.log("fileData", fileData);
  //     // const uploadedFileIds = fileData.doc.id;
  //     const uploadedFile = {
  //       id: fileData.doc.id,
  //       filename: fileData.doc.filename,
  //       url: fileData?.doc?.url,
  //     };
  //     setFormData((prev) => ({
  //       ...prev,
  //       product_files: uploadedFile,
  //     }));
  //     setErrors((prevErrors) => ({
  //       ...prevErrors,
  //       product_files: "",
  //     }));
  //     setFileUploaded(true);
  //   } catch (error) {
  //     console.error("Error uploading file:", error);
  //     toast.error("Failed to upload product files. Please try again.");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const handleRemovePdf = () => {
  //   // setProductFile(null);
  //   setFile(null);
  //   setFormData((prev) => ({
  //     ...prev,
  //     product_files: "",
  //   }));
  //   if (fileInputRef.current) {
  //     fileInputRef.current.value = "";
  //   }
  // };
  // console.log("file?.file", file);

  return (
    <div className="grid gap-6">
      <div className="flex flex-col">
        <label className="block text-sm font-medium mb-1">
          Bounty Related Files
        </label>
        <Input
          type="text"
          className="cursor-pointer w-full mx-auto border p-2 text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
          // className="w-full mx-auto border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"

          value={
            formData.product_files
              ? formData.product_files?.filename
                ? formData.product_files?.filename
                : formData.product_files?.name
              : "Click here to upload a file"
          }
          readOnly
          onClick={() => fileInputRef.current?.click()} // Clicking will trigger file input
        />
        <Input
          type="file"
          name="product_files"
          ref={fileInputRef}
          // onChange={handleFileChange}
          // onChange={(e) =>
          //   handleUploadFile(e, setFile, setLoading, setFormData, setErrors)
          // }
          onChange={(e) =>
            handleUploadFile({
              event: e,
              updateFileState: setFile,
              setLoading: setLoading,
              updateFormData: setFormData,
              updateErrors: setErrors,
            })
          }
          className="hidden w-full mx-auto border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <p className="text-sm text-gray-400 mt-1 ms-1 inline-flex items-center break-words whitespace-pre-wrap">
          {loading && (
            <span className="loader border-2 border-t-indigo-500 border-gray-300 rounded-full w-5 h-5 animate-spin me-1"></span>
          )}
          {"  "}
          If you have any file that would help clarify the bounty requirements,
          please attach it.
        </p>

        <div className="mt-2 flex flex-col space-x-2">
          {fileUploaded && formData?.product_files && (
            <span className="text-green-500 font-medium">
              ✅ File uploaded successfully!
            </span>
          )}
          {formData?.product_files?.url && (
            <div className="flex gap-2 items-center mt-2">
              <div className="relative">
                <Link href={formData?.product_files?.url} target="_blank">
                  <Image alt="pdf" src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/PDF-icon.svg.webp`} width={30} height={40} />
                </Link>
                {/* Close (X) Button */}
                <button
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"
                  // onClick={() => handleRemovePdf()}
                  // onClick={() => handleRemoveFile(setFile, setFormData)}
                  onClick={() =>
                    handleRemoveFile({
                      updateFileState: setFile,
                      updateFormData: setFormData,
                    })
                  }
                >
                  ✖
                </button>
              </div>
              <Link href={formData?.product_files?.url} target="_blank">
                <p className="text-gray-500">
                  {formData.product_files?.filename
                    ? formData.product_files?.filename
                    : formData.product_files?.name}
                </p>
                Click here to view the PDF file.
              </Link>
            </div>
          )}
        </div>
      </div>
      <div>
        <label className="flex items-center cursor-pointer">
          <input
            type="checkbox"
            name="termsAccepted"
            checked={formData.termsAccepted || false}
            onChange={handleInputChange}
            className="mr-2"
            required
          />
          <Link
            href={"/bountytandc"}
            target="_blank"
            className="text-blue-500 hover:underline"
          >
            Accept Terms and Conditions
          </Link>
        </label>
        <p className="text-sm text-gray-400 mt-1 ms-1">
          Accepting bounty terms and conditions is mandatory.
        </p>
        {errors.termsAccepted && (
          <p className="text-red-500 text-sm mt-1">{errors.termsAccepted}</p>
        )}
      </div>
    </div>
  );
};

export default StepThree;
