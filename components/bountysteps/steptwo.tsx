import React, { useState, useEffect, useRef } from "react";
import { Input } from "../ui/input";
import coinImage from "../../public/img/coin-png.png";
import { Label } from "../ui/label";
import Link from "next/link";
import PdfUpload from "./PDFUpload";
import TooltipWrapper from "../bountysteps/tooltipWrapper";
import Image from "next/image";

const StepTwo = ({ user, formData, setFormData, errors, setErrors, id }) => {
  const [updatePriceMessage, setUpdatePriceMessage] = useState(false);
  const [initialCompletionDate] = useState(formData.completionDate);
  const [initialApplyExpireDate] = useState(formData.applyExpireDate);
  console.log("formData step 2", formData);
  const originalApprovedPriceRef = useRef(null);

  useEffect(() => {
    if (
      (formData.status === "approved" ||
        formData.status === "pending" ||
        formData.status === "expired") &&
      originalApprovedPriceRef.current === null &&
      formData.estimatedPrice
    ) {
      originalApprovedPriceRef.current = parseFloat(formData.estimatedPrice);
    }
  }, [formData.status, formData.estimatedPrice]);

  useEffect(() => {
    if (
      formData?.estimatedPrice &&
      formData.status !== "approved" &&
      formData.status !== "pending" &&
      formData.status !== "expired"
    ) {
      const price = parseFloat(formData.estimatedPrice);
      if (!isNaN(price)) {
        if (price > user?.coinBalance) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            estimatedPrice: "Insufficient balance to update the bounty price.",
          }));
        } else {
          setErrors((prevErrors) => ({
            ...prevErrors,
            estimatedPrice: "",
          }));
        }
      }
    }
  }, [formData?.estimatedPrice, formData?.status, user?.coinBalance]);

  const handleInputChange = (e) => {
    // const { name, value } = e.target;
    const { name, type, value, checked } = e.target;
    const updatedValue = type === "checkbox" ? checked : value;

    if (name === "applicantsLimit") {
      if (value.trim() === "") {
        setErrors((prev) => ({
          ...prev,
          applicantsLimit: "Please enter number of accepted applicants.",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          applicantsLimit: "",
        }));
      }
    }

    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: updatedValue,
    }));

    // Clear the specific error message if the field is valid
    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "", // Clear error message for the specific field
      }));
    }
  };

  const today = new Date().toISOString().split("T")[0];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className=" space-y-6">
        <div className="flex md:flex-row flex-col gap-4 w-full">
          <div className="flex flex-col md:w-1/2">
            <label
              htmlFor="estimatedPrice"
              className="block text-sm font-medium mb-1"
            >
              Bounty Price <span className="text-red-500">*</span> :
              <span className="inline-flex items-center gap-1"></span>
            </label>
            <Input
              id="estimatedPrice"
              name="estimatedPrice"
              type="number"
              value={formData.estimatedPrice || ""}
              onChange={(e) => {
                const value = e.target.value;
                if (value === "") {
                  setFormData((prev) => ({
                    ...prev,
                    estimatedPrice: "",
                  }));
                  setErrors((prevErrors) => ({
                    ...prevErrors,
                    estimatedPrice: "Please enter bounty price.",
                  }));
                  setUpdatePriceMessage(false);
                  return;
                }

                const newPrice = parseFloat(value);
                if (isNaN(newPrice)) return;

                const originalPrice = originalApprovedPriceRef.current ?? 0;

                if (
                  formData.status === "approved" ||
                  formData.status === "pending" ||
                  formData.status === "expired"
                ) {
                  const priceDifference = newPrice - originalPrice;

                  if (newPrice < originalPrice) {
                    setFormData((prev) => ({
                      ...prev,
                      estimatedPrice: value,
                    }));
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      estimatedPrice: `You can’t decrease the bounty price once the bounty status is set to ${formData?.status}.`,
                    }));
                    setUpdatePriceMessage(false);

                    return;
                  } else if (priceDifference > user?.coinBalance) {
                    setFormData((prev) => ({
                      ...prev,
                      estimatedPrice: value,
                    }));
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      estimatedPrice:
                        "Insufficient balance to increase the bounty price.",
                    }));
                    setUpdatePriceMessage(false);

                    return;
                  } else if (newPrice === originalPrice) {
                    setFormData((prev) => ({
                      ...prev,
                      estimatedPrice: value,
                      updatedEstimatedPrice: 0,
                    }));
                    setUpdatePriceMessage(false);
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      estimatedPrice: "",
                    }));
                    return;
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      estimatedPrice: value,
                      updatedEstimatedPrice: priceDifference,
                    }));
                    setUpdatePriceMessage(true);
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      estimatedPrice: "",
                    }));
                  }
                } else {
                  if (newPrice > user?.coinBalance) {
                    setFormData((prev) => ({
                      ...prev,
                      estimatedPrice: value,
                    }));
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      estimatedPrice:
                        "Insufficient balance to update the bounty price.",
                    }));
                    return;
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      estimatedPrice: value,
                    }));

                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      estimatedPrice: "",
                    }));
                    setUpdatePriceMessage(false);
                  }
                }
              }}
              min={0}
              className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
              placeholder="Enter bounty price"
              required
              autoComplete="off"
            />

            {errors.estimatedPrice && (
              <p className="text-red-500 text-sm mt-1 break-words whitespace-normal">
                {errors.estimatedPrice}
              </p>
            )}

            {updatePriceMessage ? (
              <p className="text-sm mt-2 break-words whitespace-normal">
                You’re adding{" "}
                <span className="inline-flex items-center font-semibold ml-1">
                  {formData?.updatedEstimatedPrice}
                  <Image
                    src={coinImage.src}
                    alt="Coin"
                    width={16}
                    height={16}
                    className="w-4 h-4 ml-1"
                    loading="lazy"
                  />
                </span>{" "}
                to the bounty. This amount will be deducted from your balance.
              </p>
            ) : (
              <p className="text-sm text-gray-400 mt-1 break-words whitespace-normal truncate items-center gap-1">
                Enter the bounty price in joules. <br />
                (1
                <img
                  src={coinImage.src}
                  alt="coin"
                  className="h-5 w-5 inline-block"
                />
                = ₹1 INR ≈ $0.012 USD)
              </p>
            )}
          </div>
          <div className="flex flex-col w-full md:w-1/2">
            <label className="block text-sm font-medium mb-1">
              Number of Accepted Applicants{" "}
              <span className="text-red-500">*</span>{" "}
            </label>
            <TooltipWrapper
              isDisabled={
                Boolean(id) &&
                (formData.status === "approved" ||
                  formData.status === "expired")
              }
              status={formData.status}
            >
              <Input
                name="applicantsLimit"
                type="number"
                value={formData?.applicantsLimit || ""}
                onChange={handleInputChange}
                className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash  focus:outline-none focus:ring-2 focus:ring-purple-400"
                placeholder="Enter applicants limit"
                required
                min={0}
                disabled={
                  Boolean(id) &&
                  (formData.status === "approved" ||
                    formData.status === "expired")
                }
                autoComplete="off"
              />
            </TooltipWrapper>
            {errors.applicantsLimit && (
              <p className="text-red-500 text-sm mt-1 break-words whitespace-normal">
                {errors.applicantsLimit}
              </p>
            )}
            <p className="text-sm text-gray-400 mt-1 ms-1 break-words whitespace-normal truncate items-center">
              Set how many applicants you will accept to complete this bounty.
              {/* Number of applicants accepted, how many proposals you will accept for this bounty. */}
            </p>
          </div>
        </div>

        {/* Dates - Now fully responsive */}
        <div className="flex flex-col md:flex-row gap-4 w-full">
          <div className="flex flex-col w-full md:w-1/2">
            <label className="block text-sm font-medium mb-1">
              Bounty Completion Date <span className="text-red-500">*</span>
            </label>
            <Input
              name="completionDate"
              type="date"
              value={formData?.completionDate || ""}
              onChange={(e) => {
                const newDate = e.target.value;

                if (
                  formData.status === "approved" ||
                  formData.status === "expired"
                ) {
                  if (
                    !initialCompletionDate ||
                    new Date(newDate) >= new Date(initialCompletionDate)
                  ) {
                    setFormData((prev) => ({
                      ...prev,
                      completionDate: newDate,
                    }));
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      completionDate: "",
                    }));
                  } else {
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      completionDate:
                        "You can only increase the completion timeline from the originally approved date.",
                    }));
                  }
                } else {
                  setFormData((prev) => ({
                    ...prev,
                    completionDate: newDate,
                  }));
                  setErrors((prevErrors) => ({
                    ...prevErrors,
                    completionDate: "",
                  }));
                }
              }}
              min={today}
              className="w-full p-3 border border-gray-300 rounded-lg bg-dash text-white placeholder-white focus:outline-none focus:ring-2 focus:ring-purple-400
  [&::-webkit-calendar-picker-indicator]:invert
  [&::-webkit-datetime-edit]:text-white
  [&::-webkit-datetime-edit-year-field]:text-white
  [&::-webkit-datetime-edit-month-field]:text-white
  [&::-webkit-datetime-edit-day-field]:text-white"
              // className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
              required
              onClick={(e) => (e.target as HTMLInputElement).showPicker()} // This line ensures the date picker opens on click
            />
            {errors.completionDate && (
              <p className="text-red-500 text-sm mt-1 break-words whitespace-normal">
                {errors.completionDate}
              </p>
            )}
            <p className="text-sm text-gray-400 mt-1 ms-1 inline-flex items-center break-words whitespace-pre-wrap">
              {/* Please specify the deadline by which the bounty must be completed.{" "} */}
              Please specify the final deadline for completing this bounty.
              Applicants must submit their proposals by this date.
            </p>
          </div>

          <div className="flex flex-col w-full md:w-1/2">
            <label className="block text-sm font-medium mb-1">
              Bounty Expiry Date <span className="text-red-500">*</span>
            </label>
            <Input
              name="applyExpireDate"
              type="date"
              value={formData?.applyExpireDate || ""}
              onChange={(e) => {
                const newDate = e.target.value;

                if (
                  formData.status === "approved" ||
                  formData.status === "expired"
                ) {
                  if (
                    !initialApplyExpireDate ||
                    new Date(newDate) >= new Date(initialApplyExpireDate)
                  ) {
                    setFormData((prev) => ({
                      ...prev,
                      applyExpireDate: newDate,
                    }));
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      applyExpireDate: "",
                    }));
                  } else {
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      applyExpireDate:
                        "You can only increase the expiry timeline from the originally approved date.",
                    }));
                  }
                } else {
                  setFormData((prev) => ({
                    ...prev,
                    applyExpireDate: newDate,
                  }));
                  setErrors((prevErrors) => ({
                    ...prevErrors,
                    applyExpireDate: "",
                  }));
                }
              }}
              min={today}
              // className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
              className="w-full p-3 border border-gray-300 rounded-lg bg-dash text-white placeholder-white focus:outline-none focus:ring-2 focus:ring-purple-400
  [&::-webkit-calendar-picker-indicator]:invert
  [&::-webkit-datetime-edit]:text-white
  [&::-webkit-datetime-edit-year-field]:text-white
  [&::-webkit-datetime-edit-month-field]:text-white
  [&::-webkit-datetime-edit-day-field]:text-white"
              required
              max={formData.completionDate || undefined}
              onClick={(e) => (e.target as HTMLInputElement).showPicker()} // This line ensures the date picker opens on click
            />
            {errors.applyExpireDate && (
              <p className="text-red-500 text-sm mt-1 break-words whitespace-normal">
                {errors.applyExpireDate}
              </p>
            )}
            <p className="text-sm text-gray-400 mt-1 ms-1 inline-flex items-center break-words whitespace-pre-wrap">
              {/* Please specify the deadline by which the bounty must be expired.{" "} */}
              Please specify the last date for applicants to submit their
              proposals. After this date, new applications will no longer be
              accepted.
            </p>
          </div>
        </div>
      </div>

      <div>
        <div className="mb-4">
          <Label className="block text-sm font-semibold mb-2">
            Upload File
          </Label>
          <TooltipWrapper
            isDisabled={
              Boolean(id) &&
              (formData.status === "approved" || formData.status === "expired")
            }
            status={formData.status}
          >
            <PdfUpload
              name="product_files"
              formdata={formData}
              setFormdata={setFormData}
              removeError={() => {
                // Clear errors if needed
              }}
              disabled={
                Boolean(id) &&
                (formData.status === "approved" ||
                  formData.status === "expired")
              }
            />
          </TooltipWrapper>
        </div>

        {/* {imageError && <p className="text-red-400">{imageError}</p>} */}
        <div
          className={`${id && (formData.status === "approved" || formData.status === "expired") ? "mt-10" : ""}`}
        >
          <label className={`flex items-center cursor-pointer`}>
            <input
              type="checkbox"
              name="termsAccepted"
              checked={formData.termsAccepted || false}
              onChange={handleInputChange}
              className="mr-2"
              required
            />
            <Link
              href={"/bountytandc"}
              target="_blank"
              className="text-blue-500 hover:underline"
            >
              Accept Terms and Conditions
            </Link>
          </label>
          <p className="text-sm text-gray-400 mt-1 ms-1">
            Accepting bounty terms and conditions is mandatory.
          </p>
          {errors.termsAccepted && (
            <p className="text-red-500 text-sm mt-1">{errors.termsAccepted}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StepTwo;
