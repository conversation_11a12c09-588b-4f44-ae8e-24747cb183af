import React from "react";
import { Input } from "../ui/input";
import CustomBountyDropdown from "../ui/dashboard/bountydropdown";


const StepTwo = ({ formData, setFormData, errors, setErrors, id }) => {
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
   
    if (name === "estimatedPrice") {
      // Ensure the input doesn't allow negative values or more than one decimal point
      if (!/^\d*\.?\d{0,1}$/.test(value)) {
        return; // Prevent invalid input
      }

      // Prevent negative values programmatically
      if (parseFloat(value) < 0) {
        return;
      }
    }
    // Merge the existing formData with the new field value
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: value,
    }));

    // Clear the specific error message if the field is valid
    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "", // Clear error message for the specific field
      }));
    }
  };

  const handleBountyTypeSelect = (value) => {
    setFormData({
      ...formData,
      bountyType: value,
    });
    setErrors((prevErrors) => ({
      ...prevErrors,
      bountyType: "", // Clear error message for bountyType
    }));
  };

  const bountyTypeOptions = [
    { value: "ai", label: "Artificial Intelligence (AI)" },
    { value: "blockchain", label: "Blockchain" },
    { value: "ml", label: "Machine Learning (ML)" },
    { value: "mobile", label: "Mobile Application" },
    { value: "web", label: "Web Application" },
  ];

  const today = new Date().toISOString().split("T")[0];
  return (
    <div className="grid gap-6">
      <div className="flex flex-col">
        <label className="block text-sm font-medium mb-1">
          Bounty Price <span className="text-red-500">*</span> :
        </label>
        <Input
          name="estimatedPrice"
          type="number"
          value={formData.estimatedPrice || ""}
          onChange={handleInputChange}
          className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash  focus:outline-none focus:ring-2 focus:ring-purple-400"
          placeholder="Enter bounty price"
          required
          disabled={!!id}
          autoComplete="off"
        />
        {errors.estimatedPrice && (
          <p className="text-red-500 text-sm mt-1">{errors.estimatedPrice}</p>
        )}
        <p className="text-sm text-gray-400 mt-1 ms-1 inline-flex items-center">
          Enter the bounty price in credits. (1
          <img
            src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
            alt="coin"
            className="h-5 w-5 inline-block mx-1"
          />
          = 1 INR ~ 0.012 USD)
        </p>
      </div>

      <div className="flex flex-col">
        <label className="block text-sm font-medium mb-1">
          Bounty Completion Date <span className="text-red-500">*</span> :
        </label>
        <Input
          name="completionDate"
          type="date"
          value={formData.completionDate}
          onChange={handleInputChange}
          min={today}
          className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
          required
          onClick={(e) => (e.target as HTMLInputElement).showPicker()} // This line ensures the date picker opens on click
        />
        {errors.completionDate && (
          <p className="text-red-500 text-sm mt-1">{errors.completionDate}</p>
        )}
        <p className="text-sm text-gray-400 mt-1 ms-1 inline-flex items-center break-words whitespace-pre-wrap">
          Please specify the deadline by which the bounty must be completed.{" "}
        </p>
      </div>

      <div className="flex flex-col">
        {/* <label className="text-sm font-medium">Bounty Related to  :</label> */}
        <CustomBountyDropdown
          label={
            <>
              Bounty Related To <span className="text-red-500">*</span> :
            </>
          }
          options={bountyTypeOptions}
          selectedValue={formData.bountyType}
          onSelect={handleBountyTypeSelect}
        />
        {errors.bountyType && (
          <p className="text-red-500 text-sm mt-1">{errors.bountyType}</p>
        )}
        <p className="text-sm text-gray-400 mt-1 ms-1 inline-flex items-center">
          Please choose the bounty category that best fits your needs.{" "}
        </p>
      </div>
    </div>
  );
};

export default StepTwo;