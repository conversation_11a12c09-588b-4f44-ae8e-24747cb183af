"use client";
import React, { useEffect, useRef, useState } from "react";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import TooltipWrapper from "../bountysteps/tooltipWrapper";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { motion } from "framer-motion";

const StepOne = ({
  formData,
  setFormData,
  errors,
  setErrors,
  id,
  validateStepOne,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [tagOptions, setTagOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef(null);
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: value,
    });

    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "", // Clear error message for the specific field
      }));
    }
  };

  useEffect(() => {
    const fetchUseCases = async () => {
      try {
        setIsLoading(true);
        const res = await fetch(`/api/useCases`);
        const data = await res.json();
        if (data?.docs) {
          const formatted = data.docs.map((item) => ({
            id: item.id,
            value: item.value || item.id,
            label: item.label,
            description: item.description || "No description provided",
            price: item.price || 0,
          }));
          setTagOptions(formatted);
          setIsLoading(false);
        }
      } catch (err) {
        console.error("Error fetching use cases:", err);
      }
    };

    fetchUseCases();
  }, []);

  const handleUseCaseSelect = (useCase) => {
    // If the selected use case is already the current one, deselect it
    if (formData.useCases[0] === useCase.id) {
      if (id) {
        setFormData((prev) => ({
          ...prev,
          useCases: [],
          bountyType: "",
          estimatedPrice: formData.estimatedPrice,
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          useCases: [],
          bountyType: "",
          estimatedPrice: "",
        }));
      }
    } else {
      // Otherwise, select the new use case (replacing any previous selection)
      if (id) {
        setFormData((prev) => ({
          ...prev,
          bountyType: useCase?.value,
          useCases: [useCase.id], // Store as array with single item
          estimatedPrice: formData.estimatedPrice,
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          bountyType: useCase?.value,
          useCases: [useCase.id], // Store as array with single item
          estimatedPrice: useCase.price.toString(),
        }));
      }
    }

    // Clear error if a use case is selected
    setErrors((prev) => ({
      ...prev,
      useCases: "",
      bountyType: "",
    }));
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsDropdownOpen(false); // Close dropdown if clicked outside
    }
  };

  // Close dropdown if clicked outside
  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="">
        {/* Bounty Title */}
        <div className="flex flex-col">
          <label htmlFor="title" className=" text-sm font-medium mb-1">
            Bounty Title <span className="text-red-500">*</span>
          </label>
          <TooltipWrapper
            isDisabled={
              Boolean(id) &&
              (formData.status === "approved" || formData.status === "expired")
            }
            status={formData.status}
          >
            <Input
              id="title"
              name="title"
              type="text"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter Bounty title"
              className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:cursor-not-allowed"
              required
              disabled={
                Boolean(id) &&
                (formData.status === "approved" ||
                  formData.status === "expired")
              }
            />
          </TooltipWrapper>
          <p className="text-sm text-gray-400 mt-1 ms-1">
            Please enter the bounty title.
          </p>
          {errors.title && (
            <p className="text-red-500 text-sm mt-1">{errors.title}</p>
          )}
        </div>

        {/* Bounty Description */}
        <div className="flex flex-col mt-2">
          <label htmlFor="description" className=" text-sm font-medium mb-1">
            Bounty Description <span className="text-red-500">*</span>
          </label>
          <TooltipWrapper
            isDisabled={
              Boolean(id) &&
              (formData.status === "approved" || formData.status === "expired")
            }
            status={formData.status}
          >
            <Textarea
              id="content"
              name="content"
              placeholder="Enter Bounty Description"
              value={formData.content}
              onChange={handleInputChange}
              className=" text-white bg-dash border-indigo-500 focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:cursor-not-allowed disabled:opacity-100"
              style={{ overflow: "hidden", resize: "none" }}
              rows={5}
              required
              disabled={
                Boolean(id) &&
                (formData.status === "approved" ||
                  formData.status === "expired")
              }
            />
          </TooltipWrapper>
          <p className="text-sm text-gray-400 mt-1 ms-1 break-words whitespace-pre-wrap">
            Please provide a detailed description of the requirements and
            criteria needed to complete the bounty.
          </p>
          {errors.content && (
            <p className="text-red-500 text-sm mt-1">{errors.content}</p>
          )}
        </div>
      </div>

      <div>
        <label htmlFor="description" className=" text-sm font-medium ">
          Bounty Type <span className="text-red-500">*</span>
        </label>
        <>
          {isLoading ? (
            <div className="w-full flex justify-center items-center ">
              <Loader2
                style={{ animation: "spin 1s linear infinite" }}
                className="h-6 w-6 lg:h-10 lg:w-10 animate-spin md:mt-20"
              />
            </div>
          ) : (
            <div className="overflow-y-auto max-h-80 pr-2 space-y-2 mt-2">
              {tagOptions.map((useCase) => {
                const isSelected = formData.bountyType === useCase.value;
                const isDisabled =
                  id &&
                  (formData.status === "approved" ||
                    formData.status === "expired");
                return (
                  <TooltipWrapper
                    key={useCase.id}
                    isDisabled={isDisabled}
                    status={formData.status}
                  >
                    <div
                      className={`rounded-lg p-4 shadow-md transition-colors duration-200 flex flex-col 
                        ${isSelected ? "bg-indigo-600 border-indigo-400" : "bg-dash hover:bg-dash/30"}
                        ${isDisabled ? "cursor-not-allowed" : "cursor-pointer"}
                      `}
                      onClick={() => {
                        if (!isDisabled) {
                          handleUseCaseSelect(useCase);
                        }
                      }}
                    >
                      {/* Container for title and price/coin*/}
                      <div className="flex items-center justify-between w-full">
                        <h4 className="text-white font-semibold flex-grow">
                          {useCase.label}
                        </h4>
                        <div className="flex items-center gap-1 flex-shrink-0 ml-2">
                          <p className="text-sm text-white">
                            <span className="font-medium">{useCase.price}</span>
                          </p>
                          <div className="flex -rotate-45">
                            <div className="motion-preset-stretch motion-duration-2000">
                              <Image
                                src="/img/coin-png.png"
                                width={20}
                                height={20}
                                alt="Coin"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* Description below title and price */}
                      <p className="text-sm text-gray-200 break-words whitespace-normal overflow-hidden mt-1">
                        {useCase.description}
                      </p>
                    </div>
                  </TooltipWrapper>
                );
              })}
            </div>
          )}
        </>
        {(errors.useCases || errors.bountyType) && (
          <p className="text-red-500 text-sm mt-1">
            {errors.useCases || errors.bountyType}
          </p>
        )}
      </div>
    </div>
  );
};

export default StepOne;