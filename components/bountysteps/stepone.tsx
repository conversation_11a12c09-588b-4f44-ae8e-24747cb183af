"use client";
import React, { useEffect, useRef, useState } from "react";
import Select from "react-select";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { ChevronDown, ChevronUp, X } from "lucide-react";

const tagOptions = [
  { value: "ai/ml", label: "AI/ML" },
  { value: "assets", label: "Assets" },
  { value: "backend", label: "Backend" },
  { value: "bug", label: "Bug" },
  { value: "blockchain", label: "Blockchain" },
  { value: "chatbot", label: "Chatbot" },
  { value: "design", label: "Design" },
  { value: "finetuning", label: "Finetuning" },
  { value: "frontend", label: "Frontend" },
  { value: "fullstack", label: "Fullstack" },
  { value: "prompt", label: "Prompt" },
  { value: "testing", label: "Testing" },
  { value: "ui/ux", label: "UI/UX" },
];

const customStyles = {
  control: (base) => ({
    ...base,
    backgroundColor: "#302d7e", // Dark background for the input field
  }),
  menu: (base) => ({
    ...base,
    backgroundColor: "#302d7e", // Dark background for the dropdown menu
  }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isFocused ? "#131056" : "#302d7e", // Darker background on hover
    color: state.isFocused ? "#ffffff" : "#e0e0e0", // White text on hover, light gray text otherwise
    cursor: "pointer",
  }),
};

const StepOne = ({
  formData,
  setFormData,
  errors,
  setErrors,
  validateStepOne,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: value,
    });

    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "", // Clear error message for the specific field
      }));
    }
  };

  const handleSelectChange = (selectedOptions) => {
    setFormData({
      ...formData,
      tags: selectedOptions.map((option) => option.value),
    });

    setErrors((prevErrors) => ({
      ...prevErrors,
      tags: "", // Clear the tags error
    }));
  };

  const toggleDropdown = () => setIsDropdownOpen((prev) => !prev);

  const handleTagSelect = (value: string) => {
    setFormData((prevFormData) => {
      // Create a new array to avoid state mutation
      let updatedTags = [...prevFormData.tags];

      if (updatedTags.includes(value)) {
        // If already selected, remove it
        updatedTags = updatedTags.filter((tag) => tag !== value);
      } else {
        // If not selected, add it
        updatedTags.push(value);
      }

      return { ...prevFormData, tags: updatedTags };
    });
    if (!formData.tags.includes(value)) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        tags: "",
      }));
    }
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsDropdownOpen(false); // Close dropdown if clicked outside
    }
  };

  // Close dropdown if clicked outside
  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleTagRemove = (value: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter((tag) => tag !== value),
    });
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Bounty Title */}
      <div className="flex flex-col">
        <label htmlFor="title" className=" text-sm font-medium mb-1">
          Bounty Title <span className="text-red-500">*</span> :
        </label>
        <Input
          id="title"
          name="title"
          type="text"
          value={formData.title}
          onChange={handleInputChange}
          placeholder="Enter Bounty title"
          className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash  focus:outline-none focus:ring-2 focus:ring-purple-400"
          required
        />
        <p className="text-sm text-gray-400 mt-1 ms-1">
          Please enter the bounty title.
        </p>
        {errors.title && (
          <p className="text-red-500 text-sm mt-1">{errors.title}</p>
        )}
      </div>

      {/* Bounty Description */}
      <div className="flex flex-col">
        <label htmlFor="description" className=" text-sm font-medium mb-1">
          Bounty Description <span className="text-red-500">*</span> :
        </label>
        <Textarea
          id="content"
          name="content"
          placeholder="Enter Bounty Description"
          value={formData.content}
          onChange={handleInputChange}
          // className="w-full p-3 border text-white border-gray-300 rounded-lg bg-dash  focus:outline-none focus:ring-2 focus:ring-purple-400"
          className=" text-white bg-dash border-indigo-500 focus:outline-none focus:ring-2 focus:ring-purple-400"
          style={{ overflow: "hidden", resize: "none" }}
          rows={5}
          // col={50}
          required
        />
        <p className="text-sm text-gray-400 mt-1 ms-1 break-words whitespace-pre-wrap">
          Please provide a detailed description of the requirements and criteria
          needed to complete the bounty.
        </p>
        {errors.content && (
          <p className="text-red-500 text-sm mt-1">{errors.content}</p>
        )}
      </div>

      {/* Tags */}
      <div className="flex flex-col">
        <label className="text-sm font-medium mb-1">Bounty Tags:</label>
        <div className="max-w-sm md:max-w-none">
          {formData.tags.length > 0 && (
            <div className="flex flex-wrap mt-2 overflow-x-auto whitespace-nowrap max-w-sm md:max-w-none">
              {formData.tags.map((tag) => (
                <div
                  key={tag}
                  className="bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm mr-2 mb-2 flex items-center"
                >
                  <span>
                    {tagOptions.find((o) => o.value === tag)?.label || tag}
                  </span>
                  <button
                    className="ml-2 text-white hover:text-gray-300"
                    onClick={() => handleTagRemove(tag)}
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
        <div className="relative" ref={dropdownRef}>
          <div
            className="w-full p-3 border border-gray-300 rounded-lg bg-dash text-white cursor-pointer flex justify-between"
            onClick={toggleDropdown}
          >
            <div className="w-full overflow-x-auto whitespace-nowrap no-scrollbar">
              {formData.tags.length > 0
                ? formData.tags.join(", ")
                : "Select tags"}
            </div>
            <span className="ml-2 transform transition-transform duration-300">
              {isOpen ? (
                <ChevronUp className="inline" />
              ) : (
                <ChevronDown className="inline" />
              )}
            </span>
          </div>
          {isDropdownOpen && (
            <div
              className="absolute z-10 w-full mt-2 bg-dash border border-gray-300 rounded-lg shadow-lg"
              style={{ maxHeight: "200px", overflowY: "auto" }} // Applied max-height and scrolling
            >
              {tagOptions.map((option) => (
                <div
                  key={option.value}
                  className={`p-3 hover:bg-gray-700 cursor-pointer ${
                    formData.tags.includes(option.value) ? "bg-gray-700" : ""
                  }`}
                  onClick={() => handleTagSelect(option.value)}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>

        <p className="text-sm text-gray-400 mt-1 ms-1">
          Please select all tags that are applicable to your bounty.
        </p>
      </div>
    </div>
  );
};

export default StepOne;