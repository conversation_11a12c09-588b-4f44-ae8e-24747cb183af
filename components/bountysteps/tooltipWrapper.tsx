"use client";

import React from "react";

interface TooltipWrapperProps {
    isDisabled: boolean; 
    children: React.ReactNode; 
    status?: string;
}

const TooltipWrapper = ({ isDisabled, children, status }: TooltipWrapperProps) => {
  if (!isDisabled) return <>{children}</>;

  return (
    <div className="relative group cursor-not-allowed">
      {children}
      {status === "approved" ?
      <div className="absolute top-full left-0 mt-1 w-max bg-black text-white text-xs rounded-md p-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
      You cannot edit this field after bounty approval.
      </div> :
      <div className="absolute top-full left-0 mt-1 w-max bg-black text-white text-xs rounded-md p-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
        You cannot edit this field after bounty expired.
      </div>}
    </div>
  );
};

export default TooltipWrapper;
