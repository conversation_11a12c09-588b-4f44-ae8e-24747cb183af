"use client"
import React, { useEffect } from "react";
import { toast } from "sonner";

interface ApplicationStatusMessageProps {
  applicant?: any;
}

const ApplicationStatusMessage: React.FC<ApplicationStatusMessageProps> = ({
  applicant,
}) => {
  
  useEffect(() => {
    if (applicant.winner) {
      toast(
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <p className="font-medium text-green-700">🎉 Congratulations!</p>
          </div>
          <p className="mt-1">
            Your final submission has been approved. Your account is credited with{" "}
            <span className="font-semibold underline">{applicant.rewardJoules} joules</span> for winning this bounty.
          </p>
        </div>,
        {
          duration: 10000, // 10 seconds
          id: `approval-${Date.now()}`, // Unique ID to prevent duplicates
          className: "bg-green-50 border border-green-300 text-green-800 rounded-lg",
        }
      );
    } else if (applicant.applicantStatus === "rejected") {
      const reason = applicant.rejectionReason?.trim() || "No reason provided.";
      
      toast(
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <p className="font-medium text-red-700">Application Rejected</p>
          </div>
          <p className="mt-1 whitespace-pre-line">
            <span className="font-semibold">Reason:</span> {reason}
          </p>
        </div>,
        {
          duration: 10000, // 10 seconds
          id: `rejection-${Date.now()}`, // Unique ID to prevent duplicates
          className: "bg-red-50 border border-red-300 text-red-800 rounded-lg",
        }
      );
    } else if (applicant.applicantStatus === "approved") {
      toast(
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <p className="font-medium text-green-700">🎉 Congratulations!</p>
          </div>
          <p className="mt-1">
            Your application has been approved. Click on the{" "}
            <span className="font-semibold underline">Discussion</span> tab to communicate with the bounty owner.
          </p>
        </div>,
        {
          duration: 10000, // 10 seconds
          id: `approval-${Date.now()}`, // Unique ID to prevent duplicates
          className: "bg-green-50 border border-green-300 text-green-800 rounded-lg",
        }
      );
    }
  }, [applicant.applicantStatus, applicant.rejectionReason]); // Only run when status or rejectionReason changes

  // Return null since we're showing a toast instead of rendering in the DOM
  return null;
};

export default ApplicationStatusMessage;
