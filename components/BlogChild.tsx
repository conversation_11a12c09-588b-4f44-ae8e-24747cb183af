"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { Blog, Media } from "@/server/payload-types";
import BlogSidebar from "@/components/BlogSidebar";
import { Card<PERSON>ody, CardContainer, CardItem } from "@/components/ui/3d-card";
import { Badge } from "@/components/ui/badge";
import axios from "axios";
import SearchBar from "@/components/ui/searchbar";
import { Icons } from "@/components/Icons";
import ReactMarkdown from "react-markdown";
import { Star } from "lucide-react";

const bgColors = [
  "bg-custom-bg4",
  "bg-custom-bg5",
  "bg-custom-bg6",
  "bg-custom-bg1",
  "bg-custom-bg2",
  "bg-custom-bg3",
];

type Tags =
  | "research"
  | "casestudy"
  | "learning"
  | "development"
  | "All Blogs"
  | "";

const BlogChild = () => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filteredBlogs, setFilteredBlogs] = useState<Blog[]>([]);
  const [selectedTags, setSelectedTags] = useState<Tags>("");
  const [searchQuery, setSearchQuery] = useState<string>("");

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await axios.get(`/api/blogs`);
        setBlogs(response.data.docs);
        console.log("response ");
        setFilteredBlogs(response.data.docs);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    fetchBlogs();
  }, []);

  useEffect(() => {
    if (selectedTags === "All Blogs" || selectedTags === "") {
      setFilteredBlogs(blogs);
    } else {
      setFilteredBlogs(
        blogs.filter((blog) => blog.tags.includes(selectedTags))
      );
    }
  }, [selectedTags, blogs]);

  useEffect(() => {
    const filtered = blogs.filter(
      (blog) =>
        blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        blog.content.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredBlogs(filtered);
  }, [searchQuery, blogs]);

  const handleFilter = (Tags: Tags) => {
    setSelectedTags(Tags);
  };

  if (loading) {
    return (
      <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50 ">
        <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
      </div>
    );
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  //------------------------------for image render---------------------------------------//
  const renderImage = (image: string | Media | undefined) => {
    if (typeof image === "string") {
      return <img src={image ?? undefined} />;
    } else if (image && image.sizes && image.sizes.thumbnail) {
      return <img src={image.sizes.thumbnail.url ?? undefined} />;
    }
    return null;
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query.toLowerCase());
  };

  return (
    <>
      <div className="container md:flex lg:space-x-12 sm:space-x-2">
        <div className="mt-3 hidden md:block">
          <BlogSidebar onFilterChange={handleFilter} />
        </div>
        <div className="w-full">
          <div className="flex flex-row gap-2">
            <div className="w-full md:w-[50%]">
              <SearchBar mainheader={false} onSearch={handleSearch} />
            </div>
            <div className="">
              <BlogSidebar
                onFilterChange={handleFilter}
                className="md:hidden"
              />
            </div>
          </div>

          <hr className=" my-4" />

          {filteredBlogs.length > 0 ? (
            <div className="grid grid-cols-1 gap-8 lg:mx-0 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1 ">
              {filteredBlogs.map((blog, index) => (
                <CardContainer key={blog.id} className="inter-var">
                  <Link href={`/blog/slug?id=${blog.id}`}>
                    <CardBody>
                      <article
                        className={`flex flex-col justify-between border border-white hover:border-violet-500 rounded-2xl md:max-w-xl sm:w-sm ${
                          bgColors[index % bgColors.length]
                        }`}
                      >
                        <CardItem>
                          <div className="flex flex-wrap px-6 py-4 justify-between md:space-x-2 sm:justify-between">
                            <h1 className="text-sm text-indigo-800 font-bold">
                              {new Date(blog.createdAt).toLocaleDateString(
                                "en-US",
                                {
                                  year: "numeric",
                                  month: "long",
                                  day: "numeric",
                                }
                              )}
                            </h1>
                            <Badge className=" bg-indigo-300 rounded-lg">
                              {blog.tags}{" "}
                            </Badge>

                            {blog.isFeatured == true ? (
                              <div className="absolute top-1 right-1 text-yellow-500 rounded text-xs z-10">
                                {/* <Sparkles className="w-6 h-6 text-yellow-500 fill-current" /> */}
                                <Star className="w-6 h-6 text-yellow-500 fill-current" />
                              </div>
                            ) : (
                              ``
                            )}
                          </div>
                        </CardItem>
                        <CardItem>
                          <div
                            className="relative w-full h-44 rounded-lg aspect-h-10 overflow-hidden flex flex-col justify-center
                          sm:justify-between px-6 xl:aspect-h-10"
                          >
                            {blog.images &&
                              blog.images.length > 0 &&
                              renderImage(blog.images[0].image)}
                          </div>
                        </CardItem>
                        <CardItem>
                          <div className="font-bold text-lg bg-transparent md:text-sm lg:text-lg text-indigo-900 underline mt-4 px-6">
                            {`${blog.title.substring(0, 30)}...`}
                          </div>
                        </CardItem>
                        <CardItem>
                          <div className="font-semibold px-6 text-indigo-400 py-2">
                            <ReactMarkdown className="font-normal text-sm text-indigo-600">
                              {`${blog.content.substring(0, 155)}...`}
                              {/* </h2> */}
                            </ReactMarkdown>
                          </div>
                        </CardItem>
                        <CardItem>
                          <div className="py-4 text-right px-6">
                            <p className="text-indigo-900 text-sm font-bold">
                              Reading time: {blog.time} mins
                            </p>
                          </div>
                        </CardItem>
                      </article>
                    </CardBody>
                  </Link>
                </CardContainer>
              ))}
            </div>
          ) : (
            <div className="text-center py-24">Blog not found</div>
          )}
        </div>
      </div>
    </>
  );
};

export default BlogChild;
