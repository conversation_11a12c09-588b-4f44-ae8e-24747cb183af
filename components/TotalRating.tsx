import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Star } from "lucide-react";

const TotalRating = ({ rappId, user, onRatingFetched, totalRating }) => {

  // useEffect(() => {
  //   const fetchTotalRating = async () => {
  //     if (!user?.id) {
  //       return;
  //     }

  //     try {
  //       const response = await fetch(
  //         `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/getRapp/${rappId}`,
  //         {
  //           method: "GET",
  //           headers: {
  //             "Content-Type": "application/json",
  //             "Access-Control-Allow-Origin": "*",
  //           },
  //           credentials: "include",
  //         }
  //       );

  //       const data = await response.json();
  //       if (response.ok) {
  //         setTotalRating(data.rating); // Set the total rating from the data
  //         if(onRatingFetched){
  //           onRatingFetched(data.rating); // Call the callback function with the total rating if provided.
  //         }
  //       } else {
  //         toast.error(data.error || "Failed to fetch total rating.");
  //       }
  //     } catch (error) {
  //       console.error("Error fetching total rating:", error);
  //       toast.error("An error occurred while fetching the total rating.");
  //     }
  //   };

  //   fetchTotalRating();
  // }, [rappId, user]);

  // Calculate the filled width for the star based on the rating (1 star = 20%).
  const filledWidth = (totalRating / 5) * 100;


  if (totalRating === 0) {
    return null;
  }
  return (
    <div className="flex items-center">
      <div className="relative flex items-center ">
        {/* Background star (empty) */}
        <div
          style={{
            position: "relative",
            width: "1rem",
            height: "1rem",
            marginRight: "0.4rem",
          }}
        >
          <Star
            style={{
              position: "absolute",
              color: "#FFFFFF", // Background color for empty part
              width: "100%",
              height: "100%",
            }}
          />

          {/* Foreground star (filled) */}
          <Star
            style={{
              position: "absolute",
              color: "#FACC15", // Filled color for rating part
              width: "100%",
              height: "100%",
              clipPath: `inset(0 ${100 - filledWidth}% 0 0)`, // Fill based on rating percentage
            }}
            className="fill-yellow-400"
          />
        </div>
        <div className=" text-white font-semibold text-sm -ml-1">
          {totalRating === 0 ? 0 : totalRating.toFixed(1)}
        </div>
      </div>
    </div>
  );
};

export default TotalRating;
