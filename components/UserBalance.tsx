"use client";
import Image from "next/image";
import { useEffect } from "react";
import { cn } from "@/lib/utils";
import ShinnyButton from "./ui/ShinnyButton";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useCoinBalance, useFetchCoinBalance } from "@/lib/userStore";

// Utility function to format the coin balance
const formatBalance = (balance: number): string => {
  return balance?.toFixed(1) || '0.0';
};

const UserBalance = () => {
  const coinBalance = useCoinBalance();
  const fetchCoinBalance = useFetchCoinBalance();

  useEffect(() => {
    fetchCoinBalance();
    
    // Set up polling to refresh balance periodically (every 30 seconds)
    const interval = setInterval(fetchCoinBalance, 30000);
    return () => clearInterval(interval);
  }, [fetchCoinBalance]);

  return (
    <Link href="/pricing?openModal=true" className="cursor-pointer">
      <ShinnyButton
        className={`flex items-center text-sm font-semibold text-white pl-2 pr-0 py-1 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105`}
      >
        <div className="flex flex-row items-center px-1 gap-1">
          <div className="flex -rotate-45">
            <div className="motion-preset-stretch motion-duration-2000">
              <Image
                src="/img/coin-png.png"
                width={28}
                height={28}
                alt="Coin"
              />
            </div>
          </div>

          {/* Coin balance text with formatted value */}
          <span
            className={cn(
              "text-sm md:text-xl font-bold transition-transform duration-500 ease-in-out flex flex-row items-center",
              "bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent"
            )}
          >
            {formatBalance(coinBalance)}
            <ChevronRight className="text-white px-0" />
          </span>
        </div>
      </ShinnyButton>
    </Link>
  );
};

export default UserBalance;