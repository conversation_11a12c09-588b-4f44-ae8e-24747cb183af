"use client";
import Image from "next/image";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import ShinnyButton from "./ui/ShinnyButton";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

// Utility function to format the coin balance
const formatBalance = (balance: number): string => {
  if (balance >= 1_000_000) {
    return (balance / 1_000_000).toFixed(1).replace(/\.0$/, "") + "m";
  } else if (balance >= 1_000) {
    return (balance / 1_000).toFixed(1).replace(/\.0$/, "") + "k";
  } else {
    return balance.toFixed(1).replace(/\.0$/, ""); // Ensures one decimal place
  }
};

const UserBalance = ({ user }: any) => {
  const [userData, setUserData] = useState<any>(user);
  const [loading, setLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const getUser = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,
          {
            method: "GET",
          }
        );
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
  
        // Update balance only if it has changed
        if (data.data.coinBalance !== userData.coinBalance) {
          setUserData(data.data);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    } 
    getUser();
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <Link href="/pricing?openModal=true" className="cursor-pointer">
    <ShinnyButton
      className={`flex items-center text-sm font-semibold text-white pl-2 pr-0 py-1 rounded-xl transition-all duration-300 ease-in-out transform ${
        loading ? "cursor-not-allowed opacity-70" : "hover:scale-105"
      }`}
      disabled={loading}
    >
      <div className="flex flex-row items-center px-1 gap-1">
        <div className="flex -rotate-45">
          <div className="motion-preset-stretch motion-duration-2000">
            <Image
              src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
              width={28}
              height={28}
              alt="Coin"
            />
          </div>
        </div>

        {/* Coin balance text with formatted value */}
        <span
          className={cn(
            "text-sm md:text-xl font-bold transition-transform duration-500 ease-in-out flex flex-row items-center",
            loading ? "text-gray-300" : "bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent"
          )}
        >
          {formatBalance(userData.coinBalance)}
          <ChevronRight className="text-white px-0" />
        </span>

        
      </div>
    </ShinnyButton>
    </Link>
  );
};

export default UserBalance;
