'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider, useAuth } from '@/app/context/AuthContext';
import { isTokenExpired } from '@/payload/utilities/isTokenExpired';

export const Providers: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <TokenExpirationChecker>{children}</TokenExpirationChecker>
      </QueryClientProvider>
    </AuthProvider>
  );
};

// TokenExpirationChecker component to handle token expiration globally
const TokenExpirationChecker: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { token, setToken } = useAuth();
  const router = useRouter();

  // Function to handle token expiration
  const handleTokenExpiration = () => {
    if (token && isTokenExpired(token)) {
      setToken(null); // Clear token from state
      localStorage.removeItem('token'); // Remove token from localStorage
      router.push('/sign-in'); // Redirect to sign-in page
    }
  };

  // Check token expiration on initial load
  useEffect(() => {
    handleTokenExpiration();
  }, [token, setToken, router]);

  // Periodically check token expiration every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      handleTokenExpiration();
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval); // Cleanup interval on unmount
  }, [token, setToken, router]);

  return <>{children}</>;
};