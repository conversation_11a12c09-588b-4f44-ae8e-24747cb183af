"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight, Cross } from "lucide-react";
import { Cross1Icon } from "@radix-ui/react-icons";

interface ImageGalleryProps {
  images: string[];
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images }) => {
  const [mainImageIndex, setMainImageIndex] = useState<number>(0);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
  const [isFullImageOpen, setIsFullImageOpen] = useState<boolean>(false);

  const mainImage =
  images[mainImageIndex] ||
  "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/placeholder-09.webp";

  const selectedImage =
  (selectedImageIndex !== null && images[selectedImageIndex]) ||
    
    "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/placeholder-09.webp";

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index);
    setIsFullImageOpen(true); // Open the popup
  };

  const closePopup = () => {
    setIsFullImageOpen(false); // Close the popup
  };

  const handleNextImage = () => {
    setSelectedImageIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handlePreviousImage = () => {
    setSelectedImageIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  // Handle closing the popup with the ESC key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        closePopup();
      }
    };

    document.addEventListener("keydown", handleEscape);

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, []);

  return (
    <div>
      <div className="mb-4 relative">
        <Image
          width="500"
          height="500"
          src={mainImage}
          alt="Selected Image"
          className="h-60 lg:h-96 object-cover rounded mr-3 mb-3 w-full cursor-pointer"
          // onClick={() => setSelectedImageIndex(mainImageIndex)}
          onClick={() => {
            setSelectedImageIndex(mainImageIndex);
            setIsFullImageOpen(true); // <-- Open popup on click
          }}
        />
        {/* <button
          className="absolute top-1/2 left-2 transform -translate-y-1/2 bg-white text-black rounded-full p-2"
          onClick={handlePreviousImage}
          aria-label="Previous Image"
        >
          &larr;
        </button>
        <button
          className="absolute top-1/2 right-2 transform -translate-y-1/2 bg-white text-black rounded-full p-2"
          onClick={handleNextImage}
          aria-label="Next Image"
        >
          &rarr;
        </button> */}
      </div>
      <div className="flex overflow-x-scroll whitespace-nowrap space-x-4 mb-4 no-scrollbar">
        {images.length > 1 &&
          images.slice(1).map((url, i) => (
            <div key={i}>
              <Image
                width="500"
                height="500"
                src={url}
                alt={`Thumbnail ${i + 1}`}
                className={`object-cover min-w-36 h-28 cursor-pointer ${
                  i === selectedImageIndex ? "border-2 border-indigo-500" : ""
                }`}
                onClick={() => handleImageClick(i + 1)}
              />
            </div>
          ))}
      </div>

      {/* Full-Image Popup */}
      {isFullImageOpen && (
        <div
          className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center"
          onClick={closePopup}
        >
          {/* Close Button */}
          <button
            onClick={closePopup}
            className="absolute top-4 right-4 bg-white text-black rounded-full p-2 text-2xl font-bold z-60"
            aria-label="Close full image"
          >
            <Cross1Icon />
          </button>

          {/* Previous Button */}
          <button
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white text-black rounded-full p-2 text-2xl"
            onClick={(e) => {
              e.stopPropagation(); // Prevent popup from closing
              handlePreviousImage();
            }}
            aria-label="Previous Image"
          >
            <ChevronLeft />
          </button>

          {/* Next Button */}
          <button
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white text-black rounded-full p-2 text-2xl"
            onClick={(e) => {
              e.stopPropagation(); // Prevent popup from closing
              handleNextImage();
            }}
            aria-label="Next Image"
          >
            <ChevronRight />
          </button>

          {/* Full Image */}
          <Image
            src={selectedImage}
            className="max-w-full max-h-full object-contain"
            height={800}
            width={800}
            alt="Full Image"
          />
        </div>
      )}
    </div>
  );
};

export default ImageGallery;
