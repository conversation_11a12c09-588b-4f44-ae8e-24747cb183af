"use client"
import React from "react";
import { AuroraBackground } from "@/components/ui/aurora-background";
import { motion } from "framer-motion";
import { Highlight } from "@/components/ui/hero-highlight";

const BlogHeroSec = () => {
  return (
    <AuroraBackground>
      <motion.div
        initial={{ opacity: 0.0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.3,
          duration: 0.8,
          ease: "easeInOut",
        }}
        className="relative flex flex-col gap-4 items-center justify-center px-4 w-full"
      ></motion.div>
      <motion.h1
        initial={{
          opacity: 0,
          y: 20,
        }}
        animate={{
          opacity: 1,
          y: [20, -5, 0],
        }}
        transition={{
          duration: 0.5,
          ease: [0.4, 0.0, 0.2, 1],
        }}
        className="text-2xl px-4 md:text-4xl lg:text-5xl font-bold text-white dark:text-white max-w-4xl 
          leading-relaxed lg:leading-snug text-center md:mt-8"
      >
        <div>The #1 source for Updates</div>{" "}
        <Highlight className="text-indigo-800 dark:text-white">
          Read and Explore.
        </Highlight>
      </motion.h1>
    </AuroraBackground>
  );
};

export default BlogHeroSec;
