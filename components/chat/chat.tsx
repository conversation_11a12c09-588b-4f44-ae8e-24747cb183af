import React from "react";
import ChatTopbar from "./chat-topbar";
import Chat<PERSON>ist from "./chat-list";
import Chat<PERSON><PERSON>ombar from "./chat-bottombar";
import { ChatRequestOptions } from "ai";
import { v4 as uuidv4 } from "uuid";

export interface ChatProps {
  chatId?: string;
  setSelectedModel: React.Dispatch<React.SetStateAction<string>>;
  messages: any;
  models?:any;
  input: string;
  onImageUrlChange?: (url: string) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (
    e: React.FormEvent<HTMLFormElement>,
    chatRequestOptions?: ChatRequestOptions
  ) => void;
  isLoading: boolean;
  loadingSubmit?: boolean;
  error: undefined | Error;
  stop: () => void;
  formRef: React.RefObject<HTMLFormElement>;
  isMobile?: boolean;
  setInput?: React.Dispatch<React.SetStateAction<string>>;
  model?: any;
  generateType?: string;
  selectedModel?:string;
  fetchedModel?:string;
}

export default function Chat({
  messages,
  generateType,
  selectedModel,
  input,
  handleInputChange,
  handleSubmit,
  onImageUrlChange,
  models,
  isLoading,
  error,
  stop,
  setSelectedModel,
  chatId,
  loadingSubmit,
  formRef,
  isMobile,
  setInput,
  model,
  fetchedModel,
}: ChatProps) {
  return (
    <div className="flex flex-col justify-between w-full max-w-3xl h-full ">
      <ChatTopbar
        setSelectedModel={setSelectedModel}
        isLoading={isLoading}
        chatId={chatId}
        models={models}
        messages={messages}
        model = {model}
        fetchedModel={fetchedModel}
        generateType={generateType}
        selectedModel={selectedModel}
      />

      <ChatList
        setSelectedModel={setSelectedModel}
        messages={messages}
        generateType={generateType}
        input={input}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
        isLoading={isLoading}
        loadingSubmit={loadingSubmit}
        error={error}
        stop={stop}
        formRef={formRef}
        isMobile={isMobile}
      />

      <ChatBottombar
        setSelectedModel={setSelectedModel}
        selectedModel={selectedModel}
        models={models}
        messages={messages}
        input={input}
        onImageUrlChange={onImageUrlChange}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
        isLoading={isLoading}
        loadingSubmit={loadingSubmit}
        error={error}
        stop={stop}
        formRef={formRef}
        setInput={setInput}
      />
    </div>
  );
}
