"use client";

import React, { useEffect, useState } from "react";

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { cn } from "@/lib/utils";
import { Sidebar } from "../sidebar";
import Chat, { ChatProps } from "./chat";
import ChatList from "./chat-list";
import { HamburgerMenuIcon } from "@radix-ui/react-icons";
import { Button, buttonVariants } from "@/components/ui/button";
import { ChevronsUpDown, MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import { Icons } from "../Icons";
import { Check } from "lucide-react";
import coinImage from "../../public/img/coin-png.png";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Message } from "ai";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { useParams, useSearchParams } from "next/navigation";

interface ChatLayoutProps {
  defaultLayout: number[] | undefined;
  defaultCollapsed?: boolean;
  navCollapsedSize: number;
  chatId: string;
  selectedModel: any;
  generateType: string;
  setMessages: any;
  sendData: any;
}

interface Model {
  map(
    arg0: (m: any) => React.JSX.Element
  ):
    | import("react-i18next").ReactI18NextChild
    | Iterable<import("react-i18next").ReactI18NextChild>;
  id: string;
  name: string;
  description: string;
  imageinputopt: boolean;
  modelType: string;
  cost: number;
  commission: number;
  provider: { text: string; image: string };
  examples: { example: string; id: string }[];
}
type MergedProps = ChatLayoutProps & ChatProps;

export function ChatLayout({
  defaultLayout = [60, 120],
  defaultCollapsed = false,
  navCollapsedSize,
  messages,
  sendData,
  input,
  handleInputChange,
  onImageUrlChange,
  handleSubmit,
  isLoading,
  error,
  stop,
  chatId,
  generateType,
  setSelectedModel,
  selectedModel,
  loadingSubmit,
  formRef,
  setMessages,
  setInput,
}: MergedProps): React.JSX.Element {
  const searchParams = useSearchParams();
  const id = searchParams.get("id");
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);
  const [isMobile, setIsMobile] = useState(false);
  const [model, setModel] = useState<Model[]>([]);
  const [selectedModelId, setSelectedModelId] = useState<any>();
  const [fetchedModel, setFetchedModel] = useState<any>(null);
  const [open, setOpen] = useState(false);

  // useEffect(() => {

  //     setSelectedModel('66bf9e21a186c6e3a02b1825');

  // }, [generateType]);

  useEffect(() => {
    if (id) {
      setSelectedModel(Array.isArray(id) ? id[0] : id);
    } else {
      setSelectedModel("66bf9e21a186c6e3a02b1825");
    }
  }, [id, generateType]);

  useEffect(() => {
    const checkScreenWidth = () => {
      setIsMobile(window.innerWidth <= 1023);
    };

    // Initial check
    checkScreenWidth();

    // Event listener for screen width changes
    window.addEventListener("resize", checkScreenWidth);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener("resize", checkScreenWidth);
    };
  }, []);

  // for fetching models based on generateType
  useEffect(() => {
    const fetchModels = async () => {
      try {
        let res;
        if (generateType === "image" && selectedModelId) {
          //imagemodels
          res = await fetch(
            `/api2/models/${selectedModelId}?type=${generateType}`
          );
        } else if (
          generateType === "text" ||
          generateType === "vision" ||
          generateType === "both"
        ) {
          //textmodels + vision
          res = await fetch(`/api2/models?type=${generateType}`);
        }
        if (!res.ok) {
          throw new Error("Network response was not ok");
        }

        const { data } = await res.json();
        sendData(data)
        if (generateType === "image") {
          setFetchedModel(data);
        } else {
          setModel(data); // Store the list of models for text
        }
      } catch (err) {
        console.error(err);
      }
    };

    if (generateType) {
      fetchModels();
    }
  }, [generateType, selectedModelId]);

  const handleSelect = (id) => {
    setSelectedModel(id);
    setOpen(false); // Close the popover when selecting a model
  };

  return (
    <ResizablePanelGroup
      direction="horizontal"
      onLayout={(sizes: number[]) => {
        document.cookie = `react-resizable-panels:layout=${JSON.stringify(
          sizes
        )}`;
      }}
      className="h-full max-h-[660px]  border bg-indigo-900 rounded-xl border-primary items-stretch mt-36 md:mt-16"
    >
      <ResizablePanel
        defaultSize={defaultLayout[0]}
        collapsedSize={navCollapsedSize}
        collapsible={true}
        minSize={isMobile ? 0 : 12}
        maxSize={isMobile ? 0 : 16}
        onCollapse={() => {
          setIsCollapsed(true);
          document.cookie = `react-resizable-panels:collapsed=${JSON.stringify(
            true
          )}`;
        }}
        onExpand={() => {
          setIsCollapsed(false);
          document.cookie = `react-resizable-panels:collapsed=${JSON.stringify(
            false
          )}`;
        }}
        className={cn(
          isCollapsed
            ? "min-w-[50px] md:min-w-[70px] transition-all duration-300 ease-in-out"
            : "hidden lg:block min-w-[25%]"
        )}
      >
        <div className="relative justify-between group lg:bg-indigo-700/70 lg:dark:bg-card/35 flex flex-col h-full gap-4 p-2 w-full">
          <div className=" flex flex-col justify-between p-2 max-h-fit">
            <Button
              onClick={() => {
                // router.push("/");
                // Clear messages
                setMessages([]);
              }}
              variant="ghost"
              className="flex justify-between w-full h-14 text-sm xl:text-lg font-normal items-center "
            >
              <div className="flex gap-3 items-center ">
                {!isCollapsed && !isMobile && (
                  <Icons.logo className="h-5 fill-white stroke-white" />
                )}
                <span className="flex gap-1 items-center ">
                  New chat
                  <Pencil size={18} className="shrink-0 w-4 h-4" />
                </span>
              </div>
            </Button>

            <div className="flex flex-col gap-2">
              <p className="pl-4 text-3xl font-medium">Choose a Model</p>

              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger className="w-full px-4 py-3 bg-indigo-950/40 rounded-xl text-white text-left flex flex-row justify-between items-center border border-white">
                  {selectedModel
                    ? model.find((m) => m.id === selectedModel)?.name ||
                      "Select a Model"
                    : "Select a Model"}
                  <ChevronsUpDown size={18} className="text-white" />
                </PopoverTrigger>

                <PopoverContent
                  forceMount
                  side="bottom"
                  align="start"
                  avoidCollisions={true} // Ensures it doesn't overlap on other elements
                  sideOffset={5}
                  className="w-full max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-300 xl:min-w-[300px] rounded-xl bg-indigo-950"
                >
                  {generateType === "image" && fetchedModel ? (
                    <div
                      onClick={() => handleSelect(fetchedModel.id)}
                      className="cursor-pointer flex items-center justify-between px-4 py-2 transition-colors duration-300 bg-indigo-600 hover:bg-indigo-900 text-white rounded-xl"
                    >
                      <div className="flex flex-col items-start">
                        <p>{fetchedModel.name}</p>
                        <p className="text-gray-300 text-sm">
                          {fetchedModel.description}
                        </p>
                      </div>
                      {selectedModel === fetchedModel.id && (
                        <Check size={18} className="text-white" />
                      )}
                    </div>
                  ) : model ? (
                    model.map((m) => (
                      <div
                        key={m.id}
                        onClick={() => handleSelect(m.id)}
                        className="cursor-pointer flex items-center justify-between px-4 py-2 transition-colors duration-300 hover:bg-indigo-900 w-full rounded-lg"
                      >
                        <div className="flex flex-row items-center gap-2 justify-between w-full">
                          <p className="text-white flex flex-row items-center gap-2">
                            {m.name}{" "}
                            {selectedModel === m.id && (
                              <Check size={18} className="text-white" />
                            )}
                          </p>
                          <div className="flex items-center text-yellow-400">
                            {m.cost === 0 ? "Free" : `${m.cost.toFixed(2)}`}
                            <img
                              src={coinImage.src}
                              alt="Coin"
                              style={{ width: "17px", height: "20px" }}
                              className="inline"
                              loading="lazy"
                            />
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="px-4 py-2 text-white">Loading models...</p>
                  )}
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      </ResizablePanel>
      {/* <ResizableHandle className={cn("hidden md:flex")} withHandle /> */}
      <ResizablePanel
        className="h-full w-full flex justify-center"
        defaultSize={defaultLayout[1]}
      >
        <Chat
          chatId={chatId}
          generateType={generateType}
          setSelectedModel={setSelectedModel}
          messages={messages}
          models={model}
          input={input}
          handleInputChange={handleInputChange}
          onImageUrlChange={onImageUrlChange}
          handleSubmit={handleSubmit}
          isLoading={isLoading}
          loadingSubmit={loadingSubmit}
          error={error}
          stop={stop}
          formRef={formRef}
          isMobile={isMobile}
          setInput={setInput}
          fetchedModel={fetchedModel}
          selectedModel={selectedModel}
        />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
