import { Message, useChat } from "ai/react";
import React, { useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { ChatProps } from "./chat";
import Image from "next/image";
import CodeDisplayBlock from "../code-display-block";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Button } from "../ui/button";
import { INITIAL_QUESTIONS } from "@/app/utils/initial-questions";
//import { IMAGE_GENERATION_QUESTIONS } from "@/app/utils/initial-questions";
import { Icons } from "../Icons";
import { Copy } from "lucide-react";

export default function ChatList({
  messages,
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  error,
  stop,
  loadingSubmit,
  formRef,
  generateType,
  isMobile,
}: ChatProps) {
  const bottomRef = useRef<HTMLDivElement>(null);
  const [name, setName] = React.useState<string>("");
  const [localStorageIsLoading, setLocalStorageIsLoading] =
    React.useState(true);
  const [initialQuestions, setInitialQuestions] = React.useState<Message[]>([]);
  const [copiedMessageId, setCopiedMessageId] = React.useState(null);

  const scrollToBottom = () => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth", block: "end" });
  };


  const handleCopy = (content, id) => {

    const plainText = content
        .replace(/[#*_`>-]/g, "") 
        .replace(/\n{2,}/g, "\n"); 

    navigator.clipboard
      .writeText(plainText)
      .then(() => {
        setCopiedMessageId(id);
        setTimeout(() => setCopiedMessageId(null), 2000); 
      })
      .catch((err) => console.error("Failed to copy: ", err));
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    const username = localStorage.getItem("ollama_user");
    if (username) {
      setName(username);
      setLocalStorageIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Fetch 4 initial questions
    if (messages.length === 0) {
      const questionCount = isMobile ? 2 : 4;
      // const questionsToUse = generateType === "text" ? INITIAL_QUESTIONS : IMAGE_GENERATION_QUESTIONS;
      const questionsToUse =
        generateType === "text" ? INITIAL_QUESTIONS : INITIAL_QUESTIONS;

      setInitialQuestions(
        questionsToUse
          .sort(() => Math.random() - 0.5)
          .slice(0, questionCount)
          .map((message) => {
            return {
              id: "1",
              role: "user",
              content: message.content,
            };
          })
      );
    }
  }, [isMobile, generateType]);

  const onClickQuestion = (value: string, e: React.MouseEvent) => {
    e.preventDefault();

    handleInputChange({
      target: { value },
    } as React.ChangeEvent<HTMLTextAreaElement>);

    setTimeout(() => {
      formRef.current?.dispatchEvent(
        new Event("submit", {
          cancelable: true,
          bubbles: true,
        })
      );
    }, 1);
  };

  if (messages.length === 0) {
    return (
      <div className="w-full h-full flex justify-center items-center">
        <div className="relative flex flex-col gap-4  w-full h-full">
          <div></div>
          <div className="flex flex-col gap-2 justify-center items-center mt-10">
            <Icons.logo className="h-16 stroke-4 w-24 stroke-white fill-indigo-600" />
            <p className="text-center text-lg text-muted-foreground">
              How can I help you today?
            </p>
          </div>

          <div className="absolute bottom-0 w-full px-4 sm:max-w-3xl grid gap-2 sm:grid-cols-2 sm:gap-4 text-sm">
            {/* Only display 4 random questions */}
            {initialQuestions.length > 0 &&
              initialQuestions.map((message) => {
                const delay = Math.random() * 0.25;

                return (
                  <motion.div
                    initial={{ opacity: 0, scale: 1, y: 10, x: 0 }}
                    animate={{ opacity: 1, scale: 1, y: 0, x: 0 }}
                    exit={{ opacity: 0, scale: 1, y: 10, x: 0 }}
                    transition={{
                      opacity: { duration: 0.1, delay },
                      scale: { duration: 0.1, delay },
                      y: { type: "spring", stiffness: 100, damping: 10, delay },
                    }}
                    key={message.content}
                  >
                    <Button
                      key={message.content}
                      type="button"
                      variant="outline"
                      className="sm:text-start px-4 py-8 flex w-full justify-center sm:justify-start items-center text-sm whitespace-pre-wrap"
                      onClick={(e) => onClickQuestion(message.content, e)}
                    >
                      {message.content}
                    </Button>
                  </motion.div>
                );
              })}
          </div>
        </div>
      </div>
    );
  }
  const isUrl = (str: string) => {
    const pattern = new RegExp(
      "^(https?:\\/\\/)?" + // Protocol
        "((([a-zA-Z0-9$-_@.&+!*\\(\\),])|([a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]))@)?" + // Authentication
        "((([0-9]{1,3}\\.){3}[0-9]{1,3})|" + // IP Address
        "([a-zA-Z0-9][-a-zA-Z0-9]{0,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}))" + // Domain name
        "(\\:[0-9]{1,5})?" + // Port
        "(\\/.*)?$", // Path
      "i"
    );
    return pattern.test(str);
  };

  return (
    <div
      id="scroller"
      className="w-full overflow-y-scroll overflow-x-hidden h-full justify-end"
    >
      <div className="w-full flex flex-col overflow-x-hidden overflow-y-hidden min-h-full justify-end">
        {messages.map((message, index) => (
          <motion.div
            key={index}
            layout
            initial={{ opacity: 0, scale: 1, y: 20, x: 0 }}
            animate={{ opacity: 1, scale: 1, y: 0, x: 0 }}
            exit={{ opacity: 0, scale: 1, y: 20, x: 0 }}
            transition={{
              opacity: { duration: 0.1 },
              layout: {
                type: "spring",
                bounce: 0.3,
                duration: messages.indexOf(message) * 0.05 + 0.2,
              },
            }}
            className={cn(
              "flex flex-col gap-2 p-4 whitespace-pre-wrap",
              message.role === "user" ? "items-end" : "items-start"
            )}
          >
            <div className="flex gap-3 items-center">
              {message.role === "user" && (
                <div className="flex items-start gap-3 ">
                  <span className="bg-accent px-3 py-2 rounded-md max-w-xs sm:max-w-2xl overflow-x-auto">
                    {/* Check if the message is a URL and render image */}
                    {isUrl(message.content) ? (
                      <Image
                        src={message.content}
                        alt="Image"
                        width={300}
                        height={300}
                        className="object-contain rounded-md"
                      />
                    ) : (
                      message.content
                    )}
                  </span>
                  <div className="flex items-center">
                    <img
                      src="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/user-image.jpg" // User image
                      alt="User"
                      width={40}
                      height={40}
                      className="object-contain rounded-full"
                      loading="lazy"
                    />
                    <div className="ml-2">
                      {name && name.substring(0, 2).toUpperCase()}
                    </div>
                  </div>
                </div>
              )}

              {message.role === "assistant" && (
                <div className="flex flex-col items-start gap-2 mb-6">
                  <div className="flex items-start">
                    <img
                      src="https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/favicon.ico" // AI image
                      alt="Assistant"
                      width={40}
                      height={40}
                      className="object-contain rounded-full"
                      loading="lazy"
                    />
                    <div className="ml-2">
                      {name && name.substring(0, 2).toUpperCase()}
                    </div>
                    <span className="bg-accent px-3 py-2 rounded-md max-w-xs sm:max-w-2xl overflow-x-auto">
                      {isUrl(message?.content) ? (
                        <Image
                          src={message.content[0]}
                          alt="Image"
                          width={300}
                          height={300}
                          className="object-contain rounded-md"
                        />
                      ) : (
                        message?.content ? (
                          // <Markdown key={index} remarkPlugins={[remarkGfm]}>
                          //   {message?.content }
                          // </Markdown>
                          <ReactMarkdown key={index}
                            className="prose prose-invert max-w-none 
                                          prose-p:text-white prose-p:my-2
                                          prose-headings:text-white prose-headings:font-semibold prose-headings:my-4
                                          prose-strong:text-white prose-strong:font-semibold
                                          prose-em:text-white prose-em:italic
                                          prose-code:text-slate-300 prose-code:bg-slate-800 prose-code:px-1 prose-code:rounded
                                          prose-ul:my-2 prose-li:my-1 prose-li:marker:text-slate-400
                                          prose-blockquote:border-l-4 prose-blockquote:border-slate-400 prose-blockquote:pl-4
                                          prose-table:border-collapse prose-td:border prose-td:border-slate-600 prose-td:px-3 prose-td:py-2
                                          leading-relaxed text-sm md:text-base"
                            remarkPlugins={[remarkGfm]}
                            components={{
                              // Custom component overrides
                              a: ({ node, ...props }) => (
                                <a className="text-blue-400 hover:text-blue-300 underline" {...props} />
                              ),
                              ul: ({ node, ...props }) => (
                                <ul className="list-disc pl-5" {...props} />
                              ),
                              ol: ({ node, ...props }) => (
                                <ol className="list-decimal pl-5" {...props} />
                              ),
                              // Handle custom bullet points (•)
                              li: ({ node, children, ...props }) => {
                                if (typeof children === 'string' && children.startsWith('•')) {
                                  return (
                                    <li className="flex items-start">
                                      <span className="mr-2">•</span>
                                      <span>{children.replace('•', '').trim()}</span>
                                    </li>
                                  );
                                }
                                return <li {...props}>{children}</li>;
                              }
                            }}
                          >
                           {message?.content  || ''}
                          </ReactMarkdown>
                        ) : null

                      )}
                      {isLoading &&
                        messages?.indexOf(message) === messages?.length - 1 && (
                          <span className="animate-pulse text-white" aria-label="Typing">
                            ...asd
                          </span>
                        )}
                    </span>
                  </div>
                  <button
                    className="ml-11 relative -mt-1 group hover:text-gray-500 text-gray-300 px-2 py-[2px] rounded"
                    onClick={() => handleCopy(message.content, message.id)}
                  >
                    {copiedMessageId === message.id ? "Copied!" : <Copy />}

                    {/* Tooltip for 'Copy to clipboard' */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 translate-y-2 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300 bg-gray-700 text-white text-xs rounded-md px-2 py-1">
                      Copy
                    </div>
                  </button>{" "}
                </div>
              )}
            </div>
          </motion.div>
        ))}
        {loadingSubmit && (
          <div className="flex pl-4 pb-4 gap-2 items-center">
            <Avatar className="flex justify-start items-center">
              <AvatarImage
                src="/favicon.ico"
                alt="AI"
                width={6}
                height={6}
                className="object-contain dark:invert"
              />
            </Avatar>
            <div className="bg-accent p-3 rounded-md max-w-xs sm:max-w-2xl overflow-x-auto">
              <div className="flex gap-1">
                <span className="size-1.5 rounded-full bg-white motion-safe:animate-[bounce_1s_ease-in-out_infinite] dark:bg-slate-300"></span>
                <span className="size-1.5 rounded-full bg-white motion-safe:animate-[bounce_0.5s_ease-in-out_infinite] dark:bg-slate-300"></span>
                <span className="size-1.5 rounded-full bg-white motion-safe:animate-[bounce_1s_ease-in-out_infinite] dark:bg-slate-300"></span>
              </div>
            </div>
          </div>
        )}{" "}
      </div>
      <div id="anchor" ref={bottomRef}></div>
    </div>
  );
}
