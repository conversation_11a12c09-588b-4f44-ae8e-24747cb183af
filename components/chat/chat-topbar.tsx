"use client";

import React, { useEffect, useState } from "react";
import {
  Pop<PERSON>,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";

import { <PERSON><PERSON> } from "../ui/button";
import { CaretSortIcon, HamburgerMenuIcon, CheckIcon } from "@radix-ui/react-icons";
import { Sidebar } from "../sidebar";
import { toast } from "sonner";
import coinImage from "../../public/img/coin-png.png";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Message } from "ai";

interface ChatTopbarProps {
  setSelectedModel: React.Dispatch<React.SetStateAction<string>>;
  isLoading: boolean;
  chatId?: string;
  messages: Message[];
  model?: any;
  models: any;
  selectedModel?:string;
  generateType?: string;
  fetchedModel?: any;
}
 
interface fetchedModel {
  map(arg0: (m: any) => React.JSX.Element): import("react-i18next").ReactI18NextChild | Iterable<import("react-i18next").ReactI18NextChild>;
  id: string;
  name: string;
  description: string;
  imageinputopt : boolean;
  modelType: string;
  cost: number;
  commission: number;
  provider: { text: string; image: string };
  examples: { example: string; id: string }[];
}


export default function ChatTopbar({
  setSelectedModel,
  isLoading,
  chatId,
  messages,
  model,
  selectedModel,
  generateType,
  fetchedModel,
  models,
}: ChatTopbarProps) {
  // const [models, setModels] = React.useState<string[]>([]);

  const [currentModel, setCurrentModel] = React.useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [selectedModelId, setSelectedModelId] = React.useState(
    "66b3ca89c94560d255a7f14d"
  );
  // useEffect(() => {
  //   setCurrentModel(getSelectedModel());

  //   const env = process.env.NODE_ENV;

  //   const fetchModels = async () => {
  //     try {
  //       if (env === "production") {
  //         const fetchedModels = await fetch(process.env.NEXT_PUBLIC_OLLAMA_URL + "/api/tags");
  //         const json = await fetchedModels.json();
  //       const apiModels = json.models.map((model: any) => model.name);
  //       setModels([...apiModels]);
  //       } else {
  //         const fetchedModels = await fetch("/api/tags");
  //         const json = await fetchedModels.json();
  //       const apiModels = json.models.map((model: any) => model.name);
  //       setModels([...apiModels]);
  //       }
  //     } catch (error) {
  //       console.error("Error fetching models:", error);
  //       toast.error("Failed to fetch models. Please try again later.");
  //     }
  //   };

  //   fetchModels();
  // }, []);

  const handleModelChange = (model: string) => {
    setCurrentModel(model);
    setSelectedModel(model);
    if (typeof window !== "undefined") {
      localStorage.setItem("selectedModel", model);
    }
    setOpen(false);
  };

  const handleSelect = (id) => {
    setSelectedModel(id);
    setOpen(false); // Close the popover when selecting a model
  };


  return (
    <div className="w-full flex px-4 py-6  items-center justify-between lg:justify-center ">
      {/* <Sheet>
        <SheetTrigger>
          <HamburgerMenuIcon className="lg:hidden w-5 h-5" />
        </SheetTrigger>
        <SheetContent side="left">
          <Sidebar
            chatId={chatId || ""}
            isCollapsed={false}
            isMobile={false}
            messages={messages}
            setMessages={function (messages: Message[]): void {
              // throw new Error("Function not implemented.");
              toast.error("Function not implemented.");
            }}
          />
        </SheetContent>
      </Sheet> */}

<Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger className="w-[300px] px-4 py-3 bg-indigo-950/40 rounded-xl text-white text-left flex flex-row justify-between items-center border border-white lg:hidden">
          {selectedModel
            ? models.find((m) => m.id === selectedModel)?.name || "Select a Model"
            : "Select a Model"}
          <ChevronsUpDown size={18} className="text-white" />
        </PopoverTrigger>

        <PopoverContent className="z-50 w-full max-h-60 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-300 xl:min-w-[300px] rounded-xl bg-indigo-950 lg:hidden">
          {generateType === "image" && fetchedModel ? (
            <div
            onClick={() => handleSelect(fetchedModel?.id)}
              className="cursor-pointer flex items-center justify-between px-4 py-2 transition-colors duration-300 bg-indigo-600 hover:bg-indigo-900 text-white rounded-xl"
            >
              <div className="flex flex-col items-start">
                <p>{fetchedModel?.name}</p>
                <p className="text-gray-300 text-sm">{fetchedModel?.description}</p>
              </div>
              {selectedModel === fetchedModel.id && <Check size={18} className="text-white" />}
            </div>
          ) : models ? (
            models.map((m) => (
              <div
                key={m.id}
                onClick={() => handleSelect(m.id)}
                className="cursor-pointer flex items-center justify-between px-4 py-2 transition-colors duration-300 hover:bg-indigo-900 w-full rounded-lg"
              >
                <div className="flex flex-row items-center gap-2 justify-between w-full">
                  <p className="text-white flex flex-row items-center gap-2">
                    {m.name} {selectedModel === m.id && <Check size={18} className="text-white" />}
                  </p>
                  <div className="flex items-center text-yellow-400">
                    {m.cost === 0 ? "Free" : `${m.cost.toFixed(2)}`}
                    <img
                      src={coinImage.src}
                      alt="Coin"
                      style={{ width: "17px", height: "20px" }}
                      className="inline"
                      loading="lazy"
                    />
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="px-4 py-2 text-white">Loading models...</p>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
