"use client";

import React, { useEffect } from "react";
import { ChatProps } from "./chat";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button, buttonVariants } from "../ui/button";
import TextareaAutosize from "react-textarea-autosize";
import { motion, AnimatePresence } from "framer-motion";
import { ImageIcon, PaperPlaneIcon, StopIcon } from "@radix-ui/react-icons";
import { StopCircleIcon } from "lucide-react";
import { Mic, SendHorizonal } from "lucide-react";
// import useSpeechToText from "@/app/hooks/useSpeechRecognition";

export default function ChatBottombar({
  selectedModel,
  models,
  messages,
  input,
  handleInputChange,
  handleSubmit,
  onImageUrlChange,
  isLoading,
  loadingSubmit,
  error,
  stop,
  formRef,
  setInput,
}: ChatProps) {
  const [message, setMessage] = React.useState(input);
  const [isMobile, setIsMobile] = React.useState(false);
  const inputRef = React.useRef<HTMLTextAreaElement>(null);

  const [selectedImage, setSelectedImage] = React.useState<File | null>(null);
  const [imagePreview, setImagePreview] = React.useState<string | null>(null);

  React.useEffect(() => {
    const checkScreenWidth = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Initial check
    checkScreenWidth();

    // Event listener for screen width changes
    window.addEventListener("resize", checkScreenWidth);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener("resize", checkScreenWidth);
    };
  }, []);

  const modelImageSelect =
    models?.filter(
      (model) => model.id === selectedModel && model.imageinputopt === true
    ) || [];

  

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as unknown as React.FormEvent<HTMLFormElement>);
    }
  };

  // const { isListening, transcript, startListening, stopListening } =
  //   useSpeechToText({ continuous: true });

  // const listen = () => {
  //   isListening ? stopVoiceInput() : startListening();
  // };

  // const stopVoiceInput = () => {
  //   setInput && setInput(transcript.length ? transcript : "");
  //   stopListening();
  // };

  // const handleListenClick = () => {
  //   listen();
  // };

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        onImageUrlChange(base64String);
        setImagePreview(base64String); // Set the base64 image preview
      };
      reader.readAsDataURL(file); // Convert image to Base64
    }
  };

  const handleClearImage = () => {
    setImagePreview(null);
  };

  const submitForm = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const formData = {
      message: input,
      image: imagePreview, //base64 image url
    };
    setInput("");
    handleClearImage();
    handleSubmit(e);
  };

  return (
    <div className="p-4 pb-7 flex justify-between w-full items-center gap-2">
      <AnimatePresence initial={false}>
        <div className="w-full items-center flex relative gap-2">
          {imagePreview && (
            <div className="absolute top-[-115px] left-[16px] flex justify-start">
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-24 h-24 object-cover rounded-lg"
                  loading="lazy"
                />
                <button
                  onClick={handleClearImage}
                  className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                >
                  X
                </button>
              </div>
            </div>
          )}

          <div className="absolute left-3 z-10">
           
          <Button
  className={`shrink-0 rounded-full p-2 transition-colors ${
    modelImageSelect[0]?.imageinputopt
      ? "hover:bg-indigo-600"
      : "cursor-not-allowed"
  }`}
  variant="ghost"
  size="icon"
  disabled={!modelImageSelect[0]?.imageinputopt} 
>
  <label htmlFor="image-upload" className="cursor-pointer">
    <ImageIcon className="w-5 h-5" />
  </label>
  <input
    type="file"
    id="image-upload"
    accept="image/*"
    className="hidden"
    onChange={handleImageUpload}
    disabled={!modelImageSelect[0]?.imageinputopt} 
  />
</Button>

      
          </div>
          <form
            onSubmit={submitForm}
            className="w-full items-center flex relative gap-2"
          >
            <TextareaAutosize
              autoComplete="off"
              value={input}
              ref={inputRef}
              onKeyDown={handleKeyPress}
              onChange={handleInputChange}
              name="message"
              placeholder={"Enter your prompt here"}
              className=" max-h-24 px-14 bg-accent py-[22px] text-sm placeholder:text-muted-foreground border-2 border-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full  rounded-full flex items-center h-16 resize-none overflow-hidden dark:bg-card"
            />
            {!loadingSubmit ? (
              <div className="flex absolute right-3 items-center">
                <Button
                  className="shrink-0 rounded-full hover:bg-indigo-600"
                  variant="ghost"
                  size="icon"
                  type="submit"
                  disabled={loadingSubmit || !input.trim()}
                >
                  <SendHorizonal className="w-5 h-5 " />
                </Button>
              </div>
            ) : (
              <div className="flex absolute right-3 items-center">
                <Button
                  className="shrink-0 rounded-full"
                  variant="ghost"
                  size="icon"
                  type="submit"
                  onClick={(e) => {
                    e.preventDefault();
                    stop();
                  }}
                >
                  <StopCircleIcon className="w-5 h-5  " />
                </Button>
              </div>
            )}
          </form>
        </div>
      </AnimatePresence>
    </div>
  );
}
