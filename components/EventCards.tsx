import { cn } from "@/lib/utils";
import { MapPin } from 'lucide-react';
import { CalendarDays } from 'lucide-react';

export const EventCards = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ",
        className
      )}
    >
      {children}
    </div>
  );
};

export const EventCardItems = ({
  className,
  title,
  description,
  header,
  icon,
  location,
  eventDate
}: {
  className?: string;
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  location?:string | React.ReactNode;
  eventDate?:string | React.ReactNode;
  header?: React.ReactNode;
  icon?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        " rounded-xl group/bento hover:shadow-xl transition duration-200 shadow-input dark:shadow-none p-4 dark:bg-black dark:border-white/[0.2] bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent justify-between flex flex-col space-y-4 h-fit",
        className
      )}
    >
      {header}
      <div className="group-hover/bento:translate-x-2 transition duration-200 flex flex-col gap-2">
        {/* {icon} */}
        <div className="font-sans font-bold text-white text-lg">
          {title}
        </div>
        <div className="font-sans font-normal text-white text-sm">
          <span>Hosted By: </span>{description}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 text-sm">
            <CalendarDays size={20}/>
            <p>{eventDate}</p>
          </div>
          <div className="flex items-center gap-1 text-sm">
            <MapPin size={20}/>
            <p>{location}</p>
          </div>
          
        </div>
        
        <button className="mt-2 py-2 rounded-xl group hover:shadow-xl transition duration-200 shadow-input bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent">REGISTER</button>
      </div>
    </div>
  );
};
