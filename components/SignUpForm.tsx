"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import {
  AuthCredentialsValidator,
  TAuthCredentialsValidator,
} from "@/lib/validators/account-credentials-validator.ts";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import axios from "axios";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";

const SignUpForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TAuthCredentialsValidator>({
    resolver: zodResolver(AuthCredentialsValidator),
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);
  const router = useRouter();

  const onSubmit = async ({ email, password }: TAuthCredentialsValidator) => {
    try {
      const { data }: { data: CustomEndPointResponse } = await axios.post(
        `/api2/user/create`,
        {
          email,
          password,
        }
      );
      if (data.success === false) {
        toast.error(data.message);
        return;
      }

      toast.success(`Verification email sent to ${email}.`);
      router.push("/verify-email?to=" + email);
    } catch (e) {
      console.log("Error:", e);
      toast.error("Something went wrong. Please try again.");
    }
  };

  const BottomGradient = () => {
    return (
      <>
        <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
        <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
      </>
    );
  };

  return (
    <div className="grid gap-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1 py-2">
            <Label htmlFor="email">Email</Label>
            <Input
              {...register("email")}
              className={cn({
                "focus-visible:ring-red-500": errors?.email,
              })}
              placeholder="<EMAIL>"
            />
            {errors?.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          <div className="grid gap-1 py-2 relative">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                {...register("password")}
                type={showPassword ? "text" : "password"}
                className={cn({
                  "focus-visible:ring-red-500": errors?.password,
                })}
                placeholder="Password"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 top-1 pr-3 flex items-center text-sm leading-5"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-indigo-500" />
                ) : (
                  <Eye className="h-5 w-5 text-indigo-500" />
                )}
              </button>
            </div>
            {errors?.password && (
              <p className="text-sm text-red-500">{errors.password.message}</p>
            )}
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <input
              type="checkbox"
              name=""
              id="terms"
              onChange={(e) => setIsTermsAccepted(e.target.checked)}
            />
            <Link href={`/privacypolicy`}>
              <Label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer hover:text-blue-500"
              >
                Accept Terms and Conditions
              </Label>
            </Link>
          </div>
          <Button
            className="bg-gradient-to-br relative group/btn from-indigo-600 to-indigo-700 block w-full text-white rounded-md h-10 font-medium"
            disabled={!isTermsAccepted}
          >
            Sign up
            <BottomGradient />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SignUpForm;