"use client";

import { TQueryValidator } from "@/lib/validators/query-validator";
import BlogsListing from "./blogslistring";
import Blog from "./Blog.json";

interface BlogsReelProps {
  title: string;
  subtitle?: string;
  href?: string;
  query: TQueryValidator;
}

const FALLBACK_LIMIT = 4;

const BlogsReel = (props: BlogsReelProps) => {
  const { title, subtitle, href, query } = props;

  // const products = queryResults?.pages.flatMap((page) => page.items);

  //   let map: ( | null)[] = []
  //   if (blogs.blogs && blogs.blogs.length) {
  //     map = blogs.blogs
  //   } else if (isLoading) {
  //     map = new Array<null>(
  //       query.limit ?? FALLBACK_LIMIT
  //     ).fill(null)
  //   }
  const map = Blog.blogs;
  return (
    <section className="py-8">
      <div className="md:flex md:items-center md:justify-between mb-4">
        <div className="max-w-2xl lg:max-w-4xl lg:px-0">
          {/* {title ? (
            <h1 className='text-2xl font-bold sm:text-3xl'>
              {title}
            </h1>
          ) : null} */}
          {subtitle ? (
            <p className="mt-2 text-sm text-muted-foreground">{subtitle}</p>
          ) : null}
        </div>

        {/* {href ? (
          <Link
            href={href}
            className='hidden text-sm font-medium text-primary-muted  md:block bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent text-white rounded-full z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-3 px-6 dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none'>
            View All{' '}
            <span aria-hidden='true'>&rarr;</span>
          </Link>
        ) : null} */}
      </div>

      <div className="relative">
        <div className="mt-6 flex items-center w-full">
          <div className="w-full grid grid-cols-2 gap-x-4 gap-y-10 sm:gap-x-6 md:grid-cols-4 md:gap-y-10 lg:gap-x-8">
            {map.map((product, i) => (
              <BlogsListing key={`product-${i}`} Blogs={product} index={i} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BlogsReel;
