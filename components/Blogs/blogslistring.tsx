"use client";

// import { Product } from "@/server/payload-types";
import { useEffect, useState } from "react";

import Link from "next/link";
import { cn, formatPrice } from "@/lib/utils";
// import { PRODUCT_CATEGORIES } from "@/constants";

import { GlowingStarsBackgroundCard } from "../ui/glowing-stars";
import ImageSlider from "../ImageSlider";
import { Skeleton } from "../ui/skeleton";
import Image from "next/image";
import Blog from "./Blog.json";
// import { Data } from "payload/types";

const Data = Blog.blogs;

interface BlogsListingProps {
  Blogs: any;
  index: number;
}

const BlogsListing = ({ Blogs, index }: BlogsListingProps) => {
  const [isVisible, setIsVisible] = useState<boolean>(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, index * 75);

    return () => clearTimeout(timer);
  }, [index]);

  if (!Blogs || !isVisible) return <ProductPlaceholder />;

  //   const label = PRODUCT_CATEGORIES.find(
  //     ({ value }) => value === Blogs.category
  //   )?.label;

  //   const validUrls = Blogs.images
  //     .map(({ Blogs }) => (typeof image === "string" ? image : image.url))
  //     .filter(Boolean) as string[];

  if (isVisible && Blogs) {
    return (
      <GlowingStarsBackgroundCard>
        <Link
          className={cn(
            "invisible h-full w-full cursor-pointer rounded-t-lg group/main",
            {
              "visible animate-in fade-in-5": isVisible,
            }
          )}
          href={`/blog/${Blogs.slug}`}
        >
          <Image
            //   src="/img/bg-services3.jpg"
            src={Blogs.image}
            height={200} // Accessing the image field of each blog object
            width={300}
            alt={Blogs.title} // Using blog.title as alt text for the image
          />

          {/* <ImageSlider urls={validUrls} /> */}
          {/* <div className='flex w-full rounded-b-lg justify-between items-end bg-primary'>
            <p className=' w-auto px-2 rounded-tr-[8px] z-5 text-md bg-pink-500 text-primary-foreground'>
            {label}
            </p>
            <p className=' w-auto px-2 rounded-br-lg text-md bg-primary text-primary-foreground'>
            {product.price && formatPrice(product.price)}
          </p>
          </div>
          <h3 className='mt-1 font-medium text-md'>
            {product.name}
          </h3>
          <h3 className='mt-1 font-medium text-sm'>
            {product.description}
          </h3> */}

          <div className="flex flex-col-2 items-start gap-4 md:gap-8 justify-between mt-4">
            <div>
              <h3 className="font-medium text-sm md:text-lg">{Blogs.title}</h3>
            </div>
            {/* <div className="mt-1 items-center justify-end rounded-b-lg">
            <p className=" px-2 mb-1 z-5 text-sm rounded bg-pink-500 text-white">
              {label}
            </p>
            <p className=" px-2 rounded text-sm bg-primary text-indigo-600">
              {Blogs.price && formatPrice(Blogs.price)}
            </p>
          </div> */}
          </div>
          <h3 className="mt-1 font-medium truncate text-xs md:text-sm">
            {Blogs.paragraph}
          </h3>
        </Link>
      </GlowingStarsBackgroundCard>

      // <div className="bg-black p-4 md:w:60 rounded-lg">
      //   <Link
      //   className={cn(
      //     "invisible h-full w-full cursor-pointer rounded-t-lg group/main",
      //     {
      //       "visible animate-in fade-in-5": isVisible,
      //     }
      //   )}
      //   href={`/product/${product.id}`}
      // >
      //   <ImageSlider urls={validUrls} />
      // <div className="flex flex-col-2 items-start gap-4 justify-between mt-4">
      //   <div>
      //     <h3 className="mt-1 font-medium text-sm">{product.name}</h3>
      //     <h3 className="mt-1 font-medium tex-wrap text-overflow-hidden text-xs">{product.description}</h3>
      //   </div>
      //   <div className="block items-center justify-end rounded-b-lg">
      //     <p className=" px-2  z-5 text-sm bg-pink-500 text-primary-foreground">
      //       {label}
      //     </p>
      //     <p className=" px-2 rounded-br-lg text-sm bg-primary text-primary-foreground">
      //       {product.price && formatPrice(product.price)}
      //     </p>
      //   </div>
      // </div>
      // </Link>
      // </div>
    );
  }
};

const ProductPlaceholder = () => {
  return (
    <div className="flex flex-col w-full">
      <div className="relative bg-zinc-100 aspect-square w-full overflow-hidden rounded-xl">
        <Skeleton className="h-full w-full" />
      </div>
      <Skeleton className="mt-4 w-2/3 h-4 rounded-lg" />
      <Skeleton className="mt-2 w-full h-8 rounded-lg" />
    </div>
  );
};

export default BlogsListing;
