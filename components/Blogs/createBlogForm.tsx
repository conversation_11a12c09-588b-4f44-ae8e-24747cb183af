"use client";

import React, { useState, useEffect, useRef } from "react";
import dynamic from "next/dynamic";
import "react-quill/dist/quill.snow.css";
const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
import "react-quill/dist/quill.snow.css";
import TopHeader from "@/components/ui/topheader";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import StepNavigation from "../ui/dashboard/StepNavigation";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import ConfirmationModal from "../ui/dashboard/confirmdelete";
import { Maximize2, Minimize2 } from "lucide-react";

const BlogMultiStepForm = ({ user }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("Id");
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isPublishLoading, setisPublishLoading] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditorFullScreen, setIsEditorFullScreen] = useState(false);

  const toggleEditorFullScreen = () => {
    setIsEditorFullScreen(!isEditorFullScreen);
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  const STORAGE_KEY_FORM = "blogFormData";
  const STORAGE_KEY_STEP = "blogFormStep";

  const getStoredFormData = () => {
    if (typeof window === "undefined") return null;
    const savedData = localStorage.getItem(STORAGE_KEY_FORM);
    return savedData ? JSON.parse(savedData) : null;
  };

  const getStoredStep = () => {
    if (typeof window === "undefined") return 1;
    const savedStep = localStorage.getItem(STORAGE_KEY_STEP);
    return savedStep ? parseInt(savedStep, 10) : 1;
  };

  // Validation functions
  const validateStep1 = () => {
    if (!formData.title || !formData.content) {
      toast.error("Please fill in all fields before proceeding.");
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    if (!formData.richText) {
      toast.error("Please fill in blog content before proceeding.");
      return false;
    }
    return true;
  };
  const [isBlurred, setIsBlurred] = useState(true);
  const [error, setError] = useState("");
  const [imageUpload, setImageUpload] = useState(false);
  const fileInputRef = useRef(null);

  const [loading, setLoading] = useState(false);

  const defaultTags = ["Case Study", "Development", "Learning", "Research"];
  const tagOptions = [
    { label: "Case Study", value: "casestudy" },
    { label: "Development", value: "development" },
    { label: "Learning", value: "learning" },
    { label: "Research", value: "research" },
  ];
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    richText: "",
    images: [],
    tags: "",
    time: "",
    needsApproval: false,
    user: null,
    slug: "",
    status: "pending",
  });

  useEffect(() => {
    if (isClient) {
      const savedFormData = getStoredFormData();
      const savedStep = getStoredStep();

      if (savedFormData && !id) setFormData(savedFormData);
      if (savedStep && !id) setStep(savedStep);

      if (!id) {
        setStep(1);
        localStorage.setItem(STORAGE_KEY_STEP, "1");
      } else {
        setStep(savedStep);
      }
    }
  }, [isClient, id]);

  useEffect(() => {
    if (isClient && !id) {
      localStorage.setItem(STORAGE_KEY_FORM, JSON.stringify(formData));
      localStorage.setItem(STORAGE_KEY_STEP, step.toString());
    }
  }, [formData, step, isClient, id]);

  const totalSteps = 3;
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const payloadRichTextToHTML = (richText) => {
    if (!Array.isArray(richText)) return "";

    return richText
      .map((block) => {
        if (block?.children?.length) {
          const content = block.children
            .map((child) => child.text || "")
            .join("");
          return `<p className="break-words">${content}</p>`;
        }
        return "";
      })
      .join("");
  };

  useEffect(() => {
    if (!user) {
      router.push("/sign-in");
    }
  }, [user]);


  useEffect(() => {
    if (id) {
      const fetchBlogs = async () => {
        try {
          const response = await fetch(`/api/blogs/getBlogById/${id}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          });

          if (response.ok) {
            const Blog = await response.json();
            const blogsData = Blog.blogData;

            const richTextHTML = payloadRichTextToHTML(
              blogsData?.richText || []
            );
            const blogStatus =
              blogsData.status === "denied" ? "pending" : blogsData.status;

            setFormData({
              title: blogsData?.title || "",
              content: blogsData?.content || "",
              richText: richTextHTML,
              images: blogsData?.images || "",
              tags: blogsData?.tags || "",
              time: blogsData?.time || "",
              status: blogStatus,
              needsApproval: blogsData?.needsApproval || false, // Ensure needsApproval is set
              user: user?.id,
              slug: blogsData?.slug || "",
            });

            if (formData.images) {
              setImageUpload(true);
            }
          } else {
            console.error("Failed to fetch blog data.");
          }
        } catch (error) {
          console.error("Error fetching blogs:", error);
        }
      };

      fetchBlogs();
    }
  }, [id]);

  const parseRichText = (richTextArray) => {
    return richTextArray
      .map((block) => {
        if (block.type === "paragraph") {
          return `<p>${block.children.map((child) => child.text).join("")}</p>`;
        } else if (block.type.startsWith("heading-")) {
          const level = block.type.split("-")[1];
          return `<h${level}>${block.children.map((child) => child.text).join("")}</h${level}>`;
        } else if (block.type === "ordered-list") {
          const items = block.children
            .map(
              (child) =>
                `<li>${child.children.map((c) => c.text).join("")}</li>`
            )
            .join("");
          return `<ol>${items}</ol>`;
        } else if (block.type === "unordered-list") {
          const items = block.children
            .map(
              (child) =>
                `<li>${child.children.map((c) => c.text).join("")}</li>`
            )
            .join("");
          return `<ul>${items}</ul>`;
        }
        return "";
      })
      .join("");
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) {
      setError("No files selected.");
      return;
    }
    if (files.length > 1) {
      toast("You can only upload up to 1 images.");
      e.target.value = "";
      return;
    }

    const validFiles = Array.from(files);

    if (validFiles.length > 0) {
      setLoading(true);
      setImageUpload(false);
      const fileFormData = new FormData();
      validFiles.forEach((file) => fileFormData.append("file", file));

      try {
        const mediaRes = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/Media?depth=0`,
          {
            method: "POST",
            credentials: "include",
            body: fileFormData,
          }
        );

        const mediaData = await mediaRes.json();
        // console.log("mediaData", mediaData);
        if (mediaRes.ok) {
          const uploadedImages = Array.isArray(mediaData.doc)
            ? mediaData.doc.map(
                (item: { id: string; url: string; filename: string }) => ({
                  id: item?.id,
                  image: item?.url,
                  filename: item?.filename,
                })
              )
            : mediaData.doc?.url
              ? [
                  {
                    id: mediaData?.doc?.id,
                    image: mediaData?.doc?.url,
                    filename: mediaData?.doc?.filename,
                  },
                ]
              : [];
          // console.log("uploadedImages", uploadedImages);
          setFormData((prevData) => ({
            ...prevData,
            images: uploadedImages,
          }));
          setImageUpload(true);
          setLoading(false);

          setError("");
        } else {
          setError("Media upload failed. Please try again.");
        }
      } catch (mediaError) {
        setError("Media upload failed. Please try again.");
      }
    } else {
      setError("Please select valid image files (max 2MB each).");
    }
  };
  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        user: user.id,
      }));
    }
  }, [user]);

  const handleTagClick = (tagValue) => {
    setFormData((prevData) => ({
      ...prevData,
      tags: tagValue,
    }));
  };

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const formatTime = () => {
    const totalMinutes = parseInt(formData.time) || 0;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    let formattedTime = "";
    if (hours) {
      formattedTime += `${hours} hour ${hours > 1 ? "s" : ""}`;
    }
    if (minutes) {
      if (formattedTime) formattedTime += " and ";
      formattedTime += `${minutes} minute${minutes > 1 ? "s" : ""}`;
    }
    return formattedTime || "0 minutes";
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsBlurred(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);
  // console.log("formData ==>>", formData);

  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ list: "ordered" }],
      ["bold", "italic", "underline"],
      [
        "link",
        // "image"
      ],
    ],
  };

  const htmlToPayloadRichText = (htmlString) => {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, "text/html");

      const parseNode = (node: Node): any => {
        if (node.nodeType === Node.TEXT_NODE) {
          return { text: node.textContent || "" };
        } else if (node.nodeName === "B" || node.nodeName === "STRONG") {
          return {
            text: node.textContent || "",
            bold: true,
          };
        } else if (node.nodeName === "I" || node.nodeName === "EM") {
          return {
            text: node.textContent || "",
            italic: true,
          };
        } else if (node.nodeName === "U") {
          return {
            text: node.textContent || "",
            underline: true,
          };
        } else if (node.nodeName === "A") {
          return {
            type: "link",
            url: (node as HTMLAnchorElement).getAttribute("href") || "",
            children: Array.from(node.childNodes).map(parseNode),
          };
        } else if (node.nodeName === "P") {
          return {
            type: "paragraph",
            children: Array.from(node.childNodes).map(parseNode),
          };
        } else if (/^H[1-6]$/.test(node.nodeName)) {
          return {
            type: `heading-${node.nodeName.slice(1)}`,
            children: Array.from(node.childNodes).map(parseNode),
          };
        } else if (node.nodeName === "OL") {
          return {
            type: "ordered-list",
            children: Array.from(node.childNodes).map((child) => ({
              type: "list-item",
              children: Array.from(child.childNodes).map(parseNode),
            })),
          };
        } else if (node.nodeName === "UL") {
          return {
            type: "unordered-list",
            children: Array.from(node.childNodes).map((child) => ({
              type: "list-item",
              children: Array.from(child.childNodes).map(parseNode),
            })),
          };
        }

        return {
          text: node.textContent || "",
        };
      };

      const blocks = Array.from(doc.body.childNodes).map(parseNode);
      return blocks;
    } catch (error) {
      console.error("Error converting HTML to Payload rich text:", error);
      return [];
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    await saveBlog(
      { ...formData, needsApproval: formData.needsApproval },
      false
    ); // Save with needsApproval: false
    setIsLoading(false);
  };

  const handlePublish = async () => {
    if (
      !formData.time ||
      !formData.tags ||
      !formData.images ||
      formData.images.length === 0
    ) {
      toast.error("Please fill in all fields before proceeding.");
      return;
    }

    setisPublishLoading(true);
    await saveBlog({ ...formData, needsApproval: true }, true); // Save with needsApproval: true
    setisPublishLoading(false);
  };

  const saveBlog = async (data, publishing) => {
    if (publishing) {
      setisPublishLoading(true);
    } else {
      setIsLoading(true);
    }

    const blogImg = data.images
      .map((item) =>
        item.image && item.image.id
          ? { image: item.image.id }
          : item.id
            ? { image: item.id }
            : null
      )
      .filter(Boolean);

    const richTextPayloadFormat = htmlToPayloadRichText(data.richText);

    const blogData = {
      ...data,
      tags: data.tags,
      richText: richTextPayloadFormat,
      images: blogImg,
      time: parseInt(data.time, 10) || 0,
      user: user.id,
      status: formData.status ,
    };
    try {
      let response;
      if (id) {
        // Edit mode: Update the existing blog
        response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/blogs/${id}?depth=0`,
          {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(blogData),
          }
        );
      } else {
        // Create mode: Create a new blog
        response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/blogs?depth=0`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(blogData),
          }
        );
      }

      if (response.ok) {
        toast.success(
          id
            ? "Blog Updated Successfully"
            : data.needsApproval
              ? "Blog Created Successfully and Sent for Approval"
              : "Blog saved Successfully"
        );

        localStorage.removeItem("blogFormData");
        localStorage.removeItem("blogFormStep");
        router.push("/dashboard/blogs");

        setFormData({
          title: "",
          content: "",
          richText: "",
          images: [],
          tags: "",
          time: "",
          needsApproval: false,
          user: null,
          slug: "",
          status: "",
        });
      } else {
        const errorData = await response.json();
        console.error("Error saving blog:", errorData);
      }
    } catch (error) {
      console.error("Failed to save the blog:", error);
      alert("Failed to save the blog. Please try again.");
    } finally {
      if (publishing) {
        setisPublishLoading(false);
      } else {
        setIsLoading(false);
      }
    }
  };

  const htmlWithStyles = `<style>
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.17rem; }
  h4 { font-size: 1rem; }
  h5 { font-size: 0.83rem; }
  h6 { font-size: 0.67rem; }
</style>${formData.richText}`;

  const confirmDelete = () => {
    localStorage.removeItem("blogFormData");
    localStorage.removeItem("blogFormStep");
    router.push("/dashboard");
  };

  const closeModal = () => setIsModalOpen(false);

  if (!isClient) return null;
  return (
    <>
      <TopHeader user={user} title="Create Blog" buttonName="Blogs" />
      <div className="bg-dash-foreground max-4 md:m-6 rounded-lg py-2">
        <StepNavigation step={step} totalSteps={totalSteps} />

        {/* Sticky navigation buttons for all steps */}
        <div className="sticky top-0 z-10 bg-dash-foreground border-b border-indigo-400/30 py-2 px-4 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsModalOpen(true)}
              className="py-2 px-3 rounded text-dash text-sm font-medium focus:outline-none bg-white hover:bg-slate-200"
            >
              Cancel
            </button>
            {step > 1 && (
              <button
                onClick={prevStep}
                className="py-2 px-3 rounded text-dash text-sm font-medium focus:outline-none bg-white hover:bg-slate-200"
              >
                Previous
              </button>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {step < totalSteps ? (
              <button
                onClick={() => {
                  if (step === 2 && validateStep2()) {
                    nextStep();
                  } else if (
                    step === 1 &&
                    (!formData.title || !formData.content)
                  ) {
                    toast.error("Please fill in all fields before proceeding.");
                  } else {
                    nextStep();
                  }
                }}
                className="py-2 px-3 rounded text-white text-sm font-medium focus:outline-none bg-indigo-600 hover:bg-indigo-700"
              >
                Save & Next
              </button>
            ) : (
              <div className="flex gap-2">
                {/* Always show "Update" or "Save" button */}
                <button
                  onClick={handleSave}
                  className="py-2 px-3 rounded text-white text-sm font-medium focus:outline-none bg-indigo-600 hover:bg-indigo-700"
                  disabled={isLoading || isPublishLoading}
                >
                  {isLoading
                    ? id
                      ? "Updating..."
                      : "Saving..."
                    : id
                      ? "Update"
                      : "Save"}
                </button>
                {/* Conditionally show "Publish" button */}
                {!formData.needsApproval && (
                  <button
                    onClick={handlePublish}
                    className="py-2 px-3 rounded text-white text-sm font-medium focus:outline-none bg-green-600 hover:bg-green-700"
                    disabled={isPublishLoading || isLoading}
                  >
                    {isPublishLoading ? "Publishing..." : "Publish"}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="md:flex mt-6">
          {/* Left Panel */}
          <div className="md:w-1/2 px-4 md:px-6">
            {step === 1 && (
              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">
                    Blog Title <span className="text-red-500">*</span> :
                  </label>
                  <Input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    placeholder="Enter blog title"
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg bg-dash text-white focus:outline-none"
                  />
                  {error && (
                    <p className="mt-2 text-sm text-red-400">{error}</p>
                  )}
                  <p className="text-sm text-gray-400 mt-1 ms-1">
                    Please enter the blog title.
                  </p>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">
                    Blog Description <span className="text-red-500">*</span> :
                  </label>
                  <Textarea
                    value={formData.content}
                    onChange={(e) =>
                      handleInputChange("content", e.target.value)
                    }
                    placeholder="Enter description"
                    // className="w-full p-3 border border-gray-300 rounded-lg overflow-y-auto bg-dash text-white focus:outline-none no-scrollbar"
                    className=" text-white bg-dash border-indigo-500 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    style={{ overflow: "hidden", resize: "none" }}
                    rows={5}
                    cols={50}
                  />
                  <p className="text-sm text-gray-400 mt-1 ms-1">
                    Please enter the blog description.
                  </p>
                </div>
              </div>
            )}

            {step === 2 && (
              <div>
                {isEditorFullScreen ? (
                  <div className="fixed inset-0 z-50 bg-slate-800 p-4 flex flex-col overflow-hidden">
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-white">
                        Blog Content <span className="text-red-500">*</span> :
                      </label>
                      <button
                        type="button"
                        onClick={toggleEditorFullScreen}
                        className="p-2 rounded-md hover:bg-slate-700 transition-colors text-white"
                        aria-label="Exit full screen"
                        title="Exit full screen"
                      >
                        <Minimize2 size={18} />
                      </button>
                    </div>

                    <ReactQuill
                      theme="snow"
                      value={formData.richText}
                      onChange={(value) => handleInputChange("richText", value)}
                      placeholder="Enter blog content."
                      className="text-white rounded-lg shadow-md flex-grow"
                      style={{
                        height: "calc(100vh - 200px)",
                        marginBottom: "40px",
                      }}
                      modules={modules}
                    />
                  </div>
                ) : (
                  <div className="mb-4 relative">
                    <div className="sticky top-0 z-10 bg-dash-foreground border-b border-indigo-400/30 flex justify-between items-center">
                      <label className="block text-sm font-medium">
                        Blog Content <span className="text-red-500">*</span> :
                      </label>
                      <button
                        type="button"
                        onClick={toggleEditorFullScreen}
                        className="p-2 rounded-md hover:bg-slate-700 transition-colors"
                        aria-label="Enter full screen"
                        title="Enter full screen"
                      >
                        <Maximize2 size={18} />
                      </button>
                    </div>

                    <ReactQuill
                      theme="snow"
                      value={formData.richText}
                      onChange={(value) => handleInputChange("richText", value)}
                      placeholder="Enter blog content."
                      className="text-white rounded-lg shadow-md"
                      style={{
                        minHeight: "300px",
                        border: "1px solid",
                      }}
                      modules={modules}
                    />

                    <p className="text-sm text-gray-400 mt-1 ms-1">
                      Please write the blog here.
                    </p>
                  </div>
                )}
              </div>
            )}

            {step === 3 && (
              <div>
                <div className="">
                  <div className="mb-5">
                    <label
                      htmlFor="image"
                      className="block text-sm font-medium mb-1"
                    >
                      Upload Blog Image <span className="text-red-500">*</span>
                    </label>
                    <Input
                      type="text"
                      className="cursor-pointer w-full mx-auto border p-2 text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
                      // className="w-full mx-auto border text-white border-gray-300 rounded-lg bg-dash focus:outline-none focus:ring-2 focus:ring-purple-400"
                      value={
                        formData?.images[0]
                          ? formData?.images[0]?.filename
                            ? formData?.images[0]?.filename
                            : formData?.images[0]?.image?.filename
                          : "Click here to upload a image"
                      }
                      readOnly
                      onClick={() => fileInputRef.current?.click()} // Clicking will trigger file input
                    />
                    <Input
                      type="file"
                      id="image"
                      accept="image/*"
                      ref={fileInputRef}
                      multiple
                      onChange={handleFileChange}
                      className="hidden w-full border border-gray-300 rounded-lg bg-dash text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                    {error && (
                      <p className="mt-2 text-sm text-red-400">{error}</p>
                    )}
                    <div className="flex flex-col-reverse md:flex-row items-start md:items-center mt-1">
                      <p className="text-sm text-gray-400 ms-1">
                        Please upload thumbnail image for the blog.
                      </p>
                      <div className="flex items-center space-x-2 ms-2">
                        {loading && (
                          <span className="loader border-2 border-t-indigo-500 border-gray-300 rounded-full w-5 h-5 animate-spin"></span>
                        )}
                        {imageUpload && formData.images.length > 0 && (
                          <span className="text-green-500 font-medium">
                            ✅ Image uploaded!
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <label
                  htmlFor=""
                  className="block text-sm font-medium mt-10 mb-1"
                >
                  Choose a tag <span className="text-red-500">*</span>
                </label>
                <div className="flex flex-wrap gap-3">
                  {tagOptions.map((option, index) => (
                    <div
                      key={index}
                      className={`cursor-pointer px-4 py-2 rounded-full ${
                        formData.tags === option.value
                          ? "bg-indigo-900 text-white"
                          : "bg-indigo-700 text-white"
                      }`}
                      onClick={() => handleTagClick(option.value)}
                    >
                      {option.label}
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-400 mt-1 ms-1">
                  Please select a tag that is best suitable for the blog for
                  better user understanding.
                </p>

                <div className="mt-10 mb-6">
                  <label className="block text-sm font-medium mb-1">
                    Reading Time (in minutes){" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="number"
                    min="1"
                    value={formData.time}
                    onChange={(e) => handleInputChange("time", e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "-" || e.key === "e") e.preventDefault();
                    }}
                    className="w-full border border-gray-300 rounded-lg bg-dash text-white focus:outline-none"
                    placeholder="Enter total minutes"
                  />

                  <p className="text-sm text-gray-400 mt-1 ms-1">
                    Please mention reading time for the blog.
                  </p>
                </div>
              </div>
            )}
          </div>
          {/* Right Panel */}
          <div
            className={`md:w-1/2 px-4 max-sm:py-4 break-words whitespace-pre-wrap md:p-6 border-l md:border-indigo-400 transition-all duration-1000 ${
              isBlurred ? "shadow-2xl blur-md" : "shadow-2xl blur-none"
            }`}
          >
            <div className="overflow-hidden max-w-2xl w-full">
              <div className="bg-dash-foreground">
                <div className="flex justify-between">
                  <p className="text-sm">
                    Reading Time:{" "}
                    <span className="text-white">{formatTime()}</span>
                  </p>
                  <p className="text-sm font-medium text-white">
                    Tags:{" "}
                    <span className="text-white">
                      {formData.tags ? formData.tags : "No tags"}
                    </span>
                  </p>
                </div>
                <h2 className="text-3xl font-extrabold text-white mt-2">
                  {formData.title || "Blog Title"}
                </h2>
                <p className="font-semibold text-white mt-2 break-words whitespace-pre-wrap w-full ">
                  {formData.content || "Blog Content"}
                </p>
              </div>

              <div className="w-full overflow-hidden">
                <div className="mb-6">
                  {formData.images.length > 0 ? (
                    <div className="mt-2">
                      <p className="text-sm text-gray-200">Image Previews:</p>
                      <div className="flex flex-wrap gap-3 mt-2">
                        {formData.images.map((image, index) => (
                          <img
                            key={index}
                            src={
                              image?.image?.url
                                ? image?.image?.url
                                : image?.image
                            }
                            alt={"Blog image"}
                            className="w-full  object-cover rounded-lg"
                          />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <img
                      src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/Group%2048095914.webp`}
                      alt="Placeholder"
                      className="h-96 mx-auto mt-4 rounded-lg"
                    />
                  )}
                </div>
              </div>
              <div className="p-4">
                <p className="flex justify-between items-center mb-4">
                  <p className="text-md font-semibold">Content:</p>

                  <p className="text-md font-semibold">
                    Status: {formData.status}
                  </p>
                </p>
                <div
                  className="text-white break-words whitespace-pre-wrap w-full overflow-auto"
                  dangerouslySetInnerHTML={{
                    __html: htmlWithStyles || "No content available yet.",
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <ConfirmationModal
        component="blogForm"
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Cancel"
        message={`Are you sure want to cancel?`}
      />
    </>
  );
};

export default BlogMultiStepForm;
