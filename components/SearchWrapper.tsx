"use client";
import { globalSearch, SearchResult } from "@/app/utils/search"; // SearchResult interface import karein
import React, { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "sonner";
// Next.js App Router ke liye client-side navigation behtar karne ke liye:
// import Link from 'next/link'; // Agar Link component use karna hai

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number) {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn(...args), delay);
  };
}

const Autocomplete = () => {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const debouncedSearch = useCallback(
    debounce(async (q: string) => {
      if (!q.trim() || q.trim().length < 2) {
        setResults([]);
        setIsLoading(false); // isLoading ko false karein
        setIsDropdownOpen(false);
        return;
      }
      setIsLoading(true);
      setIsDropdownOpen(true);
      try {
        const data = await globalSearch(q);
        setResults(data);
      } catch (err: any) {
        toast.error(err.message || "Search failed");
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    // query change hone par debouncedSearch call karein, lekin initial empty query ke liye nahi.
    if (query.trim().length > 0 || (query.trim().length === 0 && results.length > 0) ) {
        debouncedSearch(query);
    } else {
        // Query khali hai aur results bhi khali hain ya nahi the, toh sab clear karein
        setResults([]);
        setIsLoading(false);
        setIsDropdownOpen(false);
    }
  }, [query, debouncedSearch]); // debouncedSearch ko dependency mein rakhein

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;
    if (results.length > 0 && results[0]?.url) {
       window.location.href = results[0].url;
    } else if (!isLoading) {
      toast.error(`No results found for "${query}"`);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    // Agar input khali ho jaye toh dropdown band karein
    // if (!newQuery.trim()) { // Yeh logic ab useEffect mein hai query change par
    //     setIsDropdownOpen(false);
    //     setResults([]);
    // }
  };

  // Naya function query clear karne ke liye
  const handleClearQuery = () => {
    setQuery("");
    setResults([]);
    setIsDropdownOpen(false);
    inputRef.current?.focus(); // Optional: input field par wapas focus karein
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current && !inputRef.current.contains(event.target as Node) &&
        dropdownRef.current && !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') setIsDropdownOpen(false);
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);


  return (
    <form onSubmit={handleSubmit} className="relative w-full flex">
      {/* Input field aur clear button ke liye wrapper */}
      <div className="relative flex-grow">
        <input
          ref={inputRef}
          type="text"
          placeholder="Search ai-apps, blogs, bounties, products..."
          value={query}
          onChange={handleInputChange}
          onFocus={() => {
              // Dropdown tabhi kholen jab query valid ho aur results hon ya loading ho
              if(query.trim().length >= 2) {
                setIsDropdownOpen(true);
                // Agar results pehle se nahi hain aur query valid hai, toh search trigger karein
                if (results.length === 0 && !isLoading) {
                    debouncedSearch(query);
                }
              }
          }}
          // Clear button ke liye padding right add karein agar query hai
          className={`text-white bg-transparent w-full focus:outline-none border-2 border-indigo-600 px-4 py-2 rounded-l ${query.trim() && !isLoading ? 'pr-10' : 'pr-4'}`}
          aria-autocomplete="list"
          aria-expanded={isDropdownOpen}
          aria-controls="search-results-dropdown"
        />
        {/* Clear (X) button - sirf tab dikhega jab query ho aur loading na ho rahi ho */}
        {query.trim() && !isLoading && (
          <button
            type="button" // Form submit hone se rokne ke liye
            onClick={handleClearQuery}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-indigo-400 hover:text-indigo-300 focus:outline-none z-10"
            aria-label="Clear search query"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="3" // Thoda mota X
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Search Button - Ab yahan loader nahi hai */}
      <button
        type="submit"
        className="bg-indigo-600 text-white px-4 py-2 rounded-r hover:bg-indigo-700 transition-colors flex items-center justify-center"
        style={{ minWidth: '80px' }}
        disabled={isLoading} // Submit button abhi bhi loading ke time disable rahega
      >
        Search
      </button>

      {/* Dropdown Results (Pehle jaisa hi, type badge updated) */}
      {isDropdownOpen && query.trim().length >=2 && (
        <div
          id="search-results-dropdown"
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 bg-gray-900 dark:bg-gray-900 border border-indigo-600 dark:border-gray-700 rounded-lg shadow-xl z-50 mt-1"
        >
          <div className="max-h-[calc(70vh-60px)] overflow-y-auto no-scrollbar">
            {isLoading ? (
              <div className="p-4 text-center text-gray-400 dark:text-gray-300 text-lg">Searching...</div>
            ) : results.length > 0 ? (
              results.map((item) => (
                <a
                  key={`${item.type}-${item.id}-${item.slug}`}
                  href={item.url}
                  className="group block p-3 hover:bg-slate-800 dark:hover:bg-gray-800 cursor-pointer border-b-[0.5px] border-indigo-700 dark:border-gray-800 last:border-b-0 transition-colors duration-150"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  <div className="flex items-center gap-3">
                    {item.image ? ( <img src={item.image} alt={item.title || 'Result image'} className="w-12 h-12 object-cover rounded-md border border-gray-700 flex-shrink-0" onError={(e) => (e.currentTarget.style.display = 'none')} loading="lazy" /> ) : ( <div className="w-12 h-12 bg-gray-700 dark:bg-gray-600 rounded-md flex items-center justify-center text-gray-400 flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg></div> )}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base font-semibold text-white dark:text-gray-100 truncate group-hover:text-indigo-500 dark:group-hover:text-indigo-400 transition-colors">
                        {item.title}
                      </h3>
                      {item.description && ( <p className="text-xs text-gray-400 dark:text-gray-400 mt-1 line-clamp-2">{item.description}</p> )}
                    </div>
                    <div className="flex items-center gap-2 ml-auto flex-shrink-0">
                      {item.uiDisplayType && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300 capitalize">
                          {item.uiDisplayType}
                        </span>
                      )}
                      <svg className="w-4 h-4 text-gray-500 group-hover:text-indigo-500 dark:group-hover:text-indigo-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" /></svg>
                    </div>
                  </div>
                </a>
              ))
            ) : query.trim() && !isLoading ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-300 text-lg">
                No results found for "{query}"
              </div>
            ) : null }
          </div>
        </div>
      )}
    </form>
  );
};

export default Autocomplete;