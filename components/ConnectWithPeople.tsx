

"use client";

import { Award, Box, ExternalLink, GraduationCap, Lock, MessagesSquare, Newspaper, Search, Settings, Sparkles, Trophy } from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import Link from "next/link";

export function GlowingEffectDemo() {
  return (
    <div className="pb-12 md:pb-24 w-11/12 mx-auto">
    <h2 className="text-3xl md:text-4xl mx-auto text-white font-medium mb-10">
         Connect With Community
       </h2>
    <ul className="grid grid-cols-1 grid-rows-none gap-1 sm:gap-4 md:grid-cols-12 max-sm:max-h-fit md:grid-rows-3 lg:gap-4 xl:max-h-[34rem] xl:grid-rows-2 justify-self-center">
      <GridItem
        area="md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]"
        icon={<Award className="h-10 w-10 text-white dark:text-neutral-400" />}
        title="Bounties"
        description="Explore rewarding opportunities and incentives through our diverse array of bounties."
        href= "/bounties"
      />

      <GridItem
        area="md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]"
        icon={<Newspaper className="h-10 w-10 text-white dark:text-neutral-400" />}
        title="Blog"
        description="Exploring blogs with insights, tips, and inspiration."
        href="/blog"
      />

      <GridItem
        area="md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]"
        icon={<GraduationCap className="h-10 w-10 text-white dark:text-neutral-400" />}
        title="Academy"
        description="Empowering learners with comprehensive courses and expert guidance to unlock their full potential. "
        href="/academy"
      />

      <GridItem
        area="md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]"
        icon={<Trophy className="h-10 w-10 text-white dark:text-neutral-400" />}
        title="Events"
        description="Join us for virtual and in-person events, hack nights, workshops and more."
        href="https://discord.gg/kPkYbzMvN3"
      />

      <GridItem
        area="md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]"
        icon={<MessagesSquare className="h-10 w-10 text-white dark:text-neutral-400" />}
        title="Discussion"
        description="Engage, share insights, and connect with a vibrant community of like-minded individuals on our discussion platform."
        href="https://discord.gg/kPkYbzMvN3"
      />
    </ul>
    </div>
  );
}

interface GridItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
  href: string;
}

const GridItem = ({ area, icon, title, description, href }: GridItemProps) => {
  return (
    <Link className={`min-h-[12rem] list-none ${area}`} href={href}>
      <div className="relative h-full rounded-xl border-4  md:p-2 transform hover:scale-105 duration-200">
        
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border p-4 md:p-6 bg-indigo-700">
        <ExternalLink className="w-5 h-5 absolute top-4 right-4"/>
          <div className="relative flex flex-1 flex-col md:justify-between gap-3">
            <div className="w-fit rounded-lg ">
              {icon}
            </div>
            <div className="space-y-3">
              <h3 className="pt-0.5 text-xl/[1.375rem] font-semibold font-sans -tracking-4 md:text-2xl/[1.875rem] text-balance bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent">
                {title}
              </h3>
              <h2
                className="[&_b]:md:font-semibold [&_strong]:md:font-semibold font-sans text-sm/[1.125rem] 
              md:text-base/[1.375rem]  text-white dark:text-neutral-400"
              >
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};
