import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
  render,
} from '@react-email/components';
import * as React from 'react';

interface EmailTemplateProps {
  actionLabel: string;
  buttonText: string;
  href: string;
}

export const EmailTemplate = ({
  actionLabel,
  buttonText,
  href,
}: EmailTemplateProps) => {
  return (
    <Html>
      <Head />
      <Preview>Welcome to RentPrompts - Verify Your Account</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Logo Section */}
          <Section style={logoContainer}>
            <Img
              src="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/image.png"
              width="180"
              height="auto"
              alt="RentPrompts"
              style={logo}
               loading="lazy"
            />
          </Section>

          {/* Greeting */}
          <Text style={heading}>Hi There,</Text>
          <Text style={paragraph}>
            Welcome to <strong>RentPrompts</strong>! Thank you for joining our community.
            We’re excited to have you with us.
          </Text>

          {/* Body Message */}
          <Text style={paragraph}>
            To get the most out of your experience, please verify your account by clicking the
            button below:
          </Text>

          {/* Action Button */}
          <Section style={btnContainer}>
            <Button style={button} href={href}>
              {buttonText}
            </Button>
          </Section>

          {/* Benefits Section */}
          <Text style={paragraph}>
            By verifying your account, you&apos;ll unlock access to our **Generative AI Marketplace**, where you can:
          </Text>
          <Text style={paragraph}>
          <ul style={listStyle}>
            <li>Connect with other community members.</li>
            <li>Explore and share innovative AI prompts.</li>
            <li>Access exclusive features and resources tailored just for you.</li>
          </ul>
          </Text>
          

          {/* Support Section */}
          <Text style={paragraph}>
            If you have any questions or need assistance, feel free to reach out to our support
            team.
          </Text>

          {/* Signature */}
          <Text style={paragraph}>
            Best regards,
            <br />
            The RentPrompts Team
          </Text>

          <Hr style={hr} />

          {/* Footer */}
          <Text style={footer}>
            If you did not request this email, you can safely ignore it.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export const PrimaryActionEmailHtml = (props: EmailTemplateProps) =>
  render(<EmailTemplate {...props} />, { pretty: true });

/* Styling */
const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
  padding: '0px',
};

const container = {
  margin: '0 auto',
  backgroundColor: '#ffffff',
  padding: '35px',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
  maxWidth: '650px',
  textAlign: 'left' as const,
};

const logoContainer = {
  textAlign: 'center' as const,
  marginBottom: '30px',
};

const logo = {
  margin: '0 auto',
  display: 'block',
};

const heading = {
  fontSize: '20px',
  marginBottom: '16px',
  color: '#555',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '26px',
  marginBottom: '20px',
  color: '#555',
};

const btnContainer = {
  textAlign: 'center' as const,
  marginBottom: '40px',
};

const button = {
  padding: '15px 30px',
  backgroundColor: '#2563eb',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  fontWeight: 'bold' as const,
};

const listStyle = {
  paddingLeft: '20px',
  marginBottom: '20px',
};

const hr = {
  border: '1px solid #eaeaea',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  textAlign: 'center' as const,
  marginTop: '40px',
};
