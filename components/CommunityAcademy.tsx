"use client";

import {
  Network,
  BrainCog,
  BrainCircuit,
  DatabaseBackup,
  Bot,
  BarChartBig,
} from "lucide-react";
import Link from "next/link";

const cardsData = [
  {
    icon: <Network size={60} />,
    content: "Basics of Deep Nueral Networking",
    description:
      "Learn foundational concepts, architectures, and techniques for building and training deep neural networks.",
  },
  {
    icon: <BrainCog size={60} />,
    content: "Basics of Prompt Engineering",
    description:
      "Explore the techniques and principles for crafting effective prompts to guide AI models in generating desired responses.",
  },
  {
    icon: <BrainCircuit size={60} />,
    content: "Advance Prompt Engineering",
    description:
      "Learn cutting-edge techniques to craft precise and effective prompts, optimizing the performance of generative AI models.",
  },
  {
    icon: <DatabaseBackup size={60} />,
    content: "Retrieval Augmented Generation",
    description:
      "Learn how to enhance generative models by integrating powerful retrieval mechanisms, improving accuracy and relevance in AI outputs.",
  },

  {
    icon: <Bot size={60} />,
    content: "AI Assistants / Agents",
    description:
      "Discover how AI Assistants/Agents streamline tasks and enhance productivity through intelligent, natural language interactions.",
  },
  {
    icon: <BarChartBig size={60} />,
    content: "Agent graph",
    description:
      "Explore the interconnected web of AI agents and their dynamic interactions in the 'Agent Graph' video.",
  },
];

const CommunityAcademy = () => {
  return (
    <>
      {/* --------------------Academy Section------------------------ */}
      <div className="w-11/12 mx-auto pb-12 md:pb-24">
        <div className="flex items-center justify-between mb-10">
          <div className="text-white text-3xl md:text-4xl font-medium">
            Academy
          </div>
          <Link href={"/academy"}>
            <button className="bg-gradient-to-r from-amber-500 to-pink-500 text-white font-semibold py-2 px-4 rounded-full text-sm md:text-md">
              VIEW MORE →
            </button>
          </Link>
        </div>

        <div className="grid gap-3 md:gap-6 lg:grid-cols-3 md:grid-cols-2 grid-cols-1">
          {cardsData.map((card, index) => (
            <div
              key={index}
              className="relative flex flex-col justify-center overflow-hidden rounded-xl pt-2"
            >
              <div className="group flex relative cursor-pointer overflow-hidden bg-indigo-600 px-6 md:px-3 pt-6 pb-8 h-72 md:h-80 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl sm:mx-auto sm:max-w-sm sm:rounded-xl sm:px-10 justify-center my-auto">
                <span className="absolute top-6 z-0 h-20 w-20 rounded-full bg-indigo-950 transition-all duration-300 group-hover:scale-[10]"></span>
                <div className="flex flex-col items-center relative z-10 max-w-md">
                  <div className="grid h-20 w-20 place-items-center p-2 rounded-full bg-indigo-950 transition-all duration-300 group-hover:bg-indigo-600">
                    <div className="">{card.icon}</div>
                  </div>
                  <div className="mt-2 font-bold text-center text-white text-xl font-dmserif">
                    {card.content}
                  </div>
                  <div className="space-y-6 pt-2 text-center leading-7 text-white/90 transition-all duration-300 group-hover:text-gray-300">
                    <p className="h-fit text-sm lg:text-md">
                      {card.description}
                    </p>
                  </div>
                  <div className="pt-5 absolute bottom-0 text-base font-semibold">
                    <Link
                      href="/academy"
                      className="text-indigo-950 transition-all duration-300 group-hover:text-white"
                    >
                      See more &rarr;
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default CommunityAcademy;
