"use client";

import { useState } from 'react';
import { useBalanceUpdater } from '@/hooks/useBalanceUpdater';
import { useCoinBalanceContext } from '@/components/coin-balance-initializer';

/**
 * Example component showing how to use manual balance updates
 * यह example है कि कैसे different scenarios में balance update करें
 */
export default function BalanceUpdateExample() {
  const { coinBalance, isLoading } = useCoinBalanceContext();
  const {
    updateBalanceAfterPurchase,
    updateBalanceAfterRecharge,
    refreshBalanceFromServer,
  } = useBalanceUpdater();

  const [amount, setAmount] = useState(10);

  // Example: Purchase simulation
  const simulatePurchase = () => {
    const purchaseAmount = amount;
    const newBalance = Math.max(0, coinBalance - purchaseAmount);
    updateBalanceAfterPurchase(newBalance, purchaseAmount);
  };

  // Example: Recharge simulation
  const simulateRecharge = () => {
    const rechargeAmount = amount;
    const newBalance = coinBalance + rechargeAmount;
    updateBalanceAfterRecharge(newBalance, rechargeAmount);
  };

  // Example: Refresh from server
  const handleRefreshFromServer = () => {
    refreshBalanceFromServer();
  };

  return (
    <div className="p-6 bg-gray-800 rounded-lg text-white">
      <h3 className="text-xl font-bold mb-4">Balance Update Examples</h3>
      
      <div className="mb-4">
        <p className="text-lg">Current Balance: <span className="text-yellow-400">{coinBalance}</span></p>
        {isLoading && <p className="text-blue-400">Updating...</p>}
      </div>

      <div className="mb-4">
        <label className="block mb-2">Amount:</label>
        <input
          type="number"
          value={amount}
          onChange={(e) => setAmount(Number(e.target.value))}
          className="px-3 py-2 bg-gray-700 rounded text-white"
          min="1"
        />
      </div>

      <div className="space-y-2">
        <button
          onClick={simulatePurchase}
          className="block w-full px-4 py-2 bg-red-600 hover:bg-red-700 rounded"
          disabled={isLoading}
        >
          Simulate Purchase (-{amount})
        </button>
        
        <button
          onClick={simulateRecharge}
          className="block w-full px-4 py-2 bg-green-600 hover:bg-green-700 rounded"
          disabled={isLoading}
        >
          Simulate Recharge (+{amount})
        </button>
        
        <button
          onClick={handleRefreshFromServer}
          className="block w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
          disabled={isLoading}
        >
          Refresh from Server
        </button>
      </div>

      <div className="mt-4 p-3 bg-gray-700 rounded text-sm">
        <p className="font-semibold mb-2">Usage Instructions:</p>
        <ul className="list-disc list-inside space-y-1 text-gray-300">
          <li>Purchase: Balance decreases immediately</li>
          <li>Recharge: Balance increases immediately</li>
          <li>Server Refresh: Fetches latest balance from API</li>
          <li>No automatic polling - updates only on user actions</li>
        </ul>
      </div>
    </div>
  );
}
