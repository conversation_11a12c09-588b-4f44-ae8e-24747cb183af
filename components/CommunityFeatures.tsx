"use client";
import { HoverEffect } from "@/components/ui/card-hover-effect";

export const projects = [
  {
    title: "Stay ahead of the curve",
    description: "Discuss the latest trends and breakthroughs in generative AI",
    content:
      "We provide the strategic insights and tools needed to turn your concepts into revenue-generating opportunities. From market analysis to business planning, we help you navigate the path from idea to income, ensuring your vision becomes a successful and sustainable enterprise.",

    // link: "https://stripe.com",
  },
  {
    title: "Colloborate & create",
    description:
      "Share knowledge and expertise with fellow Gen AI Engineers/builders",
    content:
      "Harness the power of collective intelligence on our platform, where creating and collaborating with fellow Gen AI Engineers drives progress. Share your knowledge and expertise, exchange innovative ideas, and work together on cutting-edge generative AI projects. Our community-driven environment fosters creativity, enhances skills, and drives innovation.",
    //  link: "https://netflix.com",
  },
  {
    title: "Monetize your ideas",
    description:
      "Launch your original ideas into the market successfully with our expert insights.",
    content:
      "Unlock the future with our platform, your ultimate resource for generative AI.Our platform empowers you to boost efficiency, ignite creativity, and lead in innovation. Plus, members of rentprompts will enjoy exclusive benefits such as access to all models, priority for bounties, sponsorship opportunities, and free credits every week.",
    // link: "https://google.com",
  },
  {
    title: "No Commission on AI Bounties",
    description:
      "Keep every Joule you earn on AI bounties; RentPrompts never takes a cut. Start maximizing your rewards today.",
    content:
      "Unlock the future with our platform, your ultimate resource for generative AI.Our platform empowers you to boost efficiency, ignite creativity, and lead in innovation. Plus, members of rentprompts will enjoy exclusive benefits such as access to all models, priority for bounties, sponsorship opportunities, and free credits every week.",
    // link: "https://google.com",
  },
  {
    title: "Unlimited Proposals",
    description:
      "Pitch your AI solutions without limits or credit worries. Your next big project is just one click away.",
    content:
      "Unlock the future with our platform, your ultimate resource for generative AI.Our platform empowers you to boost efficiency, ignite creativity, and lead in innovation. Plus, members of rentprompts will enjoy exclusive benefits such as access to all models, priority for bounties, sponsorship opportunities, and free credits every week.",
    // link: "https://google.com",
  },
  {
    title: "Learn and Earn",
    description:
      "Master prompt engineering with zero-cost courses and monetize your skills instantly. Learn, build, and earn with us.",
    content:
      "Unlock the future with our platform, your ultimate resource for generative AI.Our platform empowers you to boost efficiency, ignite creativity, and lead in innovation. Plus, members of rentprompts will enjoy exclusive benefits such as access to all models, priority for bounties, sponsorship opportunities, and free credits every week.",
    // link: "https://google.com",
  },
];

const CommunityHeroSec = () => {
  return (
    <div className="w-11/12 mx-auto pb-12 md:pb-24">
      <div className="text-xl sm:text-2xl md:text-4xl text-white font-medium mb-8">
        Rentprompts members are always first in line
      </div>
      <div className="mx-auto z-10">
        <HoverEffect items={projects} />
      </div>
    </div>
  );
};

export default CommunityHeroSec;
