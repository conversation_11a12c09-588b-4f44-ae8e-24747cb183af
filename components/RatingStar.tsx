"use client";
import { Star } from "lucide-react";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { User } from "../server/payload-types";

interface StarRatingProps {
  rappId?: string; // ID for rapps
  productId?: string; // ID for products
  user?: User; // The logged-in user
}

const StarRating = ({ rappId, productId, user, }: StarRatingProps) => {
  const [rating, setRating] = useState(0); // Current rating for the entity (rapp/product)
  const [hover, setHover] = useState(0); // Hover state for stars

  // Fetch the user's rating based on rapp or product
  useEffect(() => {
    const fetchUserRating = async () => {
      if (!user?.id || (!rappId && !productId)) return;

      const targetId = rappId ? `rapps/rating/${rappId}` : `products/rating/${productId}`; // Dynamic endpoint

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/${targetId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
          }
        );

        const data = await response.json();
        if (response.ok) {
          setRating(data.rating); // Set the current rating fetched from API
        } else {
          toast.error("Failed to load user rating.");
        }
      } catch (error) {
        console.error("Error fetching user rating:", error);
      }
    };

    fetchUserRating();
  }, [rappId, productId, user]);

  // Handle rating submission based on rapp or product
  const handleRatingClick = async (rate: number) => {
    if (!user?.id || (!rappId && !productId)) {
      toast.error("You are not logged in.");
      return;
    }

    setRating(rate); // Optimistic update of rating in the UI

    const targetId = rappId ? `rapps/rating/${rappId}` : `products/rating/${productId}`; // Dynamic endpoint

    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/${targetId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
          body: JSON.stringify({ rate }),
        }
      );

      if (!result.ok) {
        toast.error("Failed to submit rating.");
      } else {
        toast.success("Rating submitted!");
      }
    } catch (error) {
      console.error("Error submitting rating:", error);
    }
  };

  return (
    <div className="flex items-center">
      {[...Array(5)].map((_, index) => {
        const starRating = index + 1;
        return (
          <Star
            key={index}
            className={`cursor-pointer  ${
              starRating <= (hover || rating)
                ? "fill-yellow-500 "
                : "fill-white-300"
            }`}
            onClick={() => handleRatingClick(starRating)}
            onMouseEnter={() => setHover(starRating)}
            onMouseLeave={() => setHover(0)}
            style={{ width: "1.5rem", height: "1.5rem" }}
          />
        );
      })}
    </div>
  );
};

export default StarRating;
