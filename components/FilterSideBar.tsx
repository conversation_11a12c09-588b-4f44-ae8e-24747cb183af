"use client";

// import { cn } from "@/lib/utils"
// import { Button } from "@/components/ui/button"
// import { Label } from "@/components/ui/label"
// import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
// // import { createSlug } from "@/lib/createSlug";
// import { usePathname, useRouter, useSearchParams } from "next/navigation";

// import { ScrollArea } from "@/components/ui/scroll-area"
// import { Checkbox } from "@/components/ui/checkbox";
// import { ChevronDown, ChevronUp } from "lucide-react";
// import { Product_Type } from '@/constants'

// import { Product } from '@/server/payload-types'

// interface FilterListingProps {
//   product: Product | null
//   className?: string;
//   children?: React.ReactNode;
// }

// import { useState } from "react"


// export default function FilterSidebar({ product, className }:
//     FilterListingProps ) {
//   const [isListExpanded, setIsListExpanded] = useState(false); 
 
//   const searchParams = useSearchParams(); 
//   const item = searchParams.get("item"); 
//   const pathname = usePathname();
//   const router = useRouter();
//   // const { categoryFilters, sort, setCategoryFilters, setSort } = useFilter()  
//   const [params, setParams] = useState<any>({})
//   const [sortBy, setSortBy] = useState<any>("created_at")
  
//   return (
//     <div className={cn("pb-12", className)}>
//       <div className="flex items-center justify-between gap-2 w-full">
//         <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
//           Collections
//         </h2>
      
//       </div>
//       <div className="mt-4">
//         <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
//           Collections
//         </h2>
//         {Product_Type.slice(0, 5).map((store, i) => (
//           <FilterCheckbox
//             key={i}
//             label={store.value}
//             selectedItems={params.collections}
//           />
//         ))}
//         {isListExpanded &&
//           Product_Type
//             .slice(5)
//             .map((store, i) => (
//               <FilterCheckbox
//                 key={i}
//                 label={store.value}
//                 selectedItems={params.collections}
//               />
//             ))}
//         <Button
//           variant="secondary"
//           size="sm"
//           className="w-full mt-2"
//           onClick={() => setIsListExpanded((prev) => !prev)}
//         >
//           {isListExpanded ? (
//             <div className="flex items-center justify-center gap-2">
//               Collapse sellers <ChevronUp size={18} className="mt-[2px]" />
//             </div>
//           ) : (
//             <div className="flex items-center justify-center gap-2">
//               Show more sellers <ChevronDown size={18} className="mt-[2px]" />
//             </div>
//           )}
//         </Button>
//       </div>
//     </div>
//   )
// }


import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import FilterRadioGroup from "./FilterRadioGroup";
import { ChangeEvent } from "react"

export type SortOptions = "newest" | "featured" | "trending" | "free"

type SortProductsProps = {
  sortBy: SortOptions
  setSortBy: (value: string) => void
}

const sortOptions = [
  {
    value: "newest",
    label: "Newest",
  },
  {
    value: "featured",
    label: "Featured",
  },
  {
    value: "trending",
    label: "Trending",
  },
  {
    value: "free",
    label: "Free",
  },
]

const  FilterSideBar = ({ sortBy, setSortBy }: SortProductsProps) => {
  const handleChange = (e: ChangeEvent<HTMLButtonElement>) => {
    setSortBy(e.target.value)
  }

  return (
    <FilterRadioGroup
      items={sortOptions}
      value={sortBy}
      handleChange={handleChange}
    />
  )
}

export default FilterSideBar