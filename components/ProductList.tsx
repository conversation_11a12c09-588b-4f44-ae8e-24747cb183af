import ProductCard from "@/components/ui/ProductCard";
import { Product } from "@/types";
import { BackgroundGradient } from "./ui/background-gradient";

interface ProductListProps {
  items: Product[];
}

const ProductList: React.FC<ProductListProps> = ({ items }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {items.map((item) => (
          <BackgroundGradient key={item.id} className="rounded-lg">
          <ProductCard key={item.id} data={item} />
          </BackgroundGradient>
        ))}
      </div>
    </div>
  );
};

export default ProductList;