import React from 'react'
import Image from 'next/image'

interface props {
  image?: any
}
const Images = ({image}:props) => {
  return (
    <div>
      <Image
      className="w-96 h-60 object-cover rounded-lg mr-3 mb-4"
        width="250"
        height="250"
        src= {image?.image?.url ? image?.image?.url : `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/placeholder-09.webp`}
        alt="image"
      />
    </div>
  )
}

export default Images
