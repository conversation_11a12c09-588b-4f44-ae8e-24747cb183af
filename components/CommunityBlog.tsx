"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { Media } from "@/server/payload-types";
import { CardBody, CardContainer, CardItem } from "@/components/ui/3d-card";
import { Badge } from "@/components/ui/badge";
import axios from "axios";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import placeholderImg from "@/public/png/blog-placeholder-img.png";
import Image from "next/image";

const bgColors = [
  "bg-custom-bg4",
  "bg-custom-bg5",
  "bg-custom-bg6",
  "bg-custom-bg1",
  "bg-custom-bg2",
  "bg-custom-bg3",
];

const CommunityBlog = () => {
  const [loading, setLoading] = useState(true);
  const [filteredBlogs, setFilteredBlogs] = useState<any[]>([]);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch(`/api/blogs/getFeaturedBlogs`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
        });

        if (response.ok) {
          const featuredBlogs = await response.json();
          setFilteredBlogs(featuredBlogs.data);
          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
      }
    };
    fetchBlogs();
  }, []);

  if (loading) {
    return <div className="text-center">Loading...</div>;
  }

  //------------------------------for image render---------------------------------------
  const renderImage = (image: string | Media | undefined) => {
    if (typeof image === "string") {
      return <img src={image ?? undefined} alt="Community Blog" />;
    } else if (image && image.sizes && image.sizes.thumbnail) {
      return (
        <img
          src={image.sizes.thumbnail.url ?? image?.url}
          alt="Community Blog"
        />
      );
    }
    return null;
  };

  return (
    <>
      <div className="w-11/12 mx-auto pb-12 md:pb-24">
        <div className="flex items-center justify-between mb-10">
          <div className="text-white text-3xl md:text-4xl font-medium">
            Featured Blogs
          </div>
          <Link href={"/blog"}>
            <button className="bg-gradient-to-r from-amber-500 to-pink-500 text-white font-semibold py-2 px-4 rounded-full text-sm md:text-md">
              READ MORE →
            </button>
          </Link>
        </div>
        <div className="grid gap-4  md:max-w-4xl md:gap-6 sm:max-w-xl h-11/12 grid-cols-1 lg:max-w-none lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1">
          {filteredBlogs.slice(0, 4).map((blog, index) => (
            <CardContainer key={blog.id} className="inter-var h-full w-full">
              <Link
                href={`/blog/${(blog as any)?.slug}`}
                className="h-full w-full"
              >
                <div className="bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 shadow-lg rounded-lg h-full w-full">
                  <div className="relative w-full">
                    <Image
                      className="rounded-t-lg object-cover w-full aspect-video md:aspect-3/2"
                      src={
                        blog.images && blog.images.length > 0
                          ? (typeof blog.images[0].image === "string"
                              ? blog.images[0].image
                              : blog.images[0].image?.sizes?.thumbnail?.url) ||
                            placeholderImg
                          : placeholderImg
                      }
                      alt={blog.title}
                      width={400}
                      height={200}
                      objectFit="cover"
                      loading="lazy"
                    />
                    {blog?.isFeatured === true && (
                      <div className="absolute top-0 -left-2 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                        <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                          FEATURED
                          <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                        </div>
                      </div>
                    )}
                    <p className="text-xs text-white absolute top-2 bg-indigo-600 w-fit px-2 py-1 right-2 rounded-lg">
                      {new Date(blog.createdAt).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      })}{" "}
                      · {blog.time} mins
                    </p>
                  </div>

                  <div className="px-4 py-2 mb-6 flex flex-col justify-between">
                    <div>
                      <h5 className="text-gray-100 font-bold text-lg tracking-tight mb-2 break-words whitespace-normal line-clamp-2 capitalize">
                        {blog.title}
                      </h5>
                      <p className="font-normal text-xs text-gray-200 mb-3 line-clamp-3 first-letter:uppercase">
                        {blog.content.substring(0, 155)}...
                      </p>
                    </div>
                    <div className="flex flex-wrap gap-2 absolute bottom-3">
                      {blog.tags.split(",").map((tag, index) => (
                        <span
                          key={index}
                          className="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded first-letter:uppercase"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </Link>
            </CardContainer>
          ))}
        </div>
      </div>
    </>
  );
};

export default CommunityBlog;
