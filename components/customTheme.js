var customTheme = {
    'common.bi.image': 'data:image/png;base64,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',
    'common.bisize.width': '21px',
    'common.bisize.height': '21px',
    'common.backgroundImage': 'none',
    'common.backgroundColor': '#1e1e1e',
    'common.border': '1px',

    // header
    'header.backgroundImage': 'none',
    'header.backgroundColor': 'transparent',
    'header.border': '0px',
    // 'loadButton': 'none',
    // 'downloadButton': 'none',

    // load button
    'loadButton.display': 'none',
    'downloadButton.display': 'none',
    'loadButton.backgroundColor': '#fff',
    'loadButton.border': '1px solid #ddd',
    'loadButton.color': '#222',
    'loadButton.fontFamily': 'NotoSans, sans-serif',
    'loadButton.fontSize': '12px',

    // // download button
    // 'downloadButton.backgroundColor': '#fdba3b',
    // 'downloadButton.border': '1px solid #fdba3b',
    // 'downloadButton.color': '#fff',
    // 'downloadButton.fontFamily': 'NotoSans, sans-serif',
    // 'downloadButton.fontSize': '12px',

    // icons default
    'menu.normalIcon.color': '#8a8a8a',
    'menu.activeIcon.color': '#555555',
    'menu.disabledIcon.color': '#434343',
    'menu.hoverIcon.color': '#e9e9e9',
    'submenu.normalIcon.color': '#8a8a8a',
    'submenu.activeIcon.color': '#e9e9e9',

    'menu.iconSize.width': '24px',
    'menu.iconSize.height': '24px',
    'submenu.iconSize.width': '32px',
    'submenu.iconSize.height': '32px',

    // submenu primary color
    'submenu.backgroundColor': '#1e1e1e',
    'submenu.partition.color': '#858585',

    // submenu labels
    'submenu.normalLabel.color': '#858585',
    'submenu.normalLabel.fontWeight': 'lighter',
    'submenu.activeLabel.color': '#fff',
    'submenu.activeLabel.fontWeight': 'lighter',

    // checkbox style
    'checkbox.border': '1px solid #ccc',
    'checkbox.backgroundColor': '#fff',

    // rango style
    'range.pointer.color': '#fff',
    'range.bar.color': '#666',
    'range.subbar.color': '#d1d1d1',

    'range.disabledPointer.color': '#414141',
    'range.disabledBar.color': '#282828',
    'range.disabledSubbar.color': '#414141',

    'range.value.color': '#fff',
    'range.value.fontWeight': 'lighter',
    'range.value.fontSize': '11px',
    'range.value.border': '1px solid #353535',
    'range.value.backgroundColor': '#151515',
    'range.title.color': '#fff',
    'range.title.fontWeight': 'lighter',



    // colorpicker style
    'colorpicker.button.border': '1px solid #1e1e1e',
    'colorpicker.title.color': '#fff'

};
export default customTheme;