"use client";
import Link from "next/link";
import { Icons } from "@/components/Icons";
import { Globe, LayoutDashboard, Menu, Search, X } from "lucide-react";
import SearchWrapper from "./SearchWrapper";
import UserBalance from "./UserBalance";
import { FaExternalLinkAlt } from "react-icons/fa";
import { HiOutlineCpuChip } from "react-icons/hi2";

const arr = [
  { name: "bounties", path: "/bounties" },
  { name: "bounty term & cond.", path: "/bountytandc" },
  // { name: "events", path: "/events" },
  { name: "terms and conditions", path: "/termsandconditions" },
  { name: "purchases", path: "/purchases" },
  { name: "community", path: "/community" },
  { name: "promptthons", path: "/community" },
  { name: "marketplace", path: "/marketplace" },
  { name: "generate", path: "/generate" },
  { name: "3d", path: "/marketplace" },
  { name: "threed", path: "/marketplace" },
  { name: "code", path: "/marketplace" },
  { name: "llms", path: "/marketplace" },
  { name: "text", path: "/marketplace" },
  { name: "images", path: "/marketplace" },
  { name: "music", path: "/marketplace" },
  { name: "videos", path: "/marketplace" },
  { name: "profile", path: "/profile" },
  { name: "dashboard", path: "/dashboard" },
  { name: "ai-apps", path: "/ai-apps" },
  { name: "rentspace", path: "/ai-apps" },
  { name: "spaces", path: "/generate" },
  { name: "academy", path: "/academy" },
  { name: "blogs", path: "/blog" },
  { name: "pricing", path: "/pricing" },
  { name: "home", path: "/" },
  { name: "contact", path: "/aboutus" },
  { name: "privacy policy", path: "/privacypolicy" },
  { name: "refund policy", path: "/refundpolicy" },
  { name: "image generation", path: "/generate/image" },
  { name: "discussion", path: "/community" },
  { name: "payout", path: "/payout" },
];
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import AccountDropdown4 from "./ui/header/profilenav";
import ShinnyButton from "./ui/ShinnyButton";
import { usePathname, useRouter } from "next/navigation";
import StaticUserBalance from "./ui/header/staticpricing";
import NotificationBell from "./notification/NotificationBell";

const Navbar = ({ user }) => {
  const router = useRouter();
  const [showLayers, setShowLayers] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [menuOpen, setMenuOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const pathname = usePathname();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/category?limit=0");
        const data = await response.json();
        setCategories(data.docs);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching categories:", error);
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  let coinBalance = user?.coinBalance ?? 0;

  const handleScroll = () => {
    if (typeof window !== "undefined") {
      const currentScrollY = window.scrollY;

      // Check if the current scroll position is less than 60px
      if (currentScrollY < 60) {
        setShowLayers(true);
      } else {
        // Compare with the last scroll position to determine show/hide
        setShowLayers(currentScrollY < lastScrollY);
      }

      setLastScrollY(currentScrollY);
    }
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.addEventListener("scroll", handleScroll);
      return () => {
        window.removeEventListener("scroll", handleScroll);
      };
    }
  }, [lastScrollY]);

  const handleMenuClick = () => {
    setMenuOpen(true);
    setSearchOpen(false); // Close search if open
  };
  const handleSearchClick = () => {
    setSearchOpen(true);
    setMenuOpen(false); // Close menu if open
  };

  const overlayVariants = {
    initial: { height: 0 },
    animate: { height: "100vh" }, // Expand to full screen
    exit: { height: 0 }, // Collapse back
  };
  const dropVariants = {
    initial: { height: 0 },
    animate: { height: "50vh" }, // Expand to full screen
    exit: { height: 0 }, // Collapse back
  };
  const textVariants = {
    initial: { height: 0 },
    animate: { height: 20 }, // Expand to full screen
    exit: { height: 0 }, // Collapse back
  };

  const toggleBodyScroll = () => {
    if (menuOpen || searchOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  };

  useEffect(() => {
    toggleBodyScroll();
    return () => {
      document.body.style.overflow = "auto"; // Reset on cleanup
    };
  }, [menuOpen, searchOpen]);

  const handleCategoryClick = (category) => {
    router.push(`/marketplace?category=${category.value}`);
  };

  return (
    <header className="fixed top-0 left-0 w-full z-50">
      {/* Main Navbar */}
      <div
        className={`${
          showLayers ? "md:py-4" : "md:py-3"
        } bg-indigo-800 py-4 text-white border-b border-gray-800 z-50 relative transition-all duration-300`}
      >
        <div className="container mx-auto grid grid-cols-3 justify-between items-center px-4 w-full">
          <div className="flex flex-row gap-1 md:gap-2 items-center">
            {/* Menu Icon / Close X */}
            {!menuOpen && (
              <Menu
                className="md:hidden text-2xl cursor-pointer"
                onClick={handleMenuClick}
              />
            )}
            {menuOpen && (
              <X
                className="md:hidden text-3xl cursor-pointer"
                onClick={() => setMenuOpen(false)}
              />
            )}
            <Link
              href="/dashboard"
              className="hidden md:block cursor-pointer gotodashboard"
            >
              <ShinnyButton className="bg-white">
                <p className="text-indigo-600 ">SELL</p>
              </ShinnyButton>{" "}
            </Link>
            {user?.id ? (
              <UserBalance user={user} /> // Show UserBalance when user is present
            ) : (
              <StaticUserBalance />
            )}
          </div>

          <div className="w-fit justify-self-center">
            <Link
              href="/"
              className="flex justify-self-center my-auto betalogo"
            >
              <Icons.betalogo
                className={`${
                  showLayers ? "h-10" : "h-8"
                } hidden md:block font-bold transition-all max-sm:h-7 duration-300 fill-primary w-auto mt-2`}
              />
              <Icons.logo className="md:hidden h-8 w-auto fill-primary" />
            </Link>
          </div>

          {/* Search Icon / Close X */}

          <div className="flex justify-end items-center">
            <div>
              {!searchOpen && (
                <Search
                  className="text-2xl cursor-pointer text-white searchanything"
                  onClick={handleSearchClick}
                />
              )}
              {searchOpen && (
                <X
                  className="text-3xl cursor-pointer"
                  onClick={() => setSearchOpen(false)}
                />
              )}
            </div>
            <div className="flex flex-row ml-2">
              <div className="px-2">{user && <NotificationBell />}</div>
              <div className="checkprofile">
                <AccountDropdown4 user={user} />
              </div>
              <>
                <style>
                  {`
                    @keyframes shimmer {
                      0% { transform: translateX(-100%); }
                      100% { transform: translateX(100%); }
                    }
                    .animate-shimmer {
                      animation: shimmer 1.5s infinite linear;
                    }
                  `}
                </style>

                <button className="hidden sm:flex relative group items-center justify-center gap-2 bg-gradient-to-r from-[#5C27FE] to-[#AE48FF] text-white font-semibold py-1.5 px-3  rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out transform hover:scale-[1.05] overflow-hidden">
                  <Link
                    href={"https://rentprompts.ai"}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {/* Shine effect */}
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm animate-shimmer" />

                    {/* Glow ring on hover */}
                    <span className="absolute -inset-[1px] rounded-lg border border-transparent group-hover:border-white/20 group-hover:shadow-[0_0_12px_2px_rgba(174,72,255,0.5)] pointer-events-none"></span>

                    {/* Button content */}
                    <span className="tracking-wide text-sm sm:text-base z-10">
                      AI Studio
                    </span>
                    {/* <FaExternalLinkAlt className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300 z-10" /> */}
                  </Link>
                </button>
              </>
            </div>
          </div>
        </div>
      </div>

      {/* Animated second layer */}
      <motion.div
        initial={{ y: 0 }}
        animate={{ y: showLayers ? 0 : -80 }}
        transition={{ duration: 0.4, ease: "easeInOut" }}
        className="hidden md:block bg-indigo-800 text-white py-2 z-40 w-full border-b border-gray-800 border-opacity-70"
      >
        <div className="container mx-auto flex justify-around">
          <nav className="flex gap-4">
            {[
              { href: "/community", label: "COMMUNITY" },
              { href: "/bounties", label: "BOUNTY" },
              { href: "/ai-apps", label: "AI APPS" },
              { href: "/marketplace", label: "MARKETPLACE" },
              // { href: "/products", label: "PRODUCTS" },
              { href: "/generate", label: "GENERATE" },
              { href: "/academy", label: "ACADEMY" },
              { href: "/blog", label: "BLOGS" },
              { href: "/aboutus", label: "ABOUT US" },
            ].map(({ href, label }) => (
              <Link
                key={href}
                href={href}
                className={`text-sm font-bold transition-colors ${
                  pathname === href
                    ? "text-white" // Active link color
                    : "hover:text-muted-foreground" // Default hover color
                }`}
              >
                <button
                  className={`${
                    [
                      "/ai-apps",
                      "/marketplace",
                      "/products",
                      "/generate",
                    ].includes(href)
                      ? "bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent"
                      : ""
                  } ${
                    pathname === href
                      ? "border-b-2 border-white" // Optional active underline style
                      : ""
                  }`}
                >
                  {label}
                </button>
              </Link>
            ))}
          </nav>
        </div>
      </motion.div>

      {/* Animated third layer */}
      <motion.div
        initial={{ y: 0 }}
        animate={{ y: showLayers ? 0 : -160 }}
        transition={{ duration: 0.4, ease: "easeInOut" }}
        className="hidden md:block bg-indigo-800 text-white border-b border-gray-800 border-opacity-40 py-1 z-40 w-full relative"
      >
        <div className="container mx-auto flex justify-around w-full">
          <div className="flex gap-3 overflow-x-auto whitespace-nowrap no-scrollbar">
            {categories?.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategoryClick(category)}
                className="flex flex-row items-center justify-between text-sm font-bold hover:text-muted-foreground transition-colors rounded-lg py-1 px-2 bg-white/[0.1]"
              >

                {category?.label?.toUpperCase()}

              </button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Fullscreen Menu Overlay */}
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            variants={overlayVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.1 }}
            className={`fixed ${
              showLayers ? "md:top-[80px]" : "top-[60px]"
            } left-0 w-full bg-gray-900 text-white z-50 overflow-y-auto whitespace-nowrap no-scrollbar`}
          >
            <ul className="text-lg space-y-6 px-4 py-6 mb-10">
              {/* First Navbar Options */}

              <li>
                <Link href="/dashboard" className="flex gap-2 items-center">
                  <LayoutDashboard />
                  SELL
                </Link>
              </li>
              <li>
                <Link
                  href="/community/"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.communtiy />
                  COMMUNITY
                </Link>
              </li>
              <li>
                <Link
                  href="/bounties"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.bountyicon />
                  BOUNTY
                </Link>
              </li>

              <li>
                <Link
                  href="https://rentprompts.ai"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <HiOutlineCpuChip className="w-6 h-6 text-white" />
                  <span className="bg-gradient-to-r from-[#5C27FE] to-[#AE48FF] bg-clip-text text-transparent font-semibold">
                    AI STUDIO
                  </span>
                </Link>
              </li>

              <li>
                <Link
                  href="/ai-apps"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.renticon />
                  <span className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">
                    AI APPS
                  </span>
                </Link>
              </li>
              <li>
                <Link
                  href="/marketplace"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.exploreicon />
                  <span className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">
                    MARKETPLACE
                  </span>
                </Link>
              </li>
              <li>
                <Link
                  href="/generate"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.generateicon />
                  <span className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">
                    GENERATE
                  </span>
                </Link>
              </li>
              <li>
                <Link
                  href="/academy"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.academyicon />
                  ACADEMY
                </Link>
              </li>
              <li>
                <Link
                  href="/blog"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Icons.blogicon />
                  BLOGS
                </Link>
              </li>
              <li>
                <Link
                  href="/aboutus"
                  onClick={() => setMenuOpen(false)}
                  className="flex gap-2 items-center"
                >
                  <Globe />
                  ABOUT US
                </Link>
              </li>
            </ul>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Fullscreen Search Overlay */}
      <AnimatePresence>
        {searchOpen && (
          <motion.div
            variants={overlayVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.1 }}
            className={`fixed ${
              showLayers ? "md:top-[80px]" : "top-[60px]"
            } left-0 w-full bg-gray-900 text-white z-50`}
          >
            <div className="px-4 w-full justify-self-center mt-6 md:w-1/2 max-md:max-w-96">
              <SearchWrapper/>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Navbar;
