"use client";

import Link from "next/link";
import ButtonRotatingBackgroundGradient from "./home/<USER>";
import { Button } from "./ui/button";

export default function DynamicSquareBackground({
  title,
  tag,
  description,
  buttonText,
  buttonText1,
  buttonHref,
  buttonHref1,
  image,
  image1,
}: Readonly<{
  title: string;
  tag: string;
  description: string;
  buttonText: string;
  buttonText1: string;
  buttonHref: string;
  buttonHref1: string;
  image: string;
  image1: string;
}>) {
  return (
    <>
      <style>
        {`
        @keyframes tiles {
          0%, 40%, 80% {
            opacity: 0;
          }
          20%, 60% {
            opacity: 1;
          }
        }
      `}
      </style>
      <div className="relative flex w-full  flex-col gap-8 overflow-hidden rounded-xl md:px-8 mt-20 min-h-[16rem] shadow-sm dark:shadow-black items-center justify-center h-full">
        <DecorativeTilesBackground />
        <div className="flex items-center w-[80%] z-20 py-6">
          <div className="z-20 md:w-1/2 h-full items-center">
            <div className="">
              <h1 className="inline font-semibold text-2xl text-neutral-100">
                {title}
              </h1>
              <p className="ml-2 inline rounded-sm border px-0.5 align-top font-medium text-xs uppercase tracking-tight border-neutral-400">
                {tag}
              </p>
            </div>
            <p className="mt-1 text-neutral-400 break-words whitespace-normal w-full">
              {description}
            </p>
            <div className="flex gap-4 mt-4">
                {buttonText && (
                  <Link
                    className="z-20 inline-flex items-center justify-center"
                    href={buttonHref}
                  >
                    <Button variant="white">
                      {buttonText}
                    </Button>
                  </Link>
                )}
                {buttonText1 && (
                  <Link
                    className="z-20 inline-flex items-center justify-center"
                    href={buttonHref1}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button variant="gradient">
                      {buttonText1}
                    </Button>
                  </Link>
                )}
            </div>
          </div>
          
        </div>
        <div className="absolute w-full h-full items-center z-0">
            {/* First Image */}
            {image && (
                <img src={image} alt="widget image" className="object-cover w-full h-full object-center" />
            )}

            {/* Second Image */}
            {/* {image1 && (
              <div className="absolute top-16 right-10 lg:right-32 h-52 w-40 hidden md:block">
                <img src={image1} alt="" className="object-fill" />
              </div>
            )} */}
          </div>
      </div>
    </>
  );
}

const DecorativeTilesBackground = () => {
  const rows = 20;
  const columns = 150;
  const animationDuration = 14; // seconds

  return (
    <div
      aria-hidden="true"
      className="pointer-events-none absolute inset-0 z-10 flex select-none flex-wrap"
    >
      {Array.from({ length: rows }).map((_, rowIndex) => {
        return (
          <div
            className="flex h-[16px] w-full border-neutral-500/20 border-b border-dashed"
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            key={`line-${rowIndex}`}
          >
            {Array.from({ length: columns }).map((_, colIndex) => {
              const delay = Math.random() * animationDuration;

              return (
                <div
                  className="relative h-[16px] w-[15px] border-neutral-500/20 border-r border-dashed"
                  // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                  key={`tile-${colIndex}`}
                >
                  <div
                    className=" inset-0 h-[16px] w-[15px] bg-dash/15 backdrop-blur-4 dark:bg-sky-400/15"
                    style={{
                      opacity: 0, // Start with opacity 0
                      animationName: "tiles",
                      animationIterationCount: "infinite",
                      animationTimingFunction: "ease",
                      animationDelay: `${delay}s`,
                      animationDuration: `${animationDuration}s`,
                    }}
                  />
                </div>
              );
            })}
          </div>
        );
      })}
    </div>
  );
};
