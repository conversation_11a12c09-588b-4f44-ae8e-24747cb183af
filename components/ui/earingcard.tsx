"use client";
import React, { useState } from "react";
import Image from "next/image";

function EarningCard({ title, value, tooltipMessage }) {
  return (
    <div className="rounded-lg p-4 bg-dash-foreground w-full relative transform transition duration-500 hover:scale-105 hover:shadow-xl">
      <div className="flex items-center space-x-1">
        <span className="text-slate-400 font-medium">{title}</span>
        <div className="relative group">
          <button className="flex items-center justify-center text-gray-600 hover:text-gray-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="h-5 w-5 border-2 border-gray-600 rounded-full"
              fill="none"
            >
              <circle cx="12" cy="12" r="10" fill="white" />
              <text
                x="12"
                y="16"
                textAnchor="middle"
                fontSize="12"
                fontWeight="bold"
                fill="black"
              >
                i
              </text>
            </svg>
          </button>
          {/* Tooltip */}
          <div className="absolute left-0 -top-8 w-max bg-gray-800 text-white text-xs rounded-md px-2 py-1 opacity-0 group-hover:opacity-100 transition duration-300">
            {tooltipMessage}
          </div>
        </div>
      </div>
      <div className="flex items-center mt-2 text-2xl font-bold text-white gap-1">
        {title !== "Balance" ? (
          <button className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent text-xs">
            Coming Soon
          </button>
        ) : (
          <>
            <div className="flex -rotate-45">
              <div className="motion-preset-stretch motion-duration-2000">
                <Image
                  src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                  width={36}
                  height={36}
                  alt="Coin"
                />
              </div>
            </div>
            {value}
          </>
        )}
      </div>
    </div>
  );
}

export default EarningCard;
