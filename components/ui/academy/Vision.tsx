// components/Vision.tsx
import Image from "next/image";
import { FC } from "react";

const visionPoints = [
  {
    description:
      "Be the One Stop Academy in shaping & preparing the next generation of AI engineers.",
    highlightCount: 5,
  },
  {
    description:
      "Empower Everyone with hands-on learning experience to innovate, create and monatize their skills.",
    highlightCount: 2,
  },
  {
    description:
      "Uphold RARE values by ensuring that our training is Responsible, Affordable, Relevant, and Easy to access and apply.",
    highlightCount: 3,
  },
];

const getHighlightedText = (text: string, highlightWords: number) => {
  const words = text.split(" ");
  const highlighted = words.slice(0, highlightWords).join(" ");
  const rest = words.slice(highlightWords).join(" ");
  return { highlighted, rest };
};

const Vision: FC = () => {
  return (
    <div className="py-12 px-4 md:px-20 text-center">
      <div className="relative mx-auto max-w-5xl flex items-center justify-center text-center">
        <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text max-w-2xl">
          Three reasons to choose RentPrompts Academy as your L&D partner
        </h2>
      </div>

      <div className="grid md:grid-cols-2 gap-10 max-md:mt-10 items-center">
        <div className="hidden md:flex items-center justify-center">
          {/* <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text">
            The VISION
          </h2> */}
          <Image src="../svg/accVision.svg" className="" alt="Vission" width="500" height="500" />
        </div>

        <div className="space-y-10 text-left">
          {visionPoints.map((point, index) => {
            const { highlighted, rest } = getHighlightedText(point.description, point.highlightCount);
            return (
              <div key={index} className="flex items-center gap-4">
                <div className="bg-indigo-700 p-2 border border-indigo-600">
                  <div className="text-3xl bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent font-black">
                    0{index + 1}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold max-w-sm">
                    <span className="bg-gradient-to-br from-pink-500 to-yellow-500 text-transparent bg-clip-text font-bold">
                      {highlighted}
                    </span>{" "}
                    {rest}
                  </h3>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Vision;
