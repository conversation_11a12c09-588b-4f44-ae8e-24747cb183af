import { useState } from "react";
import { Input } from "../input";
import { Button } from "../button";
import { Textarea } from "../textarea";
import { toast } from "sonner";

interface FieldConfig {
  label: string;  // What the user sees (e.g., "Full Name")
  name: string;   // Form field name (e.g., "fullName")
  type?: string;  // Optional field type
}

interface InfoModalProps {
  title: string;
  fields: FieldConfig[];
  onClose: () => void;
}

export function InfoModal({ title, fields, onClose }: InfoModalProps) {
  const [formData, setFormData] = useState<{ [key: string]: string }>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);

  const handleChange = (fieldName: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [fieldName]: value,
    }));

    // Clear error on change
    setErrors((prev) => ({
      ...prev,
      [fieldName]: "",
    }));
  };

  const validate = () => {
    let valid = true;
    const newErrors: { [key: string]: string } = {};

    fields.forEach((field) => {
      // Check required fields
      if (!formData[field.name]?.trim()) {
        newErrors[field.name] = "This field is required";
        valid = false;
      }

      // Special validation for email
      if (field.name.toLowerCase().includes("email")) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData[field.name])) {
          newErrors[field.name] = "Invalid email address";
          valid = false;
        }
      }
    });

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = async(e: React.FormEvent) => {
    e.preventDefault();
    
    const submissionData = new FormData();
    submissionData.append("courseName", title);
    
    // Add all form fields with their labels
    fields.forEach((field) => {
      submissionData.append(field.name, formData[field.name] || "");
    });

    if (!validate()) return;
    
    // Convert to plain object for logging
    const submissionObject: Record<string, string> = { courseName: title };
    fields.forEach((field) => {
      submissionObject[field.name] = formData[field.name] || "";
    });

    setLoading(true);
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contactInfo`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(submissionObject),
        }
      );

      const data = await res.json();

      if (!res.ok) {
        const errorText = data.errors?.[0]?.message || "Unknown error";
        toast.error(errorText);
      } else {
        toast.success("Form Submitted Successfully");
      }
    } catch (error) {
      console.error("Something went wrong, please try again later:", error);
    } finally {
      setLoading(false);
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
      <div className="bg-indigo-700 p-6 rounded-lg w-full max-w-md space-y-4 max-md:mx-4">
        <h2 className="text-xl font-bold text-white">{title}</h2>
        <form className="space-y-4" onSubmit={handleSubmit}>
          {fields.map((field) => (
            <div key={field.name} className="flex flex-col">
              <label htmlFor={field.name} className="text-sm font-medium text-white">
                {field.label}
              </label>

              {field.name.toLowerCase() === "query" ? (
                <Textarea
                  id={field.name}
                  value={formData[field.name] || ""}
                  onChange={(e) => handleChange(field.name, e.target.value)}
                  className="p-2 bg-indigo-600 text-white rounded max-h-96 outline-none ring-[2px] ring-muted-foreground"
                  placeholder={`Enter ${field.label}`}
                  required
                />
              ) : (
                <Input
                  id={field.name}
                  type={field.type || (field.name.toLowerCase().includes("email") ? "email" : "text")}
                  value={formData[field.name] || ""}
                  onChange={(e) => handleChange(field.name, e.target.value)}
                  className="p-2 bg-indigo-600 text-white"
                  placeholder={`Enter ${field.label}`}
                  required
                />
              )}
              {errors[field.name] && (
                <span className="text-red-300 text-sm mt-1">{errors[field.name]}</span>
              )}
            </div>
          ))}
          <div className="flex justify-end gap-2">
            <Button
              variant="red"
              type="button"
              onClick={onClose}
              className="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="blue"
              type="submit"
              className="px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700"
              disabled={loading}
            >
              {loading ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}