"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";

type Slide = {
  imgSrc: string;
  imgAlt: string;
  title: string;
  description: string;
  ctaUrl: string;
  ctaText: string;
};

const slides: Slide[] = [
  {
    imgSrc: "https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/dither_it_9k=.png",
    imgAlt: "Vibrant abstract painting with swirling blue and light pink hues on a canvas.",
    title: "Skill-Up for Students",
    description: "Kickstart your AI journey with our Skill-Up Program for Students! Learn AI fundamentals, prompt engineering, and real-world applications to stay ahead in the digital era. Gain hands-on experience with RentPrompts Marketplace and AI Studio, an innovative platform to access and refine AI-generated content.",
    ctaUrl: "https://example.com",
    ctaText: "Learn More",
  },
  {
    imgSrc: "https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/dither_it_5 (3).png",
    imgAlt: "Vibrant abstract painting with swirling red, yellow, and pink hues on a canvas.",
    title: "Skill-Up for Working Professionals",
    description: "Future-proof your career with our AI-driven Skill-Up Program for Professionals! Master prompt engineering, AI automation, and generative AI tools to boost efficiency and innovation. Explore RentPrompts Marketplace and AI Studio to streamline your workflow and enhance AI-driven content creation.",
    ctaUrl: "https://example.com",
    ctaText: "Learn More",
  },
  {
    imgSrc: "https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/dither_it_2 (1).png",
    imgAlt: "Vibrant abstract painting with swirling blue and purple hues on a canvas.",
    title: "Skill-Up for Small-Mid Sized Business",
    description: "For companies that want a comprehensive L&D solution with essential features and support services for their team, starting at 5 users.",
    ctaUrl: "https://example.com",
    ctaText: "Learn More",
  },
  {
    imgSrc: "https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/dither_it_3 (2).png",
    imgAlt: "Vibrant abstract painting with swirling red, yellow, and pink hues on a canvas.",
    title: "Skill-Up for Enterprise",
    description: "For enterprises that want a customizable career development platform for company-wide L&D Solutions.",
    ctaUrl: "https://example.com",
    ctaText: "Learn More",
  },
  {
    imgSrc: "https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/dither_it_4 (4).png",
    imgAlt: "Vibrant abstract painting with swirling blue and purple hues on a canvas.",
    title: "Skill-Up for Educational Institutes",
    description: "Give your students the tools they need to succeed with online, on-demand Generative AI and Prompt Engineering Training that brings real-world, interactive simulations and AI-powered tools to your classroom and beyond.",
    ctaUrl: "https://example.com",
    ctaText: "Learn More",
  },
];

export default function Carousel() {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlideIndex((prevIndex) => (prevIndex + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const previous = () => {
    setCurrentSlideIndex((prevIndex) => (prevIndex - 1 + slides.length) % slides.length);
  };

  const next = () => {
    setCurrentSlideIndex((prevIndex) => (prevIndex + 1) % slides.length);
  };

  return (
    <div className="relative w-full overflow-hidden">
      <button
        type="button"
        className="absolute left-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-gradient-to-r from-amber-500 to-pink-500 p-2"
        onClick={previous}
      >
        <ChevronLeft/>
      </button>
      <button
        type="button"
        className="absolute right-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-gradient-to-r from-amber-500 to-pink-500 p-2"
        onClick={next}
      >
        <ChevronRight/>
      </button>
      <div className="relative min-h-screen md:min-h-[calc(100vh-96px)] w-full">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${index === currentSlideIndex ? "opacity-100" : "opacity-0"}`}
          >
            <div className="absolute inset-0 z-10 flex flex-col items-center justify-end gap-2 bg-gradient-to-t from-black/85 to-transparent px-16 py-12 text-center">
              <h3 className="text-2xl font-bold text-white">{slide.title}</h3>
              <p className="text-xs md:text-lg text-white md:max-w-3xl ">{slide.description}</p>
              {/* <a href={slide.ctaUrl} className="mt-2 px-4 py-2 border border-white text-white text-sm rounded-md hover:opacity-75">
                {slide.ctaText}
              </a> */}
            </div>
            <img className="absolute w-full h-full object-cover" src={slide.imgSrc} alt={slide.imgAlt} />
          </div>
        ))}
      </div>
      <div className="absolute bottom-3 left-1/2 flex -translate-x-1/2 gap-4">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlideIndex(index)}
            className={`size-2 rounded-full ${index === currentSlideIndex ? "bg-gray-300" : "bg-gray-500"}`}
          />
        ))}
      </div>
    </div>
  );
}