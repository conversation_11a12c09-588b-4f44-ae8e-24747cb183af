"use client";

import {
  Award,
  Briefcase,
  BriefcaseIcon,
  Building2,
  ExternalLink,
  GraduationCap,
  MessagesSquare,
  Newspaper,
  Trophy,
} from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import Link from "next/link";
import { useState } from "react";
import { InfoModal } from "./infoModel";

export function GlowingEffectDemo() {
  const [openModal, setOpenModal] = useState<null | "student" | "pro" | "bus" | "inst" | "ent">();

  const closeModal = () => setOpenModal(null);

  return (
    <div className="my-12 md:my-24 w-11/12 mx-auto flex flex-col items-center">
      <h2 className="text-3xl sm:text-4xl font-bold text-transparent bg-gradient-to-b from-white to-gray-400 bg-clip-text max-w-2xl mb-10 text-center">
        Pick the Perfect Learning Plan For Your Goals
      </h2>

      {/* Top 3 Cards */}
      <div className="flex flex-wrap justify-center gap-6 mb-6 md:mb-10">
      <GridItem
          icon={<Award className="h-10 w-10 text-white dark:text-neutral-400" />}
          title="Skill-Up for Students"
          description="Explore rewarding opportunities through bounties."
          buttonLabel="Get Started"
          onButtonClick={() => setOpenModal("student")}
        >
          <ul className="text-white text-sm list-disc pl-4 mt-2 space-y-1">
          <li>Instructor led sessions</li>
            <li>Live classes</li>
            <li>Hands-On Practice Lab</li>
          </ul>
        </GridItem>

        <GridItem
          icon={<Newspaper className="h-10 w-10 text-white dark:text-neutral-400" />}
          title="Skill-Up for Working Professionals"
          description="Advance your career with Gen AI & Prompt Engineering"
          buttonLabel="Sign Me Up"
          onButtonClick={() => setOpenModal("pro")}
        >
          <ul className="text-white text-sm list-disc pl-4 mt-2 space-y-1">
            <li>Instructor led sessions</li>
            <li>Live classes</li>
            <li>Hands-On Practice Lab</li>
          </ul>
        </GridItem>

        <GridItem
          icon={<Briefcase className="h-10 w-10 text-white dark:text-neutral-400" />}
          title="Skill-Up for Small-Mid Sized Business"
          description="For teams up to 50. Starting at 5 users."
          buttonLabel="Talk to Sales"
          onButtonClick={() => setOpenModal("bus")}
        >
          <ul className="text-white text-sm list-disc pl-4 mt-2 space-y-1">
            <li>Instructor led sessions</li>
            <li>Live classes</li>
            <li>Online virtual sessions</li>
            <li>Weekly Q&A with experts</li>
            <li>Hands-On Practice Lab</li>
            <li>Access to free resources</li>
          </ul>
        </GridItem>
      </div>

      {/* Bottom 2 Cards Centered */}
      <div className="flex justify-center flex-wrap gap-6">
        <GridItem
          icon={<Building2 className="h-10 w-10 text-white dark:text-neutral-400" />}
          title="Skill-Up for Enterprise"
          description="Customizable L&D platform for enterprises."
          buttonLabel="Get a Quote"
          onButtonClick={() => setOpenModal("ent")}
        >
          <ul className="text-white text-sm list-disc pl-4 mt-2 space-y-1">
            <li>All features in SMB</li>
            <li>Add SCORM, mp4, PDF</li>
            <li>Custom learning paths</li>
            <li>Compliance training</li>
            <li>Advanced insights & reporting</li>
          </ul>
        </GridItem>

        <GridItem
          icon={<GraduationCap className="h-10 w-10 text-white dark:text-neutral-400" />}
          title="Skill-Up for Educational Institutes"
          description="Give your students the tools they need to succeed."
          buttonLabel="Get a Quote"
          onButtonClick={() => setOpenModal("inst")}
        >
          <ul className="text-white text-sm list-disc pl-4 mt-2 space-y-1">
            <li>On-demand Gen AI training</li>
            <li>Interactive simulations</li>
            <li>AI-powered tools for classrooms</li>
            <li>Designed to enhance learning</li>
          </ul>
        </GridItem>
      </div>

      {/* Conditionally Render Modal */}
      {openModal === "student" && (
  <InfoModal
    title="Student Registration"
    fields={[
      { label: "Full Name", name: "userName" },
      { label: "Email Address", name: "email" },
      { label: "Your Query", name: "query" },
    ]}
    onClose={closeModal}
  />
)}

{openModal === "pro" && (
  <InfoModal
    title="Professional Signup"
    fields={[
      { label: "Full Name", name: "userName" },
      { label: "Email Address", name: "email" },
      { label: "Your Query", name: "query" },
    ]}
    onClose={closeModal}
  />
)}

{openModal === "bus" && (
        <InfoModal
          title="Businness Registration"
          fields={[
            { label: "Full Name", name: "userName" },
            { label: "Email Address", name: "email" },
            { label: "Your Query", name: "query" },
          ]}
          onClose={closeModal}
        />
      )}

{openModal === "inst" && (
        <InfoModal
          title="Institute Registration"
          fields={[
            { label: "Full Name", name: "userName" },
            { label: "Email Address", name: "email" },
            { label: "Your Query", name: "query" },
          ]}
          onClose={closeModal}
        />
      )}

{openModal === "ent" && (
        <InfoModal
          title="Enterprise Registration"
          fields={[
            { label: "Full Name", name: "userName" },
            { label: "Email Address", name: "email" },
            { label: "Your Query", name: "query" },
          ]}
          onClose={closeModal}
        />
      )}
    </div>
  );
}


interface GridItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  buttonLabel: string;
  children?: React.ReactNode;
  onButtonClick?: () => void;
}

const GridItem = ({ icon, title, description, buttonLabel, children, onButtonClick }: GridItemProps) => {
  return (
    <div className="w-full sm:w-[24rem] min-h-[16rem]">
      <div className="relative h-full rounded-xl border-4">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="relative flex h-full flex-col justify-between gap-4 overflow-hidden rounded-xl border p-6 bg-indigo-700">
          <div className="flex flex-col gap-3">
            <div className="flex flex-row gap-6 items-center">
            {/* <div>{icon}</div> */}
            <h3 className="text-2xl font-semibold font-sans bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent">
              {title}
            </h3>
            </div>
            <p className="text-white text-sm">{description}</p>
            {children}
          </div>
          <button
            className="mt-4 bg-gradient-to-r from-amber-500 to-pink-500 hover:from-amber-600 hover:to-pink-600 text-white py-2 px-4 rounded-lg w-full"
            onClick={onButtonClick}
          >
            {buttonLabel}
          </button>
        </div>
      </div>
    </div>
  );
};

