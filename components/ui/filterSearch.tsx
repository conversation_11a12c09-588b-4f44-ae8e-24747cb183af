"use client";
import React, { useState } from "react";
import { DropdownMenu } from "./dropdown-menu";
import DropdownCategory from "./dropdownCategory";

const FilterSearch = () => {

  const categories = [
    { id: 0, name: 'Image Generation', url: '#' },
    { id: 1, name: 'Video Generation', url: '#' },
    { id: 2, name: 'Music Generation', url: '#' },
    { id: 3, name: '3D Generation', url: '#' },
  ];

  return (
    <form className="mb-10">
      <div className="flex w-full items-center justify-center">
        
        <DropdownCategory categories={categories}/>
        <div className="">
          <input
            type="search"
            id="search-dropdown"
            className="p-2.5 text-sm w-96 text-gray-900 bg-transparent rounded focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-s-gray-700  dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500"
            placeholder="Search for an image..."
            required
          />
          <button
            type="submit"
            className="p-2.5 text-sm font-medium  text-white bg-gradient-to-br from-indigo-600 to-indigo-700 rounded"
          >
            <svg
              className="w-4 h-4"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 20"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
              />
            </svg>
            <span className="sr-only">Search</span>
          </button>
        </div>
      </div>
    </form>
  );
};

export default FilterSearch;
