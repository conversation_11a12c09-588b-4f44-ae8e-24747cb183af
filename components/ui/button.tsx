import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-secondary hover:bg-background hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        dark:"bg-card text-white hover:bg-secondary",
        yellow:"y-3 rounded-lg text-lg font-semibold text-white bg-gradient-to-r from-yellow-500 to-yellow-500 hover:from-yellow-600 hover:to-yellow-500 transition-all ease-in-out shadow-lg",
        blue:"text-primary bg-gradient-to-br from-indigo-800 to-indigo-900 font-bold py-2 px-3 rounded text-xs sm:text-sm",
          white:"bg-primary text-indigo-600 font-bold px-1 py-1 sm:py-2 sm:px-3 rounded text-xs sm:text-sm transform transition duration-200 hover:scale-105 hover:shadow-xl",
          iconWhite:"bg-primary text-indigo-600 font-bold px-1 py-1 sm:py-2 sm:px-2 rounded text-xs sm:text-sm",
          red:"bg-primary text-red-600 font-bold py-2 px-3 rounded text-xs sm:text-sm",
          green: "bg-green-600 font-bold px-2 py-1 sm:py-2 sm:px-3 rounded text-xs sm:text-sm",
          gradient: "bg-gradient-to-r from-amber-500 to-pink-500 font-bold px-2 py-1 sm:py-2 sm:px-3 rounded text-xs sm:text-sm transform transition duration-200 hover:scale-105 hover:shadow-xl",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
        smicon: "h-8 w-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    // const Comp = asChild ? Slot : "button"
    const Comp: React.ElementType = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
