"use client";
import React, { useState } from "react";
import ProductListing from "../ProductListing";

const ProductExpandableList = ({ items, user }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const itemsToShow = isExpanded ? items : items.slice(0, 3);

  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {itemsToShow.map((item, i) => (
          <ProductListing
            key={`product-${i}`}
            product={item}
            index={i}
            user={user}
          />
        ))}
      </div>
      {items.length > 3 && (
        <div className="text-center mt-6 flex items-center justify-center">
          <div className="flex-grow border-t border-gray-300"></div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-white font-bold cursor-pointer mx-4"
          >
            {isExpanded ? "▲ View Less" : "▼ View More"}
          </button>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>
      )}
    </div>
  );
};

export default ProductExpandableList;
