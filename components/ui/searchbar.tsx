

"use client"

import React, { useEffect, useState } from 'react';

interface MenuItemProps {
  mainheader: boolean;
  onSearch?: (query: string) => void;
  query?: string;
}

const SearchBar = ({ mainheader, onSearch, query=""}: MenuItemProps) => {
  const [inputValue, setInputValue] = useState(query);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearch(event.target.value);
  };

  
  useEffect(() => {
    setInputValue(query); // Sync if parent resets query
  }, [query]);

 
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value); // update input box instantly

    // Clear previous timer
    if (debounceTimer) clearTimeout(debounceTimer);

    // Set new debounce timer
    const newTimer = setTimeout(() => {
      onSearch(value);
    }, 10); // wait 500ms after typing stops

    setDebounceTimer(newTimer);
  };
  return (
    <>
      <div
        className={`flex border border-white rounded-lg  ${
          mainheader
            ? "mt-0 hidden md:block md:w-80 lg:w-60 mx-6"
            : "w-full justify-between md:mt-6"
        }`}
      >
        <div className="relative w-full">
          <input
            type="search"
            id="default-search"
            className="block w-full py-2 pl-9 pr-2 text-sm text-white bg-transparent focus:outline-none"
            placeholder="Search..."
            value={inputValue}
            // onChange={handleSearch}
            onChange={handleChange}
          />

          <div className="absolute inset-y-0 start-0 flex items-center pointer-events-none">
            <svg
              className="w-6 h-4 ps-3 text-gray-500 dark:text-gray-400 "
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 20"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
              ></path>
            </svg>
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchBar;

