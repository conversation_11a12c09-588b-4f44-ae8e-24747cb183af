

"use client"

import React from 'react';

interface MenuItemProps {
  mainheader: boolean;
  onSearch?: (query: string) => void;
}

const SearchBar = ({ mainheader, onSearch }: MenuItemProps) => {
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearch(event.target.value);
  };

  return (
    <>
      <div
        className={`flex border border-white rounded-lg  ${
          mainheader
            ? "mt-0 hidden md:block md:w-80 lg:w-60 mx-6"
            : "w-full justify-between md:mt-6"
        }`}
      >
        <div className="relative w-full">
          <input
            type="search"
            id="default-search"
            className="block w-full py-2 pl-9 pr-2 text-sm text-white bg-transparent focus:outline-none"
            placeholder="Search..."
            onChange={handleSearch}
          />

          <div className="absolute inset-y-0 start-0 flex items-center pointer-events-none">
            <svg
              className="w-6 h-4 ps-3 text-gray-500 dark:text-gray-400 "
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 20"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
              ></path>
            </svg>
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchBar;

