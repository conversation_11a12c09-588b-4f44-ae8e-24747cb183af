"use client"
import { useState } from 'react';
import SearchBar from './searchbar';

interface MenuItem {
  id: number;
  name: string;
  url: string;
}

interface Props {
  categories: MenuItem[];
}

const DropdownCategory: React.FC<Props> = ({ categories }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);

  const [searchQuery, setSearchQuery] = useState("");
    const handleSearch = (query: string) => {
        setSearchQuery(query.toLowerCase());
      };

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleCategorySelect = (id: number) => {
    setSelectedCategory(id);
  };

  return (
    <div className="relative flex items-center gap-2 justify-center text-left mb-10">
      <div>
        <button
          type="button"
          className="flex w-full text-white justify-center bg-gradient-to-br from-indigo-600 to-indigo-700 rounded px-3 py-2.5 text-sm font-semibold shadow-sm ring-inset hover:bg-gray-50"
          onClick={toggleMenu}
          aria-expanded={isOpen ? 'true' : 'false'}
          aria-haspopup="true"
        >
          All Category
          <svg
            className="-mr-1 h-5 w-5 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
              clipRule="evenodd"
            />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute right z-10 mt-2 w-56 origin-top-right rounded shadow-lg bg-black/[0.2] backdrop-blur-md ring-1 ring-black ring-opacity-5 focus:outline-none">
            <div className="py-1" role="none">
              {categories.map((category) => (
                <a
                  key={category.id}
                  href={category.url}
                  onClick={() => handleCategorySelect(category.id)}
                  className={`text-white hover:bg-indigo-600 flex items-center px-4 py-2 text-sm`}
                  role="menuitem"
                  tabIndex={-1}
                >
                  {selectedCategory === category.id && (
                    <svg
                      className="mr-2 h-4 w-4 text-white"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 00-1.414 0L8 12.586 4.707 9.293a1 1 0 00-1.414 1.414l4 4a1 1 0 001.414 0l8-8a1 1 0 000-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                  {category.name}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
      <SearchBar mainheader onSearch={handleSearch} />
    </div>
  );
};

export default DropdownCategory;
