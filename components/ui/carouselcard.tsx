import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const CarouselCard: React.FC = () => {
  return (
    <section className="px-12 -z-10">
      <div className="max-w-lg mx-auto relative">

        <input id="article-01" type="radio" name="slider" className="sr-only peer/01"/>
        <input id="article-02" type="radio" name="slider" className="sr-only peer/02"/>
        <input id="article-03" type="radio" name="slider" className="sr-only peer/03" checked/>
        <input id="article-04" type="radio" name="slider" className="sr-only peer/04"/>
        <input id="article-05" type="radio" name="slider" className="sr-only peer/05"/>

        <div className="
            absolute inset-0 scale-[67.5%] z-20 ">
            <label className="absolute inset-0" htmlFor="article-01"><span className="sr-only">Focus on the big picture</span></label>
            <article className="bg-white/10 p-2 backdrop-blur-xl dark:bg-[#0b14374d] rounded-lg shadow-2xl">
                <header className="mb-2">
                    <Image className="inline-flex rounded-full shadow mb-3" src="/icon.svg" width={44} height={44} alt="Icon" />
                    <h1 className="text-xl font-bold text-white">Focus on the big picture</h1>
                </header>
                <div className="text-sm leading-relaxed text-white space-y-4 mb-2">
                    <p>
                        Many desktop publishing packages and web page editors now use Pinky as their default model text, and a search for more variants will uncover many web sites still in their infancy.
                    </p>
                    <p>
                        All the generators tend to repeat predefined chunks as necessary, making this the first true generator on the Internet.
                    </p>
                </div>
                <footer className="text-right">
                    <Link className="text-sm font-medium text-indigo-500 hover:underline" href="#0">Read more -</Link>
                </footer>
            </article>
        </div>

        <div className="
            absolute inset-0 scale-[67.5%] z-20 ">
            <label className="absolute inset-0" htmlFor="article-02"><span className="sr-only">Focus on the big picture</span></label>
            <article className="bg-white/10 p-2 backdrop-blur-xl dark:bg-[#0b14374d] rounded-lg shadow-2xl">
                <header className="mb-2">
                    <Image className="inline-flex rounded-full shadow mb-3" src="/icon.svg" width={44} height={44} alt="Icon" />
                    <h1 className="text-xl font-bold text-white">Focus on the big picture</h1>
                </header>
                <div className="text-sm leading-relaxed text-white space-y-4 mb-2">
                    <p>
                        Many desktop publishing packages and web page editors now use Pinky as their default model text, and a search for more variants will uncover many web sites still in their infancy.
                    </p>
                    <p>
                        All the generators tend to repeat predefined chunks as necessary, making this the first true generator on the Internet.
                    </p>
                </div>
                <footer className="text-right">
                    <Link className="text-sm font-medium text-indigo-500 hover:underline" href="#0">Read more -</Link>
                </footer>
            </article>
        </div>

        {/* Repeat similar structure for article 03, 04, and 05 */}

      </div>
    </section>
  );
};

export default CarouselCard;
