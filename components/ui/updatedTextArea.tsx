'use client'
import * as React from "react";

import { cn } from "@/lib/utils";
import { useMotionTemplate, useMotionValue, motion } from "framer-motion";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { useRef } from "react";

export interface UpdatedTextAreaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const UpdatedTextArea = React.forwardRef<HTMLTextAreaElement, UpdatedTextAreaProps>(
  ({ className, value, onChange, ...props }, ref) => {
    const radius = 150;
    const [visible, setVisible] = React.useState(false);
    const [isExpanded, setIsExpanded] = React.useState(true); // Controls manual expansion
    const [shouldShowToggle, setShouldShowToggle] = React.useState(false);

    const textareaRef = useRef<HTMLTextAreaElement | null>(null);
    let mouseX = useMotionValue(0);
    let mouseY = useMotionValue(0);

    const defaultLineHeight = 24; // Adjust based on your font size
    const paddingVertical = 20; // Increased padding from 16 to 20
    const minHeight = `${defaultLineHeight + paddingVertical}px`; // One line height including padding

    function handleMouseMove({ currentTarget, clientX, clientY }: any) {
      let { left, top } = currentTarget.getBoundingClientRect();

      mouseX.set(clientX - left);
      mouseY.set(clientY - top);
    }

    const checkOverflow = () => {
      if (textareaRef.current) {
        const { scrollHeight } = textareaRef.current;
        setShouldShowToggle(scrollHeight > parseInt(minHeight)); // Show the toggle button only if content exceeds one line height
      }
    };

    const adjustHeight = () => {
      if (textareaRef.current) {
        if (isExpanded) {
          // Allow height to expand based on content
          textareaRef.current.style.height = "auto";
          textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        } else {
          // Set to one line height when collapsed
          textareaRef.current.style.height = minHeight;
        }
      }
    };

    React.useEffect(() => {
      adjustHeight(); // Adjust height when value or expansion state changes
      checkOverflow(); // Check if the content overflows to show/hide toggle button
    }, [value, isExpanded]);

    return (
      <motion.div
        style={{
          background: useMotionTemplate`
          radial-gradient(
            ${visible ? radius + "px" : "0px"} circle at ${mouseX}px ${mouseY}px,
            var(--blue-500),
            transparent 80%
          )
        `,
        }}
        className="relative p-[2px] rounded-lg transition duration-300 group/input"
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
      >
        <textarea
          className={cn(
            `relative flex w-full border-none shadow-input file:border-1 file:bg-muted-foreground 
            file:text-sm file:font-medium placeholder:text-muted-foreground 
            focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-muted-foreground 
            disabled:cursor-not-allowed disabled:opacity-50 
            dark:shadow-[0px_0px_1px_1px_var(--neutral-700)] 
            group-hover/input:shadow-none transition duration-400 
            p-4 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70`, 
            className
          )}
          style={{
            overflow: "hidden",
            resize: "none",
            minHeight: minHeight, // Default height set to one line height with padding
          }}
          ref={(el) => {
            textareaRef.current = el;
            if (typeof ref === "function") {
              ref(el);
            } else if (ref) {
              (
                ref as React.MutableRefObject<HTMLTextAreaElement | null>
              ).current = el;
            }
          }}
          value={value}
          onChange={(e) => {
            if (onChange) onChange(e);
            checkOverflow(); // Recheck for overflow after content changes
            adjustHeight(); // Adjust height after each change in content
          }}
          {...props}
        />

        {shouldShowToggle && (
          <button
            type="button"
            onClick={() => {
              setIsExpanded((prev) => !prev); // Toggle expansion state
              adjustHeight(); // Adjust height after expansion toggle
            }}
            className="absolute bottom-2 right-2 flex items-center bg-slate-300 text-black font-bold rounded-sm"
          >
            {isExpanded ? (
              <ChevronUpIcon className="w-4 h-4 font-bold" />
            ) : (
              <ChevronDownIcon className="w-4 h-4 font-bold" />
            )}
          </button>
        )}
      </motion.div>
    );
  }
);

UpdatedTextArea.displayName = "UpdatedTextArea";

export { UpdatedTextArea };
