"use client"
import React, { useState, useEffect } from "react";
import Joyride from "react-joyride";

const Walkthrough = ({ steps }) => {
  const [isRunning, setIsRunning] = useState(false);

  // Automatically start the walkthrough on mount
  useEffect(() => {
    const timer = setTimeout(() => setIsRunning(true), 1000);
    return () => clearTimeout(timer); // Cleanup the timer on unmount
  }, []);

  const handleJoyrideCallback = (data) => {
    const { status } = data;
    if (["finished", "skipped"].includes(status)) {
      setIsRunning(false); // Stop the walkthrough if completed or skipped
    }
  };

  return (
    <Joyride
      steps={steps}
      run={isRunning}
      continuous
      showSkipButton
      callback={handleJoyrideCallback}
      styles={{
        options: {
          arrowColor: "#ffffff",
          backgroundColor: "#2d3748", // Dark background for tooltips
          overlayColor: "rgba(0, 0, 0, 0.7)", // Darker overlay
          primaryColor: "#48bb78", // Green color for next button
          textColor: "#e2e8f0", // Light text color
          zIndex: 10000,
          width: "300px", // Custom tooltip width
          // Prevent tooltip from limiting width
        },
        buttonClose: {
          color: "#e53e3e", // Custom close button color (red)
          fontSize: "16px",
        },
        buttonNext: {
          backgroundColor: "#48bb78", // Green next button color
          color: "#ffffff",
          borderRadius: "4px",
        },
        buttonBack: {
          backgroundColor: "#e2e8f0", // Light back button
          color: "#2d3748",
        },
        tooltip: {
          backgroundColor: "#1a202c", // Dark background for tooltips
          color: "#e2e8f0", // Light color for text in tooltips
          fontSize: "16px",
        },
      }}
      locale={{
        close: "❌", // Custom close icon (use any icon you prefer)
        next: "Next →",
        back: "← Back",
        last: "Finish",
        skip: "Skip",
      }}
    />
  );
};

export default Walkthrough;
