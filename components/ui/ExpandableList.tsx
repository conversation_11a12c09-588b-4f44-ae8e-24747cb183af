"use client";
import React, { useState } from "react";
import RentListing from "./rentListing";

const ExpandableList = ({ items, user }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const itemsToShow = isExpanded ? items : items.slice(0, 6);

  return (
    <div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {itemsToShow.map((rentproduct, i) => (
          <RentListing
            key={`product-${i}`}
            rentproduct={rentproduct}
            index={i}
            user={user}
            className={"m-2 h-64 sm:h-60 md:h-72 lg:h-56"}
            component="userPortfolio"
          />
        ))}
      </div>
      {items.length > 6 && (
        <div className="text-center mt-6 flex items-center justify-center">
          <div className="flex-grow border-t border-gray-300"></div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-white font-bold cursor-pointer mx-4"
          >
            {isExpanded ? "▲ View Less" : "▼ View More"}
          </button>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>
      )}
    </div>
  );
};

export default ExpandableList;
