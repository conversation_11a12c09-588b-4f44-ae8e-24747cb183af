"use client";
import React, { useState } from "react";
import { Button } from "./button";

interface FilterOption {
  label: string;
  value: string;
  checked: boolean;
}

export interface FilterSection {
  title: string;
  options: FilterOption[];
}

interface FilterComponentProps {
  sections: FilterSection[];
  onClearFilters: () => void;
  onOptionChange: (
    sectionTitle: string,
    optionValue: string,
    checked: boolean
  ) => void;
  displayProducts: boolean;
  onToggleDisplay: () => void;
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  sections,
  onClearFilters,
  onOptionChange,
  displayProducts,
  onToggleDisplay,
}) => {
  const [isOpen, setIsOpen] = useState(false);
// console.log("sections==>",sections)
  const handleCheckboxChange = (
    sectionTitle: string,
    optionValue: string,
    checked: boolean
  ) => {
    onOptionChange(sectionTitle, optionValue, checked);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="text-white justify-between">
      {/* Dropdown button for medium and small devices */}
      <div className="block lg:hidden mb-4">
        <button
          className="bg-transparent border border-white text-white py-2 px-4 rounded w-full text-sm"
          onClick={toggleDropdown}
        >
          {isOpen ? "Filters ▲" : "Filters ▼"}
        </button>
      </div>

      {/* Filters container for large devices */}
      <div
        className={`hidden lg:block ${isOpen ? "block" : "hidden"} lg:mt-0 mt-4`}
      >
        <button
          className="bg-transparent border border-white text-white py-2 px-2 rounded mb-4 w-full"
          onClick={onClearFilters}
        >
          Reset Filters ✖
        </button>
        {sections.map((section) => (
          <div className="mb-4" key={section.title}>
            <h3 className="font-bold mb-2">{section.title}</h3>
            <div className="flex flex-wrap gap-2">
              {section.options.map((option) => (
                <div
                  key={option.value}
                  className={`cursor-pointer flex items-center justify-center px-3 py-1 border rounded-lg text-xs font-semibold ${
                    option.checked
                      ? "bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 text-white border-indigo-600"
                      : "bg-white text-dash border-indigo-400"
                  }`}
                  onClick={() =>
                    handleCheckboxChange(
                      section.title,
                      option.value,
                      !option.checked
                    )
                  }
                >
                 <span className="first-letter:uppercase"> {option.label} </span>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* <div className="mb-4">
          <h3 className="font-bold mb-2">Assets Type</h3>
          <div className="flex items-center mb-2">
            <button
              className={`py-2 px-4 rounded w-full ${displayProducts ? 'bg-gray-300 text-black' : 'bg-blue-500 text-white'}`}
              onClick={onToggleDisplay}
            >
              {displayProducts ? 'Show Rapps' : 'Show Products'} 
            </button>
          </div>
        </div> */}
      </div>

      {/* Popover for medium and small devices */}
      {isOpen && (
        <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-indigo-800 text-white p-4 rounded-md w-11/12 max-w-lg">
            <button
              className="bg-transparent border border-white text-whit py-2 px-4 rounded mb-4 w-full"
              onClick={onClearFilters}
            >
              Reset Filters ✖
            </button>
            {/* {sections.map((section) => (
              <div className="mb-4" key={section.title}>
                <h3 className="font-bold mb-2">{section.title}</h3>
                {section.options.map((option) => (
                  <label className="flex items-center mb-2" key={option.value}>
                    <input
                      type="checkbox"
                      className="form-checkbox text-white bg-white border-gray-600"
                      checked={option.checked}
                      onChange={(e) => handleCheckboxChange(section.title, option.value, e.target.checked)}
                    />
                    <span className="ml-2">{option.label}</span>
                  </label>
                ))}
              </div>
            ))} */}
            {/* <div className="grid grid-cols-2 gap-4">
              {sections.map((section) => (
                <div className="mb-4" key={section.title}>
                  <h3 className="font-bold mb-2">{section.title}</h3>
                  <div>
                    {section.options.map((option) => (
                      <label
                        className="flex items-center mb-2"
                        key={option.value}
                      >
                        <input
                          type="checkbox"
                          className="form-checkbox text-white bg-white border-gray-600"
                          checked={option.checked}
                          onChange={(e) =>
                            handleCheckboxChange(
                              section.title,
                              option.value,
                              e.target.checked
                            )
                          }
                        />
                        <span className="ml-2">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div> */}

            <div>
              <style jsx>{`
                #filterScroll::-webkit-scrollbar {
                  display: none;
                }

                #filterScroll {
                  -ms-overflow-style: none;
                  scrollbar-width: none;
                }
              `}</style>
              <div className="grid grid-cols-2 gap-4">
                {sections.map((section) => (
                  <div className="mb-4" key={section.title}>
                    <h3 className="font-bold mb-2">{section.title}</h3>
                    <div
                      id="filterScroll"
                      className="h-36 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-300"
                    >
                      {section.options.map((option) => (
                        <label
                          className="flex items-center mb-2"
                          key={option.value}
                        >
                          <input
                            type="checkbox"
                            className="form-checkbox text-white bg-white border-gray-600"
                            checked={option.checked}
                            onChange={(e) =>
                              handleCheckboxChange(
                                section.title,
                                option.value,
                                e.target.checked
                              )
                            }
                          />
                          <span className="ml-2 first-letter:uppercase">{option.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <h3 className="font-bold mb-2">Assets Type</h3>
              <div className="flex items-center gap-3 mb-2">
                <Button
                  variant={`${displayProducts ? "gradient" : "white"}`}
                  className="w-full p-2 text-center rounded-lg"
                  onClick={onToggleDisplay}
                >
                  AI Apps
                </Button>
                <Button
                  variant={`${displayProducts ? "white" : "gradient"}`}
                  className="w-full p-2 text-center rounded-lg"
                  onClick={onToggleDisplay}
                >
                  Products
                </Button>
              </div>
            </div>
            <button
              className="bg-transparent border border-white text-white py-2 px-4 rounded w-full"
              onClick={toggleDropdown}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterComponent;
