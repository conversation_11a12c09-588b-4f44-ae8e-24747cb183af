import { cn } from "@/lib/utils";

export const BentoGrid = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "grid md:auto-rows-[18rem] grid-cols-1 md:grid-cols-3 gap-4 max-w-7xl mx-auto ",
        className
      )}
    >
      {children}
    </div>
  );
};

export const  BentoGridItem = ({
  className,
  title,
  description,
  header,
  icon,
  onClick,
}: {
  className?: string;
  title?: string;
  description?: String;
  header?: React.ReactNode;
  icon?: React.ReactNode;
  onClick?: () => void;
}) => {

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };
  return (
    <div
      className={cn(
        "row-span-1 rounded-xl group/bento hover:shadow-xl transition duration-200 shadow-input dark:shadow-none p-4 dark:bg-black dark:border-white/[0.2] bg-white border border-transparent justify-between flex flex-col space-y-4",
        className
      )}
    onClick={handleClick}
    >
      
      {header}
      <div className="text-white group-hover/bento:translate-x-2 transition duration-200">
        {icon}
        <div className="font-sans font-bold mb-2 mt-2">
        {title?.substring(0, 30) + "..."}
        </div>
        <div className="font-sans font-normal text-xs ">
        {description?.substring(0, 100) + "..."} 
        </div>

      </div>
    </div>
  );
};
