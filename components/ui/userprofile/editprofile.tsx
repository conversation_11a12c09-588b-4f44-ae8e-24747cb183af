import React from "react";

interface MenuItemProps {
    visible: boolean;
    closeModel: () => void;
}
function EditProfile({ visible,closeModel }: MenuItemProps) {
  return (
    <>
  {visible && 
  <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-800 bg-opacity-75 transition-opacity duration-300">
    <div className="bg-card rounded-lg shadow-lg w-full max-w-xl transform transition-transform duration-300 scale-100">
      <div className="flex justify-between items-center p-4 border-b">
        <h5 className="text-lg font-medium">Modal title</h5>
        <button
          type="button"
          className="text-gray-400 hover:text-gray-500"
          aria-label="Close"
          onClick={closeModel}
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>
      <div className="p-4 space-y-4">
        {/* Cover Photo Upload */}
        <div>
          <label className="block mb-2 text-sm font-medium text-gray-700">Cover Photo</label>
          <input
            type="file"
            className="block w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-full file:border-0
            file:text-sm file:font-semibold
            file:bg-blue-50 file:text-blue-700
            hover:file:bg-blue-100"
          />
        </div>
        
        {/* Profile Image Upload */}
        <div>
          <label className="block mb-2 text-sm font-medium text-gray-700">Profile Image</label>
          <input
            type="file"
            className="block w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-full file:border-0
            file:text-sm file:font-semibold
            file:bg-blue-50 file:text-blue-700
            hover:file:bg-blue-100"
          />
        </div>

        {/* Username Input */}
        <div>
          <label className="block mb-2 text-sm font-medium text-gray-700">Username</label>
          <input
            type="text"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your username"
          />
        </div>

        {/* General Information */}
        <div>
          <h6 className="text-lg font-medium mb-2">General Information</h6>
          <label className="block mb-2 text-sm font-medium text-gray-700">Education</label>
          <input
            type="text"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your education"
          />
        </div>

        {/* Work Experience */}
        <div>
          <h6 className="text-lg font-medium mb-2">Work Experience</h6>
          <label className="block mb-2 text-sm font-medium text-gray-700">Work Experience</label>
          <input
            type="text"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your work experience"
          />
        </div>

        {/* Skills */}
        <div>
          <h6 className="text-lg font-medium mb-2">Skills</h6>
          <label className="block mb-2 text-sm font-medium text-gray-700">Skills</label>
          <select className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option>JavaScript</option>
            <option>Python</option>
            <option>Java</option>
            <option>React</option>
            <option>Node.js</option>
          </select>
        </div>

        {/* Projects */}
        <div>
          <h6 className="text-lg font-medium mb-2">Projects</h6>
          <label className="block mb-2 text-sm font-medium text-gray-700">Projects</label>
          <input
            type="text"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your projects"
          />
        </div>
      </div>
      <div className="flex justify-end p-4 border-t">
        <button
          type="button"
          className="bg-gray-500 text-white px-4 py-2 rounded mr-2"
          onClick={closeModel}
        >
          Close
        </button>
        <button
          type="button"
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Save changes
        </button>
      </div>
    </div>
  </div>
}

    </>
  );
}

export default EditProfile;
