"use client";

import { useState } from "react";
import {
  FacebookIcon,
  GithubIcon,
  InstagramIcon,
  PencilIcon,
  TwitterIcon,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";

interface MenuItemProps {
  user: any;
}

type FormData = {
  [key: string]: any;
};

const SocialMediaIcon = ({ user }: MenuItemProps) => {
  const [isEdit, setIsEdit] = useState(false);
  const { register, handleSubmit, setValue, reset, formState } = useForm({
    defaultValues: {
      facebook: user?.socialMediaLinks?.facebook ?? "",
      twitter: user?.socialMediaLinks?.twitter ?? "",
      discord: user?.socialMediaLinks?.discord ?? "",
      instagram: user?.socialMediaLinks?.instagram ?? "",
      github: user?.socialMediaLinks?.github ?? "",
    },
  });

  const onSubmit: SubmitHandler<FormData> = async (formData) => {
    try {
      const req = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${user?.id}`, {
        method: "PATCH",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          socialMediaLinks: {
            facebook: formData.facebook,
            twitter: formData.twitter,
            discord: formData.discord,
            instagram: formData.instagram,
            github: formData.github,
          },
        }),
      });
      const data = await req.json();
      toast.success("User Updated Successfully");
    } catch (err) {
      console.log(err);
      toast.error("User Update Failed, Please try again");
    }
  };

  return (
    <>
      <div className="bg-card rounded-xl px-4 py-10 mt-4">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-8 w-full">
            <div className="flex justify-between items-center">
              <h2 className="px-2 text-2xl font-bold text-navy-700 dark:text-white">
                Social Media Link
              </h2>
              {isEdit ? (
                <Button
                  type="button"
                  onClick={() => setIsEdit((prev) => !prev)}
                  className="h-7"
                >
                  Update
                </Button>
              ) : (
                <Button
                  type="submit"
                  onClick={() => setIsEdit((prev) => !prev)}
                  className="h-7 gap-1"
                >
                  Edit
                  <PencilIcon className="w-3 cursor-pointer" />
                </Button>
              )}
            </div>
          </div>
          {/* Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            <div className="flex justify-between rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <div className="flex flex-col items-start justify-center w-full">
                <div className="flex w-full items-center">
                  <FacebookIcon className="w-6 h-6 mr-2" />
                  <input
                    className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                    placeholder="Facebook"
                    type="text"
                    readOnly={!isEdit}
                    {...register("facebook")}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-between rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <div className="flex flex-col items-start justify-center w-full">
                <div className="flex w-full items-center">
                <svg
                  className="w-6 h-6 mr-2"
                  viewBox="0 0 396 396"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M301.026 37.125H355.608L236.362 173.415L376.645 358.875H266.805L180.774 246.394L82.335 358.875H27.72L155.265 213.097L20.691 37.125H133.32L211.084 139.936L301.026 37.125ZM281.869 326.205H312.114L116.886 68.079H84.4305L281.869 326.205Z" />
                </svg>
                  <input
                    className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                    placeholder="Twitter"
                    type="text"
                    readOnly={!isEdit}
                    {...register("twitter")}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-between rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <div className="flex flex-col items-start justify-center w-full">
                <div className="flex w-full items-center">
                <svg
                className="w-6 h-6 mr-2"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path d="M20.317 4.369a19.791 19.791 0 00-4.885-1.515.074.074 0 00-.079.037 13.9 13.9 0 00-.608 1.248 19.22 19.22 0 00-5.785 0 13.365 13.365 0 00-.617-************* 0 00-.079-.037c-1.692.295-3.343.82-4.885 1.515a.064.064 0 00-.029.025C.533 9.473-.32 14.444.099 19.378a.08.08 0 00.031.056 19.982 19.982 0 005.996 ************* 0 00.084-.028 13.452 13.452 0 001.144-************ 0 00-.041-.108 13.123 13.123 0 01-1.872-.903.078.078 0 01-.008-.13c.125-.094.25-.192.371-.291a.075.075 0 01.077-.01c3.927 1.832 8.18 1.832 12.061 0a.075.075 0 01.079.009c.122.1.247.198.372.291a.078.078 0 01-.006.13 12.62 12.62 0 01-1.873.903.077.077 0 00-.04.109c.362.654.765 1.288 1.144 1.87a.078.078 0 00.084.028 19.953 19.953 0 006.001-3.044.08.08 0 00.031-.056c.512-5.308-.883-10.258-4.452-14.984a.067.067 0 00-.03-.025zM8.02 15.331c-1.185 0-2.167-1.086-2.167-2.419 0-1.333.955-2.419 2.167-2.419 1.217 0 2.202 1.092 2.173 2.419 0 1.333-.955 2.419-2.173 2.419zm7.959 0c-1.185 0-2.167-1.086-2.167-2.419 0-1.333.955-2.419 2.167-2.419 1.217 0 2.202 1.092 2.173 2.419 0 1.333-.955 2.419-2.173 2.419z" />
              </svg>
                  <input
                    className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                    placeholder="Discord"
                    type="text"
                    readOnly={!isEdit}
                    {...register("discord")}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-between rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <div className="flex flex-col items-start justify-center w-full">
                <div className="flex w-full items-center">
                  <InstagramIcon className="w-6 h-6 mr-2" />
                  <input
                    className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                    placeholder="Instagram"
                    type="text"
                    readOnly={!isEdit}
                    {...register("instagram")}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-between rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <div className="flex flex-col items-start justify-center w-full">
                <div className="flex w-full items-center">
                  <GithubIcon className="w-6 h-6 mr-2" /> 
                  <input
                    className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                    placeholder="Github"
                    type="text"
                    readOnly={!isEdit}
                    {...register("github")}
                  />
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};
export default SocialMediaIcon;
