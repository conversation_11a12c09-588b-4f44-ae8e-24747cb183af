"use client"
import Image from "next/image";
import { PencilIcon } from "lucide-react";
import { useEffect, useState } from "react";
import Link from "next/link";
import image1 from "../../../public/png/avatar/1.png";
import image2 from "../../../public/png/avatar/2.png";
import image3 from "../../../public/png/avatar/3.png";
import image4 from "../../../public/png/avatar/4.png";
import coinImage from "../../../public/img/coin-png.png";
import { toast } from "sonner";
import axios from "axios";
import { Button } from "../button";
import { useForm, SubmitHandler } from "react-hook-form";
import debounce from "lodash.debounce";
import { useCoinBalanceContext } from "../../coin-balance-initializer";

interface UserProfileProps {
  user: any;
}

interface FormData {
  coverImage: FileList;
  profileImage: FileList;
  user_name: string;
  // other fields...
}

const UserProfile = ({ user }: UserProfileProps) => {
  const [color, setColor] = useState("");
  const [coverImageUrl, setCoverImageUrl] = useState(user?.coverImage);
  const [profileImagePreview, setprofileImagePreview] = useState<string | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [spinLoading, setSpinLoading] = useState<boolean>(false);
  const [showUpdateButton, setShowUpdateButton] = useState<boolean>(false);
  const [username, setUsername] = useState(user?.name);
  const [rappCount, setRappCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [fnLoading, setFnLoading] = useState(false);
  const [productCount, setProductCount] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [usernameExists, setUsernameExists] = useState(false);
  const images = [image1, image2, image3, image4];
  const colors = ["#6EACDA", "#E3A5C7", "#E2E2B6", "#088395", "#824D74"];

  // Context hook for coin balance - अब automatic fetching नहीं है
  const { coinBalance } = useCoinBalanceContext();

  const userId = user.id;
  const email = user?.email?.split("@")[0];

  const {
    register,
    handleSubmit,
    formState: { errors },
    clearErrors
  } = useForm<FormData>();

  const getRandomImage = () => {
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  const getRandomColor = () => {
    const randomIndex = Math.floor(Math.random() * colors.length);
    return colors[randomIndex];
  };

  const randomImage = getRandomImage();

  useEffect(() => {
    setColor(getRandomColor());
    // Fetch initial coin balance
    fetchCoinBalance();
  }, []);

  useEffect(() => {
    const fetchRappCount = async () => {
      try {
        const { data } = await axios.get(`/api/users/getRappCount/${userId}`);
        const count = data.count.totalDocs;
        setRappCount(count);
      } catch (err) {
        console.error("Error fetching rapps:", err);
        setLoading(false);
      } finally {
        setLoading(false);
      }
    };
    fetchRappCount();
  }, [userId]);

  useEffect(() => {
    const fetchProductCount = async () => {
      try {
        const { data } = await axios.get(
          `/api/users/getProductCount/${userId}`
        );
        const count = data.count.totalDocs;
        setProductCount(count);
      } catch (err) {
        console.error("Error fetching rapps:", err);
        setLoading(false);
      } finally {
        setLoading(false);
      }
    };
    fetchProductCount();
  }, [userId]);

  const follower = user?.followersLength;
  const following = user?.followingLength;
  const profileImage = user?.profileImage ?? randomImage;

  const facebookLink = user?.socialMediaLinks?.facebook
    ? user.socialMediaLinks.facebook.startsWith("http")
      ? user.socialMediaLinks.facebook
      : `https://${user.socialMediaLinks.facebook}`
    : "#";

  const instagramLink = user?.socialMediaLinks?.instagram
    ? user.socialMediaLinks.instagram.startsWith("http")
      ? user.socialMediaLinks.instagram
      : `https://${user.socialMediaLinks.instagram}`
    : "#";

  const twitterLink = user?.socialMediaLinks?.twitter
    ? user.socialMediaLinks.twitter.startsWith("http")
      ? user.socialMediaLinks.twitter
      : `https://${user.socialMediaLinks.twitter}`
    : "#";

  const githubLink = user?.socialMediaLinks?.github
    ? user.socialMediaLinks.github.startsWith("http")
      ? user.socialMediaLinks.github
      : `https://${user.socialMediaLinks.github}`
    : "#";

  const discordLink = user?.socialMediaLinks?.discord
    ? user.socialMediaLinks.discord.startsWith("http")
      ? user.socialMediaLinks.discord
      : `https://${user.socialMediaLinks.discord}`
    : "#";

  const handleCoverImageFileChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setShowUpdateButton(true);
    const file = e.target.files?.[0] || null;

    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setCoverImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setCoverImagePreview(null);
    }
  };

  const handleProfileImageFileChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setShowUpdateButton(true);
    const file = e.target.files?.[0] || null;

    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setprofileImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setprofileImagePreview(null);
    }
  };

  const handleOnSubmitImage: SubmitHandler<FormData> = async (
    formData,
    event
  ) => {
    if (!user) {
      toast.error("Please login to use this space");
      return;
    }

    setSpinLoading(true);

    if (formData.coverImage && formData.coverImage.length > 0) {
      const fileFormData = new FormData();
      Array.from(formData.coverImage).forEach((file) => {
        fileFormData.append("file", file);
      });

      try {
        const mediaRes = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/media`,
          {
            method: "POST",
            credentials: "include",
            body: fileFormData,
          }
        );

        const mediaData = await mediaRes.json();

        if (mediaRes.ok) {
          try {
            const uploadedFilesIds = mediaData.doc.id;
            const body = { coverImage: uploadedFilesIds };

            const req = await fetch(
              `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${user?.id}`,
              {
                method: "PATCH",
                credentials: "include",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify(body),
              }
            );
            toast.success("Cover Image Updated Successfully");
            setTimeout(() => {
              window.location.reload();
            }, 2000);
            setSpinLoading(false);
            setShowUpdateButton(false);
          } catch (error) {
            console.error("Error updating user profile:", error);
            toast.error("Failed to update profile image. Please try again.");
            setSpinLoading(false);
          }
        } else {
          console.error("Error uploading media:", mediaData);
          toast.error("Media upload failed. Please try again.");
        }
      } catch (mediaError) {
        console.error("Media upload error:", mediaError);
        toast.error("Media upload failed. Please try again.");
      }
    } else if (formData.profileImage && formData.profileImage.length > 0) {
      const fileFormData = new FormData();
      Array.from(formData.profileImage).forEach((file) => {
        fileFormData.append("file", file);
      });

      try {
        const mediaRes = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/media`,
          {
            method: "POST",
            credentials: "include",
            body: fileFormData,
          }
        );

        const mediaData = await mediaRes.json();

        if (mediaRes.ok) {
          try {
            const uploadedFilesIds = mediaData.doc.id;
            const body = { profileImage: uploadedFilesIds };

            const req = await fetch(
              `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${user?.id}`,
              {
                method: "PATCH",
                credentials: "include",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify(body),
              }
            );
            toast.success("Profile Image Updated Successfully");
            setTimeout(() => {
              window.location.reload();
            }, 2000);
            setSpinLoading(false);
            setShowUpdateButton(false);
          } catch (error) {
            console.error("Error updating user profile:", error);
            toast.error("Failed to update profile image. Please try again.");
            setSpinLoading(false);
          }
        } else {
          console.error("Error uploading media:", mediaData);
          toast.error("Media upload failed. Please try again.");
        }
      } catch (mediaError) {
        console.error("Media upload error:", mediaError);
        toast.error("Media upload failed. Please try again.");
      }
    }
  };

  const displaycoinBalance = coinBalance.toFixed(1);

  const userInfo = [
    {
      value: follower,
      label: "Followers",
    },
    {
      value: following,
      label: "Following",
    },
    {
      value: displaycoinBalance,
      label: "Credits",
      image: (
        <img
          src={coinImage.src}
          className="w-5 md:w-6 h-5 md:h-7 mt-1 md:mt-0 "
          alt="Coin"
        />
      ),
    },
    {
      value: productCount,
      label: "Assests",
    },
    {
      value: rappCount,
      label: "AI Apps",
    },
  ];

  const checkUsernameExists = async (user_name: string) => {
    try {
      const { data }: { data: CustomEndPointResponse } = await axios.post(
        `/api2/user/check-username`,
        {
          user_name,
        }
      );
      if (data.success === false) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error checking username:", error);
      return false;
    }
  };

  const debouncedCheckUsernameExists = debounce(async (user_name) => {
    setLoading(true);
    try {
      const exists = await checkUsernameExists(user_name);
      setUsernameExists(exists); // Update the state based on response
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error checking username:", error);
    }
  }, 200);

  const handleSubmitUserName: SubmitHandler<FormData> = async (
    formData,
    event
  ) => {
    if (!user) {
      toast.error("Please login to use this space");
      return;
    }

    setFnLoading(true);

    try {
      const req = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${user?.id}`,
        {
          method: "PATCH",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_name: formData.user_name,
          }),
        }
      );
      const data = await req.json();
      setIsEditing(false);
      setFnLoading(false);
      if(req.ok){
        toast.success("Username Updated Successfully");
      }else{
        toast.error("Failed to update username");
      }
    } catch (err) {
      console.log(err);
      toast.error("User Update Failed, Please try again");
    }
  };

  return (
    <>
      <div className="bg-card rounded-xl pb-3 mb-4">
        <div className="rounded-xl pb-3 mb-4">
          <form onSubmit={handleSubmit(handleOnSubmitImage)}>
            <div
              className="relative mt-1 flex h-40 w-full justify-center rounded-t-lg bg-cover pb-6"
              style={{
                backgroundImage: coverImagePreview
                  ? `url(${coverImagePreview})`
                  : `url(${coverImageUrl})`,
                backgroundColor: color,
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <input
                type="file"
                className="hidden"
                id="coverImageInput"
                multiple
                {...register("coverImage", {
                  onChange: (e) => handleCoverImageFileChange(e),
                })}
              />
              <label
                htmlFor="coverImageInput"
                className="absolute right-[8px] top-[5px] cursor-pointer"
              >
                <PencilIcon fill="black" className="w-4" />
              </label>

              <div className="absolute -bottom-12 flex h-[87px] w-[87px] items-center justify-center rounded-full border-[4px] border-white bg-pink-400 dark:!border-navy-700">
                <div className="w-full h-full relative">
                  <input
                    type="file"
                    className="hidden"
                    id="profileImageInput"
                    multiple
                    {...register("profileImage", {
                      onChange: (e) => handleProfileImageFileChange(e),
                    })}
                  />
                  <label
                    htmlFor="profileImageInput"
                    className="absolute right-[-3px] bottom-[6px] text-[#1e1b4b] bg-white rounded-[5px] text-[12px] px-[5px] cursor-pointer"
                  >
                    <PencilIcon className="w-[0.7rem] h-[1.3rem]" />
                  </label>

                  <Image
                    width="87"
                    height="87"
                    className="h-full w-full rounded-full object-cover"
                    src={profileImagePreview ?? profileImage}
                    alt="Profile Image"
                  />
                </div>
              </div>

              {showUpdateButton && (
                <div className="absolute cursor-pointer rounded-[5px] bg-white -bottom-[32px] right-[5px]">
                  <Button
                    type="submit"
                    className="text-[#1e1b4b] text-[12px] h-8 px-2"
                  >
                    {spinLoading ? "Updating Image..." : "Update Image"}
                  </Button>
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Name and position */}
        <div className="mt-14 flex flex-col items-center">
          <form onSubmit={handleSubmit(handleSubmitUserName)}>
            <div className="flex">
              {isEditing ? (
                <div>
                  <div className="flex">
                    <input
                      type="text"
                      {...register("user_name", {
                        required: {
                          value: true,
                          message: "Please enter a username",
                        },
                        minLength: {
                          value: 4,
                          message:
                            "Username must be at least 4 characters long",
                        },
                        validate: () => {
                          if (usernameExists) return "Username already exists";
                          return true;
                        },
                      })}
                      onChange={(e) => {
                        const enteredUsername = e.target.value;
                        setUsername(enteredUsername);
                        if (
                          errors.user_name?.type === "required" &&
                          enteredUsername.length > 0
                        ) {
                          clearErrors("user_name");
                        }
                        debouncedCheckUsernameExists(enteredUsername);
                      }}
                      value={username}
                      onKeyDown={(e) => {
                        if (e.key === ' ') {
                          e.preventDefault() // Prevent space key input
                        }
                      }}
                      className="border border-gray-300 px-2 py-1 rounded text-indigo-900"
                      autoFocus
                    />
                    <button
                      type="submit"
                      className="ml-2 text-white bg-blue-500 px-3 py-1 rounded"
                    >
                      {fnLoading ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                  <div className="min-h-[20px]">
                    {loading && (
                      <p className="text-yellow-200 text-sm">
                        checking username...
                      </p>
                    )}
                    {!loading && usernameExists && (
                      <p className="text-yellow-200 text-xs mt-1">
                        Username already exists. Please choose another.
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                <h4 className="text-xl font-bold">{username}</h4>
              )}

              {!isEditing && (
                <button
                  type="button"
                  onClick={() => setIsEditing(true)}
                  className="ml-2"
                >
                  <PencilIcon className="w-4 h-5" />
                </button>
              )}
            </div>
          </form>
          <div className="flex mt-8 lg:mt-4 items-center lg:items-start lg:justify-start justify-center space-x-6 text-primary">
            <Link
              className="hover:text-indigo-500"
              href={facebookLink}
              target="_blank"
              rel="noreferrer"
            >
              <span className="sr-only"> Facebook </span>
              <svg
                className="w-6 h-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
            <Link
              className="hover:text-indigo-500"
              href={instagramLink}
              target="_blank"
              rel="noreferrer"
            >
              <span className="sr-only"> Instagram </span>
              <svg
                className="w-6 h-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
            <Link
              className="hover:text-indigo-500"
              href={twitterLink}
              target="_blank"
              rel="noreferrer"
            >
              <span className="sr-only"> Twitter </span>
              <svg
                className="w-5 h-5"
                viewBox="0 0 396 396"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M301.026 37.125H355.608L236.362 173.415L376.645 358.875H266.805L180.774 246.394L82.335 358.875H27.72L155.265 213.097L20.691 37.125H133.32L211.084 139.936L301.026 37.125ZM281.869 326.205H312.114L116.886 68.079H84.4305L281.869 326.205Z" />
              </svg>
            </Link>
            <Link
              className="hover:text-indigo-500"
              href={githubLink}
              target="_blank"
              rel="noreferrer"
            >
              <span className="sr-only"> GitHub </span>
              <svg
                className="w-6 h-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
            <Link
              className="hover:text-indigo-500"
              href={discordLink}
              target="_blank"
              rel="noreferrer"
            >
              <span className="sr-only"> Discord </span>
              <svg
                className="w-6 h-6"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path d="M20.317 4.369a19.791 19.791 0 00-4.885-1.515.074.074 0 00-.079.037 13.9 13.9 0 00-.608 1.248 19.22 19.22 0 00-5.785 0 13.365 13.365 0 00-.617-************* 0 00-.079-.037c-1.692.295-3.343.82-4.885 1.515a.064.064 0 00-.029.025C.533 9.473-.32 14.444.099 19.378a.08.08 0 00.031.056 19.982 19.982 0 005.996 ************* 0 00.084-.028 13.452 13.452 0 001.144-************ 0 00-.041-.108 13.123 13.123 0 01-1.872-.903.078.078 0 01-.008-.13c.125-.094.25-.192.371-.291a.075.075 0 01.077-.01c3.927 1.832 8.18 1.832 12.061 0a.075.075 0 01.079.009c.122.1.247.198.372.291a.078.078 0 01-.006.13 12.62 12.62 0 01-1.873.903.077.077 0 00-.04.109c.362.654.765 1.288 1.144 1.87a.078.078 0 00.084.028 19.953 19.953 0 006.001-3.044.08.08 0 00.031-.056c.512-5.308-.883-10.258-4.452-14.984a.067.067 0 00-.03-.025zM8.02 15.331c-1.185 0-2.167-1.086-2.167-2.419 0-1.333.955-2.419 2.167-2.419 1.217 0 2.202 1.092 2.173 2.419 0 1.333-.955 2.419-2.173 2.419zm7.959 0c-1.185 0-2.167-1.086-2.167-2.419 0-1.333.955-2.419 2.167-2.419 1.217 0 2.202 1.092 2.173 2.419 0 1.333-.955 2.419-2.173 2.419z" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Post followers */}
        <div className="my-4 flex gap-4 md:!gap-14 justify-center text-white">
          {userInfo.map((info, index) => {
            return (
              <div
                key={index}
                className="flex flex-col items-center justify-center"
              >
                <div className="flex items-center">
                  <h4 className="text-lg md:text-2xl font-bold text-navy-700 dark:text-white">
                    {info.value}
                  </h4>
                  {info.image && info.image}
                </div>
                <p className="text-xs md:text-sm font-normal text-slate-400">
                  {info.label}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};
export default UserProfile;