"use client";

import { ProjectBox } from "./projectbox";
import Link from "next/link";

interface MenuItemProps {
  user: any;
}

const AllProject = ({ user }: MenuItemProps) => {
  return (
    <>
      <div className="bg-card rounded-xl px-4 py-10 my-4">
        <div className="mb-8 w-full px-2">
          <div className="flex justify-between align-items-center text-xl font-bold text-navy-700 dark:text-white">
            <h2>Recent Projects</h2>
            {/* <Link
              className="w-auto bg-gradient-to-r from-blue-600 to-violet-600 rounded text-white hover:text-white hover:-translate-y-1 scale-105 py-2 px-5 text-sm rounded transition-transform duration-300"
              href="/recentproject"
            >
              View all
            </Link> */}
          </div>
          <p className="mt-2 text-base text-slate-400">
            Here you can find more details about your projects.
          </p>
        </div>

        <ProjectBox recentProject={true} user={user.products}/>
      </div>
    </>
  );
};
export default AllProject;
