"use client";

import { useState } from "react";
import { PencilIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";

interface MenuItemProps {
  user: any;
}
type FormData = {
  [key: string]: any;
};

const interestsTags = [
  { value: "prompt engineering", label: "Prompt Engineering" },
  { value: "content creation", label: "Content Creation" },
  { value: "assets", label: "Assets" },
  { value: "ai applications", label: "AI Applications" },
  { value: "learning and courses", label: "Learning and Courses" },
  { value: "blogs", label: "Blogs" },
  { value: "bounties", label: "Bounties" },
  { value: "community collaboration", label: "Community Collaboration" },
];

const GeneralInfo = ({ user }: MenuItemProps) => {
  const [isEdit, setIsEdit] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [interests, setInterests] = useState(user?.genInfo?.interests || []);

  const handleSelect = (value) => {
    if (interests.includes(value)) {
      setInterests(interests.filter((item) => item !== value)); // Remove if already selected
    } else {
      setInterests([...interests, value]); // Add new selection
    }
  };

  const { register, handleSubmit, setValue, reset, formState } = useForm({
    defaultValues: {
      age: user?.genInfo?.age ?? "",
      education: user?.genInfo?.education ?? "",
      gender: user?.genInfo?.gender ?? "",
      skills: user?.genInfo?.skills ?? "",
      workExperience: user?.genInfo?.workExperience ?? "",
      profession: user?.genInfo?.profession ?? "",
      interests: user?.genInfo?.interests ?? [],
    },
  });

  const onSubmit: SubmitHandler<FormData> = async (formData) => {

    try {
      const req = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${user?.id}`,
        {
          method: "PATCH",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            genInfo: {
              age: formData.age,
              education: formData.education,
              gender: formData.gender,
              skills: formData.skills,
              workExperience: formData.workExperience,
              profession: formData.profession,
              interests: interests,
            },
          }),
        }
      );
      const data = await req.json();

      toast.success("User Updated Successfully");
    } catch (err) {
      console.log(err);
      toast.error("User Update Failed, Please try again");
    }
  };

  return (
    <>
      <div className="bg-card rounded-xl px-4 py-10">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-8 w-full">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-navy-700 dark:text-white">
                About yourself
              </h2>
              {isEdit ? (
                <Button
                  type="button"
                  onClick={() => {setIsEdit((prev) => !prev), setIsDropdownOpen(false)}}
                  className="h-7"
                >
                  Update
                </Button>
              ) : (
                <Button
                  type="submit"
                  onClick={() => setIsEdit((prev) => !prev)}
                  className="h-7 gap-1"
                >
                  Edit
                  <PencilIcon className="w-3 cursor-pointer" />
                </Button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 ">
            <div className="flex flex-col items-start justify-center w-full rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <label className="text-sm text-navy-700 dark:text-white font-semibold">
                Age
              </label>
              <input
                className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                placeholder="Age"
                type="number"
                min="18"
                max="100"
                readOnly={!isEdit}
                {...register("age")}
              />
            </div>

            <div className="flex flex-col items-start justify-center w-full rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <label className="text-sm text-navy-700 dark:text-white font-semibold">
                Education
              </label>
              <input
                className="bg-transparent w-full placeholder:text-base placeholder:font-medium outline-none"
                placeholder="Education"
                type="text"
                readOnly={!isEdit}
                {...register("education")}
              />
            </div>

            <div className="flex flex-col items-start justify-center w-full rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <label className="text-sm text-navy-700 dark:text-white font-semibold pl-1">
                Gender
              </label>
              <select
                className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                {...register("gender")}
                disabled={!isEdit}
                defaultValue=""
              >
                <option value="" disabled style={{ color: "black" }}>
                  Gender
                </option>
                <option style={{ color: "black" }} value="male">
                  Male
                </option>
                <option style={{ color: "black" }} value="female">
                  Female
                </option>
                <option style={{ color: "black" }} value="other">
                  Other
                </option>
              </select>
            </div>

            <div className="flex flex-col items-start justify-center w-full rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <label className="text-sm text-navy-700 dark:text-white font-semibold">
                Skills
              </label>
              <input
                className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                placeholder="Skills"
                type="text"
                readOnly={!isEdit}
                {...register("skills")}
              />
            </div>

            <div className="flex flex-col items-start justify-center w-full rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <label className="text-sm text-navy-700 dark:text-white font-semibold">
                Work Experience
              </label>
              <input
                className="bg-transparent w-full placeholder:text-base placeholder:font-medium"
                placeholder="Work Experience"
                type="number"
                min="0"
                max="100"
                readOnly={!isEdit}
                {...register("workExperience")}
              />
            </div>

            <div className="flex flex-col items-start justify-center w-full rounded-2xl bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
              <label className="text-sm text-navy-700 dark:text-white font-semibold">
                Profession
              </label>
              <input
                className="bg-transparent w-full placeholder:text-base placeholder:font-medium capitalize"
                placeholder="Profession"
                type="text"
                readOnly={!isEdit}
                {...register("profession")}
              />
            </div>
          </div>
          <div className="relative flex flex-col items-start justify-center w-full rounded-2xl mt-4 bg-clip-border px-3 py-4 shadow-shadow-500 dark:!bg-navy-700 dark:shadow-none transition ease-in-out delay-150 hover:border-slate-400 hover:-translate-y-1 hover:scale-100 duration-300 bg-[#FFFFFF0F]">
            <label className="text-sm text-navy-700 dark:text-white font-semibold">
              Interests
            </label>
            <div
              className="flex items-center justify-between w-full cursor-pointer bg-transparent"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <span
                className={`${interests.length === 0 ? "text-gray-400" : "capitalize"}`}
              >
                {interests.length === 0 ? "Interests" : interests.join(", ")}
              </span>
              {isDropdownOpen ? (
                <ChevronUpIcon size={15} />
              ) : (
                <ChevronDownIcon size={15} fontWeight={5} />
              )}
            </div>

            {isEdit && isDropdownOpen && (
              <div className="absolute z-10 top-20 right-0 w-full shadow-lg rounded-lg mt-2 max-h-60 overflow-y-auto">
                {interestsTags.map((option) => (
                  <div
                    key={option.value}
                    className={`px-4 py-2 cursor-pointer ${
                      interests.includes(option.value)
                        ? "bg-gray-600"
                        : "bg-gray-800"
                    } hover:bg-blue-700 rounded-lg`}
                    onClick={() => handleSelect(option.value)}
                  >
                    <input
                      type="checkbox"
                      hidden
                      checked={interests.includes(option.value)}
                      onChange={() => handleSelect(option.value)}
                      className="mr-2"
                    />
                    {option.label}
                  </div>
                ))}
              </div>
            )}
          </div>
        </form>
      </div>
    </>
  );
};
export default GeneralInfo;
