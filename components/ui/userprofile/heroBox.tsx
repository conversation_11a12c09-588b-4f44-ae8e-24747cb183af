"use client";
import { ArrowRight } from "lucide-react";
import { Card<PERSON>ody, CardContainer, CardItem } from "../3d-card";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Models } from "@/payload/collections/Models/Models";

interface PlanCardProps {
  id: string;
  name: string;
  organization: string;
  description: string;
  type?: string;
  className?: string;
  modelImage?: string;
  onClick?: () => void;
}

function PlanCard({
  id,
  name,
  organization,
  modelImage,
  description,
  type,
  className,
}: PlanCardProps) {
  
  return (
   <Link 
      href={type === "text" ? `/generate/${type}?id=${id}` : `/generate/${type}/${id}`} 
      className="h-full"
    >
      <CardContainer className=" p-2 max-lg:min-w-80">
        <CardBody className="relative border shadow-sm rounded-xl overflow-hidden dark:border-neutral-700 cursor-pointer flex flex-col h-auto w-full">
          {/* Background Image */}
          <div className="relative">
            <Image
              src={
                modelImage ||
                "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSU_deW0kRi7dabGk6lVHIdhzKsgnHgad5MOt6A8FZkiVtiZx9UTbSyh0gmCQ3plai6jVw&usqp=CAU"
              } // Use m.image or a placeholder
              alt={name}
              width="100"
              height="80"
              objectFit="cover"
              className="w-full h-60 sm:h-52 md:h-44 object-cover aspect-video"
            />
            <div className="absolute top-0 -left-2 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
              <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                FEATURED
                <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
              </div>
            </div>
            <CardItem
              as="p"
              translateZ="40"
              className="text-gray-300 absolute bottom-2 right-2 text-sm flex items-center bg-indigo-600 px-2 py-1 rounded-lg"
            >
              <span className="font-bold uppercase">{type}</span>
            </CardItem>
            {/* Overlay */}
            {/* <div className="absolute inset-0 bg-gradient-to-b from-gray-900/80 to-transparent" /> */}
          </div>

          {/* Card Content (Below Image) */}
          <div className="relative z-10 flex flex-col p-4 bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 h-40">
            {/* Card Title */}
            <CardItem
              translateZ="40"
              className="text-lg font-bold text-white line-clamp-1 break-words whitespace-normal capitalize"
            >
             {organization}/{name}
            </CardItem>

            {/* Card Description */}
            <CardItem
              as="p"
              translateZ="40"
              className="text-gray-300 text-sm mt-2 line-clamp-3 break-words whitespace-normal first-letter:upercase"
            >
              {description ||
                "Effortlessly generate stunning images using our AI model."}
            </CardItem>

            <CardItem
              as="p"
              translateZ="40"
              className="text-gray-300 absolute bottom-3 text-sm flex items-center bg-white/20 px-2 py-1 rounded-lg"
            >
              <span className="font-bold first-letter:uppercase">{organization}</span>
            </CardItem>

            {/* Arrow Button */}
            <div className="mt-auto absolute bottom-0 right-0 p-2 flex justify-end">
              <CardItem
                translateZ={40}
                className="flex items-center justify-center px-4 py-2 rounded-lg bg-indigo-100 text-indigo-700 hover:text-white text-xs font-bold hover:bg-indigo-600"
              >
                <ArrowRight className="w-4 h-4" />
              </CardItem>
            </div>
          </div>
        </CardBody>
      </CardContainer>
     </Link>


  );
}

function HeroBoxes() {
  const [model, setModel] = useState<any>([]);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const res = await fetch(`/api2/models?limit=20`);
        const getdata = await res.json();
        setModel(getdata.data);
      } catch (err) {
        console.error(err);
      }
    };
    fetchModels();
  }, []);

  return (
    <div className="px-4 py-6 md:py-10 mx-auto">
      {model.length > 0 && (
        <div className="mb-2 flex flex-col max-w-lg">
          <h2 className="text-2xl md:text-4xl font-semibold text-white">Featured Models</h2>
          <p className="text-gray-400 max-sm:text-sm">
            Explore top-tier AI models handpicked for exceptional performance
            and versatility across various creative and professional tasks.
          </p>
        </div>
      )}
      <div className="border-t-2 border-indigo-600">
        <div className="flex overflow-x-auto no-scrollbar whitespace-nowrap flex-nowrap gap-4 lg:grid lg:grid-cols-4 py-2">
          {model
            .filter((model) => model.isFeatured)
            .map((model, index) => (
              <PlanCard
                id={model.id}
                key={index}
                name={model.name}
                organization={model.organization}
                modelImage={model.modelImage[0]?.image?.url}
                description={model.description}
                type={model.type}
              />
            ))}
        </div>
      </div>
    </div>
  );
}

export default HeroBoxes;
