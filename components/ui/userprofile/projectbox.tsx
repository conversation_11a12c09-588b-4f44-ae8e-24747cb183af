import React from "react";
import { BentoGrid, BentoGridItem } from "../bento-grid";
import Image from "next/image";

interface MenuItemProps {
  user: {
    name: string;
    description: String;
    product_files: {
      url: any;
    };
    images: {
      image: {
        url: string;
      };
    }[];
    // Add other properties if needed
  }[];
  recentProject: boolean;
}
export function ProjectBox({ recentProject, user }: MenuItemProps) {
  const allproducts = user?.filter((product) => typeof(product) === "object");
 
  return (
    <BentoGrid
      className={`mx-auto grid-cols-2 ${
        recentProject ? "md:grid-cols-3 max-w-4xl" : "max-w-5xl md:grid-cols-4"
      }`}
    >
      {Array.isArray(allproducts) &&
        allproducts.map((item, i) => {
          const firstImageUrl =
            item.images?.[0]?.image?.url ?? "/img/noImage.jpg";

          return (
            <BentoGridItem
              key={i}
              title={item.name}
              description={item.description} 
              header={<Skeleton url={firstImageUrl} />}
              className={`rounded-lg ${
                recentProject
                  ? `${
                      i === 3 || i === 6 ? "md:col-span-2" : ""
                    } bg-[#FFFFFF0F]`
                  : "bg-card"
              }`}
            />
          );
        })}
    </BentoGrid>
  );
}

const Skeleton = ({ url }: { url: string }) => (
  <div className="flex w-full h-full object-cover min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-200 to-neutral-100">
    <Image
      width={400}
      height={400}
      src={url}
      alt="events"
      className="rounded-xl w-full h-full object-cover"
    />
  </div>
);
