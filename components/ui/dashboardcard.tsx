import { Plus } from "lucide-react";
import Link from "next/link";

export const DashboardCard = ({ items }) => {
  const { title, description, Icon, href, disabled, Target } = items;
  return (
    <Link
      href={href}
      {...(Target ? { target: Target } : {})}
      className={`flex flex-row gap-2 justify-between items-center w-full p-4 rounded-lg relative overflow-hidden group shadow-lg  bg-dash-foreground transform transition duration-500 hover:scale-105 hover:shadow-xl backdrop-blur-sm h-full ${
        disabled
          ? "opacity-50 cursor-not-allowed"
          : "hover:scale-105 hover:shadow-xl"
      }`}
    >
      <div className="w-full ">
        <h3 className="font-medium md:text-lg text-white group-hover:text-white relative z-10 duration-300 break-normal whitespace-normal">
          {title}
        </h3>
        <p className="text-slate-400 pt-3 group-hover:text-violet-200 relative z-10 duration-300 break-normal whitespace-normal line-clamp-2 text-sm md:text-md">
          {description}
        </p>
      </div>
      <div className="">
        <Plus className="bg-gradient-to-r from-amber-500 to-pink-500 h-12 w-12 p-2 rounded-full  text-2xl text-white group-hover:text-dash transition-colors relative z-10 duration-300" />
      </div>
    </Link>
  );
};
