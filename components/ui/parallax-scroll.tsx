"use client";
import { useScroll, useTransform } from "framer-motion";
import { useRef, useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { cn } from "@/lib/utils";

export const ParallaxScroll = ({
  images,
  className,
}: {
  images: {img: string; prompt: string}[];
  className?: string;
}) => {
  const gridRef = useRef<any>(null);
  const { scrollYProgress } = useScroll({
    container: gridRef, // remove this if your container is not fixed height
    offset: ["start start", "end start"], // remove this if your container is not fixed height
  });
  const [popoverIndex, setPopoverIndex] = useState<number | null>(null);
  
  const translateFirst = useTransform(scrollYProgress, [0, 1], [0, -200]);
  const translateSecond = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const translateThird = useTransform(scrollYProgress, [0, 1], [0, -200]);
  const translateFourth = useTransform(scrollYProgress, [0, 1], [0, 200]);

  // const third = Math.ceil(images.length / 3);

  // const firstPart = images.slice(0, third);
  // const secondPart = images.slice(third, 2 * third);
  // const thirdPart = images.slice(2 * third);

  const third = Math.ceil(images.length / 4); // Calculate the size of each part

const firstPart = images.slice(0, third);
const secondPart = images.slice(third, 2 * third);
const thirdPart = images.slice(2 * third, 3 * third);
const fourthPart = images.slice(3 * third); // Slice the array for the fourth part
const handleInfoClick = (index: number) => {
  setPopoverIndex(popoverIndex === index ? null : index);
};

return (
  <div className={cn('h-[50rem] items-start overflow-y-auto w-full no-scrollbar', className)} ref={gridRef}>
    <div className="hidden md:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 items-start  max-w-full mx-auto gap-10 pt-5 pb-40 md:px-10">
      <div className="grid gap-10">
        {firstPart.map((el, idx) => (
          <motion.div style={{ y: translateFirst }} key={'grid-1' + idx} className="relative">
            <Image
              src={el.img}
              className="h-80 w-full object-cover object-center rounded-lg gap-10 !m-0 !p-0 transition-opacity duration-300 hover:opacity-100"
              height="400"
              width="400"
              alt="thumbnail"
            />
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-300 opacity-0 hover:opacity-100">
              <div className="text-white text-center">
                <h3 className="text-xl px-5 font-bold">{el.prompt}</h3>
                {/* <p className="text-sm">Description goes here...</p> */}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      <div className="grid gap-10">
        {secondPart.map((el, idx) => (
          <motion.div style={{ y: translateSecond }} key={'grid-1' + idx} className="relative">
            <Image
              src={el.img}
              className="h-80 w-full object-cover object-center rounded-lg gap-10 !m-0 !p-0 transition-opacity duration-300 hover:opacity-100"
              height="400"
              width="400"
              alt="thumbnail"
            />
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-300 opacity-0 hover:opacity-100">
              <div className="text-white text-center">
                <h3 className="text-xl px-5 font-bold">{el.prompt}</h3>
                {/* <p className="text-sm">Description goes here...</p> */}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      <div className="grid gap-10">
        {thirdPart.map((el, idx) => (
          <motion.div style={{ y: translateThird }} key={'grid-1' + idx} className="relative">
            <Image
              src={el.img}
              className="h-80 w-full object-cover object-center rounded-lg gap-10 !m-0 !p-0 transition-opacity duration-300 hover:opacity-100"
              height="400"
              width="400"
              alt="thumbnail"
            />
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-300 opacity-0 hover:opacity-100">
              <div className="text-white text-center">
                <h3 className="text-xl px-5 font-bold">{el.prompt}</h3>
                {/* <p className="text-sm">Description goes here...</p> */}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      <div className="grid gap-10">
        {fourthPart.map((el, idx) => (
          <motion.div style={{ y: translateFourth }} key={'grid-1' + idx} className="relative">
            <Image
              src={el.img}
              className="h-80 w-full object-cover object-center rounded-lg gap-10 !m-0 !p-0 transition-opacity duration-300 hover:opacity-100"
              height="400"
              width="400"
              alt="thumbnail"
            />
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-300 opacity-0 hover:opacity-100">
              <div className="text-white text-center">
                <h3 className="text-xl px-5 font-bold">{el.prompt}</h3>
                {/* <p className="text-sm">Description goes here...</p> */}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      {/* Render secondPart and thirdPart similarly */}
    </div>
    <div className="md:hidden">
        {images.map((e, i) => (
          <div key={i} className="relative mb-10">
            <Image
              src={e.img}
              className="h-80 w-full object-cover object-center rounded-lg !m-0 !p-0 transition-opacity duration-300 hover:opacity-100"
              height="400"
              width="400"
              alt="thumbnail"
            />
            <div className="absolute top-2 right-2 bg-indigo-600 rounded p-1 cursor-pointer" onClick={() => handleInfoClick(i)}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-white"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8 4a1 1 0 100-2 1 1 0 000 2zm1-8a1 1 0 10-2 0v4a1 1 0 002 0V6z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            {popoverIndex === i && (
              <div className="absolute top-11 right-2 left-2 bg-black bg-opacity-75 text-white rounded p-3 shadow-lg z-10">
                <h3 className="text-xl px-5 font-bold">{e.prompt}</h3>
              </div>
                          )}
                          <div className="flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-300 opacity-0 hover:opacity-100">
                          </div>
                        </div>
              
        ))}
      </div>
  </div>
);
};
