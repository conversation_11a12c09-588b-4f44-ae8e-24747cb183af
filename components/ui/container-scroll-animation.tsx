"use client";
import { useRef, useEffect, useState, <PERSON>} from "react";
import { useScroll, useTransform, motion, MotionValue } from "framer-motion";
import React from "react";

let interval: any;


const CardStack = ({
    items,
    offset,
    scaleFactor,
  }: {
    items: any;
    offset?: number;
    scaleFactor?: number;
  }) => {
    const CARD_OFFSET = offset || 5;
    const SCALE_FACTOR = scaleFactor || 0.06;
    const [cards, setCards] = useState<any>(items);
   
    useEffect(() => {
      startFlipping();
   
      return () => clearInterval(interval);
    }, []);
    const startFlipping = () => {
      interval = setInterval(() => {
        setCards((prevCards: any) => {
          const newArray = [...prevCards]; // create a copy of the array
          newArray.unshift(newArray.pop()!); // move the last element to the front
          return newArray;
        });
      }, 5000);
    };
   
    return (
      <div className="relative  h-60 w-60 md:h-60 md:w-96">
        {cards.map((card, index) => {
          return (
            <motion.div
              key={card.id}
              className="absolute dark:bg-black bg-white h-60 w-60 md:h-60 md:w-96 rounded-3xl p-4 shadow-xl border border-neutral-200 dark:border-white/[0.1]  shadow-black/[0.1] dark:shadow-white/[0.05] flex flex-col justify-between"
              style={{
                transformOrigin: "top center",
              }}
              animate={{
                top: index * -CARD_OFFSET,
                scale: 1 - index * SCALE_FACTOR, // decrease scale for cards that are behind
                zIndex: cards.length - index, //  decrease z-index for the cards that are behind
              }}
            >
              {/* <Card >
               {items.card}
              </Card> */}
            </motion.div>
          );
        })}
      </div>
    );
  };


 
  export const ContainerScroll = ({
    titleComponent,
    children,
  }: {
    titleComponent: string | React.ReactNode;
    children: React.ReactNode;
  }) => {
    const containerRef = useRef<any>(null);
    const { scrollYProgress } = useScroll({
      target: containerRef,
    });
    const [isMobile, setIsMobile] = React.useState(false);
   
    React.useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth <= 768);
      };
      checkMobile();
      window.addEventListener("resize", checkMobile);
      return () => {
        window.removeEventListener("resize", checkMobile);
      };
    }, []);
   
    const scaleDimensions = () => {
      return isMobile ? [0.7, 0.9] : [1.05, 1];
    };
   
    const rotate = useTransform(scrollYProgress, [1, 0], [0, 20]);
    const translate = useTransform(scrollYProgress, [0, 1], [0, 100]);
   
    return (
      <div
        className="h-10 mt-36 md:h-[80rem] max-h-[20rem] flex items-center justify-center relative md:p-20"
        ref={containerRef}
      >
        <div
          className="py-10 md:py-40 w-full relative"
          style={{
            perspective: "500px",
          }}
        >
          <Header translate={translate} titleComponent={titleComponent} />
          <Card rotate={rotate} translate={translate}>
            {children}
          </Card>
        </div>
      </div>
    );
  };
   
  export const Header = ({ translate, titleComponent }: any) => {
    return (
      <motion.div
        style={{
          translateY: translate,
        }}
        className="div max-w-4xl mx-auto text-center"
      >
        {titleComponent}
      </motion.div>
    );
  };
   
  export const Card = ({
    rotate,
    children,
  }: {
    rotate: MotionValue<number>;
    translate: MotionValue<number>;
    children: React.ReactNode;
  }) => {
    return (
      <motion.div
        style={{
          rotateX: rotate,
          boxShadow:
            "0 0 #0000004d, 0 9px 20px #0000004a, 0 37px 37px #00000042, 0 84px 50px #00000026, 0 149px 60px #0000000a, 0 233px 65px #00000003",
        }}
        className="max-w-4xl -mt-12 mx-auto h-[10rem] md:h-[20rem] w-full border-2 border-muted-foreground p-2 md:p-2 bg-[#FFFFFF0F] backdrop-blur-md rounded-3xl shadow-2xl"
      >
        <div className=" h-full w-full  overflow-hidden rounded-xl bg-gray-100 dark:bg-zinc-900 md:rounded-2xl ">
          {children}
        </div>
      </motion.div>
    );
  };