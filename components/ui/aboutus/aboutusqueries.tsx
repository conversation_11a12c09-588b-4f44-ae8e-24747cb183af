"use Client";
import Link from "next/link";

import { MapPin, MailOpenIcon, BookOpen } from "lucide-react";
import MaxWidthWrapper from "@/components/MaxWidthWrapper";
import { GetInTouch } from "@/components/services/getintouch";
import { QuerieForm } from "./querieform";
import { Label } from "../label";
import { Button } from "../button";
import { Input } from "../input";
import { Textarea } from "../textarea";

const AboutUsQueries = ({ showFields }) => {
  // return (
  //   <>
  //     <MaxWidthWrapper className="px-5">
  //       <div
  //         id="connect-us"
  //         className="text-white flex flex-col items-center pt-20 sm:justify-center sm:pt-0"
  //       >
  //         <div className="my-4 md:my-8 mb-12 text-center"></div>
  //         <div className="grid grid-cols-1 md:flex md:flex-row gap-6 w-full">
  //           {/* First Column: Form (8/12 width on medium screens and above) */}
  //           <div className="relative w-full md:w-8/12 mx-auto bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 shadow-[20px_0_20px_20px] shadow-slate-500/10 dark:shadow-white/20 rounded-md border-white/20 border-l-white/20 border-r-white/20 sm:shadow-sm lg:shadow-none">
  //             {/* <div className="relative -mb-px h-px w-full bg-gradient-to-r from-transparent via-sky-300 to-transparent"></div> */}
  //             {/* <GetInTouch showFields={showFields}/> */}
  //             <QuerieForm/>
  //           </div>
  //           {/* Second Column: Image (4/12 width on medium screens and above) */}
  //           <div className="relative md:md:w-4/12 w-full mx-auto bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 sm:border-t-white/20 shadow-[20px_0_20px_20px] shadow-slate-500/10 dark:shadow-white/20 rounded-lg border-white/20 border-l-white/20 border-r-white/20 sm:shadow-sm lg:shadow-none">
  //             {/* <!-- Add your image here --> */}
  //             <div className="w-3/4 h-full m-auto py-4 md:py-8">
  //               <div className="mb-3">
  //                 <h2 className="text-3xl font-bold tracking-tight sm:text-3xl text-center mb-8">
  //                   Connect to us
  //                 </h2>
  //               </div>
  //               <div className="mb-3">
  //                 <Link href="mailto:<EMAIL>" target="_blank">
  //                   <div className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-5 px-5 cursor-pointer">
  //                     <MailOpenIcon width={20} height={29} />
  //                     <div className="flex flex-col">
  //                       <h2>
  //                         <span className="inline-flex font-semibold bg-clip-text text-transparent text-white">
  //                           Mail
  //                         </span>
  //                       </h2>
  //                       <span className="text-sm text-muted-foreground break-words overflow-wrap break-word"
  //                        style={{ wordBreak: "break-word" }}>
  //                         <EMAIL>
  //                       </span>
  //                     </div>
  //                   </div>
  //                 </Link>
  //               </div>
  //               <div className="mb-3">
  //                 <div className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-5 px-5">
  //                   <MapPin width={40} height={32} />
  //                   <div className="flex flex-col">
  //                     <h2>
  //                       <span className="inline-flex  font-semibold bg-clip-text text-transparent text-white ">
  //                         Address
  //                       </span>
  //                     </h2>
  //                     <p className="text-sm text-muted-foreground">
  //                       VF-29, Treasure Town, IDA State, Indore,
  //                       MP, India - 452012
  //                     </p>
  //                   </div>
  //                 </div>
  //               </div>
  //               <Link  href="https://discord.gg/kPkYbzMvN3" target="_blank" className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-5 px-5">
  //                 <BookOpen width={24} height={32} />

  //                 <div className="flex flex-col">
  //                   <h2>
  //                     <span className="inline-flex font-semibold bg-clip-text text-transparent text-white ">
  //                       For Enquiry
  //                     </span>
  //                   </h2>
  //                   <span

  //                     className="text-sm text-muted-foreground hover:text-indigo-500"
  //                   >
  //                     Join Discord
  //                   </span>
  //                 </div>
  //               </Link>
  //             </div>
  //           </div>
  //         </div>
  //       </div>
  //     </MaxWidthWrapper>
  //   </>
  // );r

  return (
    <section className="p-4 md:p-6 lg:py-16">
      <div className=" py-10 rounded-xl mx-auto text-center" >
        <div className="gap-x-6 gap-y-6 flex flex-col md:flex-row items-start ">

          <div className="w-full md:w-1/2 h-fit bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 p-5 rounded-xl hover:border-gray-300">
            <h2
              color="blue-gray"
              className="mb-4 !text-3xl lg:!text-4xl font-bold tracking-tight"
            >
              Contact us
              {/* We&apos;re Here to Help */}
            </h2>
            <iframe className="mt-10 h-60 w-full"
               id="gmap_canvas" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d316.155237526922!2d75.88259669549066!3d22.761999986593455!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x804f412bcded6bb5%3A0x774865efae79f015!2sEstancia%20%3B%20The%20pro-working%20space!5e0!3m2!1sen!2sin!4v1743575237738!5m2!1sen!2sin"></iframe>

            <div className="flex flex-col gap-6 md:flex-row mt-8">
                <Link href="mailto:<EMAIL>" className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-3 px-5 cursor-pointer w-full" target="_blank">
                    <MailOpenIcon className="w-10 h-10" />
                    <div className="flex flex-col items-start">
                      <h2>
                        <span className="inline-flex font-semibold bg-clip-text text-transparent text-white">
                          Mail
                        </span>
                      </h2>
                      <span className="text-sm text-muted-foreground break-words overflow-wrap break-word"
                        style={{ wordBreak: "break-word" }}>
                        <EMAIL>
                      </span>
                    </div>
                </Link>
              <Link href="https://discord.gg/kPkYbzMvN3" target="_blank" className="bg-indigo-800 flex gap-3 rounded-lg py-3 px-5 w-full">
                <BookOpen className="w-10 h-10"  />

                <div className="flex flex-col items-start">
                  <h2>
                    <span className="inline-flex font-semibold bg-clip-text text-transparent text-white ">
                      For Enquiry
                    </span>
                  </h2>
                  <span

                    className="text-sm text-muted-foreground hover:text-indigo-500"
                  >
                    Join Discord
                  </span>
                </div>
              </Link>
            </div>
          </div>
          {/* <form
            action="#"
            className="flex p-5 bg-gradient-to-bl from-indigo-950 via-indigo-900 to-indigo-950 flex-col gap-3 justify-center md:w-1/2 rounded-xl hover:border-gray-300 "
          >
            <Label
              color="blue-gray"
              className="mb-4 !text-3xl lg:!text-4xl"
            >
              Write to us
            </Label>

            <div className="grid grid-cols-2 gap-4 mt-5">
              <div>
                <Label
                  className="mb-2 flex text-left font-medium !text-white"
                >
                  First Name
                </Label>
                <Input
                  color="gray"

                  placeholder="First Name"
                  name="first-name"
                  className="focus:border-t-gray-900"

                />
              </div>
              <div>
                <Label

                  className="mb-2 flex text-left font-medium !text-white"
                >
                  Last Name
                </Label>
                <Input
                  color="gray"

                  placeholder="Last Name"
                  name="last-name"
                  className="focus:border-t-gray-900"

                />
              </div>
            </div>
            <div>
              <Label

                className="mb-2 flex text-left font-medium !text-white"
              >
                Your Email
              </Label>
              <Input
                color="gray"

                placeholder="<EMAIL>"
                name="email"
                className="focus:border-t-gray-900"

              />
            </div>
            <div>
              <Label

                className="mb-2 flex text-left font-medium !text-white"
              >
                Your Message
              </Label>
              <Textarea
                rows={6}
                color="gray"
                placeholder="Message"
                name="message"
                className="focus:border-t-gray-900"

              />
            </div>
            <Button className="w-full" color="gray">
              Send message
            </Button>
          </form> */}
      <QuerieForm/>
        </div>
      </div>
    </section>
  );


};
export default AboutUsQueries;
