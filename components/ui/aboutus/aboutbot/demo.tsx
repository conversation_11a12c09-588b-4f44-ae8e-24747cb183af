'use client'

import { Card } from "@/components/ui/card"
// import dynamic from 'next/dynamic'
// import { Suspense } from 'react'

// // Dynamically import Spline with no SSR
// const Spline = dynamic(() => import('@splinetool/react-spline'), {
//   ssr: false,
//   loading: () => <div className="w-full h-full bg-gray-200" />,
// })

export function SplineSceneBasic() {
  return (
    <Card className="w-full h-full md:[h-screen-50px] overflow-hidden">
      <div className="h-full  bg-dash mt-14 max-md:py-20 px-4">
        {/* Left content */}
        <div className="pt-10 md:pt-20 z-10 flex flex-col relative justify-center items-center text-center bg-dash">
          <h1 className="text-4xl md:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-300">
            Welcome to Rentprompts
          </h1>
          <p className="mt-4 text-neutral-300 max-w-lg text-2xl">
            It is a community-driven Gen AI marketplace and builder platform
            focused on human and AI collaboration.
          </p>
        </div>

        {/* Right content */}
        {/* <div className="flex md:absolute md:top-70 lg:top-64 bg-dash">
          <Suspense fallback={<div className="w-full h-full bg-gray-200" />}>
            <Spline
              scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
              className="w-full h-full"
            />
          </Suspense>
        </div> */}
      </div>
    </Card>
  )
}