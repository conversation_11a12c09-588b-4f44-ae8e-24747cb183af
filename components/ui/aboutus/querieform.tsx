"use client";
import React, { useState } from "react";
import { toast } from "sonner";
import { Label } from "../label";
import { Textarea } from "../textarea";
import { Input } from "../input";
import { Button } from "../button";

interface FormData {
  email: string;
  username: string;
  query: string;
}

export function QuerieForm() {
  const [formData, setFormData] = useState<FormData>({
    email: "",
    username: "",
    query: "",
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isPrivacyChecked, setIsPrivacyChecked] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));

    // Clear error when the user starts typing
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsPrivacyChecked(e.target.checked);
  };

  const validateForm = () => {
    const fieldErrors: Partial<FormData> = {};
    if (!formData.username) {
      fieldErrors.username = "User Name is required.";
    }
    if (!formData.email) {
      fieldErrors.email = "Email Address is required.";
    }
    if (!formData.query) {
      fieldErrors.query = "Query is required.";
    }
    // if (!isPrivacyChecked) {
    //   toast.error("You must agree to the privacy policy.");
    // }

    setErrors(fieldErrors);
    return Object.keys(fieldErrors).length === 0; 
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    const formDataToSend = {
      email: formData.email,
      userName: formData.username,
      query: formData.query,
    };

    setIsLoading(true);
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contactInfo`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formDataToSend),
        }
      );

      const data = await res.json();

      if (!res.ok) {
        const errorText = data.errors?.[0]?.message || "Unknown error";
        toast.error(errorText);
      } else {
        toast.success("Form submitted successfully");
        setFormSubmitted(true);
        setFormData({
          username: "",
          email: "",
          query: "",
        });
      }
    } catch (error) {
      console.error("There was a problem with the fetch operation:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full md:w-1/2 mx-auto rounded-xl pt-10 px-4 md:p-8 bg-gradient-to-bl from-indigo-950 via-indigo-900 to-indigo-950">
      <h2 className="mb-4 !text-3xl lg:!text-4xl font-bold tracking-tight">
        Write to us
      </h2>

     
      <form
        className="rounded-lg text-white w-full mx-auto "
        onSubmit={handleSubmit}
      >
        <div className="flex flex-col md:flex-row md:space-x-4 mt-5">
          <div className="w-full md:w-1/2">
            <label htmlFor="username" className="text-white flex text-left">
              User Name
            </label>
            <Input
              id="username"
              name="username"
              type="text"
              placeholder="Enter your name"
              value={formData.username}
              onChange={handleChange}
              className="focus:border-t-gray-900"
            />
            {errors.username && (
              <p className="text-yellow-300 text-sm text-left">{errors.username}</p>
            )}
          </div>

          <div className="w-full md:w-1/2 mt-4 md:mt-0">
            <label htmlFor="email" className="text-white flex text-left">
              Email
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleChange}
              className="focus:border-t-gray-900"
            />
            {errors.email && (
              <p className="text-yellow-300 text-sm text-left">{errors.email}</p>
            )}
          </div>
        </div>

        <div className="mt-4">
          <label htmlFor="query" className="text-white flex text-left">
            Your Query
          </label>
          <Textarea
            id="query"
            name="query"
            placeholder="Enter your message here"
            value={formData.query}
            onChange={handleChange}
            className="focus:border-t-gray-900"
            rows={6}
          ></Textarea>
          {errors.query && (
            <p className="text-yellow-300 text-sm text-left">{errors.query}</p>
          )}
        </div>

        <div className="mb-4 mb-md-0 mt-4 flex flex-col items-start space-y-2 md:flex-row md:items-center md:space-x-2 md:space-y-0">
          {/* Checkbox with equal width */}
          {/* <div className="w-full md:w-1/3 flex items-center">
            <Input
              id="privacyPolicy"
              name="privacyPolicy"
              type="checkbox"
              checked={isPrivacyChecked} 
              onChange={handleCheckboxChange}
              className="w-4 h-4 text-green-400 bg-transparent border border-white rounded focus:ring-0 focus:outline-none"
            />
            <label
              htmlFor="privacyPolicy"
              className="text-sm text-gray-300 ml-2"
            >
              I agree to the{" "}
              <a href="/privacypolicy" className="text-white-400 font-bold	">
                privacy policy
              </a>
            </label>
          </div> */}

          {/* Spacer div for alignment */}


          {/* Button with equal width */}
          <Button
            className=" w-full"
            type="submit"
          >
            {isLoading ? "Submitting..." : "Send →"}
          </Button>
        </div>

        {formSubmitted && (
          <p className="items-center h-full pb-3 " >
            Thank you for contacting us, Our team will get back to you soon.
          </p>
        )}
      </form>
    </div>
  );
}

const LabelInputContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={`flex flex-col space-y-2 w-full ${className}`}>
      {children}
    </div>
  );
};
