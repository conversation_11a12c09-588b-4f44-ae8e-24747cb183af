// "use client";
// import React, { useEffect, useRef, useState } from "react";

import { Timeline } from "../timeline";
import Image from "next/image";

// interface GridItemProps {
//   title: string;
//   description: string;

//   date: string;
//   index: number;
// }

// const GridItem: React.FC<GridItemProps> = ({
//   title,
//   description,
//   date,
//   index,
// }) => {
//   return (
//     <div className="flex-shrink-0 bg-card w-80 h-80 flex flex-col justify-between pt-4">
//       {index % 2 !== 0 && (
//         <div className="flex p-5 flex flex-col justify-start h-40 ">
//           <p className="text-3xl font-semibold text-white">{date}</p>
//         </div>
//       )}
//       <div
//         className={`h-40 bg-gradient-to-br from-[#160c99] to-[#2921ad] p-5 ${index % 2 !== 0 ? "" : "rounded-t-lg"}`}
//       >
//       {/*   [#6366F1] */}
//         <h3 className="text-base font-semibold text-white mb-2">{title}</h3>
//         <p className="text-white text-sm">{description}</p>
//       </div>
//       {index % 2 === 0 && (
//         <div className="flex p-5 flex flex-col justify-end h-40">
//           <p className="text-3xl font-semibold text-white">{date}</p>
//         </div>
//       )}
//     </div>
//   );
// };

// const Grid: React.FC = () => {
//   const data = [
//     {
//       title: "Product Development",
//       description: "Laying the groundwork for our innovative product launch.",
//       date: "May 2024",
//     },
//     {
//       title: "Beta Testing & User Feedback",
//       description: "Releasing the beta version for selected users to test and provide feedback.",
//       date: "Sep 2024",
//     },
//     {
//       title: "Launch of rentprompts.ai",
//       description:"Private Rapps with APIs, collaborate and share rapps with your team members.",
//       date: "Dec 2024",
//     },
//     {
//       title: "Agentic Rapps ",
//       description:
//         "Create and customize your agents with your data seamlessly.",
//       date: "Mar 2025 ",
//     },
//     {
//       title: "Effortless Task Management",
//       description:
//         "Create , Configure and Automate your tasks seamlessly.",
//       date: "June 2025",
//     },
//   ];


//   const repeatedData = [...Array(3)].flatMap(() => data); // Repeat the data 3 times

//   const containerRef = useRef<HTMLDivElement>(null);
//   const [isHovered, setIsHovered] = useState(false);

//   useEffect(() => {
//     const container = containerRef.current;
//     let scrollInterval: NodeJS.Timeout;

//     if (container && !isHovered) {
//       scrollInterval = setInterval(() => {
//         container.scrollBy({ left: 1, behavior: "smooth" });
//       }, 10); // Adjust the speed of the scroll here
//     }

//     return () => clearInterval(scrollInterval);
//   }, [isHovered]);

//   return (
//     <>
//       <h3 className="text-3xl font-bold mb-8 text-center">Our Progress</h3>
//       <div
//         ref={containerRef}
//         className="flex overflow-x-auto hide-scrollbar"
//         onMouseEnter={() => setIsHovered(true)}
//         onMouseLeave={() => setIsHovered(false)}
//       >
//         {repeatedData.map((item, index) => (
//           <GridItem
//             key={index}
//             index={index}
//             title={item.title}
//             description={item.description}
//             date={item.date}
//           />
//         ))}
//       </div>
//     </>
//   );
// };

// export default Grid;




const TimelineDemo = () => {
  const data = [
    {
      title: "August 2024",
      content: (
        <div>
          <p className="text-white dark:text-neutral-200 text-xs md:text-lg font-medium mb-8 ">
            In our first year, we successfully launched RentPrompts Marketplace received positive feedback from early users, and formed partnerships with key industry players.
          </p>
          <div className="grid gap-4">
            <Image
              src="asset.rentprompts.com/Launch.png"
              alt="startup template"
              width={900}
              height={500}
              className="rounded-lg object-fit h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            />
            {/* <Image
              src="https://assets.aceternity.com/templates/startup-2.webp"
              alt="startup template"
              width={500}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            /> */}
          </div>
        </div>
      ),
    },
    {
      title: " Sept 2024",
      content: (
        <div>
          <p className="text-white dark:text-neutral-200 text-xs md:text-lg font-medium mb-8">
            We expanded into new markets, improved operational efficiency, and saw an increase in customer satisfaction and adoption.
          </p>
          <div className="grid gap-4">
            {/* <Image
              src="https://assets.aceternity.com/templates/startup-1.webp"
              alt="startup template"
              width={500}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            /> */}
            <Image
              src="asset.rentprompts.com/newMarket.png"
              alt="startup template"
              width={900}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            />

          </div>
        </div>
      ),
    },
    {
      title: "Dec 2024",
      content: (
        <div>
          <p className="text-white dark:text-neutral-200 text-xs md:text-lg font-medium mb-8">
  We are releasing our offerings based on customer feedback, Helped 20+ enterprises to reduce cost and save time,
</p>
<ul className="text-white dark:text-neutral-200 text-xs md:text-lg font-medium mb-8 list-disc pl-5 space-y-1">
  <li>Beta Users: 500+</li>
  <li><span className="font-bold">AI Apps Built</span>: 400+</li>
  <li><span className="font-bold">Generations</span>: 50,000+</li>
  <li><span className="font-bold">Community Members</span>: 5,000</li>
  <li><span className="font-bold">Time Saved</span>: 1,40,000+ hours</li>
</ul>
          <div className="grid gap-4">
            {/* <Image
              src="https://assets.aceternity.com/templates/startup-1.webp"
              alt="startup template"
              width={500}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            /> */}
            <Image
              src="asset.rentprompts.com/Achievement.png"
              alt="startup template"
              width={900}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            />

          </div>
        </div>
      ),
    },
    {
      title: "Present",
      content: (
        <div>
          <p className="text-white dark:text-neutral-200 text-xs md:text-lg font-medium mb-8">
            We achieved profitability, expanded our product line, and strengthened our brand reputation through launch of Rentprompts.ai the no code AI editor.
          </p>

          <div className="grid gap-4">
            {/* <Image
              src="https://assets.aceternity.com/pro/hero-sections.png"
              alt="hero template"
              width={500}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            /> */}
            <Image
              src="asset.rentprompts.com/ai%20studio.png"
              alt="feature template"
              width={900}
              height={500}
              className="rounded-lg object-cover h-20 md:h-44 lg:h-60 w-full shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]"
            />
          </div>
        </div>
      ),
    },

  ];
  return (
    <div className="w-full">
      <Timeline data={data} />
    </div>
  );
}
export default TimelineDemo;