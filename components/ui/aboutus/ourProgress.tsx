import React from 'react'

const timelineData = [
    {
      title: "Flowbite Application UI v2.0.0",
      date: "Released on January 13th, 2022",
      description:
        "Get access to over 20+ pages including a dashboard layout, charts, kanban board, calendar, and pre-order E-commerce & Marketing pages.",
    },
    {
      title: "Flowbite Figma v1.3.0",
      date: "Released on December 7th, 2021",
      description:
        "All of the pages and components are first designed in Figma and we keep a parity between the two versions even as we update the project.",
    },
    {
      title: "Flowbite Library v1.2.2",
      date: "Released on December 2nd, 2021",
      description:
        "Get started with dozens of web components and interactive elements built on top of Tailwind CSS.",
    },
  ];
function OurProgress() {
  return (
    <>
    
  
        <h3 className="text-3xl font-bold mb-8 text-center">Our Progress</h3>

        <div className="relative flex flex-col md:grid md:grid-cols-9 mx-auto p-2 text-blue-50 pt-6">
          {timelineData.map((item, index) => (
            <div key={index} className="flex md:contents">
              {/* For larger screens (md and up) */}
              {index % 2 === 0 ? (
                <>
                  {/* Left side for even index on larger screens */}
                  <div
                    className="hidden md:block col-start-1 col-end-5 p-4 rounded-xl my-4 md:my-0 ml-auto text-white bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 
                  border-muted-foreground hover:border-gray-300 rounded-md rounded-lg shadow-xl"
                  >
                    <h3 className="font-semibold">{item.title}</h3>
                    <time className="block mb-2 text-sm font-normal text-gray-400 dark:text-gray-500">
                      {item.date}
                    </time>
                    <p className="text-base">{item.description}</p>
                  </div>
                  {/* Line */}
                  <div className="col-start-5 col-end-6 mx-auto relative">
                    <div className="h-full w-6 flex items-center justify-center">
                      <div className="h-full w-1 bg-gray-300 pointer-events-none"></div>
                    </div>
                    <div className="w-6 h-6 absolute top-1/2 -mt-3 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center ring-8 ring-white dark:ring-gray-900">
                      <svg
                        className="w-2.5 h-2.5 text-blue-800 dark:text-blue-300"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                      </svg>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {/* Line */}
                  <div className=" col-start-5 col-end-6 mx-auto relative">
                    <div className="h-full w-6 flex items-center justify-center">
                      <div className="h-full w-1 bg-gray-300 pointer-events-none"></div>
                    </div>
                    <div className="w-6 h-6 absolute top-1/2 -mt-3 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center ring-8 ring-white dark:ring-gray-900">
                      <svg
                        className="w-2.5 h-2.5 text-blue-800 dark:text-blue-300"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" />
                      </svg>
                    </div>
                  </div>
                  {/* Right side for odd index on larger screens */}
                  <div
                    className="hidden md:block col-start-6 col-end-10 p-4 rounded-xl my-4 md:my-0 mr-auto text-white bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 
                  border-muted-foreground hover:border-gray-300 rounded-md rounded-lg shadow-xl"
                  >
                    <h3 className="font-semibold">{item.title}</h3>
                    <time className="block mb-2 text-sm font-normal text-gray-400 dark:text-gray-500">
                      {item.date}
                    </time>
                    <p className="text-base">{item.description}</p>
                  </div>
                </>
              )}

              {/* For mobile screens, all items on the right side */}
              <div
                className=" me-8 md:hidden col-start-1 col-end-10 p-4 rounded-xl my-4 mr-auto text-white bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 
                  border-muted-foreground hover:border-gray-300 rounded-md rounded-lg shadow-xl "
              >
                <h3 className="font-semibold">{item.title}</h3>
                <time className="block mb-2 text-sm font-normal text-gray-400 dark:text-gray-500">
                  {item.date}
                </time>
                <p className="text-base">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
   
    
    </>
  )
}

export default OurProgress