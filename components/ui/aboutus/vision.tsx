import { IconBinoculars } from "@tabler/icons-react";
import { Lightbulb, Target } from "lucide-react";
import React from "react";

const Vision = () => {
  return (
    <div
      className="mt-5 bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 
                  border-muted-foreground hover:border-gray-300 rounded-md rounded-lg w-full p-2 flex justify-center p-8"
    >
      {/* Container for the full layout */}
      <div className=" rounded-md flex max-w-6xl w-full">


        
        {/* Right Section (Our Vision & Mission Text) */}
        <div className="rounded-md  w-1/3 p-8 flex flex-col justify-center">
          <h1 className="text-3xl font-bold mb-4">Our Vision & Mission</h1>
          <p className="text-white text-sm">
            This slide is 100% editable. Adapt it to your needs and capture your
            audiences attention.
          </p>
        </div>
       

        {/* Middle Section (Image) */}
        <div className="w-1/3">
          <img
            src="/img/mision.png"
            alt="Person in mountains"
            className="w-full h-full object-cover rounded-none"
             loading="lazy"
          />
        </div>
 {/* Left Section (Vision & Mission) */}
 <div className="text-white p-8 w-1/3 flex flex-col space-y-8 rounded-l-lg">
          {/* Vision Section */}
          <div className="flex items-start space-x-4">
            <div className="text-3xl">
              {/* Vision Icon */}
              <IconBinoculars className="w-8 h-8" />
            </div>
            <div>
              <h2 className="font-bold text-lg">Our Vision</h2>
              <p className="text-sm">
                This slide is 100% editable. Adapt it to your needs and capture
                your audiences attention.
              </p>
            </div>
          </div>

          {/* Mission Section 1 */}
          <div className="flex items-start space-x-4">
            <div className="text-3xl">
              {/* Mission Icon 1 */}
              <Target className="w-8 h-8" />
            </div>
            <div>
              <h2 className="font-bold text-lg">Our Mission</h2>
              <p className="text-sm">
                This slide is 100% editable. Adapt it to your needs and capture
                your audiences attention.
              </p>
            </div>
          </div>

          {/* Mission Section 2 */}
          <div className="flex items-start space-x-4">
            <div className="text-3xl">
              {/* Mission Icon 2 */}
              <Lightbulb className="w-8 h-8" />
            </div>
            <div>
              <h2 className="font-bold text-lg">Our Mission</h2>
              <p className="text-sm">
                This slide is 100% editable. Adapt it to your needs and capture
                your audiences attention.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Vision;
