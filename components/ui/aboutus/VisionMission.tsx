// import { IconBinoculars } from "@tabler/icons-react";
// import { Lightbulb, Target, Settings, Users } from "lucide-react";
import React from "react";

// Mapping icons dynamically
// const getIconComponent = (iconName) => {
//   switch (iconName) {
//     case "IconBinoculars":
//       return <IconBinoculars className="w-8 h-8" />;
//     case "Target":
//       return <Target className="w-8 h-8" />;
//     case "Lightbulb":
//       return <Lightbulb className="w-8 h-8" />;
//     case "Users":
//       return <Users className="w-8 h-8" />;
//     case "Settings":
//       return <Settings className="w-8 h-8" />;
//     default:
//       return null;
//   }
// };VisionMission = () => {
const VisionMission = ()=>{
  return (
   
      <div id="about" className="relative bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 overflow-hidden mt-16">
    <div className="max-w-7xl mx-auto">
        <div className="relative z-10 pb-8 bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <svg className="hidden lg:block absolute right-0 inset-y-0 h-full w-48 text-white transform translate-x-1/2"
                fill="transparent" viewBox="0 0 100 100" preserveAspectRatio="none" aria-hidden="true">
                <polygon points="50,0 100,0 50,100 0,100"></polygon>
            </svg>

            <div className="pt-1"></div>

            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
                <div className="sm:text-center lg:text-left">
                    <h2 className="my-6 text-2xl tracking-tight font-extrabold text-white sm:text-3xl md:text-4xl">
                        Our vision
                    </h2>

                    <p>
                        Donec porttitor, enim ut dapibus lobortis, lectus sem tincidunt dui, eget ornare lectus ex non
                        libero. Nam rhoncus diam ultrices porttitor laoreet. Ut mollis fermentum ex, vel viverra lorem
                        volutpat sodales. In ornare porttitor odio sit amet laoreet. Sed laoreet, nulla a posuere
                        ultrices, purus nulla tristique turpis, hendrerit rutrum augue quam ut est. Fusce malesuada
                        posuere libero, vitae dapibus eros facilisis euismod. Sed sed lobortis justo, ut tincidunt
                        velit. Mauris in maximus eros.
                    </p>
                </div>
            </main>
        </div>
    </div>
    <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <img className="h-56 w-full object-cover object-top sm:h-72 md:h-96 lg:w-full lg:h-full" src="https://cdn.pixabay.com/photo/2016/03/23/04/01/woman-1274056_960_720.jpg" alt=""/>
    </div>
</div>
    
    // <div
    //   className={`bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 flex justify-center rounded-lg w-full m-5 max-w-6xl  ${className}`}
    // >
    //   {/* <div className="flex flex-row md:flex-row  "> */}

    //   <div className="rounded-md flex flex-row md:flex-row w-fit">
    //     {/* Left Section (Dynamically showing points based on Vision or Mission) */}
    //     {/* <div
    //         className={`text-white py-5 px-0 md:p-5 md:w-1/2 justify-between flex flex-row space-y-8 rounded-l-lg ${isVision ? "md:order-3 order-3" : "order-3 md:order-3"
    //           }`}
    //       >
    //         Map through the points array and render each point
    //         {points.map((point, index) => (
    //           <div key={index} className="flex items-start space-x-6">
    //             <div className="text-4xl"> */}
    //     {/* Dynamic icon */}
    //     {/* {getIconComponent(point.icon)}
    //             </div>
    //             <div>
    //               <h2 className="font-bold text-xl">{point.title}</h2>
    //               <p className="text-sm">{point.shortDescription}</p>
    //             </div>
    //           </div>
    //         ))}
    //       </div> */}

    //     <div className="flex flex-row md:w-2/5 ">
    //       {/* Middle Section (Image) */}
    //       {/* Right Section (Our Vision & Mission Text) */}
    //       <div className={` order-1 ${isVision ? "" : ""}`}>
    //         <img
    //           src={imgSrc}
    //           alt={isVision ? "Vision Image" : "Mission Image"}
    //           className=" object-cover  object-center rounded-md"
    //           loading="lazy"
    //         />
    //       </div>
    //     </div>
    //     <div
    //       className={`rounded-md text-justify w-3/5 flex flex-col ${isVision ? "order-2" : "order-1 md:order-1"
    //         }`}
    //     >
    //       <h2 className="text-5xl font-bold mb-8 mt-5 flex justify-center">{title}</h2>
    //       <p className="text-white text-lg  mx-20">{description}</p>
    //     </div>
    //   </div>
    // </div>
    // </div>
  );
};

export default VisionMission;
