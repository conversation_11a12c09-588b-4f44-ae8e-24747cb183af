"use client";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import axios from "axios";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { Copy } from "lucide-react";


interface BuyNowPromptProps {
  rapp: Rapp;
  user: User;
  id: any;
}
interface Rapp {
  price: number;
  id: string;
  promptcost: number;
  prompt: string;
  promptpurchase: any;
}

interface User {
  id: string;
  coinBalance: number;
  // Add other properties if needed
}

const BuyPrompt = ({ rapp, user, id }: BuyNowPromptProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isPromptModalOpen, setPromptIsModalOpen] = useState(false);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<any>();

  //----------------- Fetching Rapp prompt -----------------
  const fetchPrompts = async () => {
    try {
      setIsLoading(true);
      const result = await axios.get(`/api2/prompts/${id}`);
      setData(result.data);
    } catch (error) {
      console.error("Error fetching prompts:", error);
    } finally {
      setIsLoading(false);
    }
  };
  const rapps = data ? data?.data : []; //not getting rapps

  const handleBuyNow = () => {
    if (user) {
      if (user.coinBalance >= rapp.price) {
        setIsModalOpen(true);
      } else {
        toast.error("Insufficient Credits to buy the prompt.");
      }
    } else {
      toast.error("Please login to Buy Prompt");
    }
  };

  const handleViewPrompt = () => {
    if (user) {
      fetchPrompts();
      setPromptIsModalOpen(true);
    } else {
      toast.error("Please login to view prompt");
    }
  };

  const handleConfirmPurchase = async () => {
    try {
      toast.loading("Purchasing...");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/purchasePrompt`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            rappId: rapp.id,
          }),
        }
      );
      const result = await response.json();
      toast.dismiss();

      if (response.ok) {
        setIsModalOpen(false);
        toast.success(
          "Prompt Purchase Successful and Credits have been deducted."
        );
        fetchPrompts();
        setPromptIsModalOpen(true);
      } else {
        toast.dismiss();
        toast.error(result.message);
      }
    } catch (error) {
      toast.error(`An error occurred while purchasing the prompt.`);
    }
  };

  //copy prompt settings
  const copyToClipboard = (text) => {
    const plainText = text?.replace(/[#*_`>-]/g, "")?.replace(/\n{2,}/g, "\n"); 
    navigator.clipboard
      .writeText(plainText)
      .then(() => toast.success("Text copied to clipboard!"))
      .catch((error) => console.error("Failed to copy text:", error));
  };

  //const purchase_userIds = rapps?.promptpurchase?.map(purchase => purchase.user?.id).filter(id => id !== undefined);
  const purchaseuserIds = rapp?.promptpurchase
    ?.map((purchase) => purchase?.user?.id)
    .filter((id) => id !== undefined);

  return (
    <>
      {purchaseuserIds?.includes(user?.id) ? (
        <button
          onClick={handleViewPrompt}
          className="font-semibold text-base"
        >
          View Prompt
        </button>
      ) : (
        <button
          onClick={handleBuyNow}
          className="font-semibold text-base"
        >
          Get Prompt
        </button>
      )}

      {isModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">
              {rapp.promptcost === 0 ? (
                "This asset is free and No credits will be deducted for this asset. Confirm your purchase"
              ) : (
                <div>
                  The Prompt Price of
                  <span style={{ marginLeft: "6px", marginRight: "6px" }}>
                    {rapp?.promptcost}
                  </span>
                  <img
                    src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                    alt="Coin"
                    style={{
                      width: "25px",
                      height: "27px",
                      display: "inline",
                      margin: "0px 4px 4px 2px",
                    }}
                  />
                  credits will be deducted from your account.
                </div>
              )}
            </p>
            {errorMessage && <p className="text-red-500">{errorMessage}</p>}
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button
                variant="outline"
                onClick={handleConfirmPurchase}
                className="bg-white text-indigo-800"
              >
                Get Prompt
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}

      {/*----------------------------------- model for revealing prompt --------------------------------*/}
      {isPromptModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isPromptModalOpen}
          onClose={() => {
            setPromptIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-1 h-[400px] overflow-auto mb-2 no-scrollbar">

            {/* system prompt */}
            <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    System Prompt
                  </p>
                  <button
                    onClick={() => copyToClipboard(rapps.systemprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.systemprompt ? (
                  <p className="text-sm font-semibold">
                    {" "}
                    {rapps.systemprompt}{" "}
                  </p>
                ) : (
                  <p className="text-sm text-slate-300">there have no system prompt available</p>
                )}


            <div style={{ display: "flex", alignItems: "center" }}>
              <p className="text-xl font-bold text-neo-foreground">
                User Prompt
              </p>
              <button
                onClick={() => copyToClipboard(rapps.prompt)}
                title="Copy Prompt"
                style={{ marginLeft: "10px", cursor: "pointer" }}
              >
                <Copy size={18} />
              </button>
            </div>
            {rapps.prompt ? (
              <p className="text-sm font-semibold"> {rapps.prompt} </p>
            ) : (
              <p className="text-sm text-slate-300">there have no user prompt available</p>
            )}

            {rapps.modelType === "text" ? (
              <>
                {/* negative prompts */}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    Negative Prompt
                  </p>
                  <button
                    onClick={() => copyToClipboard(rapps.negativeprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.negativeprompt ? (
                  <p className="text-sm font-semibold">
                    {" "}
                    {rapps.negativeprompt}{" "}
                  </p>
                ) : (
                  <p className="text-sm text-slate-300">there have no negative prompt available</p>
                )}


                {/* settings  */}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    settings
                  </p>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        `${rapps.settings.name}  ${rapps.settings.value}`
                      )
                    }
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                <>
                  {rapps.settings?.name && (
                    <p className="text-sm font-semibold">
                      name: {rapps.settings.name}
                    </p>
                  )}
                  {rapps.settings?.value && (
                    <p className="text-sm font-semibold">
                      value: {rapps.settings.value}
                    </p>
                  )}
                  {rapps.settings?.seed && (
                    <p className="text-sm font-semibold">
                      seed: {rapps.settings.seed}
                    </p>
                  )}
                  {rapps.settings?.steps && (
                    <p className="text-sm font-semibold">
                      steps: {rapps.settings.steps}
                    </p>
                  )}
                  {rapps.settings?.maxtokens && (
                    <p className="text-sm font-semibold">
                      maxTokens: {rapps.settings.maxtokens}
                    </p>
                  )}
                  {rapps.settings?.stopsequences && (
                    <p className="text-sm font-semibold">
                      stopsequences: {rapps.settings.stopsequences}
                    </p>
                  )}
                  {rapps.settings?.temperature && (
                    <p className="text-sm font-semibold">
                      temperature: {rapps.settings.temperature}
                    </p>
                  )}
                </>
              </>
            ) : rapps.modelType === "image" ? (
              <>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    Negative Prompt
                  </p>
                  <button
                    onClick={() => copyToClipboard(rapps.negativeprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.negativeprompt ? (
                  <p className="text-sm font-semibold">
                    {" "}
                    {rapps.negativeprompt}{" "}
                  </p>
                ) : (
                  <p className="text-sm text-slate-300">there have no negative prompt available</p>
                )}

                {/* system prompt */}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    system Prompt
                  </p>
                  <button
                    onClick={() => copyToClipboard(rapps.systemprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.systemprompt ? (
                  <p className="text-sm font-semibold">
                    {" "}
                    {rapps.systemprompt}{" "}
                  </p>
                ) : (
                  <p className="text-sm text-slate-300">there have no system prompt available</p>
                )}

                {/* settings  */}

                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    settings Prompt
                  </p>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        `${rapps.settings.name}  ${rapps.settings.value}`
                      )
                    }
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.settings?.name && (
                  <p className="text-sm font-semibold">
                    name: {rapps.settings.name}
                  </p>
                )}
                {rapps.settings?.value && (
                  <p className="text-sm font-semibold">
                    value: {rapps.settings.value}
                  </p>
                )}
                {rapps.settings?.seed && (
                  <p className="text-sm font-semibold">
                    seed: {rapps.settings.seed}
                  </p>
                )}
                {rapps.settings?.steps && (
                  <p className="text-sm font-semibold">
                    steps: {rapps.settings.steps}
                  </p>
                )}
                {rapps.settings?.maxtokens && (
                  <p className="text-sm font-semibold">
                    maxTokens: {rapps.settings.maxtokens}
                  </p>
                )}
                {rapps.settings?.stopsequences && (
                  <p className="text-sm font-semibold">
                    stopsequences: {rapps.settings.stopsequences}
                  </p>
                )}
                {rapps.settings?.temperature && (
                  <p className="text-sm font-semibold">
                    temperature: {rapps.settings.temperature}
                  </p>
                )}
                <></>
              </>
            ) : rapps.modelType === "audio" ? (
              <>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    Negative Prompt
                  </p>
                  <button
                    onClick={() => copyToClipboard(rapps.negativeprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.negativeprompt ? (
                  <p className="text-sm font-semibold">
                    {" "}
                    {rapps.negativeprompt}{" "}
                  </p>
                ) : (
                  <p className="text-sm text-slate-300">there have no negative prompt available</p>
                )}

                {/* system prompt */}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    system Prompt
                  </p>
                  <button
                    onClick={() => copyToClipboard(rapps.systemprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.systemprompt ? (
                  <p className="text-sm font-semibold">
                    {" "}
                    {rapps.systemprompt}{" "}
                  </p>
                ) : (
                  <p className="text-sm text-slate-300">there have no system prompt available</p>
                )}

                {/* settings  */}

                <div style={{ display: "flex", alignItems: "center" }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">
                    settings Prompt
                  </p>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        `${rapps.settings.name}  ${rapps.settings.value}`
                      )
                    }
                    title="Copy Prompt"
                    style={{ marginLeft: "10px", cursor: "pointer" }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                {rapps.settings?.name && (
                  <p className="text-sm font-semibold">
                    name: {rapps.settings.name}
                  </p>
                )}
                {rapps.settings?.value && (
                  <p className="text-sm font-semibold">
                    value: {rapps.settings.value}
                  </p>
                )}
                {rapps.settings?.seed && (
                  <p className="text-sm font-semibold">
                    seed: {rapps.settings.seed}
                  </p>
                )}
                {rapps.settings?.steps && (
                  <p className="text-sm font-semibold">
                    steps: {rapps.settings.steps}
                  </p>
                )}
                {rapps.settings?.maxtokens && (
                  <p className="text-sm font-semibold">
                    maxTokens: {rapps.settings.maxtokens}
                  </p>
                )}
                {rapps.settings?.stopsequences && (
                  <p className="text-sm font-semibold">
                    stopsequences: {rapps.settings.stopsequences}
                  </p>
                )}
                {rapps.settings?.temperature && (
                  <p className="text-sm font-semibold">
                    temperature: {rapps.settings.temperature}
                  </p>
                )}
                <></>
              </>
            ) : null}

            {/*------------------------------ Prompt Setting---------------------------- */}

            {rapps.modelType === "image" &&
            rapps.imagesettings &&
            Object.keys(rapps.imagesettings).length > 0 ? (
              <div>
                {rapps.imagesettings !== undefined && (
                  <h2 className="text-xl font-bold text-neo-foreground mt-4">
                    Image Settings
                    <button
                      onClick={() => copyToClipboard(rapps.imagesettings)}
                      title="Copy Image Settings"
                      style={{ marginLeft: "10px" }}
                    >
                      <Copy size={18} />
                    </button>
                  </h2>
                )}

                {rapps.imagesettings.quality !== undefined && (
                  <p>Quality: {rapps.imagesettings.quality}</p>
                )}
                {rapps.imagesettings.seed !== undefined && (
                  <p>Seed: {rapps.imagesettings.seed}</p>
                )}
                {rapps.imagesettings.cfg !== undefined && (
                  <p>Cfg: {rapps.imagesettings.cfg}</p>
                )}
                {rapps.imagesettings.steps !== undefined && (
                  <p>Steps: {rapps.imagesettings.steps}</p>
                )}
                {rapps.imagesettings.prompt_strength !== undefined && (
                  <p>Prompt Strength: {rapps.imagesettings.prompt_strength}</p>
                )}
              </div>
            ) : rapps.modelType === "text" &&
              rapps.textsettings &&
              Object.keys(rapps.textsettings).length > 0 ? (
              <div>
                {(rapps.textsettings !== undefined ||
                  rapps.textsettings ===
                    rapps?.textsettings?.stopsequences) && (
                  <h2 className="text-xl font-bold text-neo-foreground mt-4">
                    Text Settings
                    <button
                      onClick={() => copyToClipboard(rapps.textsettings)}
                      title="Copy Text Settings"
                      style={{ marginLeft: "10px" }}
                    >
                      <Copy size={18} />
                    </button>
                  </h2>
                )}

                {rapps.textsettings.temperature !== undefined && (
                  <p>Temperature: {rapps.textsettings.temperature}</p>
                )}
                {rapps.textsettings.mintokens !== undefined && (
                  <p>Min Tokens: {rapps.textsettings.mintokens}</p>
                )}
                {rapps.textsettings.maxtokens !== undefined && (
                  <p>Max Tokens: {rapps.textsettings.maxtokens}</p>
                )}
                {rapps.textsettings.seed !== undefined && (
                  <p>Seed: {rapps.textsettings.seed}</p>
                )}
                {rapps.textsettings.top_k !== undefined && (
                  <p>Top K: {rapps.textsettings.top_k}</p>
                )}
                {rapps.textsettings.top_p !== undefined && (
                  <p>Top P: {rapps.textsettings.top_p}</p>
                )}
                {rapps.textsettings.length_penalty !== undefined && (
                  <p>Length Penalty: {rapps.textsettings.length_penalty}</p>
                )}
                {rapps.textsettings.presense_penalty !== undefined && (
                  <p>Presence Penalty: {rapps.textsettings.presense_penalty}</p>
                )}
              </div>
            ) : rapps.modelType === "audio" &&
              rapps.audiosettings &&
              Object.keys(rapps.audiosettings).length > 0 ? (
              <div>
                {rapps.audiosettings !== undefined && (
                  <h2 className="text-xl font-bold text-neo-foreground mt-4">
                    Audio Settings
                    <button
                      onClick={() => copyToClipboard(rapps.audiosettings)}
                      title="Copy Audio Settings"
                      style={{ marginLeft: "10px" }}
                    >
                      <Copy size={18} />
                    </button>
                  </h2>
                )}
                <p className="text-lg font-bold">Steps Groups</p>
                {rapps.audiosettings.steps_group?.steps !== undefined && (
                  <p>Steps: {rapps.audiosettings.steps_group.steps}</p>
                )}
                {rapps.audiosettings.steps_group?.steps_min !== undefined && (
                  <p>Steps Min: {rapps.audiosettings.steps_group.steps_min}</p>
                )}

                {rapps.audiosettings.steps_group?.steps_max !== undefined && (
                  <p>Steps Max: {rapps.audiosettings.steps_group.steps_max}</p>
                )}
                <p className="text-lg font-bold">Second Start Groups </p>

                {rapps.audiosettings.seconds_start_group?.seconds_start !==
                  undefined && (
                  <p>
                    Seconds Start:{" "}
                    {rapps.audiosettings.seconds_start_group.seconds_start}
                  </p>
                )}
                {rapps.audiosettings.seconds_start_group?.seconds_start_min !==
                  undefined && (
                  <p>
                    Seconds Start Min:{" "}
                    {rapps.audiosettings.seconds_start_group.seconds_start_min}
                  </p>
                )}
                {rapps.audiosettings.seconds_start_group?.seconds_start_max !==
                  undefined && (
                  <p>
                    Seconds Start Max:{" "}
                    {rapps.audiosettings.seconds_start_group.seconds_start_max}
                  </p>
                )}
                <p className="text-lg font-bold">Second Total Groups </p>

                {rapps.audiosettings.seconds_total_group?.seconds_total !==
                  undefined && (
                  <p>
                    Seconds Total:{" "}
                    {rapps.audiosettings.seconds_total_group.seconds_total}
                  </p>
                )}
                {rapps.audiosettings.seconds_total_group?.seconds_total_min !==
                  undefined && (
                  <p>
                    Seconds Total Min:{" "}
                    {rapps.audiosettings.seconds_total_group.seconds_total_min}
                  </p>
                )}
                {rapps.audiosettings.seconds_total_group?.seconds_total_max !==
                  undefined && (
                  <p>
                    Seconds Total Max:{" "}
                    {rapps.audiosettings.seconds_total_group.seconds_total_max}
                  </p>
                )}
              </div>
            ) : null}
          </div>
        </SpaceModal>
      )}
    </>
  );
};

export default BuyPrompt;
