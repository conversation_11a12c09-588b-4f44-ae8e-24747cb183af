"use client"

import Image from "next/image";
import MaxWidthWrapper from "../MaxWidthWrapper";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";

const JoinOurCommunity = () => {
  const router = useRouter();

  const ViewServices = () => {
    router.push("/services");
  };

  
  return (
    <>
      <MaxWidthWrapper className="px-0 md:px-10">
        <div className="relative  h-auto ">
          <div className="mx-auto pt-[50px] pb-8 px-0 sm:pb-5 md:grid lg:grid-cols-2 md:gap-x-8 md:gap-y-6">
            <div className="relative md:self-center h-full z-10 flex flex-col justify-center items-center bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-3 px-6 dark:focus:ring-offset-gray-800 transition-all ">
              <div className="absolute inset-0 flex justify-center items-center rounded">
                <Image
                  height={400}
                  width={1100}
                  src="https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/Image.png"
                  alt=""
                  className="object-cover w-full h-full rounded"
                />
              </div>
              <div className="mx-3 my-7 md:m-7 flex flex-col py-6 md:px-8 text-center gap-1.5 justify-center px-4 w-10/12 h-[80%] items-center rounded bg-[#FFFFFF1F] backdrop-blur-sm z-10">
                <h3 className="text-4xl font-medium">
                  {/* Do you have any questions?{" "} */}
                  Our Services
                </h3>
                <p className="text-base">Click View All to see more services</p>
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-violet-600 text-white hover:text-white hover:-translate-y-1 scale-105 py-4 px-5 text-md mt-2 rounded transition-transform duration-300"
                  onClick={ViewServices}
                >
                  View All
                </Button>
              </div>
            </div>

            <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent text-white rounded z-10
             focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white 
             dark:focus:ring-offset-gray-800 transition-all text-center mt-5 md:mt-0  border-slate-800 md:self-end h-full px-4
              md:px-8 py-8"> 
            <div className="w-full h-full pt-4 flex flex-col py-6 md:px-8 gap-1.5 justify-center items-center">
                <h3 className="text-4xl w-full font-bold">
                  {/* Do you have any questions?{" "} */}
                  Join Our Community
                </h3>
                <p className="text-base mt-2">Connect with us on discord</p>

                <Link href={"https://discord.gg/kPkYbzMvN3"} target="_blank">
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-violet-600 text-white hover:text-white hover:-translate-y-1 scale-105 py-4 px-5 text-md mt-2 rounded transition-transform duration-300"
                >
                  Join Us
                </Button>
                </Link>
                {/* <span className="text-base mt-6">
                  Join our social networks and stay updated with all the latest
                  news!
                </span> */}
              </div>
            
            
              {/* <div className=" py-6 md:px-8 text-center gap-1.5 justify-center">
                <h2 className="font-bold text-center tracking-tight text-4xl">
                  Join our community
                </h2>
                <div className="text-base py-2 mt-4">
                  <h1>Connect with us on discord</h1>
                  <Link
                    href="#"
                    className="text-muted-foreground ms-2 font-semibold border-b border-transparent transition-border
                     border-opacity-0 hover:border-opacity-100 hover:border-muted-foreground hover:-translate-y-1 scale-105"
                  >
                   <Button
                    size="sm"
                    className="ms-2 bg-gradient-to-r from-blue-600 to-violet-600 rounded text-white hover:text-white 
                    hover:-translate-y-1 scale-105 py-4 px-5 text-md mt-2 my-4 rounded transition-transform duration-300"
                  >
                  Join Us
                  </Button> 
                 </Link> 
                </div> */}
                {/* <ul className="list-disc mt-4">
                    <li>List some key features of your product.</li>
                    <li>Highlight any unique selling points.</li>
                  </ul> */}
                {/* <ul className="mt-3 flex justify-center gap-4 md:gap-6">
                  <li>
                    <a
                      href="#"
                      rel="noreferrer"
                      target="_blank"
                      className="text-primary transition hover:text-gray-700/75"
                    >
                      <span className="sr-only">Facebook</span>

                      <Facebook />
                    </a>
                  </li>

                  <li>
                    <a
                      href="#"
                      rel="noreferrer"
                      target="_blank"
                      className="text-primary transition hover:text-gray-700/75"
                    >
                      <span className="sr-only">Instagram</span>

                      <Instagram />
                    </a>
                  </li>

                  <li>
                    <a
                      href="#"
                      rel="noreferrer"
                      target="_blank"
                      className="text-primary transition hover:text-gray-700/75"
                    >
                      <span className="sr-only">Twitter</span>

                      <Twitter />
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      rel="noreferrer"
                      target="_blank"
                      className="text-primary transition hover:text-gray-700/75"
                    >
                      <span className="sr-only">Twitter</span>
                      <LinkedinIcon />
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      rel="noreferrer"
                      target="_blank"
                      className="text-primary transition hover:text-gray-700/75"
                    >
                      <span className="sr-only">GitHub</span>
                      <svg
                        className="h-6 w-6"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  </li>

                 
                </ul> */}

              {/* </div>
            </div> */}
            </div>
          </div>
        </div>
      </MaxWidthWrapper>
    </>
  );
};
export default JoinOurCommunity;
