import React, { useState, useEffect } from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Info } from "lucide-react";
// import Slider from "@radix-ui/react-slider";
import * as Slider from "@radix-ui/react-slider";


const RangeSliderInput = ({ setting, value, onChange }) => {
  const { 
    name, 
    description, 
    type,
    minValue,
    maxValue,
    defaultValue
  } = setting;

  // Local state to handle the slider value
  const [localValue, setLocalValue] = useState(value ?? defaultValue ?? minValue);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value ?? defaultValue ?? minValue);
  }, [value, defaultValue, minValue]);

  // Calculate appropriate step based on type and range
  const step = type === 'float' ? 0.1 : 1;

  // Format display value based on type
  const formatDisplayValue = (val) => {
    if (type === 'float') {
      return Number(val).toFixed(1);
    }
    return Math.round(val);
  };

  const handleSliderChange = (newValues) => {
    const newValue = newValues[0];
    setLocalValue(newValue);
    onChange(name, newValue);
  };

  const handleInputChange = (e) => {
    let newValue = parseFloat(e.target.value);
    
    // Handle invalid input
    if (isNaN(newValue)) {
      newValue = minValue;
    }

    // Clamp value between min and max
    newValue = Math.max(minValue, Math.min(maxValue, newValue));
    
    // Format based on type
    if (type === 'integer') {
      newValue = Math.round(newValue);
    }
    
    setLocalValue(newValue);
    onChange(name, newValue);
  };

  return (
    <div className="mb-6 w-full space-y-2">
      <div className="flex gap-1 items-center">
        <Label className="text-lg">{name.replace(/_/g, " ")}</Label>
        {description && (
          <div className="relative">
            <div className="group -mt-3">
              <Info className="w-4 h-4 text-yellow-400 cursor-pointer z-20" />
              <div className="absolute z-40 left-full top-1/2 transform -translate-y-1/2 ml-2 w-48 sm:w-60 p-2 bg-gray-800 text-white text-sm rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <p className="italic text-sm text-yellow-400/[0.8] break-words whitespace-normal">
                  {description}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-grow">
          <Slider.Root
            className="relative flex items-center select-none touch-none w-full h-8"
            defaultValue={[localValue]}
            value={[localValue]}
            onValueChange={handleSliderChange}
            min={minValue}
            max={maxValue}
            step={step}
          >
            <Slider.Track className="bg-gray-300 relative grow rounded-full h-2">
              <Slider.Range className="absolute bg-blue-500 rounded-full h-full" />
            </Slider.Track>
            <Slider.Thumb 
              className="block w-6 h-6 bg-blue-500 rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400"
            >
              <div className="absolute top-[-28px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded-md shadow-lg">
                {formatDisplayValue(localValue)}
              </div>
            </Slider.Thumb>
          </Slider.Root>
        </div>
        <div className="w-20">
          <Input
            type="number"
            value={formatDisplayValue(localValue)}
            onChange={handleInputChange}
            min={minValue}
            max={maxValue}
            step={step}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex justify-between text-sm text-gray-500">
        <span>{formatDisplayValue(minValue)}</span>
        <span>{formatDisplayValue(maxValue)}</span>
      </div>
    </div>
  );
};

export default RangeSliderInput;