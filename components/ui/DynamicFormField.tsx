import React from "react";
import RangeSliderInput from "./RangeSliderInput";

interface FieldConfig {
  type: "select" | "range" | "text" | "checkbox";
  options?: { value: string; label: string }[];
  min?: number;
  max?: number;
}

interface DynamicFieldProps {
  setting: string;
  value: any;
  config: FieldConfig;
  onChange: (name: string, value: any) => void;
}

const DynamicFormField: React.FC<DynamicFieldProps> = ({
  setting,
  value,
  config,
  onChange,
}) => {
  // console.log("config", config);
  // const handleChange = (
  //   e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  // ) => {
  //   const newValue =
  //     e.target.type === "range" ? Number(e.target.value) : e.target.value;
  //   onChange(setting, newValue);
  //   console.log(newValue)
  // };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    // Check if the target is a number or range input
    let newValue: number | string;
    if (e.target.type === "range" || e.target.type === "number") {
      newValue = parseInt(e.target.value, 10); // Convert to integer
    } else {
      newValue = e.target.value; // For other types like text, keep as string
    }

    if (typeof newValue === "string") {
      // Convert "1_1" -> "1:1", "16_9" -> "16:9", etc.
      newValue = newValue.replace("_", ":");
    }
    // If it's not a valid number, default to 0
    if (typeof newValue === "number" && isNaN(newValue)) {
      newValue = 0; // Default value
    }

    // Call the onChange function with the appropriate value
    onChange(setting, newValue as number); // Explicitly cast to number for safety

    // console.log("newValue",newValue);
  };

  // console.table([setting]);
  // console.table([setting, value, config, onChange]);
  switch (config.type) {
    case "select":
      return (
        <select
          name={setting}
          value={value || ""}
          onChange={handleChange}
          className="w-full p-3 bg-indigo-900 text-white rounded-lg focus:ring-2 focus:ring-yellow-400"
        >
          <option value="" disabled>
            Select an option
          </option>
          {config.options?.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      );

    case "range":
      const isMaxZero = config.max === 0; // Check if max is zero
      return (
        <div className="flex items-center gap-4">
          <input
            type="range"
            name={setting}
            min={config.min || 0}
            max={isMaxZero ? 0 : config.max || 100} // Set max to 0 if it's zero
            value={value || 0}
            step={isMaxZero ? 0 : 0.5}
            onChange={isMaxZero ? undefined : handleChange}
            disabled={isMaxZero}
            className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${
              isMaxZero
                ? "opacity-50 cursor-not-allowed"
                : "bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"
            } focus:outline-none scroll-smooth`}
            style={{
              background: `linear-gradient(to right, #4F46E5 0%, #4F46E5 ${
                ((value - (config.min || 0)) /
                  ((config.max || 100) - (config.min || 0))) *
                100
              }%, #E5E7EB ${
                ((value - (config.min || 0)) /
                  ((config.max || 100) - (config.min || 0))) *
                100
              }%, #E5E7EB 100%)`,
            }}
          />
          <div className="w-20 text-center">
            <span className="text-xl font-medium text-white">{value || 0}</span>
          </div>
        </div>
      );

    case "checkbox":
      return (
        <input
          type="checkbox"
          name={setting}
          checked={Boolean(value)}
          onChange={(e) => onChange(setting, e.target.checked)}
          className="w-5 h-5 bg-indigo-900 rounded-lg focus:ring-2 focus:ring-yellow-400"
        />
      );

    case "text":
    default:
      return (
        // <input
        //   type="number"
        //   name={setting}
        //   value={value}
        //   min={0}
        //   max={100}
        //   onChange={handleChange}
        //   className="w-full p-2 rounded bg-indigo-700 text-white placeholder-gray-300 scroll-smooth"
        //   placeholder={`Enter ${setting.replace(/_/g, " ")}`}
        // />
          <div className="mb-4 w-full">
            <RangeSliderInput
          // key={name}
          setting={setting}
          value={value}
          onChange={handleChange}
        />
          </div>
      );
  }
};

export default DynamicFormField;
