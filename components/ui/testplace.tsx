"use client";
import { use<PERSON>allback, useEffect, useRef, useState } from "react";
import axios from "axios";
import Container from "@/components/ui/container";
import { BackgroundBeams } from "@/components/ui/background-beams";
import SearchBar from "@/components/ui/searchbar";
import ProductReel from "@/components/ProductReel";
import RentReel from "@/components/RentReel";
import FilterComponent from "@/components/ui/filterComponent";
import Loader from "@/components/Loader";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "./button";
import DynamicSquareBackground from "../landingWiget";
import ProductReelExplore from "../ProductReelExplore";
import { usePathname } from "next/navigation";

interface FilterOption {
  label: string;
  value: string;
  checked: boolean;
}

export interface FilterSection {
  title: string;
  options: FilterOption[];
}

const initialFilters = {
  all: [
    {
      title: "Sort By",
      options: [
        { label: "All", value: "all", checked: true },
        { label: "Featured", value: "isFeatured", checked: false },
        { label: "Most Popular", value: "purchases", checked: false },
        { label: "Trending", value: "likes", checked: false },
      ],
    },

    {
      title: "Category",
      options: [],
    },
    {
      title: "Model",
      options: [
        // { label: "All", value: "all", checked: true },
        // { label: "FLUX", value: "FLUX-SCHNELL", checked: false },
        // { label: "GPT 4o Mini", value: "GPT-4o-Mini", checked: false },
        // { label: "GPT 4o", value: "GPT-4o", checked: false },
        // { label: "LLAMA3", value: "LLAMA3-70B", checked: false },
        // { label: "Llama-3.2-11b-vision", value: "llama-3.2-11b-vision", checked: false },
        // { label: "llava v1.5", value: "llavav1.5", checked: false },
        // { label: "PuLID-Image Editing", value: "PuLID-Image", checked: false },
        // { label: "Stable Audio Open", value: "Stable Audio Open", checked: false },
        // { label: "SDXL", value: "SDXL", checked: false },
        // { label: "Codeformer", value: "Codeformer", checked: false },
        // { label: "Ltx-video", value: "ltx-video", checked: false },
        // { label: "Ideogram-v2a", value: "ideogram-v2a", checked: false },
        // { label: "Gemma2-9b-it", value: "gemma2-9b-it", checked: false },
        // { label: "Qwen-2.5-32b", value: "qwen-2.5-32b", checked: false },
        // { label: "Kokoro-82m", value: "kokoro-82m", checked: false },
        // { label: "DeepSeek-R1", value: "DeepSeek-R1", checked: false },
      ],
    },
    {
      title: "Tags",
      options: [],
    },
  ],
  productsOnly: [
    {
      title: "Sort By",
      options: [
        { label: "All", value: "all", checked: true },
        { label: "Featured", value: "isFeatured", checked: false },
        { label: "Most Popular", value: "purchases", checked: false },
        { label: "Trending", value: "likes", checked: false },
      ],
    },
    {
      title: "Product Type",
      options: [
        { label: "All", value: "all", checked: true },
        { label: "GPT", value: "gpt", checked: false },
        { label: "Image", value: "image", checked: false },
        { label: "Audio", value: "music", checked: false },
        { label: "Video", value: "video", checked: false },
        { label: "3D Model", value: "3d", checked: false },
      ],
    },
    {
      title: "Price",
      options: [
        { label: "All", value: "all", checked: true },
        { label: "Free", value: "free", checked: false },
        { label: "Paid", value: "paid", checked: false },
      ],
    },
    {
      title: "Category",
      options: [],
    },
  ],
};

interface RentPlaceProps {
  user: any;
}

const TestPlace: React.FC<RentPlaceProps> = ({ user }) => {
  const skipURLParamsRef = useRef(false);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname(); // Get current path

  const [skipURLSync, setSkipURLSync] = useState(false);
  const [filters, setFilters] = useState(initialFilters.all);
  const [displayProducts, setDisplayProducts] = useState(false);
  const [sortingString, setSortingString] = useState("");
  const [modelString, setModelString] = useState("");
  const [tagsString, setTagsString] = useState("");
  const [categoryString, setCategoryString] = useState("");
  const [productTypeString, setProductTypeString] = useState("");
  const [rentReelFilterCriteria, setRentReelFilterCriteria] = useState<{
    [key: string]: string[];
  }>({});
  const [productReelFilterCriteria, setProductReelFilterCriteria] = useState<{
    [key: string]: string[];
  }>({});
  const [selectedView, setSelectedView] = useState("rapps");
  const [modelOptions, setModelOptions] = useState([]);
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [tagOptions, setTagOptions] = useState([]);
  const skipSyncRef = useRef(false);
  const fetchCategoriesAndTagsAndModels = useCallback(async () => {
    try {
      const [categoryResponse, tagResponse, modelResponse] = await Promise.all([
        axios.get(`/api/category?limit=0`),
        axios.get(`/api/tags?limit=0`),
        axios.get(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/models/get-model-name`
        ),
      ]);

      const categories = categoryResponse.data.docs.map((category) => ({
        label: category.label,
        value: category.value,
        checked: false,
      }));

      const tags = tagResponse.data.docs.map((tag) => ({
        label: tag.label,
        value: tag.value,
        checked: false,
      }));

      const models = modelResponse.data.modelsArr.map((model) => ({
        label: model,
        value: model,
        checked: false,
      }));

      setCategoryOptions(categories);
      setTagOptions(tags);
      setModelOptions(models);
    } catch (error) {
      console.error("Error fetching categories or tags:", error);
    }
  }, []);

  useEffect(() => {
    if (
      categoryOptions.length === 0 ||
      tagOptions.length === 0 ||
      modelOptions.length === 0
    ) {
      fetchCategoriesAndTagsAndModels();
    }
  }, [
    categoryOptions,
    tagOptions,
    modelOptions,
    displayProducts,
    fetchCategoriesAndTagsAndModels,
  ]);

  useEffect(() => {
    const categoryFromURL = searchParams?.get("category") || "";
    console.log("category ==>", categoryFromURL);
    const tagFromURL = searchParams?.get("tag") || "";
    const modelFromURL = searchParams?.get("model") || "";
    const productTypeFromURL = searchParams?.get("productType") || "";
    const valueFromURL = searchParams?.get("value") || "";
    const labelFromURL = searchParams?.get("label") || "";

    const updatedFilters = filters.map((section) => {
      if (section.title === "Category") {
        return {
          ...section,
          options: [
            {
              label: "All",
              value: "all",
              checked: !categoryFromURL && !valueFromURL,
            },
            ...categoryOptions.map((option) => ({
              ...option,
              checked:
                categoryFromURL === option.value ||
                valueFromURL === option.value,
            })),
          ],
        };
      }
      if (section.title === "Tags" && !displayProducts) {
        return {
          ...section,
          options: [
            { label: "All", value: "all", checked: !tagFromURL },
            ...tagOptions.map((option) => ({
              ...option,
              checked: tagFromURL === option.value,
            })),
          ],
        };
      }
      if (section.title === "Model" && !displayProducts) {
        return {
          ...section,
          options: [
            { label: "All", value: "all", checked: !modelFromURL },
            ...modelOptions.map((option) => ({
              ...option,
              checked: modelFromURL === option.value,
            })),
          ],
        };
      }
      if (section.title === "Product Type" && displayProducts) {
        return {
          ...section,
          options: section.options.map((option) => ({
            ...option,
            checked: productTypeFromURL
              ? productTypeFromURL === option.value
              : option.value === "all",
          })),
        };
      }
      return section;
    });
    console.log("updatedFilters ==>", updatedFilters);
    setFilters(updatedFilters);

    if (categoryFromURL || valueFromURL) {
      setCategoryString(categoryFromURL || valueFromURL);
      setProductReelFilterCriteria((prev) => ({
        ...prev,
        category: [categoryFromURL || valueFromURL],
      }));
      setRentReelFilterCriteria((prev) => ({
        ...prev,
        category: [categoryFromURL || valueFromURL],
      }));
    }

    if (tagFromURL && !displayProducts) {
      setTagsString(tagFromURL);
      setRentReelFilterCriteria((prev) => ({
        ...prev,
        tags: [tagFromURL],
      }));
    }

    if (modelFromURL && !displayProducts) {
      setModelString(modelFromURL);
      setRentReelFilterCriteria((prev) => ({
        ...prev,
        model: [modelFromURL],
      }));
    }

    if (productTypeFromURL && displayProducts) {
      setProductTypeString(productTypeFromURL);
      setProductReelFilterCriteria((prev) => ({
        ...prev,
        productType: [productTypeFromURL],
      }));
    }
  }, [
    searchParams,
    displayProducts,
    categoryOptions,
    tagOptions,
    modelOptions,
    filters,
  ]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const displayProductsParam = params.get("displayProducts");
      if (displayProductsParam === "true") {
        setDisplayProducts(true);

        setFilters(initialFilters.productsOnly);
      } else {
        setDisplayProducts(false);
        setFilters(initialFilters.all);
      }
    }
  }, []);

  // useEffect(() => {
  //   console.log("check outsite the settimeout");
  //   if (selectedView === "products") {
  //     console.log("check inside the products");

  //     setDisplayProducts(true);
  //     handleClearFilters();
  //     setFilters(initialFilters.productsOnly);
  //   } else if (selectedView === "rapps") {
  //     setDisplayProducts(false);

  //     console.log("check inside the rapps");

  //     handleClearFilters();
  //     setFilters(initialFilters.all);
  //   }
  // }, [selectedView]);
  // useEffect(() => {
  //   if (selectedView) {
  //     handleClearFilters();
  //   }
  // }, [selectedView]);
  const handleViewChange = async (view: "products" | "rapps") => {
    await handleClearFilters();
    // Pass flag if neede
    setSearchQuery(""); // Reset search bar
    handleSearch("");
    setProductReelFilterCriteria(() => ({
      category: [""],
    }));
    setRentReelFilterCriteria(() => ({
      category: [""],
    }));

    if (view === "products") {
      setFilters(initialFilters.productsOnly);
      setDisplayProducts(true);
    } else if (view === "rapps") {
      setFilters(initialFilters.all);
      setDisplayProducts(false);
    }
    setSelectedView(view);
  };

  const handleClearFilters = () => {
    console.log("lkajfkladsjfkld");
    // Reset all filters to their initial state
    // Update URL to remove query parameters
    const newSearchParams = new URLSearchParams(searchParams.toString());

    // Delete all query parameters
    newSearchParams.delete("category");
    newSearchParams.delete("tag");
    newSearchParams.delete("model");
    newSearchParams.delete("productType");

    // Update the URL using Next.js router
    router.replace(`${pathname}?${newSearchParams.toString()}`);
    // if (typeof window !== "undefined") {
    //   const url = new URL(window.location.href);
    //   url.searchParams.delete("category");
    //   url.searchParams.delete("tag");
    //   url.searchParams.delete("model");
    //   url.searchParams.delete("productType");
    //   window.history.replaceState({}, "", url.toString());
    // }
    const resetFilters = filters.map((section) => ({
      ...section,
      options: section.options.map((option) => ({
        ...option,
        checked: option.value === "all",
      })),
    }));

    console.log("resetFilters", resetFilters);
    setFilters(resetFilters);
    setSortingString("");
    setModelString("");
    setCategoryString("");
    setTagsString("");
    setModelString("");
    setProductTypeString("");
    setProductReelFilterCriteria({});
    setRentReelFilterCriteria({});
  };

  const handleOptionChange = (
    sectionTitle: string,
    optionValue: string,
    checked: boolean
  ) => {
    const updatedFilters = filters.map((section) => {
      if (section.title === sectionTitle) {
        // For sections that allow multiple selections
        if (
          section.title === "Tags" ||
          section.title === "Category" ||
          section.title === "Model" ||
          section.title === "Product Type"
        ) {
          if (optionValue === "all") {
            // When "all" is selected, uncheck all other options
            return {
              ...section,
              options: section.options.map((option) => ({
                ...option,
                checked: option.value === "all" ? !option.checked : false,
              })),
            };
          } else {
            // For other options, allow multiple selections and uncheck "all"
            return {
              ...section,
              options: section.options.map((option) => {
                if (option.value === optionValue) {
                  return { ...option, checked: checked };
                }
                if (option.value === "all") {
                  return { ...option, checked: false };
                }
                return option;
              }),
            };
          }
        }
        // For Price section, handle as a special case for multiple selection
        else if (section.title === "Price") {
          if (optionValue === "all") {
            return {
              ...section,
              options: section.options.map((option) => ({
                ...option,
                checked: option.value === "all" ? !option.checked : false,
              })),
            };
          } else {
            return {
              ...section,
              options: section.options.map((option) => {
                if (option.value === optionValue) {
                  return { ...option, checked: checked };
                }
                if (option.value === "all") {
                  return { ...option, checked: false };
                }
                return option;
              }),
            };
          }
        }
        // For other sections (like "Sort By"), maintain single selection
        else {
          return {
            ...section,
            options: section.options.map((option) => ({
              ...option,
              checked: option.value === optionValue,
            })),
          };
        }
      }
      return section;
    });

    setFilters(updatedFilters);

    // Update filter states based on the new selections
    let newSortingString = "";
    let newModelString = "";
    let newCategoryString = "";
    let newTagsString = "";
    let newProductTypeString = "";
    let newRentReelFilterCriteria: { [key: string]: any } = {
      ...rentReelFilterCriteria,
    };
    let newProductReelFilterCriteria: { [key: string]: any } = {
      ...productReelFilterCriteria,
    };

    updatedFilters.forEach((section) => {
      const activeOptions = section.options
        .filter((option) => option.checked)
        .map((option) => option.value);

      if (section.title === "Sort By") {
        const isAllSelected =
          activeOptions.includes("all") || activeOptions.length === 0;
        newSortingString = isAllSelected ? "" : activeOptions[0];
        setSortingString(newSortingString);
      } else if (section.title === "Model") {
        const isAllSelected =
          activeOptions.includes("all") || activeOptions.length === 0;
        newModelString = isAllSelected ? "" : activeOptions.join(",");
        setModelString(newModelString);
      } else if (section.title === "Category") {
        // Handle multiple category selections - apply to both products and rapps
        const isAllSelected =
          activeOptions.includes("all") || activeOptions.length === 0;
        newCategoryString = isAllSelected ? "" : activeOptions.join(",");
        setCategoryString(newCategoryString);

        if (!isAllSelected) {
          newProductReelFilterCriteria["category"] = activeOptions;
          newRentReelFilterCriteria["category"] = activeOptions;

          // Update URL with category parameter
          if (typeof window !== "undefined") {
            const url = new URL(window.location.href);
            url.searchParams.set("category", activeOptions.join(","));
            window.history.replaceState({}, "", url.toString());
          }
        } else {
          delete newProductReelFilterCriteria["category"];
          delete newRentReelFilterCriteria["category"];

          // Remove category from URL
          if (typeof window !== "undefined") {
            const url = new URL(window.location.href);
            url.searchParams.delete("category");
            window.history.replaceState({}, "", url.toString());
          }
        }
      } else if (section.title === "Tags" && !displayProducts) {
        // Handle multiple tag selections - only apply to rapps
        const isAllSelected =
          activeOptions.includes("all") || activeOptions.length === 0;
        newTagsString = isAllSelected ? "" : activeOptions.join(",");
        setTagsString(newTagsString);

        if (!isAllSelected) {
          newRentReelFilterCriteria["tags"] = activeOptions;

          // Update URL with tag parameter
          if (typeof window !== "undefined") {
            const url = new URL(window.location.href);
            url.searchParams.set("tag", activeOptions.join(","));
            window.history.replaceState({}, "", url.toString());
          }
        } else {
          delete newRentReelFilterCriteria["tags"];

          // Remove tag from URL
          if (typeof window !== "undefined") {
            const url = new URL(window.location.href);
            url.searchParams.delete("tag");
            window.history.replaceState({}, "", url.toString());
          }
        }
      } else if (section.title === "Product Type" && displayProducts) {
        // Handle multiple product type selections - only apply to products
        const isAllSelected =
          activeOptions.includes("all") || activeOptions.length === 0;
        newProductTypeString = isAllSelected ? "" : activeOptions.join(",");
        setProductTypeString(newProductTypeString);

        if (!isAllSelected) {
          newProductReelFilterCriteria["productType"] = activeOptions;

          // Update URL with productType parameter
          if (typeof window !== "undefined") {
            const url = new URL(window.location.href);
            url.searchParams.set("productType", activeOptions.join(","));
            window.history.replaceState({}, "", url.toString());
          }
        } else {
          delete newProductReelFilterCriteria["productType"];

          // Remove productType from URL
          if (typeof window !== "undefined") {
            const url = new URL(window.location.href);
            url.searchParams.delete("productType");
            window.history.replaceState({}, "", url.toString());
          }
        }
      } else if (section.title === "Products") {
        if (activeOptions.length > 0) {
          if (activeOptions.includes("all")) {
            // Reset product filters when "all" is selected
            delete newProductReelFilterCriteria["isFeatured"];
          } else {
            // Handle feature flag
            if (activeOptions.includes("isFeatured")) {
              newProductReelFilterCriteria["isFeatured"] = true;
            } else {
              delete newProductReelFilterCriteria["isFeatured"];
            }
          }
        }
      } else if (section.title === "Price") {
        const isFreeSelected = activeOptions.includes("free");
        const isPaidSelected = activeOptions.includes("paid");

        if (activeOptions.includes("all") || activeOptions.length === 0) {
          delete newProductReelFilterCriteria["price"];
        } else {
          if (isFreeSelected && isPaidSelected) {
            // Both free and paid are selected - no price filter needed
            delete newProductReelFilterCriteria["price"];
          } else if (isFreeSelected) {
            newProductReelFilterCriteria["price"] = ["0"];
          } else if (isPaidSelected) {
            newProductReelFilterCriteria["price"] = ["gt_0"];
          }
        }
      }
    });

    setRentReelFilterCriteria(newRentReelFilterCriteria);
    setProductReelFilterCriteria(newProductReelFilterCriteria);
  };
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query) {
      const newRentReelFilters = {
        ...rentReelFilterCriteria,
        name: [query.toLowerCase()],
      };
      const newProductReelFilters = {
        ...productReelFilterCriteria,
        name: [query.toLowerCase()],
      };
      setRentReelFilterCriteria(newRentReelFilters);

      setProductReelFilterCriteria(newProductReelFilters);
    } else {
      // Remove name filter if query is empty
      const { name: _, ...restRentFilters } = rentReelFilterCriteria;
      const { name: __, ...restProductFilters } = productReelFilterCriteria;

      setRentReelFilterCriteria(restRentFilters);
      setProductReelFilterCriteria(restProductFilters);
    }
  };

  const handleToggleDisplay = () => {
    setDisplayProducts((prevDisplayProducts) => {
      const newDisplayProducts = !prevDisplayProducts;

      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        if (newDisplayProducts) {
          url.searchParams.set("displayProducts", "true");
          setFilters(initialFilters.productsOnly);
        } else {
          url.searchParams.delete("displayProducts");
          setFilters(initialFilters.all);
        }
        window.history.replaceState({}, "", url.toString());
      }
      return newDisplayProducts;
    });
  };
  return (
    <Container>
      <div className="w-full mx-auto">
        <DynamicSquareBackground
          buttonHref="#"
          buttonHref1="/dashboard"
          buttonText=""
          buttonText1="LIST NOW"
          description="The ultimate platform for AI engineers, and creative minds to buy, sell, and monetize AI assests, prompts and applications effortlessly."
          tag="Explore"
          title="Marketplace"
          image="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/marketplace.png"
          image1=""
        />
      </div>
      <Loader />
      <BackgroundBeams className="flex items-center -z-10 flex-col justify-center px-2 md:px-10 w-full h-full"></BackgroundBeams>

      <div className="w-full mx-auto flex min-h-screen px-4">
        <aside className="sticky top-16 hidden lg:flex flex-col px-8 w-60 overflow-y-auto mt-4">
          <FilterComponent
            sections={filters}
            onClearFilters={handleClearFilters}
            onOptionChange={handleOptionChange}
            displayProducts={displayProducts}
            onToggleDisplay={handleToggleDisplay}
          />
        </aside>

        <main className="flex-1">
          <div className="flex flex-row gap-2 lg:items-center">
            <div className="w-full md:w-[50%] md:-mt-6 py-4">
              <SearchBar
                mainheader={false}
                onSearch={handleSearch}
                query={searchQuery}
              />
            </div>
            <div className="hidden md:hidden lg:block">
              <div className="flex gap-2">
                <Button
                  variant={`${selectedView === "rapps" ? "gradient" : "white"}`}
                  className={`w-full p-2 text-center rounded-lg ${
                    selectedView === "rapps"
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200"
                  }`}
                  // onClick={() => setSelectedView("rapps")}
                  onClick={() => handleViewChange("rapps")}
                >
                  AI Apps
                </Button>
                <Button
                  variant={`${
                    selectedView === "products" ? "gradient" : "white"
                  }`}
                  className={`w-full p-2 text-center rounded-lg ${
                    selectedView === "products"
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200"
                  }`}
                  // onClick={() => setSelectedView("products")}
                  onClick={() => handleViewChange("products")}
                >
                  Products
                </Button>
              </div>
            </div>

            <div className="block md:block lg:hidden md:w-36 w-36 py-4">
              <FilterComponent
                sections={filters}
                onClearFilters={handleClearFilters}
                onOptionChange={handleOptionChange}
                displayProducts={displayProducts}
                onToggleDisplay={handleToggleDisplay}
              />
            </div>
          </div>
          {displayProducts ? (
            <ProductReelExplore
              query={{}}
              filters={productReelFilterCriteria}
              title=""
              user={user}
              component="marketplace"
            />
          ) : (
            <div className="flex flex-col">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-6 border-t-2 border-indigo-600 py-4 items-center justify-between">
                <RentReel
                  href=""
                  query={{}}
                  sort={sortingString}
                  model={modelString}
                  category={categoryString}
                  tags={tagsString}
                  className="h-60 sm:h-48 md:h-48 lg:h-44 xl:h-40 max-w-full transform transition duration-200 hover:scale-105 hover:shadow-xl"
                  user={user}
                  rentReelFilterCriteria={rentReelFilterCriteria}
                  paginate={true}
                />
              </div>
            </div>
          )}
        </main>
      </div>
    </Container>
  );
};

export default TestPlace;
