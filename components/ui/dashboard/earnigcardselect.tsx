"use client";
import React, { useState } from "react";

const earningsData = [
  { title: "Balance", value: "$0" },
  { title: "Last 7 Days", value: "$150" },
  { title: "Last 28 Days", value: "$600" },
  { title: "Last 6 Months", value: "$1200" },
  { title: "Previous Year", value: "$5000" },
  { title: "Total Earnings", value: "$2000" },
];

function Earnings() {
  const [selectedPeriod, setSelectedPeriod] = useState("All");

  // Filter data based on the selected period
  const filteredData = earningsData.filter((item) =>
    selectedPeriod === "All" ? true : item.title === selectedPeriod
  );

  return (
    <div className="p-6">
      {/* Dropdown for selecting time period */}
      <div className="mb-4">
        <label
          htmlFor="period"
          className="block text-sm font-medium text-white mb-1"
        >
          Select Time Period
        </label>
        <select
          id="countries"
          className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full max-w-sm p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value)}
        >
          <option value="All">All</option>
          <option value="Last 7 Days">Last 7 Days</option>
          <option value="Last 28 Days">Last 28 Days</option>
          <option value="Total Earnings">Total Earnings</option>
        </select>
      </div>

      {/* Grid for displaying filtered data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {filteredData.map((item, index) => (
          <EarningCard key={index} title={item.title} value={item.value} />
        ))}
      </div>
    </div>
  );
}

// EarningCard Component
function EarningCard({ title, value }) {
  return (
    <div className="border rounded-lg p-4 bg-gray-50 max-w-sm relative transform transition duration-500 hover:scale-105 hover:shadow-xl">
      <div className="flex items-center space-x-1">
        <span className="text-gray-700 font-medium">{title}</span>
        <div className="relative group">
          <button className="flex items-center justify-center text-gray-600 hover:text-gray-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="h-5 w-5 border-2 border-gray-600 rounded-full"
              fill="none"
            >
              <circle cx="12" cy="12" r="10" fill="white" />
              <text
                x="12"
                y="16"
                textAnchor="middle"
                fontSize="12"
                fontWeight="bold"
                fill="black"
              >
                i
              </text>
            </svg>
          </button>
          {/* Tooltip */}
          <div className="absolute left-0 -top-8 w-max bg-gray-800 text-white text-xs rounded-md px-2 py-1 opacity-0 group-hover:opacity-100 transition duration-300">
            This is the tooltip message
          </div>
        </div>
      </div>
      <div className="mt-2 text-2xl font-bold text-gray-900">{value}</div>
    </div>
  );
}

export default Earnings;
