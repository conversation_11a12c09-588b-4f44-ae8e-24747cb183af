"use client";
import { getPayloadClient } from "@/server/get-payload";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { BellRing, Pencil, PlusCircle, Trash } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { DotsHorizontalIcon } from "@heroicons/react/outline";
import ActionBtn from "./actionbtn";
import CreateNew from "./createnew";
import InfiniteScrollList from "./InfiniteScrollList";
import { SpaceModal } from "@/components/SpaceModal";
import { useRouter } from "next/navigation";
import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";

const BlogList = ({
  initialBlogs,
  totalDocs,
  userId,
  title,
  limit,
}: {
  initialBlogs?: any[];
  totalDocs?: number;
  userId: string;
  title?: string;
  limit: number;
}) => {
  const [blogsData, setBlogsData] = React.useState(initialBlogs);
  const [adminReview, setAdminReview] = useState(false);
  const router = useRouter();

  const fetchBlogs = async (page: number) => {
    const res = await fetch(`/api/blogs?userId=${userId}&page=${page}`);
    const data = await res.json();
    return { items: data.docs, total: data.totalDocs };
  };

  useEffect(() => {
    fetchBlogs;
  }, []);

  const renderBlogs = (data: any) => (
    <article
      key={data.id}
      className="md:flex items-center bg-[#111D45] rounded-lg shadow-lg overflow-hidden cursor-pointer "
      onClick={(e) => {
        const target = e.target as HTMLElement; // Cast to HTMLElement
        if (!target.closest(".prevent-navigation")) {
          router.push(`/blog/${data.slug}`);
        }
      }}
    >
      {/* Left Image Section */}
      <div className="md:w-1/4">
        {" "}
        {/* Reduced width for the image */}
        <img
          src={data.images?.[0]?.image?.url || "/placeholder-image.jpg"}
          alt={data.title || "Unnamed Blog"}
          className="object-cover w-full md:h-56 h-full md:p-0 p-1"
        />
      </div>

      {/* Right Content Section */}
      <div className="md:w-3/4 p-5 text-white">
        <div className="flex justify-between text-xs text-gray-400 mb-1">
          <div>
            {new Date(data.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }) || "Unknown Date"}
          </div>
          <div className="flex gap-2 items-center">
            <div
              className="relative group"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                className="p-2 text-white hover:bg-indigo-800  rounded-full focus:outline-none"
                // onClick={(e) => e.stopPropagation()}
              >
                <DotsHorizontalIcon className="h-5 w-5 text-gray-400" />
              </button>
              <div className="hidden group-focus-within:block bg-indigo-800 absolute right-0 mt-2 w-24 bg-dash rounded-lg shadow-lg">
                <Link
                  href={`/dashboard/create/blog?Id=${data?.id}`}
                  className="flex items-center text-white p-2 hover:bg-indigo-700 rounded-t-lg"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Pencil className="text-gray-400 h-4 w-4 mr-2" />
                  Edit
                </Link>
                <div className="flex items-center space-x-2  text-white hover:bg-indigo-700 rounded-b-lg">
                  {/* <Trash className="text-gray-400 h-4" />  */}
                  <ActionBtn
                    slug={data.id}
                    collectionName="blogs"
                    className="flex gap-2 items-center .prevent-navigation w-full p-2"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </div>
            {data.adminReview && (
              <div
                className="cursor-pointer hover:bg-indigo-800  rounded-full p-2"
                onClick={(e) => {
                  e.stopPropagation();
                  setAdminReview(!adminReview);
                }}
                // onClick={() => setAdminReview(!adminReview)}
              >
                <BellRing size={18} />
                <SpaceModal
                  title="Admin Review "
                  description="this blog is not approved by admin..."
                  isOpen={adminReview}
                  onClose={() => setAdminReview(false)}
                >
                  {data.adminReview}
                </SpaceModal>
              </div>
            )}
          </div>
        </div>
        <h2 className="text-lg font-semibold mb-2 break-all whitespace-normal capitalize">
          <Link href={`/blog/${data.slug}`}>
            {data.title
              ? `${data.title.substring(0, 45)}...`
              : "No title Available"}
          </Link>
        </h2>
        <p className="text-sm text-gray-300 mb-2 break-all whitespace-normal first-letter:uppercase">
          {data.content
            ? `${data.content.substring(0, 250)}...`
            : "No Content Available"}
        </p>
        <div className="text-sm font-medium text-gray-400 mb-2 ">
          {" "}
          Reading time: {data.time} mins
        </div>
        <div className="flex items-center justify-between gap-2 text-gray-400">
          <div className="text-sm font-medium mt-2">
            <Badge className=" bg-indigo-300 rounded-lg cursor-default ">
              {capitalizeFirstLetter(data.tags || "Unknown Tags")}
            </Badge>
          </div>
          <Badge className=" bg-indigo-300 rounded-lg cursor-default ">
            {capitalizeFirstLetter(data.status || "Unknown Status")}
          </Badge>
        </div>
      </div>
    </article>
  );

  return (
    <div className="relative ">
      <div className="relative overflow-x-hidden">
        {blogsData.length === 0 ? (
          <CreateNew
            messg=" No blogs available. Create your first blog!"
            link="create/blog"
            moduleName="Blog"
          />
        ) : (
          <InfiniteScrollList
            fetchData={fetchBlogs}
            renderItem={renderBlogs}
            initialItems={blogsData}
            totalItems={totalDocs}
          />
        )}
      </div>
    </div>
  );
};

export default BlogList;
