import { PlusCircle } from "lucide-react";
import Link from "next/link";
import React from "react";

function CreateNew({ messg, link, moduleName }) {
  return (
    <div className="container mt-5 px-4 flex flex-col items-center justify-center gap-4 p-6 border-2 border-dotted border-gray-400 rounded-lg bg-black/[0.2]">
      <p className="text-base text-gray-300">{messg}</p>
      <Link
        href={`/dashboard/${link}`} // Adjust this URL to your "create new blog" page
        className="flex items-center gap-2 text-white bg-blue-600 px-4 py-2 rounded-lg hover:bg-blue-700"
      >
        <PlusCircle className="w-5 h-5" />
        Create New {moduleName}
      </Link>
    </div>
  );
}

export default CreateNew;
