"use client";

import { Penci<PERSON>, Trash } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import ConfirmationModal from "./confirmdelete";
import { useState } from "react";

async function handleDelete(Id: string, collectionName: string) {
  try {
    const response = await fetch(`/api/${collectionName}/${Id}`, {
      method: "DELETE",
    });

    if (response.ok) {
      toast.success(`${collectionName} deleted successfully.`);
      // Optionally, you can refresh or update the product list here
      window.location.reload(); // Reload the page or re-fetch products
    } else {
      toast.error(`Failed to delete the ${collectionName}. Please try again.`);
    }
  } catch (error) {
    console.error(`<PERSON>rror deleting ${collectionName}:`, error);
    toast.error(`Failed to delete the ${collectionName}. Please try again.`);
  }
}

function ActionBtn({
  slug,
  collectionName,
  className,
  onClick,
  pageName,
}: {
  slug: string;
  collectionName: string;
  className?: string;
  onClick?: any;
  pageName?: any;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (e) => {
    setIsModalOpen(true);
    onClick(e);
  };
  const closeModal = () => setIsModalOpen(false);

  const confirmDelete = (e) => {
    handleDelete(slug, collectionName);
    closeModal();
    onClick(e);
  };

  return (
    <>
      <button
        onClick={(e) => {
          openModal(e); // Open the modal
        }}
        className={className}
      >
        <Trash className="text-gray-400 h-4 w-4" />
        Delete
      </button>

      <ConfirmationModal
        isOpen={isModalOpen}
        onConfirm={confirmDelete}
        onCancel={closeModal}
        title="Confirm Deletion"
        message={`Are you sure you want to delete this  ${collectionName?.slice(0,-1)}?`}
      />
    </>
  );
}

export default ActionBtn;
