import React, { useState, useRef, useEffect } from "react";

const CustomBountyDropdown = ({ options, selectedValue, onSelect, label }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);  

  const toggleDropdown = () => setIsDropdownOpen((prev) => !prev);

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsDropdownOpen(false); // Close dropdown if clicked outside
    }
  };
  const closedropdown = (values: string) => {
    onSelect(values);
    setIsDropdownOpen(false);
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="text-sm font-medium">{label}</label>
      <div
        className="w-full p-3 border border-gray-300 rounded-lg bg-dash text-white cursor-pointer"
        onClick={toggleDropdown}
      >
        {selectedValue
          ? options.find((option) => option.value === selectedValue)?.label
          : "Select a type"}
      </div>
      {isDropdownOpen && (
        <div
          className="absolute z-10 w-full mt-2 bg-dash border border-gray-300 rounded-lg shadow-lg"
          style={{ maxHeight: "200px", overflowY: "auto" }}
        >
          {options.map((option) => (
            <div
              key={option.value}
              className={`p-3 hover:bg-gray-700 cursor-pointer ${
                selectedValue === option.value ? "bg-gray-700" : ""
              }`}
              onClick={() => closedropdown(option.value)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomBountyDropdown;
