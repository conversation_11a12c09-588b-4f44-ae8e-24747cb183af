"use client";
import { getPayloadClient } from "@/server/get-payload";
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { BellRing, Pencil, PlusCircle, Trash } from "lucide-react";
import { Each } from "@/components/each";
import { toast } from "sonner";
import ActionBtn from "./actionbtn";

import { Badge } from "@/components/ui/badge";
import { DotsHorizontalIcon } from "@heroicons/react/outline";
import CreateNew from "./createnew";
import InfiniteScrollList from "./InfiniteScrollList";
import { Button } from "react-day-picker";
import { SpaceModal } from "@/components/SpaceModal";
import ImageSlider from "@/components/ImageSlider";
import { useRouter } from "next/navigation";

const headers = ["Image", "Name", "Category", "Status", "Price", "Actions"];

const AIAppList = ({
  initialRapps,
  totalDocs,
  userId,
  title,
  limit,
}: {
  initialRapps?: any[];
  totalDocs?: number;
  userId: string;
  title?: string;
  limit: number;
}) => {
  const [rapps, setrapps] = React.useState(initialRapps);
  const [adminReview, setAdminReview] = useState(false);
  const router = useRouter();

  const fetchRapps = async (page: number) => {
    const res = await fetch(`/api/rapps?creator=${userId}&page=${page}`);
    const data = await res.json();
    return { items: data.docs, total: data.totalDocs };
  };
  const handleArrowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };
  

  const renderRapp = (data: any) => (
    <article
      key={data.id}
      className="md:flex items-center bg-dash-foreground rounded-lg shadow-lg overflow-hidden md:h-60 md:mx-6 cursor-pointer"
      onClick={(e) => {
        const target = e.target as HTMLElement; // Cast to HTMLElement
        if (!target.closest(".prevent-navigation")) {
          router.push(`/ai-apps/${data.slug}`);
        }
      }}
    >
      {/* Left Image Section */}
      <div className="md:w-1/4 ">
        {" "}
        <div className="">
          {data?.images?.length > 0 ? (
            <ImageSlider
              urls={data.images
                .map((img: any) => img?.image?.url)
                .filter(Boolean)}
              onArrowClick={handleArrowClick}
            />
          ) : (
            <img
              src={
                `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/placeholder-09.webp`
              }
              alt="App list"
              style={{ width: "350px", height: "400px" }}
              loading="lazy"
            />
          )}
        </div>
      </div>
      {/* Right Content Section */}
      <div className="md:w-3/4 p-5 text-white">
        <div className="flex justify-between text-xs text-gray-400 mb-1">
          <div>
            {new Date(data.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }) || "Unknown Date"}
          </div>
          <div className="flex gap-2 items-center">
            <div className="relative group">
              <button
                className="p-2 text-white hover:bg-indigo-800 rounded-full focus:outline-none"
                onClick={(e) => e.stopPropagation()}
              >
                <DotsHorizontalIcon className="h-5 w-5 text-gray-400" />
              </button>
              <div className="hidden group-focus-within:block absolute right-0 mt-2 w-32 bg-indigo-800 rounded-lg shadow-lg">
                <Link
                  href={`https://rentprompts.ai/dashboard/projects`}
                  target="_blank"
                  className="flex items-center text-white p-2 hover:bg-indigo-700 rounded-t-lg"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Pencil className="text-gray-400 h-4 w-4 mr-2" />
                  Edit
                </Link>

                <div className="flex items-center text-white space-x-2 hover:bg-indigo-700 rounded-b-lg">
                  {/* <Trash className="text-gray-400 h-4" />  */}
                  <ActionBtn
                    slug={data.id}
                    collectionName="rapps"
                    className="flex items-center space-x-2 p-2 gap-2 .prevent-navigation w-full"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </div>
            {data.adminReview && (
              <div
                className="cursor-pointer hover:bg-indigo-800  rounded-full p-2"
                onClick={(e) => {
                  e.stopPropagation();
                  setAdminReview(!adminReview);
                }}
                // onClick={() => setAdminReview(!adminReview)}
              >
                <BellRing size={18} />
                <SpaceModal
                  title="Admin Review "
                  description="this Rapp is not approved by admin..."
                  isOpen={adminReview}
                  onClose={() => setAdminReview(false)}
                >
                  {data.adminReview}
                </SpaceModal>
              </div>
            )}
          </div>
        </div>
        <h2 className="text-lg font-semibold mb-2 break-all whitespace-normal">
          <Link href={`/ai-apps/${data.slug}`}>
            {data.name
              ? `${data.name.substring(0, 45)}...`
              : "No name Available"}
          </Link>
        </h2>
        <p className="text-sm text-gray-300 mb-2 break-all whitespace-normal">
          {data.description
            ? `${data.description.substring(0, 250)}...`
            : "No description Available"}
        </p>
        <div className="flex justify-between gap-1 text-base font-medium text-gray-400 ">
          <div className="flex items-center gap-2">
            <Badge className=" bg-indigo-300">
              <div>
                <img src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`} alt="coin" className="h-5 w-5" />
              </div>
              <p className="pl-1 text-sm"> {data.price}</p>
            </Badge>

            <Badge className=" bg-indigo-300">
              <p className="pl-1 text-sm"> {data.status}</p>
            </Badge>
          </div>
          <div className="flex items-center  gap-2 text-gray-400">
            {/* <div className="text-sm font-medium">
              {data.category && (
                <Badge className=" bg-indigo-300 rounded-lg cursor-default">
                  {data.category || "Unknown category"}
                </Badge>
              )}
            </div> */}
            <div>
              {data.product_type && (
                <Badge className=" bg-indigo-300 rounded-lg cursor-default">
                  {data.product_type || "Unknown product_type"}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    </article>
  );
  return (
    <div className="relative overflow-x-hidden">
      {rapps.length === 0 ? (
        <CreateNew
          messg="No Apps available. Create your first AI App!"
          link="https://rentprompts.ai/dashboard"
          moduleName="AI Apps"
        />
      ) : (
        <InfiniteScrollList
          fetchData={fetchRapps}
          renderItem={renderRapp}
          initialItems={initialRapps}
          totalItems={totalDocs}
        />
      )}
    </div>
  );
};

export default AIAppList;
