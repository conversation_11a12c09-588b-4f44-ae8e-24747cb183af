"use client";
import { getPayloadClient } from "@/server/get-payload";
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { BellRing, Pencil, PlusCircle, Trash } from "lucide-react";
import { Each } from "@/components/each";
import { toast } from "sonner";
import ActionBtn from "./actionbtn";
import coinImage from "../../../public/img/coin-png.png";
import { Badge } from "@/components/ui/badge";
import { DotsHorizontalIcon } from "@heroicons/react/outline";
import CreateNew from "./createnew";
import InfiniteScrollList from "./InfiniteScrollList";
import { Button } from "react-day-picker";
import { SpaceModal } from "@/components/SpaceModal";
import ImageSlider from "@/components/ImageSlider";
import { useRouter } from "next/navigation";
import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";
import { getStatusColorClass } from "@/lib/utils";

const headers = ["Image", "Name", "Category", "Status", "Price", "Actions"];

const ProductsList = ({
  initialProducts,
  totalDocs,
  userId,
  title,
  limit,
}: {
  initialProducts?: any[];
  totalDocs?: number;
  userId: string;
  title?: string;
  limit: number;
}) => {
  const [products, setProducts] = React.useState(initialProducts);
  const [adminReview, setAdminReview] = useState(false);
  const router = useRouter();
  const fetchProducts = async (page: number) => {
    const res = await fetch(`/api/products?userId=${userId}&page=${page}`);
    const data = await res.json();

    return { items: data.docs, total: data.totalDocs };
  };
  const handleBellClick = (e, review) => {
    e.stopPropagation();

    if (review) {
      setAdminReview(review); // Set selected review when clicked
    }
  };

  const closeModal = () => {
    setAdminReview(null); // Always set to null when modal closed
  };
  useEffect(() => {
    fetchProducts;
  }, []);

  const handleArrowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };
  const renderProduct = (data: any) => (
    <article
      key={data.id}
      className="md:flex items-center bg-dash-foreground rounded-lg shadow-lg overflow-hidden md:h-60  md:mx-6 cursor-pointer"
      //onClick={() => router.push(`/product/${data.slug}`)} // Navigate on click
      onClick={(e) => {
        const target = e.target as HTMLElement; // Cast to HTMLElement
        if (!target.closest(".prevent-navigation")) {
          router.push(`/product/${data.slug}`);
        }
      }}
    >
      {/* Left Image Section */}
      <div className="md:w-1/4 ">
        {" "}
        <div className="">
          {data?.images?.length > 0 ? (
            <ImageSlider
              urls={data.images
                .map((img: any) => img?.image?.url)
                .filter(Boolean)}
              onArrowClick={handleArrowClick}
            />
          ) : (
            <div>No images available</div> // Fallback if no images exist
          )}
        </div>
      </div>

      {/* Right Content Section */}
      <div className="md:w-3/4 p-5 text-white">
        <div className="flex justify-between items-center text-xs text-gray-400 mb-1">
          <div>
            {new Date(data.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }) || "Unknown Date"}
          </div>
          <div className="flex gap-2 items-center">
            <div className="relative group">
              <button
                className="p-2 text-white hover:bg-indigo-800 rounded-full focus:outline-none"
                onClick={(e) => e.stopPropagation()}
              >
                <DotsHorizontalIcon className="h-5 w-5 text-gray-400" />
              </button>
              <div className="hidden group-focus-within:block absolute right-0 mt-2 w-32 bg-indigo-800 rounded-lg shadow-lg">
                <Link
                  href={`/dashboard/create/product?Id=${data.id}`}
                  className="flex items-center text-white p-2 hover:bg-indigo-700 rounded-t-lg"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Pencil className="text-gray-400 h-4 w-4 mr-2" />
                  Edit
                </Link>

                <div className="flex items-center text-white space-x-2 hover:bg-indigo-700 rounded-b-lg">
                  {/* <Trash className="text-gray-400 h-4" />  */}
                  <ActionBtn
                    slug={data.id}
                    collectionName="products"
                    pageName="product"
                    className="flex gap-2 items-center .prevent-navigation w-full p-2"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </div>
            {data.adminReview && (
              <div
                className="cursor-pointer hover:bg-indigo-800  rounded-full p-2"
                onClick={(e) => handleBellClick(e, data.adminReview)}
              >
                <BellRing size={18} />
              </div>
            )}
          </div>
        </div>
        <h2 className="text-lg font-semibold mb-2 break-all whitespace-normal capitalize">
          {/* <Link href={`/product/${data.slug}`}> */}
          {data.name ? `${data.name.substring(0, 45)}...` : "No name Available"}
        </h2>
        <p className="text-sm text-gray-300 mb-4 break-all whitespace-normal first-letter:uppercase">
          {data.description
            ? `${data.description.substring(0, 250)}...`
            : "No description Available"}
        </p>
        <div className="flex justify-between items-center gap-4 text-base font-medium text-gray-400">
          {/* Left Section */}
          <div className="flex items-center gap-3">
            {/* Price Badge */}
            <Badge className="bg-indigo-300 flex items-center gap-0.5 py-0.5 px-2 rounded-lg">
              <img src={coinImage.src} alt="Coin" className="h-5 w-5" />
              <p className="text-base text-gray-800 font-bold">{data.price}</p>
            </Badge>

            {/* Category and Product Type Badges */}
            <div className="flex items-center gap-2">
              {data.category && (
                <Badge className="bg-indigo-300 text-gray-800 text-sm font-medium py-1 px-2 rounded-lg cursor-default first-letter:uppercase">
                  {capitalizeFirstLetter(data.category || "Unknown category")}
                </Badge>
              )}
              {data.product_type && (
                <Badge className="bg-purple-500 hover:bg-purple-600 text-white text-sm font-medium py-1 px-2 rounded-lg cursor-default ">
                  {capitalizeFirstLetter(
                    data.product_type || "Unknown product_type"
                  )}
                </Badge>
              )}
            </div>
          </div>

          {/* Right Section */}
          <div className="flex items-center justify-between text-gray-400">
            <Badge
              className={`${getStatusColorClass(data.approvedForSale, "approvedForSale")} text-gray-200 px-3 py-1 rounded-md cursor-default`}
            >
              {data.approvedForSale
                ? data.approvedForSale.charAt(0).toUpperCase() +
                  data.approvedForSale.slice(1)
                : "Unknown Status"}
            </Badge>
          </div>
        </div>
      </div>
    </article>
  );

  return (
    <div className="relative overflow-x-hidden px-4 md:px-0">
      {products.length === 0 ? (
        <CreateNew
          messg="No products available. Create your first products!"
          link="create/product"
          moduleName="Products"
        />
      ) : (
        <InfiniteScrollList
          fetchData={fetchProducts}
          renderItem={renderProduct}
          initialItems={initialProducts}
          totalItems={totalDocs}
        />
      )}
      <SpaceModal
        title="Admin Review "
        description="this bountie is not approved by admin..."
        isOpen={Boolean(adminReview)}
        // onClose={() => setAdminReview(null)}
        onClose={closeModal}
      >
        {adminReview}
      </SpaceModal>
    </div>
  );
};

export default ProductsList;
