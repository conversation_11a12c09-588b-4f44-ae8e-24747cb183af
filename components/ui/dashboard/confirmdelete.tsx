import React, { useEffect, useRef } from "react";

interface ConfirmationModalProps {
  component?: string;
  isOpen: boolean;
  onConfirm: (e: any) => void;
  onCancel: () => void;
  title?: string;
  message?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  component,
  isOpen,
  onConfirm,
  onCancel,
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onCancel(); // Close the modal if clicked outside
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onCancel]);
  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black ${component === "pro1st" || "pro2nd" || "pro3rd" || "bountyForm" || "blogForm" ? "bg-opacity-85" : "bg-opacity-50"} flex items-center justify-center z-50`}>
      <div
        className="bg-[#111C44] p-6 rounded shadow-lg max-w-sm w-full "
        ref={modalRef}
      >
        <h2 className="text-lg font-semibold mb-4">{title}</h2>
        <p className="text-sm text-white mb-6">{message}</p>
        <div className="flex justify-end space-x-3"> 
          <button
            // onClick={onCancel}
            onClick={(e) => {
              e.stopPropagation();
              onCancel();
            }}


            className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
