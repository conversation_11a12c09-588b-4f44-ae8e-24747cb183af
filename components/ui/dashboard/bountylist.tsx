"use client";
import { getPayloadClient } from "@/server/get-payload";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { BellRing, Pencil, PlusCircle, Trash } from "lucide-react";
import { Each } from "@/components/each";
import ActionBtn from "./actionbtn";
import { Badge } from "@/components/ui/badge";
import { DotsHorizontalIcon } from "@heroicons/react/outline";
import CreateNew from "./createnew";
import InfiniteScrollList from "./InfiniteScrollList";
import { SpaceModal } from "@/components/SpaceModal";
import { useRouter } from "next/navigation";

interface ItemsListProps {
  userId: string; // Assuming userProducts is the user ID
  title?: string;
}

// const headers = ["Name", "Bounty Type", "Status", "Price", , "Actions"];

const BountyList = ({
  initialBounty,
  totalDocs,
  userId,
  title,
  limit,
}: {
  initialBounty?: any[];
  totalDocs?: number;
  userId: string;
  title?: string;
  limit: number;
}) => {
  const [bountyData, setBountyData] = React.useState(initialBounty);
  const [adminReview, setAdminReview] = useState(false);
    const router = useRouter();

  const fetchBounties = async (page: number) => {
    const res = await fetch(`/api/bounties?userId=${userId}&page=${page}`);
    const data = await res.json();
    return { items: data.docs, total: data.totalDocs };
  };

  useEffect(() => {
    fetchBounties;
  }, []);

  const renderBounties = (data: any) => (
    <article
      key={data.id}
      className="md:flex items-center bg-dash-foreground rounded-lg shadow-lg overflow-hidden w-full mx-auto cursor-pointer"
      onClick={(e) => {
        const target = e.target as HTMLElement; // Cast to HTMLElement
        if (!target.closest(".prevent-navigation")) {
          router.push(`/bounties/${data.slug}`);
        }
      }}
    >
      <div className="p-4 text-white w-full">
        {/* Date and Menu Section */}
        <div className="flex justify-between items-center text-xs text-gray-400 mb-3">
          <div>
            {new Date(data.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }) || "Unknown Date"}
          </div>
          <div className="flex gap-2 items-center">
            <div className="relative group">
              <button className="p-2 text-white hover:bg-indigo-800 rounded-full focus:outline-none">
                <DotsHorizontalIcon className="h-5 w-5 text-gray-400" onClick={(e) => e.stopPropagation()} />
              </button>
              <div className="hidden group-focus-within:block absolute right-0 mt-2 w-32 bg-indigo-800 rounded-lg shadow-lg z-10">
                <Link
                  href={`/dashboard/create/bounty?Id=${data?.id}`}
                  className="flex items-center text-white p-2 hover:bg-indigo-700 rounded-t-lg"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Pencil className="text-gray-400 h-4 w-4 mr-2" />
                  Edit
                </Link>
                <div className="flex items-center text-white space-x-2 hover:bg-indigo-700 rounded-b-lg">
                  {/* <Trash className="text-gray-400 h-4 w-4 " /> */}
                  <ActionBtn
                    slug={data.id}
                    collectionName="bounties"
                    className="flex items-center w-full gap-2 p-2"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </div>
            {data.adminReview && (
              <div
                 className="cursor-pointer hover:bg-indigo-800  rounded-full p-2"
                onClick={(e) => {
                  e.stopPropagation();
                  setAdminReview(!adminReview);
                }}
                // onClick={() => setAdminReview(!adminReview)}
              >
                <BellRing size={18} />
                <SpaceModal
                  title="Admin Review "
                  description="this blog is not approved by admin..."
                  isOpen={adminReview}
                  onClose={() => setAdminReview(false)}
                >
                  {data.adminReview}
                </SpaceModal>
              </div>
            )}
          </div>
        </div>

        {/* Title Section */}
        <div className="flex items-center gap-1 text-2xl font-medium">
          <div>
            <img src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`} alt="coin" className="h-6 w-6" />
          </div>
          <p>{data.estimatedPrice}</p>
        </div>
        <h2 className="text-lg font-semibold mb-2 break-all  whitespace-normal capitalize">
         
            <Link href={`/bounties/${data.slug}`}>
              {data.title
                ? `${data.title.substring(0, 100)}...`
                : "No Title Available"}
            </Link>
         
        </h2>

        {/* Content Section */}
        <p className="text-sm text-gray-300 mb-3 break-all  whitespace-normal">
          {data.content
            ? `${data.content.substring(0, 500)}...`
            : "No Content Available"}
        </p>

        {/* Price and Status Section */}
        <div className="flex items-center justify-between text-gray-400">
          <Badge className="bg-indigo-500 text-white px-3 py-1 rounded-md cursor-default">
            {data.status || "Unknown Status"}
          </Badge>
          <Badge className="bg-purple-500 text-white px-3 py-1 rounded-md cursor-default">
            {data.bountyType || "Unknown BountyType"}
          </Badge>
        </div>
      </div>
    </article>
  );

  return (
    <div className="relative overflow-x-hidden">
      {bountyData.length === 0 ? (
        <CreateNew
          messg="No bounty available. Create your first bounty!"
          link="create/bounty"
          moduleName="Bounty"
        />
      ) : (
        <InfiniteScrollList
          fetchData={fetchBounties}
          renderItem={renderBounties}
          initialItems={bountyData}
          totalItems={totalDocs}
        />
      )}
    </div>
  );
};

export default BountyList;
