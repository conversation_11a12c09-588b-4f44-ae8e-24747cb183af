import { useState, useEffect, useRef, ReactNode } from "react";
import { Label } from "../label";
import { ChevronDown, ChevronUp } from "lucide-react";

type DropdownProps = {
  options: string[];
  selectedValue: string;
  onSelect: (value: string) => void;
  label: string | ReactNode;
  error?: string ;
  disabled?: boolean;
};

const CustomDropdown = ({ options, selectedValue, onSelect, label, error,disabled }: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // const toggleDropdown = () => setIsOpen((prev) => !prev);
  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen((prev) => !prev);
    }
  };

  const handleOptionSelect = (value: string) => {
    if (!disabled) {
    onSelect(value.toLowerCase());
    setIsOpen(false); // Close the dropdown after selection
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <Label className="block text-sm font-medium mb-2">
        {label}{" "}
      </Label>
      <div
        // className="w-full px-3 py-2 border border-gray-300 bg-dash rounded-lg text-white focus:outline-none cursor-pointer flex justify-between"
        className={`w-full px-3 py-2 border border-gray-300 bg-dash rounded-lg text-white focus:outline-none cursor-pointer flex justify-between ${
          disabled ? "cursor-not-allowed opacity-50 " : ""
        }`}
        onClick={toggleDropdown}
      >
        <span>{selectedValue || "Select an option"}</span>
        <span className="ml-2 transform transition-transform duration-300">
          {isOpen ? <ChevronUp className="inline" /> : <ChevronDown className="inline" />}
        </span>
      </div>
      {isOpen && !disabled && (
        <div className="absolute z-10 w-full mt-2 bg-dash border border-gray-300 rounded-lg shadow-lg">
          {options.map((option, index) => (
            <div
              key={index}
              className="p-3 hover:bg-gray-700 cursor-pointer"
              onClick={() => handleOptionSelect(option)}
            >
              {option}
            </div>
          ))}
        </div>
      )}
      {error && <p className="mt-2 text-sm text-red-400">{error}</p>}
    </div>
  );
};

export default CustomDropdown;
