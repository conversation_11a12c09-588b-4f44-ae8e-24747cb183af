import { getPayloadClient } from "@/server/get-payload";
import React from "react";

interface ItemsListProps {
  userId: string;
  title?: string;
}

const DataCards: React.FC<ItemsListProps> = async ({ userId, title }) => {
  const payload = await getPayloadClient();

  // Helper function to count items by status
  const countByStatus = async (collectionName: any, statusField: string) => {
    const approved = await payload.count({
      collection: collectionName,
      where: {
        user: {
          equals: userId,
        },
        [statusField]: {
          equals: "approved",
        },
      },
    });

    const pending = await payload.count({
      collection: collectionName,
      where: {
        user: {
          equals: userId,
        },
        [statusField]: {
          equals: "pending",
        },
      },
    });

    const denied = await payload.count({
      collection: collectionName,
      where: {
        user: {
          equals: userId,
        },
        [statusField]: {
          equals: "denied",
        },
      },
    });

    const total = approved.totalDocs + pending.totalDocs + denied.totalDocs;

    return {
      approved: approved.totalDocs,
      pending: pending.totalDocs,
      denied: denied.totalDocs,
      total,
    };
  };

  // Count statuses for each collection
  const productCounts = await countByStatus("products", "approvedForSale");
  const bountyCounts = await countByStatus("bounties", "status");
  const blogCounts = await countByStatus("blogs", "status");

  // Data for cards
  const data = [
    { type: "Products", counts: productCounts },
    { type: "Bounties", counts: bountyCounts },
    { type: "Blogs", counts: blogCounts },
  ];

  return (
    <div className="relative overflow-x-hidden">
      {title && (
        <h3 className="text-base md:text-3xl font-bold text-white mb-5">
          {title}
        </h3>
      )}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {data.map((item, index) => (
          <div
            key={index}
            className="p-4 bg-dash-foreground rounded-lg shadow-md text-white"
          >
            <h4 className="text-lg font-bold mb-4 text-center">{item.type}</h4>
            <div className="space-y-4">
              {["approved", "pending", "denied"].map((status) => {
                const percentage =
                  (item.counts[status] / item.counts.total) * 100 || 0;

                return (
                  <div key={status}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm capitalize">{status}</span>
                      <span className="text-sm font-semibold">
                        {item.counts[status]}
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5">
                      <div
                        className={`h-2.5 rounded-full ${
                          status === "approved"
                            ? "bg-green-500"
                            : status === "pending"
                            ? "bg-yellow-500"
                            : "bg-red-500"
                        }`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DataCards;
