import Link from "next/link";
import React from "react";

interface Product {
  id: string;
  name: string;
  price: number;
  imageUrl: string;
  color: string;
  category: string;
}

interface TableProps {
  headers: string[];
  title?:string;
  data: {
    id: string;
    name: string;
    price?: number;
    images: {
        image: {
          url: string;
        };
      }[];
    category?: string;
  }[];
}

const DashboardTable: React.FC<TableProps> = ({title, headers, data }) => {
    
  return (
    <div className="relative overflow-x-hidden">
      <h3 className="text-base md:text-3xl font-bold text-white mb-5">
          {title}
        </h3>
      <table className="w-full text-sm text-left rtl:text-right ">
       
        <thead className="text-xs uppercase ">
          <tr className="bg-gradient-to-br from-black/[0.3] via-black/[0.1] to-black/[0.4] rounded-lg">
            {headers.map((header, index) => (
              <th key={index} className="px-6 py-3 text-base">
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((product) => (
            <tr key={product.id} className="">
              <td className="px-6 py-1">
                <Link href="#" ></Link>
                <img
                  src={product.images[0]?.image?.url}
                  alt={product.name}
                  className="h-12 w-16 object-cover rounded-lg"
                />
              </td>
              <td className="px-6 py-1 text-base text-white font-semibold">
                {product.name}
              </td>
              {/* <td className="px-6 py-4">{product.color}</td> */}
              <td className="px-6 py-1 text-base font-semibold">{product.category}</td>
             {product.price &&  <td className="px-6 py-1 text-base font-semibold ">${product.price.toFixed(2)}</td>}
              {/*  <td className="px-6 py-4 text-right">
                <a href="#" className="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                  Edit
                </a>
              </td> */}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DashboardTable;
