"use client";
import React, { useState, useRef, useEffect } from "react";
import { LayoutGrid } from "../ui/layoutgrid";

export function LayoutGridDemo() {
  return (
    <div className="h-screen hidden lg:block max-h-96 mt-5 w-1/2">
      <LayoutGrid cards={cards} />
    </div>
  );
}

const SkeletonOne = () => {
  return (
    <div>
      <p className="font-bold text-xl text-white">paper craft style, MÄRCHENTANTE, ernst haeckel maria sibylla merian. tristan eaton, victo ngai, artgerm, rhads, ross draws, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, sci-fi, colors, neon lights, digital painting, pixiv, by <PERSON><PERSON>, neon lights, 3D, perspective, two colors, blue turquoise.</p>
      <p className="font-normal text-base text-white"></p>
      {/* <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        A serene and tranquil retreat, this house in the woods offers a peaceful
        escape from the hustle and bustle of city life.
      </p> */}
    </div>
  );
};

const SkeletonTwo = () => {
  return (
    <div>
      <p className="font-bold text-xl text-white">Ultra realistic в взphoto of a very high tech 3d software code lit with golden hour light.</p>
      <p className="font-normal text-base text-white"></p>
      {/* <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      ultra realistic в взphoto of a very high tech 3d software code lit with golden hour light.
      </p> */}
    </div>
  );
};
const SkeletonThree = () => {
  return (
    <div>
      <p className="font-bold text-xl text-white">robot in the style of 90s vintage anime, holding smartphone in hand, surrealism, akira style, detailed line art, HDR,realistic, cinematic light, fine details, cute face, dramatic light 50mm film photography. inside an nebula.</p>
      <p className="font-normal text-base text-white"></p>
      {/* <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      robot in the style of 90s vintage anime, holding smartphone in hand, surrealism, akira style, detailed line art, HDR,realistic, cinematic light, fine details, cute face, dramatic light 50mm film photography. inside an nebula.
      </p> */}
    </div>
  );
};
const SkeletonFour = () => {
  return (
    <div>
      <p className="font-bold text-xl text-white">Violet Blue futuristic world, 8K, cinematic</p>
      <p className="font-normal text-base text-white"></p>
      {/* <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        A house by the river is a place of peace and tranquility. It&apos;s the
        perfect place to relax, unwind, and enjoy life.
      </p> */}
    </div>
  );
};

const cards = [
  {
    id: 1,
    content: <SkeletonOne />,
    className: "md:col-span-2",
    thumbnail:
    "asset.rentprompts.com/75156637-e092-46e0-9f58-618458da325b.jpeg",
  },
  {
    id: 2,
    content: <SkeletonTwo />,
    className: "col-span-1",
    thumbnail:
    "asset.rentprompts.com/AD_00008-ezgif.com-video-to-webp-converter.webp",
  },
  {
    id: 3,
    content: <SkeletonThree />,
    className: "col-span-1",
    thumbnail:
    "asset.rentprompts.com/AD_00005-ezgif.com-gif-to-webp-converter.webp",
  },
  {
    id: 4,
    content: <SkeletonFour />,
    className: "md:col-span-2",
    thumbnail:
    "asset.rentprompts.com/4be56469-a73c-4fe6-a9a0-549fb8d06862.jpeg",
  },
];
