"use client";

import {
  Box,
  ImageIcon,
  MessageSquare,
  Music,
  Video,
  VideoIcon,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

interface Category {
  id: number;
  title: string;
  author: string;
  date: string;
  image: string;
  avatar: string;
  link: string;
  icon: any;
}

const categorys: Category[] = [
  {
    id: 1,
    title: "Generate Video",
    author: "<PERSON>",
    date: "12.10.1999",
    image: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/text-to-video.webp`,
    avatar: "https://i.pravatar.cc/150?img=1",
    link: "/generate/video",
    icon: <VideoIcon />,
  },
  {
    id: 2,
    title: "Generate Text",
    author: "<PERSON>",
    date: "05.07.2023",
    image: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/texttotexttttt.webp`,
    avatar: "https://i.pravatar.cc/150?img=2",
    link: "/generate/text",
    icon: <MessageSquare />,
  },
  {
    id: 3,
    title: "Generate Image",
    author: "<PERSON>",
    date: "18.03.2021",
    image: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/text-to-text.webp`,
    avatar: "https://i.pravatar.cc/150?img=3",
    link: "/generate/image",
    icon: <ImageIcon />,
  },
  {
    id: 4,
    title: "Generate Audio",
    author: "Alice Johnson",
    date: "24.12.2022",
    image: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/texttomusic-1.webp`,
    avatar: "https://i.pravatar.cc/150?img=4",
    link: "/generate/audio",
    icon: <Music />,
  },
  {
    id: 5,
    title: "Generate 3D (Coming Soon)",
    author: "Robert Brown",
    date: "02.11.2024",
    image: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/textto3D.webp1`,
    avatar: "https://i.pravatar.cc/150?img=5",
    link: "/generate/",
    icon: <Box />,
  },
];

export default function SolidBento() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <>
      <div className="mx-4 mb-2 flex flex-col max-w-lg">
        <h2 className="text-2xl md:text-4xl font-semibold text-white">Generation</h2>
        <p className="text-gray-400 max-sm:text-sm">Harness AI to generate text, images, videos, music, and more—bringing your ideas to life effortlessly.
        </p>
      </div>

      <div className="border-t-2 border-indigo-600 mx-4">
      <div className="grid h-auto lg:h-[40rem] px-2 py-4 sm:grid-cols-2 lg:grid-cols-3 lg:grid-rows-3 gap-4">
        {categorys.map((category, index) => (
          <Link
            key={category.id}
            href={category.link}
            className={`group relative flex overflow-hidden rounded-2xl  transition-all duration-300 hover:scale-[1.02] h-72 lg:h-auto ${
              index === 0
                ? "sm:col-span-2 sm:row-span-2"
                : index === 1
                ? "sm:col-span-1 sm:row-span-1"
                : "sm:col-span-1 sm:row-span-1 lg:row-span-2"
            }`}
          >
            <Image
              src={category.image}
              alt={category.title}
              fill
              className="transition-all duration-300 group-hover:scale-110 group-hover:opacity-50 object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-transparent" />
            <div className="relative flex h-fit items-center w-full flex-row justify-end p-6 gap-2 text-white">
              {category.icon}
              <h2 className="text-2xl font-bold leading-tight">
                {category.title}
              </h2>
              {/* <div className="flex items-center space-x-2">
              <Image
                src={category.avatar}
                alt={category.author}
                width={32}
                height={32}
                className="rounded-full"
              />
              <span className="text-sm font-medium">{category.author}</span>
              <span className="text-xs text-gray-300">{category.date}</span>
            </div> */}
            </div>
          </Link>
        ))}
      </div>
      </div>
    </>
  );
}
