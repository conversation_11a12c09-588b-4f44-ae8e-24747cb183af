"use client";

import React, { useEffect, useState } from "react";
import { AiTools } from "./aitoollist";


export function CustomTools() {
  return (
    <div className="h-[10rem] rounded-md flex flex-col antialiased dark:bg-black dark:bg-grid-white/[0.05] items-center justify-center relative overflow-hidden">
      <AiTools items={testimonials} direction="left" speed="slow" infobox={false}/>
    </div>
  );
}

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    img: "/img/aitoolimg/dall-e-logo.jpg",
  },
  {
    name: "Midjourney",
    img: "/img/aitoolimg/midjoy.png",
  },
  {
    name: "ChatGpt",
    img: "/img/aitoolimg/chat-gpt-logo.svg",
  },
  {
    name: "Stable Diffusion",
    img: "/img/aitoolimg/stable-diffusion-logo.png",
  },
  {
    name: "<PERSON>",
    img: "/img/aitoolimg/gemini.gif",
  },
  {
    name: "<PERSON>",
    img: "/img/aitoolimg/leonardo-logo.svg",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    img: "https://claude.ai/images/claude_app_icon.png",
  },
];
