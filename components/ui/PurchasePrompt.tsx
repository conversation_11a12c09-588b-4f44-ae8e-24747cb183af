"use client";
import React, { useState } from "react";
import DownloadButton from "@/app/(mainapp)/thank-you/DownloadButton";
import Loader from "@/components/Loader";
import { useRef } from "react";
import { ChevronLeft, ChevronRight, Copy } from "lucide-react";
import Link from "next/link";
import { Purchase } from "@/server/payload-types";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

const PurchasePrompt = ({ user, purchases }: any) => {
  const [showAlldescription, setShowAllDescription] = useState(null);
  const scrollContainerRefTopFeatured = useRef(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isPromptModalOpen, setPromptIsModalOpen] = useState(false);

  const scrollLeft = (ref) => {
    if (ref.current) {
      ref.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = (ref) => {
    if (ref.current) {
      ref.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  const handleCardClick = (purchase: Purchase) => {
    setShowAllDescription(purchase);
  };

  const matchingIds = purchases
    .map(rap => {
      return rap.promptpurchase
        .filter(purchase => purchase.user.id === user.id)
        .map(purchase => purchase.id);
    })
    .flat();

  const handleViewPrompt = () => {
      if (user) {
        setPromptIsModalOpen(true);
      } 
      else {
        toast.error("Please login to view prompt");
      }
    };

  //copy prompt settings 
    const copyToClipboard = (text) => {
      const settingsText = JSON.stringify(text, null, 2); // Format settings as JSON
      navigator.clipboard.writeText(settingsText)
        .then(() => alert("Settings copied to clipboard!"))
        .catch((error) => console.error("Failed to copy settings:", error));
    };

  return (
    <>
      <Loader />
      <div className="mx-auto lg:px-6 pt-12">
        <h1 className="md:mt-10 text-4xl font-bold tracking-tight sm:text-5xl text-center">
          Product Purchases
        </h1>

        {!purchases ||
          (purchases.length === 0 ? (
            <div className="text-center mt-20 mb-20">
              No Purchases Available
            </div>
          ) : (
            <>
              <div className="relative px-4 md:px-8 mt-12 text-lg font-normal">
                <button
                  onClick={() => scrollLeft(scrollContainerRefTopFeatured)}
                  className="absolute left-5 md:-left-3 top-1/2 transform -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 transition-transform transition-colors duration-300 ease-in-out active:scale-90"
                  aria-label="Scroll left"
                >
                  <ChevronLeft className="h-4 w-4 text-indigo-700" />
                </button>
                <button
                  onClick={() => scrollRight(scrollContainerRefTopFeatured)}
                  className="absolute right-5 md:-right-3 top-1/2 transform -translate-y-1/2 bg-gray-300 p-2 sm:p-3 rounded-full z-10 hover:bg-gray-400 transition-transform transition-colors duration-300 ease-in-out active:scale-90"
                  aria-label="Scroll right"
                >
                  <ChevronRight className="h-4 w-4 text-indigo-700" />
                </button>
                <div
                  ref={scrollContainerRefTopFeatured}
                  className="overflow-x-auto no-scrollbar whitespace-nowrap py-6"
                >
                  {purchases.map((purchase: any) => (
                    <div key={purchase.id} className="inline-block mr-2 w-full sm:w-[25rem] transition-transform duration-500">
                      <div
                        className="bg-indigo-700 border border-gray-200 p-2 sm:p-6 rounded-lg shadow-lg cursor-pointer"
                        onClick={() => handleCardClick(purchase)}
                      >
                        <div className="space-y-4">
                          <div className="text-gray-200">
                            {matchingIds.length > 0 ? (
                              matchingIds.map((id, index) => (
                                <p key={index}>
                                  <span className="font-bold">Purchase ID:</span>{" "}
                                  {id.length > 18
                                    ? id.substring(0, 18) + "..."
                                    : id}
                                </p>
                              ))
                            ) : (
                              <p>No matching purchases found</p>
                            )}
                          </div>

                          <p className="text-gray-200">
                            <span className="font-bold">Rapp Name:</span>{" "}
                             {purchase?.name?.length > 20
                              ? purchase.name.substring(0, 20) + "..."
                              : purchase.name} 
                          </p>

                          <div className="w-max" onClick={(e) => e.stopPropagation()}>
                            {/* <DownloadButton
                              productFiles={purchase.product.product_files}
                              product={purchase.product}
                              userId={user.id}
                              type="product"
                            /> */} 
                            <button
                            onClick={handleViewPrompt}
                            className="bg-white w-full text-indigo-600 rounded-md h-10 font-semibold"
                             >
                             View Prompt
                            </button>
                              {/*----------------------------------- model for revealing prompt --------------------------------*/}
      {isPromptModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isPromptModalOpen}
          onClose={() => {
            setPromptIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-1 h-[400px] overflow-auto mb-2">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <p className="text-xl font-bold text-neo-foreground">User Prompt</p>
              <button
                onClick={() => copyToClipboard(purchase.prompt)}
                title="Copy Prompt"
                style={{ marginLeft: '10px', cursor: 'pointer' }}
              >
                <Copy size={18} />
              </button>
            </div>
            <p className="text-sm font-semibold"> {purchase.prompt}</p>

            {purchase.modelType === "text" ? (
              <>
                {/* <h3 className="text-xl font-bold text-neo-foreground  ">System Prompt</h3> */}
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">System Prompt</p>
                  <button
                    onClick={() => copyToClipboard(purchase.systemprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: '10px', cursor: 'pointer' }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                <p className="text-sm font-semibold"> {purchase.systemprompt} </p>
              </>
            ) : (
              <>
                {/* <h3 className="text-xl font-bold text-neo-foreground ">Negative Prompt</h3> */}
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <p className="text-xl font-bold text-neo-foreground mt-4">Negative Prompt</p>
                  <button
                    onClick={() => copyToClipboard(purchase.negativeprompt)}
                    title="Copy Prompt"
                    style={{ marginLeft: '10px', cursor: 'pointer' }}
                  >
                    <Copy size={18} />
                  </button>
                </div>
                <p className="text-sm font-semibold"> {purchase.negativeprompt} </p>
              </>
            )}

            {/*------------------------------ Prompt Setting---------------------------- */}
            {/* {rapps?.imagesettings ? ( */}
            {(purchase.imagesettings && Object.keys(purchase.imagesettings).length > 0) ? (
              <div>
                {purchase.imagesettings !== undefined && (
                  <h2 className="text-xl font-bold text-neo-foreground mt-4">
                    Prompt Settings
                    <button onClick={() => copyToClipboard(purchase.imagesettings)} title="Copy Image Settings" style={{ marginLeft: '10px' }}>
                      <Copy size={18} />
                    </button>
                  </h2>
                )}

                {purchase.imagesettings.quality !== undefined && (
                  <p>Quality: {purchase.imagesettings.quality}</p>
                )}
                {purchase.imagesettings.seed !== undefined && (
                  <p>Seed: {purchase.imagesettings.seed}</p>
                )}
                {purchase.imagesettings.cfg !== undefined && (
                  <p>Cfg: {purchase.imagesettings.cfg}</p>
                )}
                {purchase.imagesettings.steps !== undefined && (
                  <p>Steps: {purchase.imagesettings.steps}</p>
                )}
                {purchase.imagesettings.prompt_strength !== undefined && (
                  <p>Prompt Strength: {purchase.imagesettings.prompt_strength}</p>
                )}
              </div>
            // ) : rapps.textsettings ? (
            ) : (purchase.textsettings && Object.keys(purchase.textsettings).length > 0) ? (
              <div>
                {purchase.textsettings !== undefined && (
                  <h2 className="text-xl font-bold text-neo-foreground mt-4">
                    Settings
                    <button onClick={() => copyToClipboard(purchase.textsettings)} title="Copy Text Settings" style={{ marginLeft: '10px' }}>
                      <Copy size={18} />
                    </button>
                  </h2>
                )}

                {purchase.textsettings.temperature !== undefined && (
                  <p>Temperature: {purchase.textsettings.temperature}</p>
                )}
                {purchase.textsettings.mintokens !== undefined && (
                  <p>Min Tokens: {purchase.textsettings.mintokens}</p>
                )}
                {purchase.textsettings.maxtokens !== undefined && (
                  <p>Max Tokens: {purchase.textsettings.maxtokens}</p>
                )}
                {purchase.textsettings.seed !== undefined && (
                  <p>Seed: {purchase.textsettings.seed}</p>
                )}
                {purchase.textsettings.top_k !== undefined && (
                  <p>Top K: {purchase.textsettings.top_k}</p>
                )}
                {purchase.textsettings.top_p !== undefined && (
                  <p>Top P: {purchase.textsettings.top_p}</p>
                )}
                {purchase.textsettings.length_penalty !== undefined && (
                  <p>Length Penalty: {purchase.textsettings.length_penalty}</p>
                )}
                {purchase.textsettings.presense_penalty !== undefined && (
                  <p>Presence Penalty: {purchase.textsettings.presense_penalty}</p>
                )}
              </div>
            ) : null}

          </div>
        </SpaceModal>
      )}



























                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-8 text-right px-4 md:px-8">
                <Link
                  href="/"
                  className="text-sm font-medium text-blue-500 hover:text-blue-600 transition"
                >
                  Continue Shopping →
                </Link>
              </div>
            </>
          ))}
      </div>
     
    </>
  );
};

export default PurchasePrompt;
