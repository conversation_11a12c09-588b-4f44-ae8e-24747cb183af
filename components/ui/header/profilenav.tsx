import { useAuth } from "@/app/hooks/use-auth";
import { Icons } from "@/components/Icons";
import NavLoginButtonWrapper from "@/components/NavLoginButtonWrapper";
import {
  CreditCard,
  LayoutDashboard,
  LogOut,
  ShoppingBag,
  User,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Avatar, AvatarFallback, AvatarImage } from "../avatar";

const AccountDropdown4 = ({ user }) => {
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const router = useRouter();
  const trigger = useRef<HTMLButtonElement | null>(null);
  const dropdown = useRef<HTMLDivElement | null>(null);
  const profileImage =
    user?.profileImage?.url ??
    `https://th.bing.com/th/id/OIP.XV86Zs9u6Z-B5Z93-ZNB6gD6D6?rs=1&pid=ImgDetMain`;

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target as Node) ||
        trigger.current?.contains(target as Node)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  }, [dropdownOpen]);

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  }, [dropdownOpen]);

  const { signOut } = useAuth();

  // After sign-out, it returns to the previous path
  const handleSignOut = async () => {
    const currentPath = window.location.pathname + window.location.search; // Include the search parameters
    await signOut();
    localStorage.removeItem('token');
    router.push(`/sign-in?from=${encodeURIComponent(currentPath)}`);
  };

  return (
    <section className="bg-gray-2 dark:bg-dark">
      {!user && <NavLoginButtonWrapper />}
      <div className="flex justify-center items-center relative">
        {user && (
          <button
            ref={trigger}
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="inline-flex  items-center justify-center gap-2 rounded-lg border border-stroke text-base font-medium text-dark"
          >
            {/* <span className="sr-only">Open user menu</span> */}
            <div className="pr-2">
              <Avatar className="cursor-hand shadow-md shadow-[#1e1b4b] ">
                <AvatarImage
                  className="rounded-full hover:ring-1 hover:ring-offset-2 hover:ring-offset-blue-300"
                  src={profileImage}
                  // src="https://th.bing.com/th/id/OIP.XV86Zs9u6Z-B5Z93-ZNB6gD6D6?rs=1&pid=ImgDetMain"
                />
                <AvatarFallback>
                  {user?.email ? user.email.slice(0, 2) : "NA"}
                </AvatarFallback>
              </Avatar>
            </div>
          </button>
        )}
        <div
          ref={dropdown}
          onFocus={() => setDropdownOpen(true)}
          onBlur={() => setDropdownOpen(false)}
          className={`absolute right-0 top-full mt-2 w-[240px] divide-y divide-stroke overflow-hidden rounded-lg bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 shadow-lg dark:divide-dark-3 dark:bg-dark-2 ${
            dropdownOpen ? "block" : "hidden"
          }`}
        >
          <div className="px-4 py-3 flex flex-row items-center gap-2">
            <Image
              src={profileImage}
              width={32}
              height={32}
              alt="Coin"
              className="rounded-full object-cover h-8 w-8"
            />
            <div>
              <p className="text-sm font-semibold text-dark dark:text-white">
                {user?.user_name}
              </p>
              <p className="text-sm text-body-color dark:text-dark-6">
                {user?.email ? user.email : "NA"}
              </p>
            </div>
          </div>
          <div>
            <Link
              href="/pricing"
              className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-yellow-400  hover:bg-gray-50 hover:text-yellow-600"
            >
              <span className="flex items-center gap-2 ">
                <div className="flex -rotate-45">
                  <div className=" motion-preset-stretch motion-duration-2000">
                    <Image
                      src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                      width={22}
                      height={22}
                      alt="Coin"
                    />
                  </div>
                </div>
                Recharge
              </span>
              {/* <span className="text-xs text-dark-5"> </span> */}
            </Link>
          </div>
          <div>
            <Link
              href="/profile"
              className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
            >
              <span className="flex items-center gap-2">
                <User className="h-5 w-5" />
                View Profile
              </span>
            </Link>
            {/* <Link
                  href="#0"
                  className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
                >
                  <span className="flex items-center gap-2">
                  <Icons.renticon className="h-5 w-5"/>
                    My Ai Apps
                  </span>
                </Link> */}
            <Link
              href="/dashboard"
              className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
            >
              <span className="flex items-center gap-2">
                <LayoutDashboard className="h-5 w-5" />
                Dashboard
              </span>
            </Link>
          </div>
          <div>
            <Link
              href="/payout"
              className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
            >
              <span className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payout
              </span>
            </Link>
            <Link
              href="/purchases"
              className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
            >
              <span className="flex items-center gap-2">
                <ShoppingBag className="h-5 w-5" />
                Purchases
              </span>
            </Link>
          </div>
          {/* <div>
                <a
                  href="#0"
                  className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
                >
                  <span className="flex items-center gap-2">
                    Changelog
                  </span>
                </a>
                <a
                  href="#0"
                  className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
                >
                  <span className="flex items-center gap-2">
                    Slack Community
                  </span>
                </a>
                <a
                  href="#0"
                  className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
                >
                  <span className="flex items-center gap-2">
                    Support
                  </span>
                </a>
                <a
                  href="#0"
                  className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
                >
                  <span className="flex items-center gap-2">
                    API
                  </span>
                </a>
              </div> */}
          <div>
            <button
              className="flex w-full items-center justify-between px-4 py-2.5 text-sm font-medium text-dark hover:bg-gray-50 hover:text-indigo-800"
              onClick={handleSignOut}
            >
              <span className="flex items-center gap-2">
                <LogOut className="h-5 w-5" />
                Log out
              </span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AccountDropdown4;
