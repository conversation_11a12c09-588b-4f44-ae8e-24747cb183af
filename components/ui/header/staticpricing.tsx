"use client";
import Image from "next/image";
import Link from "next/link";
import ShinnyButton from "../ShinnyButton";
import { ChevronRight } from "lucide-react";


const StaticUserBalance = () => {
  return (
    <Link href="/pricing" className="cursor-pointer">
      <ShinnyButton
        className="flex items-center text-sm font-semibold text-white px-2 py-1 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        <div className="flex flex-row items-center px-1 gap-1">
          <div className="flex -rotate-45">
            <div className="motion-preset-stretch motion-duration-2000">
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                width={28}
                height={28}
                alt="Coin"
              />
            </div>
          </div>

          {/* Chevron right icon */}
          <ChevronRight className="text-white" />
        </div>
      </ShinnyButton>
    </Link>
  );
};

export default StaticUserBalance;
