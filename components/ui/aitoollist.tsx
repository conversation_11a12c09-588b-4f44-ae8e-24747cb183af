"use client";

import { cn } from "@/lib/utils";
import Image from "next/image";
import React, { useEffect, useState } from "react";

export const AiTools = ({
  items,
  direction = "left",
  speed = "fast",
  pauseOnHover = true,
  className,
  infobox = false,
}: {
  items: {
    img: string;
    // quote: string;
    name: string;
    institution?: string;
    // title: string;
  }[];
  direction?: "left" | "right";
  speed?: "fast" | "normal" | "slow";
  pauseOnHover?: boolean;
  infobox: boolean;
  className?: string;
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const scrollerRef = React.useRef<HTMLUListElement>(null);

  useEffect(() => {
    addAnimation();
  }, []);
  const [start, setStart] = useState(false);
  function addAnimation() {
    if (containerRef.current && scrollerRef.current) {
      const scrollerContent = Array.from(scrollerRef.current.children);

      scrollerContent.forEach((item) => {
        const duplicatedItem = item.cloneNode(true);
        if (scrollerRef.current) {
          scrollerRef.current.appendChild(duplicatedItem);
        }
      });

      getDirection();
      getSpeed();
      setStart(true);
    }
  }
  const getDirection = () => {
    if (containerRef.current) {
      if (direction === "left") {
        containerRef.current.style.setProperty(
          "--animation-direction",
          "forwards"
        );
      } else {
        containerRef.current.style.setProperty(
          "--animation-direction",
          "reverse"
        );
      }
    }
  };
  const getSpeed = () => {
    if (containerRef.current) {
      if (speed === "fast") {
        containerRef.current.style.setProperty("--animation-duration", "20s");
      } else if (speed === "normal") {
        containerRef.current.style.setProperty("--animation-duration", "40s");
      } else {
        containerRef.current.style.setProperty("--animation-duration", "80s");
      }
    }
  };
  return (
    <div
      ref={containerRef}
      className={cn(
        "scroller relative z-5 max-w-7xl overflow-hidden [mask-image:linear-gradient(to_right,transparent,white_20%,white_80%,transparent)]",
        className
      )}
    >
      <ul
        ref={scrollerRef}
        className={cn(
          " flex min-w-full shrink-0 gap-4 py-0 md:py-3 w-max flex-nowrap items-center",
          start && "animate-scroll ",
          pauseOnHover && "hover:[animation-play-state:paused]"
        )}
      >
        {items.map((item, idx) => (
          <li
            className={`relative transition ease-in-out  rounded-xl delay-150 py-4 md:py-0 hover:border-slate-400 hover:-translate-y-1 hover:scale-90 duration-300 flex-shrink-0 ${
              infobox
                ? "px-2 py-1"
                : "px-5 md:py-3 bg-[#FFFFFF0F] h-fit md:h-[120px] min-w-[180px] md:min-w-[220px] border border-transparent shadow"
            }`}
            //    style={{ boxShadow: 'inset 0 3px 13px -3px #04a038' }}
            key={item.name}
          >
            <blockquote className="h-1/2 md:h-full flex justify-center">
              <div
                aria-hidden="true"
                className="user-select-none -z-1 pointer-events-none absolute -left-0.5 -top-0.5 h-[calc(100%_+_4px)] w-[calc(100%_+_4px)]"
              ></div>
              {/* <span className=" relative z-20 text-sm leading-[1.6] text-gray-100 font-normal">
                {item.quote}
              </span> */}
              <div className="relative z-20 flex flex-row items-center justify-center">
                <span className="flex gap-1 items-center">
                  <span className="rounded-full shadow bg-[#FFFFFF0F] p-2">
                    <Image
                      src={item.img}
                      alt={item.name}
                      width={infobox ? 15 : 30}
                      height={infobox ? 15 : 30}
                      className="rounded-full"
                    />
                  </span>
                  <span
                    className={`text-white font-normal ${
                      infobox
                        ? "text-sm"
                        : "font-semibold text-lg leading-[1.6]"
                    }`}
                  >
                    {item.name}
                  </span>
                  {/* <span className=" text-sm leading-[1.6] text-gray-400 font-normal">
                    {item.title}
                  </span> */}
                </span>
              </div>
            </blockquote>
          </li>
        ))}
      </ul>
    </div>
  );
};
