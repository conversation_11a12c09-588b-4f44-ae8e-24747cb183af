"use client";
import React, { useState } from "react";
import { IconArrowLeft, IconUserBolt } from "@tabler/icons-react";
import { Box, Kanban, LayoutDashboard, LogOut, Slack, Users } from "lucide-react";
import { Icons } from "../Icons";
import { toast } from "sonner";

import { Sidebar, SidebarBody, SidebarLink } from "../siderbar";
import { useUser } from "@/provider/User";
import { useAuth } from "@/app/hooks/use-auth";
import { useRouter } from "next/navigation";
import { FaHouseUser } from "react-icons/fa";

export function SidebarComponent() {
  const [open, setOpen] = useState(false);
  const user = useUser();
  const { signOut } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    const currentPath = window.location.pathname + window.location.search; // Include the search parameters
    await signOut();
    localStorage.removeItem('token');
    router.push(`/sign-in?from=${encodeURIComponent(currentPath)}`);
  };
  const links = [
    {
      label: "Home",
      href: "/",
      icon: (
        <FaHouseUser className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Dashboard",
      href: "/dashboard",
      icon: (
        <LayoutDashboard className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "AI Apps",
      href: "/dashboard/AI-Apps",
      icon: (
        <Icons.renticon className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Products",
      href: "/dashboard/products",
      icon: (
        <Kanban className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Blogs",
      href: "/dashboard/blogs",
      icon: (
        <Icons.blogicon className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Bounty",
      href: "/dashboard/bounties",
      icon: (
        <Icons.bountyicon className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Marketplace",
      href: "/marketplace",
      icon: (
        <Icons.exploreicon className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    // {
    //   label: "Logout",
    //   href: "/",
    //   icon: (
    //     <IconArrowLeft className="text-white dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    //   ),
    // },
  ];

  return (
    <Sidebar open={open} setOpen={setOpen}>
      <SidebarBody className="justify-between gap-10 bg-dash-foreground">
        <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
          <div className="flex flex-col gap-2">
            <SidebarLink
              link={{
                label: (
                  <div className="italic font-semibold text-xl mb-2 ">
                    RENTPROMPTS
                  </div>
                ),
                href: "/",
                icon: (
                  <Icons.logo
                    className="text-white dark:text-neutral-200 h-7 w-7 flex-shrink-0 mb-2 "
                    fill="white"
                  />
                ),
              }}
            />
            {links.map((link, idx) => (
              <SidebarLink key={idx} link={link} />
            ))}
          </div>
        </div>
        <SidebarLink
          onLogoutClick={handleLogout}
          link={{
            label: "Logout",
            href: "/",
            icon: (
              <LogOut className="text-warning dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
            ),
          }}
        />
      </SidebarBody>
    </Sidebar>
  );
}
