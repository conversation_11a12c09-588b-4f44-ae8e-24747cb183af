"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerDescription,
} from "@/components/ui/drawer";
import Link from "next/link";
import { Skeleton } from "./skeleton";
import { Rapp, User } from "@/server/payload-types";
import TotalRating from "../TotalRating";
import coinImage from "../../public/img/coin-png.png";
import Container from "./container";
import { Heart } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import ReactMarkdown from "react-markdown";
import { actionAsyncStorage } from "next/dist/client/components/action-async-storage.external";
import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";

interface RentListingProps {
  rentproduct: Rapp | any;
  index: number;
  className?: string;
  component?: string;
  user?: User;
}

const RentListing = ({
  rentproduct,
  index,
  className,
  component,
  user,
}: RentListingProps) => {
  const router = useRouter();
  const validUrls = useMemo(
    () =>
      rentproduct?.images
        ?.map(({ image }) => (typeof image === "string" ? image : image?.url))
        .filter(Boolean) || [],
    [rentproduct]
  );
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [likes, setLikes] = useState(rentproduct?.likes_length ?? 0);
  const [isLike, setIsLike] = useState(false);
  const [buttonLoading, setbuttonLoading] = useState<boolean>(false);
  const [totalRating, setTotalRating] = useState<number>(0);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeImage, setActiveImage] = useState(validUrls[0] ?? "");
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const rappImage = rentproduct?.images[0]?.image?.url;
  // const rappImage = useMemo(() => {
  //   const img = rentproduct?.images[0]?.image;

  //   if (typeof img === "string") {
  //     return img?.startsWith("http") ? img : "";
  //   }

  //   if (typeof img === "object" && typeof img?.url === "string") {
  //     return img.url.startsWith("http") ? img.url : "";
  //   }

  //   return "";
  // }, [rentproduct]);

  // console.log("rappImage ==>", rappImage);
  const toggleDescription = () =>
    setIsDescriptionExpanded(!isDescriptionExpanded);

  const handleImageClick = useCallback(
    (imageSrc: string) => setActiveImage(imageSrc),
    []
  );

  const handleLikeClick = useCallback(async () => {
    if (!user?.id) {
      toast.error("You are not logged in.");
      return;
    }

    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/rappsLikes/${rentproduct?.id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!result.ok) {
        console.log("Server error. Are you online?");
        return;
      }

      const body = await result.json();
      setLikes(body.likeCount);
      setIsLike(body.isLikeUpdated);
    } catch (error) {
      console.error("Error updating likes:", error);
    }
  }, [rentproduct, user]);

  const handleLinkClick = useCallback(() => {
    setbuttonLoading(true);
    router.push(`/ai-apps/${rentproduct?.slug}`);
  }, [rentproduct, router]);

  const handleRatingFetched = (rating: number) => {
    setTotalRating(rating);
  };

  useEffect(() => {
    if (rentproduct?.likes_id && user?.id) {
      setIsLike(
        rentproduct?.likes_user_id?.includes(user.id) // Check if user.id is in likes_user_id array
      );
    }
    if (rentproduct?.likes_id) {
      setLikes(rentproduct.likes_length);
    }
  }, [rentproduct, user]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, index * 75);

    return () => clearTimeout(timer);
  }, [index]);

  if (!rentproduct || !isVisible) return <ProductPlaceholder />;
  const getRelativeTime = (dateString: string): string => {
    const createdAt = new Date(dateString).getTime(); // Convert to timestamp (number)
    const now = new Date().getTime(); // Convert to timestamp (number)
    const diffInMs = now - createdAt; // Now both are numbers

    const diffInSec = Math.floor(diffInMs / 1000);
    const diffInMin = Math.floor(diffInSec / 60);
    const diffInHours = Math.floor(diffInMin / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInMonths / 12);

    if (diffInYears > 0)
      return ` ${diffInYears} Year${diffInYears > 1 ? "s" : ""} ago`;
    if (diffInMonths > 0)
      return ` ${diffInMonths} Month${diffInMonths > 1 ? "s" : ""} ago`;
    if (diffInDays > 0)
      return ` ${diffInDays} Day${diffInDays > 1 ? "s" : ""} ago`;
    if (diffInHours > 0)
      return ` ${diffInHours} Hour${diffInHours > 1 ? "s" : ""} ago`;
    if (diffInMin > 0)
      return ` ${diffInMin} Minute${diffInMin > 1 ? "s" : ""} ago`;
    return "Created just now";
  };
  return (
    <>
      <div
        className={cn(
          `w-auto ${
            component === "userPortfolio" ? "" : "max-w-96"
          } bg-transparent rounded-lg overflow-hidden relative cursor-pointer hover:shadow-xl`,
          className
        )}
        onClick={() => setIsDrawerOpen(true)}
      >
        {rentproduct.isFeatured && (
          <div className="absolute top-0 right-0 w-auto  z-20 text-sm font-bold rounded-xl flex justify-center items-center">
            <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-xs px-2 py-1 rounded-bl-lg overflow-hidden animate-pulse-glow">
              FEATURED
              <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
            </div>
          </div>
        )}
        <div className="h-full aspect-ratio-3/2">
          <Image
            alt={rentproduct.name || "Product Image"}
            className="h-full w-full object-cover object-center"
            width="1000"
            height="1000"
            src={
              activeImage ||
              rappImage ||
              "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/placeholder-09.webp"
            }
            loading="lazy"
          />
          <div className="text-white absolute bg-gradient-to-br from-indigo-950 via-indigo-900 to-indigo-950 px-2 py-1 w-full bottom-0 left-0 z-20">
            <div className="flex justify-between items-center gap-2">
              <p className="font-bold text-sm truncate capitalize">
                {rentproduct.name}
              </p>
              <div
                className={`flex ${totalRating ? "gap-2" : "gap-1"} items-center  px-2 py-1 rounded-md`}
              >
                <div className=" flex items-center rounded-md gap-1">
                  <TotalRating
                    totalRating={rentproduct.rating}
                    rappId={rentproduct.id}
                    user={rentproduct}
                    onRatingFetched={handleRatingFetched}
                  />
                </div>
                <div className="flex items-center rounded-md gap-[3px]">
                  <Heart
                    className={`cursor-pointer h-4 w-4 ${isLike ? "text-red-500 fill-red-500" : ""}`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleLikeClick();
                    }}
                  />
                  <p className="font-semibold text-xs sm:text-sm md:text-sm">
                    {likes}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute top-2 left-2 bg-indigo-500 px-2 py-1 rounded text-xs uppercase">
            {rentproduct.modelType}
          </div>
          {/* <div className="absolute top-2 right-2 bg-indigo-500 px-2 py-1 rounded text-xs">
            4.0 ★
          </div> */}
        </div>
      </div>
      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <DrawerContent className="max-h-[80vh] md:max-h-[70vh] py-2 justify-between">
          <Container>
            <div className="flex flex-col md:flex md:flex-row mx-4 gap-2">
              <div className="flex flex-col md:grid md:grid-cols-2 gap-4 md:w-1/2">
                <Image
                  src={
                    validUrls[0]
                      ? validUrls[0]
                      : "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/placeholder-09.webp"
                  }
                  className="h-40 md:h-60 lg:h-60 object-cover object-center rounded-lg"
                  height="400"
                  width="400"
                  alt="thumbnail"
                />
                <div className="md:grid flex  md:grid-cols-2 overflow-x-scroll whitespace-nowrap gap-2 md:w-fit no-scrollbar">
                  {validUrls.slice(1).map((imageSrc, index) => (
                    <div key={index} onClick={() => handleImageClick(imageSrc)}>
                      <Image
                        src={imageSrc}
                        className={`w-24 h-20 md:w-28 md:h-28 object-cover object-center rounded-lg ${
                          activeImage === imageSrc
                            ? "border-2 border-indigo-500"
                            : ""
                        }`}
                        height="100"
                        width="100"
                        alt="thumbnail"
                      />
                    </div>
                  ))}
                </div>
              </div>
              <div className=" md:w-1/2 flex-grow relative">
                <div className="flex justify-between items-center mb-2 sm:mb-2 lg:mb-3">
                  <DrawerTitle className="text-xl mb-1 truncate capitalize">
                    {rentproduct.name}
                  </DrawerTitle>
                  <div className="flex rounded-xl items-center bg-white/30 px-2 py-1 gap-2">
                    <Heart
                      className={`cursor-pointer ${
                        isLike ? "text-red-500 fill-red-500" : ""
                      }`}
                      onClick={(e) => {
                        handleLikeClick();
                      }}
                    />
                    <p className="font-bold">{likes}</p>
                  </div>
                </div>
                <DrawerDescription className="mb-1 max-h-20 md:max-h-48 overflow-y-auto">
                  <ReactMarkdown
                    components={{
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline"
                        >
                          {children}
                        </a>
                      ),
                    }}
                    className="first-letter:uppercase"
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                  >
                    {isDescriptionExpanded
                      ? rentproduct.description
                      : `${rentproduct.description?.substring(0, 100)}...`}
                  </ReactMarkdown>
                  <span
                    className="text-indigo-500 cursor-pointer"
                    onClick={toggleDescription}
                  >
                    {isDescriptionExpanded ? " Show less" : " Show more"}
                  </span>
                </DrawerDescription>

                <Link href={`/users/${rentproduct?.creator?.user_name}`}>
                  <p className="my-2 hover:text-indigo-400 first-letter:uppercase">
                    @
                    {capitalizeFirstLetter(
                      rentproduct?.creator?.user_name || "Anonymous"
                    )}
                  </p>
                </Link>
                <div className="flex justify-between">
                  <div
                    className={`flex flex-row rounded ${rentproduct.totalCost + rentproduct.price === 0 ? "bg-green-500 pl-[7px] tracking-wider" : "bg-white/30"} w-fit mb-3 px-2 items-center lg:mb-3`}
                  >
                    <p className="text-xl font-bold">
                      {" "}
                      {rentproduct.totalCost + rentproduct.price === 0
                        ? "FREE"
                        : rentproduct.totalCost + rentproduct.price}
                    </p>
                    {rentproduct.totalCost + rentproduct.price === 0 ? (
                      ""
                    ) : (
                      <img
                        src={coinImage.src}
                        alt="Coin"
                        style={{ width: "23px", height: "23px" }}
                        loading="lazy"
                      />
                    )}
                  </div>
                  <div>
                    <div className="text-sm bg-white/30 text-white px-2 py-1 rounded">
                      {getRelativeTime(
                        rentproduct?.newest || rentproduct?.createdAt || ""
                      )}
                    </div>
                  </div>
                </div>
                <p className="pb-16 lg:pb-16">
                  {rentproduct.affiliated_with ? (
                    <div>
                      Affiliated With :{" "}
                      <a
                        href={rentproduct.affiliated_with}
                        className="text-green-500 underline "
                        target="_blank"
                      >
                        {rentproduct.affiliated_with.substring(0, 30) + "..."}
                      </a>
                    </div>
                  ) : (
                    ""
                  )}
                </p>
                <div className="absolute bottom-2 w-full">
                  <button
                    className="bg-gradient-to-br from-indigo-600 to-indigo-700 w-full text-white rounded-md h-10 font-medium text-center align-middle"
                    // href={`/rent/${rentproduct.id}`}
                    onClick={handleLinkClick}
                  >
                    {buttonLoading ? <div>Loading...</div> : "Try Now"}
                  </button>
                </div>
              </div>
            </div>
          </Container>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default RentListing;

const ProductPlaceholder = () => {
  return (
    <div className="flex flex-col w-full">
      <div className="relative bg-zinc-100 aspect-video w-full overflow-hidden rounded-xl">
        <Skeleton className="h-full w-full" />
      </div>
    </div>
  );
};
