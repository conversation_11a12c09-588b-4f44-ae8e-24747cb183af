"use client";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { IconHours12 } from "@tabler/icons-react";

let interval: any;

type Card = {
  id: number;
  name: string;
  title:string;
  designation: string;
  content: React.ReactNode;
  image?:string;
};

export const CardStack = ({
  items,
  offset,
  scaleFactor,
}: {
  items: Card[];
  offset?: number;
  scaleFactor?: number;
}) => {
  const CARD_OFFSET = offset || 10;
  const SCALE_FACTOR = scaleFactor || 0.06;
  const [cards, setCards] = useState<Card[]>(items);

  useEffect(() => {
    startFlipping();

    return () => clearInterval(interval);
  }, []);
  const startFlipping = () => {
    interval = setInterval(() => {
      setCards((prevCards: Card[]) => {
        const newArray = [...prevCards]; // create a copy of the array
        newArray.unshift(newArray.pop()!); // move the last element to the front
        return newArray;
      });
    }, 5000);
  };

  return (
    <div className="relative h-60 w-full">
      {cards.map((card, index) => {
        return (
          <motion.div
            key={card.id}
            className="absolute w-full dark:bg-black bg-indigo-900 rounded-2xl border border-indigo-500 p-2 shadow-xl dark:border-white/[0.1]  shadow-black/[0.1] dark:shadow-white/[0.05] flex flex-col justify-between"
            style={{
              transformOrigin: "top center",
            }}
            animate={{
              top: index * -CARD_OFFSET,
              scale: 1 - index * SCALE_FACTOR, // decrease scale for cards that are behind
              zIndex: cards.length - index, //  decrease z-index for the cards that are behind
            }}
          >
          <div className="p-4">
            <img src={card.image} alt="Image" className="w-full h-32 rounded-xl px-4 object-cover" loading="lazy"/>

            <h1 className="pt-2 text-bold text-lg text-center">Basics of</h1>
            <h1 className="font-bold text-center text-white dark:text-neutral-200">{card.content}</h1>

            <div className="flex justify-between items-center flex-wrap py-2 mt-2 font-bold text-sm px-4 space-x-12 lg:space-x-20 md:space-x-16">
                <h1 className="">{card.designation}</h1>
                <h1 className="">{card.name}</h1>
            </div>
          </div>
       
          </motion.div>
        );
      })}
    </div>
  );
};
