import { useState } from "react";

function ImageUploader({ model, setBase64Image}) {
  const [uploadedFileName, setUploadedFileName] = useState("");
  const [loading, setLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setLoading(true);
      setUploadedFileName(file.name);

      const reader = new FileReader();
      reader.onloadend = () => {
        setBase64Image(reader.result as string);
        setImageFile(file);
        setLoading(false);
      };
      reader.readAsDataURL(file);
    }
    else if (!file && uploadedFileName) 
      resetFile();
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setIsDragOver(false);
    const file = event.dataTransfer.files[0];
    if (file) processFile(file);
    else resetFile();
  };

  const resetFile = () => {
    setBase64Image("");
    setUploadedFileName("");
    setImageFile(null);
  };

  const processFile = (file) => {
    setLoading(true);
    setUploadedFileName(file.name);

    const reader = new FileReader();
    reader.onloadend = () => {
      setBase64Image(reader.result as string);
      setImageFile(file);
      setLoading(false);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="mb-8">
      <label
        htmlFor="image"
        className="block text-xl font-semibold mb-3 text-yellow-300"
      >
        {model.name === "PuLID-Image Editing" ? (
          <>
            Upload Face Image <span className="text-red-500">*</span>
          </>
        ) : (
          <>
            Upload Image 
            {/* <span className="text-red-500">*</span> */}
          {model.imageinput && (
             <span className="text-red-500">*</span>
          )} 
          </>
        )}
      </label>

      
      <div
        className={`flex items-center justify-center w-full h-48 border border-dashed rounded-xl transition-shadow duration-300 ${
          isDragOver ? "bg-indigo-700 border-white shadow-lg" : "border-indigo-500 bg-gradient-to-br from-indigo-700 to-indigo-800"
        }`}
        onDragOver={(event) => {
          event.preventDefault();
          setIsDragOver(true);
        }}
        onDragLeave={() => setIsDragOver(false)}
        onDrop={handleDrop}
      >
        <label
          htmlFor="image"
          className={`relative flex flex-col items-center justify-center w-full h-full ${
            loading
              ? "bg-gray-700"
              : "bg-gradient-to-br from-indigo-700 to-indigo-800"
          } border border-indigo-500 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-xl cursor-pointer hover:bg-indigo-600`}
        >
          {loading ? (
            <div className="flex items-center">
              <svg
                className="animate-spin mr-2"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="10" strokeOpacity="0.5" />
                <path d="M4 12a8 8 0 1 1 16 0" />
              </svg>
              <p className="text-white">Uploading...</p>
            </div>
          ) : (
            <>
              {isDragOver ? (
                <p className="text-xl text-white font-semibold">Drop the image here!</p>
              ) : uploadedFileName ? (
                <div className="flex flex-col items-center p-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="48"
                    height="48"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-file-image text-yellow-400" // Color remains
                  >
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                    <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                    <circle cx="10" cy="12" r="2" />
                    <path d="m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22" />
                  </svg>
                  <div className="mt-4 space-y-2 text-center">
                    <p>
                      <span className="text-green-400 font-semibold text-base">
                        Image uploaded,{" "}
                      </span>
                      <span className="text-white font-semibold text-base italic underline decoration-dotted">
                        Tap to change
                      </span>
                    </p>
                    <p className="text-green-400 text-sm font-medium">
                      Uploaded: <span className="text-white">{uploadedFileName}</span>
                    </p>
                  </div>
                </div>
              ) : (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="48"
                    height="48"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-yellow-400 mb-3"
                  >
                    <path d="M16 5h6" />
                    <path d="M19 2v6" />
                    <path d="M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5" />
                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                    <circle cx="9" cy="9" r="2" />
                  </svg>
                  <p className="text-base text-gray-200 font-semibold">
                    Tap or Drop to upload
                  </p>
                  <p className="text-xs text-gray-400">
                    Accepted formats: PNG, JPG, & WEBP
                  </p>
                </>
              )}
            </>
          )}

        {model.imageinput && (
          <input
            id="image"
            type="file"
            name="image"
            accept="image/*"
            onChange={handleFileChange}
            required
            className="hidden"
          />
        )} 

        {model.imageinputopt && (
          <input
            id="image"
            type="file"
            name="image"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        )} 

        </label>
      </div>




    </div>
  );
}

export default ImageUploader;