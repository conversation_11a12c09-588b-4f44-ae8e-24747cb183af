"use client";
import Image from "next/image";
import React, { useState } from "react";
import {
  motion,
  useTransform,
  AnimatePresence,
  useMotionValue,
  useSpring,
} from "framer-motion";
import Link from "next/link";

interface AnimatedTooltipProps {
  items: any[]; // Adjust the type as per your requirements
  component: string; // Define the type of the bountypage prop
}
export const AnimatedTooltip: React.FC<AnimatedTooltipProps> = ({
  items,
  component,
}: AnimatedTooltipProps) => {

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const springConfig = { stiffness: 100, damping: 5 };
  const x = useMotionValue(0);

  const rotate = useSpring(
    useTransform(x, [-100, 100], [-45, 45]),
    springConfig
  );

  const translateX = useSpring(
    useTransform(x, [-100, 100], [-50, 50]),
    springConfig
  );
  const handleMouseMove = (event: any) => {
    const halfWidth = event.target.offsetWidth / 2;
    x.set(event.nativeEvent.offsetX - halfWidth); // set the x value, which is then used in transform and rotate
  };

  return (
    <>
      {items.map((item, idx) => (
        <div
          className="-mr-4 relative group"
          key={idx}
          onMouseEnter={() => setHoveredIndex(item.id)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <AnimatePresence mode="wait">
            {hoveredIndex === item.id && (
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.6 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  scale: 1,
                  transition: {
                    type: "spring",
                    stiffness: 260,
                    damping: 10,
                  },
                }}
                exit={{ opacity: 0, y: 20, scale: 0.6 }}
                style={{
                  translateX: translateX,
                  rotate: rotate,
                  whiteSpace: "nowrap",
                }}
                className="absolute -top-16 -left-1/2 translate-x-1/2 flex text-xs  flex-col items-center justify-center rounded-md bg-black z-10 shadow-xl px-4 py-2"
              >
                <div className="absolute inset-x-10 z-10 w-[20%] -bottom-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent h-px " />
                <div className="absolute left-10 w-[40%] z-10 -bottom-px bg-gradient-to-r from-transparent via-sky-500 to-transparent h-px " />
                <div className="font-bold text-white relative z-30 text-base">
                  {component === "bountypage"
                    ? item.name
                    : item?.user_name
                    ?? item?.email?.split("@")[0]
                  }
                </div>
                <div className="text-white text-xs">
                  {component === "bountypage"
                    ? item.designation
                    : item?.generalInformation?.profession
                    ?? item?.role
                  }
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          <Link href={`/users/${item.id}`}>
          <Image
            onMouseMove={handleMouseMove}
            height={100}
            width={100}
            src={
              component === "bountypage"
                ? item.image
                : item?.profileImage?.url
                ?? "/mailly.png"
              }
            alt={item.id}
            className={`object-cover !m-0 !p-0 object-top rounded-full ${
              component === "bountypage"
                ? "h-8 w-8 sm:h-10 sm:w-10"
                : "sm:h-32 sm:w-32 h-24 w-24"
            } border-2 group-hover:scale-105 group-hover:z-10 border-white  relative transition duration-500`}
          />
          </Link>
        </div>
      ))}
    </>
  );
};