'use client'
import * as React from "react";

import { cn } from "@/lib/utils";
import { useMotionTemplate, useMotionValue, motion } from "framer-motion";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { useRef } from "react";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, value, onChange, ...props }, ref) => {
    const defaultHeight = 116; 
    const radius = 150; // change this to increase the rdaius of the hover effect
    const [visible, setVisible] = React.useState(false);
    const [isExpanded, setIsExpanded] = React.useState(true);
    const [shouldShowToggle, setShouldShowToggle] = React.useState(false);

    const textareaRef = useRef(null);
    let mouseX = useMotionValue(0);
    let mouseY = useMotionValue(0);

    function handleMouseMove({ currentTarget, clientX, clientY }: any) {
      let { left, top } = currentTarget.getBoundingClientRect();

      mouseX.set(clientX - left);
      mouseY.set(clientY - top);
    }
    const checkOverflow = () => {
      if (textareaRef.current) {
        const { scrollHeight, clientHeight } = textareaRef.current;
        setShouldShowToggle(scrollHeight > defaultHeight); // Show button only if content exceeds default height
      }
    };
    const adjustHeight = () => {
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto"; // Reset height

        if (isExpanded) {
          textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`; // Adjust height to content
        } else {
          textareaRef.current.style.height = `${defaultHeight}px`; // Fixed height for 5 rows
        }
      }
    };
    React.useEffect(() => {
      adjustHeight();
      checkOverflow();
    }, [value, isExpanded]);
    return (
      <motion.div
        style={{
          background: useMotionTemplate`
          radial-gradient(
            ${visible ? radius + "px" : "0px"} circle at ${mouseX}px ${mouseY}px,
            var(--blue-500),
            transparent 80%
          )
        `,
        }}
        className="relative p-[2px] rounded-lg transition duration-300 group/input"
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
      >
        <textarea
          className={cn(
            `relative flex min-h-[80px] w-full border-none bg-background shadow-input rounded-md px-3 py-2 text-sm  file:border-1 file:bg-muted-foreground 
                    file:text-sm file:font-medium placeholder:text-[#757575] 
                    focus-visible:outline-none focus-visible:ring-[2px]  focus-visible:ring-muted-foreground
                     disabled:cursor-not-allowed disabled:opacity-50
                     dark:shadow-[0px_0px_1px_1px_var(--neutral-700)]
                     group-hover/input:shadow-none transition duration-400
                     `,
            className
          )}
          style={{
            overflow: "hidden",
            resize: "none",
            minHeight: isExpanded ? "auto" : "116px",
          }}
          // ref={ref}
          ref={(el) => {
            textareaRef.current = el;
            if (typeof ref === "function") {
              ref(el);
            } else if (ref) {
              (
                ref as React.MutableRefObject<HTMLTextAreaElement | null>
              ).current = el;
            }
          }}
          value={value}
          // onChange={(e) => {
          //   if (onChange) onChange(e);
          //   if (e.target.value.trim()) {
          //     setIsExpanded(true); // Expand when user starts typing
          //   } else {
          //     setIsExpanded(false); // Collapse if empty
          //   }
          //   adjustHeight();
          // }}
          onChange={(e) => {
            if (onChange) onChange(e);
            checkOverflow();
          }}
          // ref={ref}
          {...props}
        />

        {shouldShowToggle && (
          <button
            type="button"
            onClick={() => {
              setIsExpanded(!isExpanded);
              // setTimeout(adjustHeight, 10) // Ensures height readjusts when collapsed
            }}
            className="absolute bottom-2 right-2 flex items-center bg-slate-300 text-black font-bold rounded-sm"
          >
            {isExpanded ? (
              <>
                <ChevronUpIcon className="w-4 h-4 font-bold" />
              </>
            ) : (
              <>
                <ChevronDownIcon className="w-4 h-4 font-bold" />
              </>
            )}
          </button>
        )}
      </motion.div>
    );
  }
);
Textarea.displayName = "Textarea";

export { Textarea };
