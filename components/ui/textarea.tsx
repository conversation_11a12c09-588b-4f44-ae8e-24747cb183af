"use client";
import * as React from "react";
import { cn } from "@/lib/utils";
import { useMotionTemplate, useMotionValue, motion } from "framer-motion";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, value, onChange, ...props }, ref) => {
    const defaultHeight = 116;
    const radius = 150;

    const [visible, setVisible] = React.useState(false);
    const [isExpanded, setIsExpanded] = React.useState(false); // default collapsed
    const [shouldShowToggle, setShouldShowToggle] = React.useState(false);

    const textareaRef = React.useRef<HTMLTextAreaElement | null>(null);

    const mouseX = useMotionValue(0);
    const mouseY = useMotionValue(0);

    function handleMouseMove({ currentTarget, clientX, clientY }: any) {
      let { left, top } = currentTarget.getBoundingClientRect();
      mouseX.set(clientX - left);
      mouseY.set(clientY - top);
    }

    const checkOverflow = () => {
      if (textareaRef.current) {
        const { scrollHeight } = textareaRef.current;
        setShouldShowToggle(scrollHeight > defaultHeight);
      }
    };

    const adjustHeight = () => {
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
        if (isExpanded) {
          textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        } else {
          textareaRef.current.style.height = `${defaultHeight}px`;
        }
      }
    };

    // Adjust height and overflow logic when content or expansion changes
    React.useEffect(() => {
      adjustHeight();
      checkOverflow();
    }, [value, isExpanded]);

    // On first mount, check overflow (for prefilled data)
    React.useEffect(() => {
      checkOverflow();
    }, []);

    return (
      <motion.div
        style={{
          background: useMotionTemplate`
            radial-gradient(
              ${visible ? radius + "px" : "0px"} circle at ${mouseX}px ${mouseY}px,
              var(--blue-500),
              transparent 80%
            )
          `,
        }}
        className="relative p-[2px] rounded-lg transition duration-300 group/input"
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
      >
        <textarea
          className={cn(
            `relative flex w-full border-none bg-background shadow-input rounded-md px-3 py-2 text-sm file:border-1 file:bg-muted-foreground 
              file:text-sm file:font-medium placeholder:text-[#757575] 
              focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-muted-foreground
              disabled:cursor-not-allowed disabled:opacity-50
              dark:shadow-[0px_0px_1px_1px_var(--neutral-700)]
              group-hover/input:shadow-none transition duration-400`,
            className
          )}
          style={{
            overflow: "hidden",
            resize: "none",
            minHeight: "116px",
          }}
          ref={(el) => {
            textareaRef.current = el;
            if (typeof ref === "function") {
              ref(el);
            } else if (ref) {
              (ref as React.MutableRefObject<HTMLTextAreaElement | null>).current = el;
            }
          }}
          value={value}
          onChange={(e) => {
            if (onChange) onChange(e);
            adjustHeight();
            checkOverflow();
          }}
          {...props}
        />

        {shouldShowToggle && (
          <button
            type="button"
            onClick={() => {
              setIsExpanded(!isExpanded);
            }}
            className="absolute bottom-2 right-2 flex items-center bg-slate-300 text-black font-bold rounded-sm"
          >
            {isExpanded ? (
              <ChevronUpIcon className="w-4 h-4 font-bold text-white bg-indigo-400" />
            ) : (
              <ChevronDownIcon className="w-4 h-4 font-bold text-white bg-indigo-400" />
            )}
          </button>
        )}
      </motion.div>
    );
  }
);

Textarea.displayName = "Textarea";

export { Textarea };
