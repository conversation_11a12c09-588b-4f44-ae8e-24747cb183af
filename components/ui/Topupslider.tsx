// components/TopUpSlider.tsx

import { useState } from "react";
import Image from "next/image";

const TopUpSlider = ({ topUp, onPayClick }) => {
  const middleIndex = Math.floor(topUp.length / 2);
  const [selectedIndex, setSelectedIndex] = useState(middleIndex);

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const index = parseInt(e.target.value);
    setSelectedIndex(index);
  };

  const getPrice = (coins: number) => {
    const inrPrice = coins?.toFixed(2);
    const usdPrice = (parseFloat(inrPrice) * 0.012).toFixed(2); // Example conversion rate: 1 INR = 0.012 USD
    return { inrPrice, usdPrice };
  };

  const selectedTopUpItem = topUp[selectedIndex];

  const sliderProgress = (selectedIndex / (topUp.length - 1)) * 100;

  return (
    <div className="flex flex-col items-center justify-center">
      <div className=" w-full px-4 mb-6 rounded-xl">
        {/* Slider to select top-up item */}
        <div className="mb-6">
          <div className="text-center text-xl font-extrabold mb-4 bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">
            SELECT RECHARGE
          </div>

          <input
            type="range"
            min="0"
            max={topUp.length - 1}
            value={selectedIndex}
            onChange={handleSliderChange}
            className="w-full h-3 rounded-lg appearance-none mb-6"
            aria-label="Select top-up option"
            style={{
              background: `linear-gradient(to right, #4F46E5 ${sliderProgress}%, #e5e7eb ${sliderProgress}%)`,
            }}
          />

          <div className="text-center flex justify-center">
            <div className="flex gap-2 items-center">
              <Image
                src="/img/coin-png.png"
                width={40}
                height={40}
                alt="Coin"
                className="rotate-45"
              />
              <p className="text-2xl font-bold">
                Get {selectedTopUpItem?.numberOfCoins} Joules
              </p>
            </div>
          </div>
        </div>

        {/* Coin image and payment button */}
        <button
          onClick={() => onPayClick(selectedTopUpItem)}
          className="bg-gradient-to-r from-amber-500 to-pink-500 hover:bg-gradient-to-4 hover:from-amber-600 hover:to-pink-600 hover:bg-purple-700 text-white md:text-xl font-black py-2 px-6 rounded-lg transition disabled:opacity-50 w-full"
        >
          Pay Now<span className="mx-1 text-xl font-bold"></span>
          <span className="md:text-xl font-bold">
            ₹{getPrice(selectedTopUpItem?.numberOfCoins).inrPrice}
          </span>
          <span className="mx-1 text-xl font-bold">/</span>
          <span className="text-sm font-bold justify-self-end">
            ${getPrice(selectedTopUpItem?.numberOfCoins).usdPrice}
          </span>
        </button>
      </div>
    </div>
  );
};

export default TopUpSlider;
