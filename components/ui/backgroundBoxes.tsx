"use client";
import React from "react";
import { Boxes } from "../ui/background-boxes";
import { cn } from "@/lib/utils";

export function BackgroundBoxesDemo() {
  return (
    <div className="h-60 relative w-full overflow-hidden bg-transparent flex flex-col items-center justify-center rounded-lg">
      <div className="absolute inset-0 w-full h-full z-20 [mask-image:radial-gradient(red, white)] pointer-events-none" />
      <Boxes />
      <h1 className={cn("md:text-6xl font-black text-4xl text-white relative z-20")}>
        Explore
      </h1>
      <p className="text-center text-xl mt-2 sm-2xl text-neutral-200 relative z-20">
        Dive into a world of possibilities 
      </p>
    </div>
  );
}
