"use client";
import { Plus } from "lucide-react";
import Link from "next/link";
import React from "react";
import { usePathname } from "next/navigation";
import UserBalance from "../UserBalance";
import StaticUserBalance from "./header/staticpricing";

interface TopHeaderProps {
  title?: string;
  buttonName?: string; // Optional prop
  buttonLink?: string; // Optional prop
  user: any; // Optional prop
}

const TopHeader: React.FC<TopHeaderProps> = ({
  buttonName,
  buttonLink,
  title,
  user,
}) => {
  const pathname = usePathname();

  // Get the current route path and split it into segments
  const pathSegments = pathname.split("/").filter((segment) => segment !== "");

  return (
    <nav className="sticky top-4 z-40 m-4 flex flex-row flex-wrap items-center justify-between rounded-xl bg-white/10 p-2 backdrop-blur-xl dark:bg-[#0b14374d] px-4">
      <div>
        <div className="h-6 w-[224px] pt-1">
          {pathSegments.length > 0 ? (
            pathSegments.map((segment, index) => {
              const isLastSegment = index === pathSegments.length - 1;
              const pathToSegment =
                "/" + pathSegments.slice(0, index + 1).join("/");
              const isExcludedSegment = segment === "create";

              return (
                <React.Fragment key={index}>
                  {!isLastSegment && !isExcludedSegment ? (
                    <Link
                      href={pathToSegment}
                      className="text-sm font-normal text-navy-700 hover:underline dark:text-white dark:hover:text-white"
                    >
                      {segment.replace(/-/g, " ").toLowerCase()}
                    </Link>
                  ) : (
                    <span className="text-sm cursor-default font-normal text-navy-700 dark:text-white">
                      {segment.replace(/-/g, " ").toLowerCase()}
                    </span>
                  )}
                  {!isLastSegment && (
                    <span className="mx-1 cursor-default text-sm text-navy-700 hover:text-navy-700 dark:text-white">
                      /
                    </span>
                  )}
                </React.Fragment>
              );
            })
          ) : (
            <Link
              href="/"
              className="text-sm font-normal text-navy-700 hover:underline dark:text-white dark:hover:text-white"
            >
              Home
            </Link>
          )}
        </div>
        <div className="flex gap-2 items-center">
          <p className="shrink text-[33px] capitalize text-navy-700 dark:text-white">
            <span className="font-bold capitalize">
              {pathSegments.at(-1)?.replace(/-/g, " ") || "Home"}
            </span>
          </p>
          {buttonLink && (
            <Link href={buttonLink}>
              <Plus className="bg-gradient-to-r mt-1 from-amber-500 to-pink-500 h-8 w-8 p-2 rounded-full  text-2xl text-white group-hover:text-dash transition-colors relative z-10 duration-300" />
            </Link>
          )}
        </div>
      </div>

      {/* Button */}
      <div className="flex">
        {/* {buttonName && buttonLink && (
          <Link href={buttonLink}>
            <span className="inline-block px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition duration-300">
              {buttonName}
            </span>
          </Link>
        )} */}
        {user?.id ? (
          <UserBalance user={user} /> // Show UserBalance when user is present
        ) : (
          <StaticUserBalance />
        )}
      </div>
    </nav>
  );
};

export default TopHeader;
