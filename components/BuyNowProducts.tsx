import { useEffect, useState } from "react";
import { toast } from "sonner";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import coinImage from "../public/img/coin-png.png";
import { useRouter } from "next/navigation";

interface BuyNowProductsProps {
  product: Product;
  user: User;
}
interface Product {
  price?: number;
  id: string;
}

interface User {
  coinBalance: number;
  // Add other properties if needed
}

const BuyNowProducts = ({ product, user }: BuyNowProductsProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const router = useRouter();

  const handleBuyNow = () => {
    if (user) {
      if (user.coinBalance >= product.price) {
        setIsModalOpen(true);
      } else {
        toast.error("Insufficient Credits to buy the product");
      }
    } else {
      toast.error("Please login to Buy Product");
    }
  };

  const handleConfirmPurchase = async () => {
    try {
      toast.loading("Purchasing...");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/purchases/purchaseProduct`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            productId: product.id,
          }),
        }
      );

      const result = await response.json();
      toast.dismiss();

      if (response.ok) {
        setIsModalOpen(false);
        toast.success(
          "Product Purchase Successful and Credits have been deducted."
        );
        router.push(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/thank-you?purchaseId=${result.purchase.id}`
        );
      } else {
        // throw new Error(result.message);
        toast.dismiss();
        toast.error(result.message);
      }
    } catch (error) {
      toast.error(`An error occurred while purchasing the product`);
    }
  };

  return (
    <>
      <button
        onClick={handleBuyNow}
        className="bg-white w-full text-indigo-600 rounded-md h-10 font-semibold"
      >
        Buy Now
      </button>
      {isModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">
              {product.price === 0 ? (
                "This asset is free and No credits will be deducted for this asset. Confirm your purchase"
              ) : (
                <div>
                  The Assets Price of {product.price}
                  <img
                    src={coinImage.src}
                    alt="Coin"
                    style={{
                      width: "25px",
                      height: "27px",
                      display: "inline",
                      margin: "0px 4px 4px 2px",
                    }}
                    loading="lazy"
                  />
                  Credits will be deducted from your account for this purchase.
                </div>
              )}
            </p>
            {errorMessage && <p className="text-red-500">{errorMessage}</p>}
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Close
              </Button>
              <Button variant="outline" onClick={handleConfirmPurchase}>
                Confirm
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}
    </>
  );
};

export default BuyNowProducts;
