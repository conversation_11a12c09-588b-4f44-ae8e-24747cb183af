"use client";
import { useState } from "react";

type FieldConfig = {
  name: string;
  label: string;
  type: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
};

type StepConfig = {
  title: string;
  fields: FieldConfig[];
};

type MultistepFormProps = {
  steps: StepConfig[];
  initialValues: Record<string, any>;
  onSubmit: (values: Record<string, any>) => void;
};

const MultistepForm: React.FC<MultistepFormProps> = ({
  steps,
  initialValues,
  onSubmit,
}) => {
  const [step, setStep] = useState(0);
  const [formData, setFormData] = useState(initialValues);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const nextStep = () => {
    if (step < steps.length - 1) {
      setStep((prev) => prev + 1);
    }
  };
  
  const prevStep = () => {
    if (step > 0) {
      setStep((prev) => prev - 1);
    }
  };
  

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen ">
      <form
        onSubmit={handleSubmit}
        className="p-8 shadow-md rounded-lg max-w-md w-full bg-dark"
      >
        <h2 className="text-2xl font-bold mb-6">{steps[step].title}</h2>

        {steps[step].fields.map((field) => (
          <div key={field.name} className="mb-4">
            <label htmlFor={field.name} className="block mb-2 font-medium">
              {field.label}
            </label>
            {field.type === "select" ? (
              <select
                id={field.name}
                name={field.name}
                multiple={field.options?.length ? true : false}
                onChange={handleChange}
                className="w-full p-3 border border-gray-300 rounded bg-transparent text-gray-800 focus:ring-2 focus:ring-blue-500"
              >
                {field.options?.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            ) : (
              <input
                id={field.name}
                type={field.type}
                name={field.name}
                placeholder={field.placeholder}
                value={formData[field.name]}
                onChange={handleChange}
                className="w-full p-3 border border-gray-300 rounded bg-transparent text-gray-800 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required={field.required}
              />
            )}
          </div>
        ))}

        <div className="flex justify-between">
          {step > 0 && (
            <button
              type="button"
              onClick={prevStep}
              className="bg-gray-500 hover:bg-gray-700 text-white px-4 py-2 rounded transition"
            >
              Previous
            </button>
          )}
          {step < steps.length - 1 ? (
            <button
              type="button"
              onClick={nextStep}
              className="bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded transition"
            >
              Next
            </button>
          ) : (
            <button
              type="submit"
              className="bg-green-500 hover:bg-green-700 text-white px-4 py-2 rounded transition"
            >
              Submit
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default MultistepForm;
