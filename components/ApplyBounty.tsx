"use client";
import { Bounty, User } from "@/server/payload-types";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import axios from "axios";
import Link from "next/link";

interface PageProps {
  bounty: any;
  index: number;
  user: User | null;
  bountyStatus: string;
}

const ApplyBounty: React.FC<PageProps> = ({ bounty, user }) => {
  //--------------------------------------------------deleting bounty---------------------------------------------------------
  const deleteBounty = async (bounty: Bounty) => {
    if (bounty.status === "approved") {
      toast.error("Cannot delete an approved bounty");
      return;
    }
    setLoading(true)
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`,
        {
          method: "DELETE",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await res.json();

      if (res.ok) {
        toast.success("Bounty deleted Successfully");
        setISDialogOpen(false)
        window.location.href = "/bounties"; //
      } else {
        toast.error(data.errors[0].message);
      }
    } catch (err) {
      console.log(err);
    }finally{
      setLoading(false)
    }
  };

  //---------------------------for copying the url ------------------------
  const [copied, setCopied] = useState(false);

  const copyURLToClipboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);
      toast.message("URL copied");
      setTimeout(() => {
        setCopied(false);
      }, 500);
    });
  };

  //--------------------------------------update code here ---------------------------------------------------
  const [isApplied, setIsApplied] = useState<boolean>(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setISDialogOpen] = useState(false);

  useEffect(() => {
    if (bounty && Array.isArray(bounty.applicants) && user) {
      if (
        bounty.applicants.some(
          (applicant: any) => applicant.userId === user.id
        )
      ) {
        setIsApplied(true);
      }
    }
  }, [bounty, user]);

  const message = async (e: any) => {
    const currentDate = new Date();
    const bountyDate = new Date(bounty.completionDate);

    const completionDateTime = new Date(bountyDate).setHours(0, 0, 0, 0);
    const currentDateTime = currentDate.setHours(0, 0, 0, 0);

    if (!user || !user.id) {
      toast.error("Please login to apply in bounty.");
      setDialogOpen(false);
      return;
    }
    if (isApplied) {
      toast.error("Already Applied");
      setDialogOpen(false);
      return;
    }
    if (completionDateTime >= currentDateTime) {
      setDialogOpen(true);
      return;
    }
    if (currentDate > bountyDate) {
      toast.error("Bounty has expired");
      setDialogOpen(false);
      return;
    } else {
      setDialogOpen(true);
    }
    return;
  };

  const [phone, setNumber] = useState("");
  const [linkedin, setLinkedInId] = useState("");
  const [approach, setApproach] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [linkedinError, setLinkedInError] = useState("");
  const [approachError, setApproachError] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [termsError, setTermsError] = useState("");

  const validateForm = () => {
    const phoneRegex = /^[0-9]{10}$/;
    const linkedinRegex =
      /^(https?:\/\/)?(www\.)?linkedin\.com\/in\/[a-zA-Z0-9_-]+(\/)?$/;

    if (!phoneRegex.test(phone)) {
      setPhoneError("Please enter a valid 10-digit phone number.");
      return false;
    } else {
      setPhoneError("");
    }

    if (!linkedinRegex.test(linkedin)) {
      setLinkedInError("Please enter a valid LinkedIn URL.");
      return false;
    } else {
      setLinkedInError("");
    }

    if (!approach.trim()) {
      setApproachError("Please provide your approach for the solution.");
      return false;
    } else {
      setApproachError("");
    }

    if (!termsAccepted) {
      setTermsError("You must accept the terms and conditions.");
      return false;
    } else {
      setTermsError("");
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
  
    if (!validateForm()) return;
  
    let uploadedFileId = null;
    setLoading(true);
  
    // File upload logic
    if (file) {
      const fileFormData = new FormData();
      fileFormData.append("file", file);
  
      try {
        const mediaRes = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files?depth=0`,
          {
            method: "POST",
            credentials: "include",
            body: fileFormData,
          }
        );
  
        const mediaData = await mediaRes.json();
  
        if (mediaRes.ok) {
          uploadedFileId = mediaData.doc.id; // Store only the file ID
        } else {
          throw new Error("File upload failed");
        }
      } catch (err) {
        toast.error("Failed to upload file.");
        return;
      }
    }
  
    // Create new applicant with only the file ID
    const updatedApplicant = {
      userId: user?.id,
      userName: user?.user_name,
      phone,
      linkedin,
      approach,
      relatedFile: uploadedFileId ? uploadedFileId : null, 
    };
    
    try {
      // Fetch existing bounty applicants
      const { data: existingBounty } = await axios.get(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`
      );
      
  
      // Map over existing applicants and extract the file IDs
      const updatedExistingApplicants = existingBounty.applicants?.map(applicant => ({
        ...applicant,
        relatedFile: applicant.relatedFile
                  ? applicant.relatedFile.map(file => file) // Convert to file IDs if relatedFile exists
                  : "" // If relatedFile doesn't exist, set it to an empty array
      }));

      // Add the new applicant with only the file ID
      const newUpdatedApplicants = updatedExistingApplicants ? [...updatedExistingApplicants, updatedApplicant] : [updatedApplicant];
  
      // Update the bounty with the new list of applicants

      try {
        await axios.patch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`,
          { applicants: newUpdatedApplicants }
        );
        toast.success("Applied Successfully!");
        setIsApplied(true);
        window.location.reload();
        // Optionally reload the data without reloading the page
      } catch (patchError) {
        console.error("Error updating bounty:", patchError);
        toast.error("Failed to Apply.");
      }
      finally {
        setLoading(false); // Enable button when API call completes
      }
    } catch (error) {
      toast.error("Failed to apply.");
    }
    finally {
      setLoading(false); // Enable button when API call completes
    }
    
  };

  return (
    <>
      <div className="flex flex-wrap gap-2">
        {user && user.id === bounty?.user.id ? (
          <div className="flex gap-1">
            <Link  href={`/dashboard/create/bounty?Id=${bounty?.id}`}>
              <Button
                variant="outline"
                className=" flex bg-indigo-700 px-2  hover:bg-white hover:text-indigo-900 "
              >
                Edit
              </Button>
            </Link>
            <Dialog open={isDialogOpen} onOpenChange={setISDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="bg-indigo-900 px-2 hover:bg-white hover:text-indigo-900 "
                >
                  Delete
                </Button>
              </DialogTrigger>
              <DialogContent>
                <div>
                <button
                onClick={() => setISDialogOpen(false)}
                className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100"
              >
                <X size={15} />
              </button>
                  <h1 className="text-center text-bold py-2 text-lg md:text-2xl">
                    {" "}
                    Are you sure want to delete bounty ?{" "}
                  </h1>
                  <div className="flex justify-center gap-2 mt-3">
                    <Button
                      onClick={() => setISDialogOpen(false)}
                      variant="outline"
                      className="bg-transparent  text-white px-4 py-1 hover:bg-indigo-800"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={(e) => {
                        deleteBounty(bounty);
                      }}
                      disabled={loading}
                      variant="outline"
                      className="bg-white text-indigo-700 px-4 py-1 hover:bg-indigo-800"
                    >
                      {loading ? "Deleting..." : "Delete"}
                    </Button>

                    
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        ) : (
          <Dialog open={dialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={message}
                variant="outline"
                className="bg-indigo-900 text-white px-2 py-1 hover:bg-indigo-800"
              >
                {isApplied ? "Applied" : "Apply"}
              </Button>
            </DialogTrigger>

            {/* <Dialog open={dialogOpen}> */}
            <DialogContent className="sm:max-w-[425px] text-center justify-between font-bold shadow-md">
              <button
                onClick={() => setDialogOpen(false)}
                className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100"
              >
                <X size={15} />
              </button>
              <DialogHeader>
                <DialogTitle className="text-center">
                  Enter details to apply
                </DialogTitle>
              </DialogHeader>

              {/*  -----------------------------------------form -------------------------------------------- */}
              <form
                onSubmit={handleSubmit}
                className="max-w-md mx-auto p-4 rounded-lg"
              >
                <div className="mb-4 flex flex-wrap">
                  <label
                    htmlFor="number"
                    className="block text-white font-normal mb-1"
                  >
                    Phone Number :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <input
                    type="text"
                    id="number"
                    value={phone}
                    onChange={(e) => setNumber(e.target.value)}
                    className="flex shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  />
                  {phoneError && (
                    <p className="text-red-500 text-sm">{phoneError}</p>
                  )}
                </div>

                <div className="mb-4 flex flex-wrap">
                  <label
                    htmlFor="linkedInId"
                    className=" block text-white font-normal mb-1"
                  >
                    LinkedIn ID :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <input
                    type="text"
                    id="linkedInId"
                    value={linkedin}
                    onChange={(e) => setLinkedInId(e.target.value)}
                    className="flex shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  />
                  {linkedinError && (
                    <p className="text-red-500 text-sm">{linkedinError}</p>
                  )}
                </div>

                <div className="mb-4 flex flex-wrap">
                  <label
                    htmlFor="approach"
                    className=" block text-white font-normal mb-1"
                  >
                    Approach for the solution :{" "}
                    <sup className="text-red-500 font-bold">*</sup>
                  </label>
                  <textarea
                    id="approach"
                    value={approach}
                    onChange={(e) => setApproach(e.target.value)}
                    className="flex shadow appearance-none resize-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  />
                  {approachError && (
                    <p className="text-red-500 text-sm">{approachError}</p>
                  )}
                </div>

                <div className="mb-4 flex flex-wrap">
                  <label
                    htmlFor="relatedFile"
                    className="block text-white font-normal mb-1"
                  >
                    Related Files :
                    {/* <sup className="text-red-500 font-bold">*</sup> */}
                  </label>
                  <input
                    type="file"
                    id="relatedFile"
                    onChange={(e) => setFile(e.target.files?.[0] || null)}
                    className="border border-gray-200 px-1 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                  />
                  {fileError && (
                    <p className="text-red-500 text-sm">{fileError}</p>
                  )}
                </div>

                <div className="py-2">
                  <label
                    htmlFor="termsAccepted"
                    className="flex items-center gap-1 font-normal"
                  >
                    <input
                      type="checkbox"
                      id="termsAccepted"
                      name="termsAccepted"
                      // checked={formData.termsAccepted}
                      //onChange={handleChange}
                      // required
                      checked={termsAccepted}
                      onChange={(e) => setTermsAccepted(e.target.checked)}
                      className="h-4 w-4"
                    />
                    <Link
                      href={"/bountytandc"}
                      className=" hover:text-blue-500"
                      target="_blank"
                    >
                      Accept Terms and Conditions
                    </Link>
                  </label>
                  {termsError && (
                    <p className="text-red-500 text-sm text-start">{termsError}</p>
                  )}
                </div>

                <div className="items-center justify-between pt-4">
                  <button
                    type="submit"
                    className="bg-white text-indigo-900 rounded-l hover:bg-indigo-500 hover:text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-all duration-200"
                    disabled={loading}
                  >
                   {loading ? "Applying..." : "Apply"}
                  </button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </>
  );
};

export default ApplyBounty;