"use client";
import { Bounty, User } from "@/server/payload-types";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle, Clock, X, XCircle } from "lucide-react";
import axios from "axios";
import Link from "next/link";
import { Info } from "lucide-react";

interface PageProps {
  bounty: any;
  index: number;
  user: User | null;
  bountyStatus: string;
  isApplicantsLimitReached: boolean;
}

const ApplyBounty: React.FC<PageProps> = ({
  bounty,
  user,
  isApplicantsLimitReached,
}) => {
  let imageUrl: string | undefined;

  if (user?.profileImage && typeof user.profileImage !== "string") {
    // It's a Media object, safe to access `url`
    imageUrl = user.profileImage.url;
  }
  //--------------------------------------------------deleting bounty---------------------------------------------------------
  const deleteBounty = async (bounty: Bounty) => {
    if (bounty.status === "approved" || bounty.status === "expired") {
      toast.error("Cannot delete an approved bounty");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`,
        {
          method: "DELETE",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await res.json();

      if (res.ok) {
        toast.success("Bounty deleted Successfully");
        setISDialogOpen(false);
        window.location.href = "/bounties"; //
      } else {
        toast.error(data.errors[0].message);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  //---------------------------for copying the url ------------------------
  const [copied, setCopied] = useState(false);

  const copyURLToClipboard = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true);
      toast.message("URL copied");
      setTimeout(() => {
        setCopied(false);
      }, 500);
    });
  };

  const [isApplied, setIsApplied] = useState<boolean>(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setISDialogOpen] = useState(false);

  useEffect(() => {
    if (bounty && Array.isArray(bounty.applicants) && user) {
      if (
        bounty.applicants.some((applicant: any) => applicant.userId === user.id)
      ) {
        setIsApplied(true);
      }
    }
  }, [bounty, user]);

  const message = async (e: any) => {
    const currentDate = new Date();
    const bountyDate = new Date(bounty.completionDate);

    const completionDateTime = new Date(bountyDate).setHours(0, 0, 0, 0);
    const currentDateTime = currentDate.setHours(0, 0, 0, 0);

    if (!user || !user.id) {
      toast.error("Please login to apply in bounty.");
      setDialogOpen(false);
      return;
    }
    if (isApplied) {
      toast.error("Already Applied");
      setDialogOpen(false);
      return;
    }

    if (completionDateTime >= currentDateTime) {
      setDialogOpen(true);
      return;
    }
    if (currentDate > bountyDate) {
      toast.error("Bounty has expired");
      setDialogOpen(false);
      return;
    } else {
      setDialogOpen(true);
    }
    return;
  };

  const [phone, setNumber] = useState("");
  const [linkedin, setLinkedInId] = useState("");
  const [approach, setApproach] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [linkedinError, setLinkedInError] = useState("");
  const [approachError, setApproachError] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [termsError, setTermsError] = useState("");

  const validateForm = () => {
    const phoneRegex = /^[0-9]{10}$/;
    const linkedinRegex =
      /^(https?:\/\/)?(www\.)?linkedin\.com\/in\/[a-zA-Z0-9_-]+(\/)?$/;

    if (!phoneRegex.test(phone)) {
      setPhoneError("Please enter a valid 10-digit phone number.");
      return false;
    } else {
      setPhoneError("");
    }

    if (!linkedinRegex.test(linkedin)) {
      setLinkedInError("Please enter a valid LinkedIn URL.");
      return false;
    } else {
      setLinkedInError("");
    }

    if (!approach.trim()) {
      setApproachError("Please provide your approach for the solution.");
      return false;
    } else {
      setApproachError("");
    }

    if (!termsAccepted) {
      setTermsError("You must accept the terms and conditions.");
      return false;
    } else {
      setTermsError("");
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    let uploadedFileId = null;
    setLoading(true);

    if (file) {
      const fileFormData = new FormData();
      fileFormData.append("file", file);

      try {
        const mediaRes = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files?depth=0`,
          {
            method: "POST",
            credentials: "include",
            body: fileFormData,
          }
        );

        const mediaData = await mediaRes.json();

        if (mediaRes.ok) {
          uploadedFileId = mediaData.doc.id; // Store only the file ID
        } else {
          throw new Error("File upload failed");
        }
      } catch (err) {
        toast.error("Failed to upload file.");
        return;
      }
    }

    // Create new applicant with only the file ID
    const updatedApplicant = {
      userId: user?.id,
      userName: user?.user_name,
      userImage: imageUrl ?? null,
      phone,
      linkedin,
      approach,
      relatedFile: uploadedFileId ? uploadedFileId : null,
    };

    try {
      // Fetch existing bounty applicants
      const { data: existingBounty } = await axios.get(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`
      );

      const updatedExistingApplicants = existingBounty.applicants?.map(
        (applicant) => ({
          ...applicant,
          relatedFile: applicant.relatedFile
            ? applicant.relatedFile.map((file) => file)
            : "",
        })
      );

      const newUpdatedApplicants = updatedExistingApplicants
        ? [...updatedExistingApplicants, updatedApplicant]
        : [updatedApplicant];

      try {
        await axios.patch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties/${bounty.id}?depth=0`,
          { applicants: newUpdatedApplicants }
        );
        toast.success("Applied Successfully!");
        setIsApplied(true);
        window.location.reload();
        // Optionally reload the data without reloading the page
      } catch (patchError) {
        console.error("Error updating bounty:", patchError);
        toast.error("Failed to Apply.");
      } finally {
        setLoading(false); // Enable button when API call completes
      }
    } catch (error) {
      toast.error("Failed to apply.");
    } finally {
      setLoading(false); // Enable button when API call completes
    }
  };

  const currentDate = new Date();
  const getDateOnly = (date: Date) =>
    new Date(date.getFullYear(), date.getMonth(), date.getDate());

  // Parse bounty dates
  const completionDate = new Date(bounty.completionDate as string);
  const applyExpireDate = new Date(bounty.applyExpireDate as string);

  // Get date-only for accurate comparisons
  const today = getDateOnly(currentDate);
  const applyExpireDay = getDateOnly(applyExpireDate);
  const completionDay = getDateOnly(completionDate);

  // Condition: Can still apply?
  const isApplyExpired = today > applyExpireDay;

  // Condition: Show "Completed" only AFTER completion date
  const isCompleted = bounty.status === "completed" && today > completionDay;

  // Condition: Expired based on completionDate
  const isExpired = today > completionDay;

  return (
    <>
      <div className="flex flex-wrap gap-2">
        {user &&
        user.id === bounty?.user.id &&
        bounty?.status !== "completed" ? (
          <div className="flex gap-1">
            <Link href={`/dashboard/create/bounty?Id=${bounty?.id}`}>
              <Button
                className={`flex bg-white ${bounty?.status === "pending" || bounty?.status === "denied" ? "px-2" : "px-4"} text-indigo-600 hover:bg-indigo-500 hover:text-white`}
              >
                Edit
              </Button>
            </Link>
            {(bounty?.status === "pending" || bounty?.status === "denied") && (
              <Dialog open={isDialogOpen} onOpenChange={setISDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-white px-2 text-indigo-600 hover:bg-indigo-500 hover:text-white">
                    Delete
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <div>
                    <div className="flex flex-col gap-2 mt-3 px-4">
                      {/* X Button aligned to top-right */}
                      <div className="flex justify-end">
                        <Button
                          onClick={() => setISDialogOpen(false)}
                          className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 p-1 h-auto"
                        >
                          <X size={15} />
                        </Button>
                      </div>

                      {/* Title */}
                      <h1 className="text-center font-bold py-2 text-lg md:text-2xl">
                        Are you sure want to delete bounty?
                      </h1>

                      {/* Buttons */}
                      <div className="flex justify-center gap-2 mt-3">
                        <Button
                          onClick={() => setISDialogOpen(false)}
                          variant="outline"
                          className="bg-transparent text-white px-4 py-1 hover:bg-indigo-800"
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={() => deleteBounty(bounty)}
                          disabled={loading}
                          variant="outline"
                          className="bg-white text-indigo-700 px-4 py-1 hover:bg-indigo-800"
                        >
                          {loading ? "Deleting..." : "Delete"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        ) : (
          <>
            {!isCompleted && !isExpired && !isApplyExpired ? (
              <div className="flex flex-row items-start">
                <Dialog open={dialogOpen}>
                  <DialogTrigger asChild>
                    <>
                      {/* <div className="relative flex items-center gap-2 w-full">
                    <Button
                      onClick={message}
                      variant="gradient"
                      className="relative bg-gradient-to-r from-amber-500 to-pink-500 font-bold px-2 py-1 sm:py-2 sm:px-3 rounded text-xs sm:text-sm text-white w-full"
                      disabled={loading || isApplied}
                    >
                      {isApplied ? (
                        <>
                          {bounty?.applicants?.map((applicant: any) => {
                            if (applicant.userId === user?.id) {
                              if (applicant.applicantStatus === "approved")
                                return (
                                  <span
                                    key={applicant.id}
                                    className="text-white"
                                  >
                                    Accepted
                                  </span>
                                );
                              if (applicant.applicantStatus === "rejected")
                                return (
                                  <span
                                    key={applicant.id}
                                    className="text-white"
                                  >
                                    Rejected
                                  </span>
                                );
                              return (
                                <span key={applicant.id} className="text-white">
                                  Pending
                                </span>
                              );
                            }
                          })}
                        </>
                      ) : (
                        "Apply"
                      )}
                    </Button>

                    {isApplied && (
                      <>
                        {bounty?.applicants?.map((applicant: any) => {
                          if (applicant.userId === user?.id) {
                            let color = "text-yellow-400";
                            let defaultMessage =
                              "Your application is pending review.";

                            if (applicant.applicantStatus === "approved") {
                              color = "text-green-400";
                              defaultMessage =
                                "Your application has been accepted!";
                            } else if (
                              applicant.applicantStatus === "rejected"
                            ) {
                              color = "text-red-400";
                              defaultMessage =
                                "Your application has been rejected.";
                            }

                            return (
                              <div
                                key={applicant.id}
                                className="relative inline-block group"
                              >
                                <span className={`${color} cursor-pointer`}>
                                  <Info size={16} />
                                </span>
                                <div className="absolute left-1/2 transform -translate-x-1/2 -bottom-2 translate-y-full z-50 hidden group-hover:block w-64 bg-white border border-gray-200 text-black text-sm p-3 rounded-lg shadow-lg">
                                  <div className="font-medium mb-1 flex items-center gap-2">
                                    {applicant.applicantStatus ===
                                      "approved" && (
                                      <>
                                        <CheckCircle
                                          className="text-green-500"
                                          size={16}
                                        />
                                        <span>Accepted</span>
                                      </>
                                    )}
                                    {applicant.applicantStatus ===
                                      "rejected" && (
                                      <>
                                        <XCircle
                                          className="text-red-500"
                                          size={16}
                                        />
                                        <span>Rejected</span>
                                      </>
                                    )}
                                    {applicant.applicantStatus ===
                                      "pending" && (
                                      <>
                                        <Clock
                                          className="text-yellow-500"
                                          size={16}
                                        />
                                        <span>Pending</span>
                                      </>
                                    )}
                                  </div>
                                  <p className="text-gray-700">
                                    {applicant.message || defaultMessage}
                                  </p>
                                  {applicant.rejectionReason &&
                                    applicant.applicantStatus ===
                                      "rejected" && (
                                      <div className="mt-2 p-2 bg-red-50 text-red-600 text-xs rounded">
                                        <strong>Reason:</strong>{" "}
                                        {applicant.rejectionReason}
                                      </div>
                                    )}
                                </div>
                              </div>
                            );
                          }
                        })}
                      </>
                    )}
                  </div> */}

                      {/* Tooltip message shown on button hover */}
                      <div className="relative flex items-center w-full group">
                        <Button
                          onClick={message}
                          variant="gradient"
                          className="relative bg-gradient-to-r from-amber-500 to-pink-500 font-bold px-2 py-1 sm:py-2 sm:px-3 rounded text-xs sm:text-sm text-white w-full"
                          disabled={loading || isApplied}
                        >
                          {isApplied ? (
                            <>
                              {bounty?.applicants?.map((applicant: any) => {
                                if (applicant.userId === user?.id) {
                                  if (applicant.applicantStatus === "approved")
                                    return (
                                      <span key={applicant.id}>Accepted</span>
                                    );
                                  if (applicant.applicantStatus === "rejected")
                                    return (
                                      <span key={applicant.id}>Rejected</span>
                                    );
                                  return (
                                    <span key={applicant.id}>Pending</span>
                                  );
                                }
                              })}
                            </>
                          ) : (
                            "Apply"
                          )}
                        </Button>

                        {/* Tooltip message shown on button hover */}
                        {isApplied &&
                          bounty?.applicants?.map((applicant: any) => {
                            if (applicant.userId !== user?.id) return null;

                            // Set icon, message and status
                            let icon = (
                              <Clock className="text-yellow-500" size={16} />
                            );
                            let label = "Pending";
                            let message =
                              applicant.message ||
                              "Your application is currently under review.";

                            if (applicant.applicantStatus === "approved") {
                              icon = (
                                <CheckCircle
                                  className="text-green-500"
                                  size={16}
                                />
                              );
                              label = "Accepted";
                              message = "Your application has been accepted!";
                            } else if (
                              applicant.applicantStatus === "rejected"
                            ) {
                              icon = (
                                <XCircle className="text-red-500" size={16} />
                              );
                              label = "Rejected";
                              message = "Your application has been rejected.";
                            }

                            return (
                              <div
                                key={applicant.id}
                                className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50 hidden group-hover:block w-72 bg-white border border-gray-200 text-black text-sm p-3 rounded-lg shadow-xl transition-all duration-200"
                              >
                                <div className="font-medium mb-2 flex items-center gap-2">
                                  {icon}
                                  <span>{label}</span>
                                </div>
                                <p className="text-gray-700 text-sm leading-snug">
                                  {message}
                                </p>

                                {applicant.applicantStatus === "rejected" &&
                                  applicant.rejectionReason && (
                                    <div className="mt-2 p-2 bg-red-50 text-red-600 text-xs rounded">
                                      <strong>Reason:</strong>{" "}
                                      {applicant.rejectionReason}
                                    </div>
                                  )}
                              </div>
                            );
                          })}
                      </div>
                    </>
                  </DialogTrigger>

                  {/* <Dialog open={dialogOpen}> */}
                  <DialogContent className="sm:max-w-[425px] text-center justify-between font-bold shadow-md">
                    <button
                      onClick={() => setDialogOpen(false)}
                      className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100"
                    >
                      <X size={15} />
                    </button>
                    <DialogHeader>
                      <DialogTitle className="text-center">
                        Enter details to apply
                      </DialogTitle>
                    </DialogHeader>

                    {/*  -----------------------------------------form -------------------------------------------- */}
                    <form
                      onSubmit={handleSubmit}
                      className="max-w-md mx-auto p-4 rounded-lg"
                    >
                      <div className="mb-4 flex flex-wrap">
                        <label
                          htmlFor="number"
                          className="block text-white font-normal mb-1"
                        >
                          Phone Number :{" "}
                          <sup className="text-red-500 font-bold">*</sup>
                        </label>
                        <input
                          type="text"
                          id="number"
                          value={phone}
                          onChange={(e) => setNumber(e.target.value)}
                          className="flex shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                        {phoneError && (
                          <p className="text-red-500 text-sm">{phoneError}</p>
                        )}
                      </div>

                      <div className="mb-4 flex flex-wrap">
                        <label
                          htmlFor="linkedInId"
                          className=" block text-white font-normal mb-1"
                        >
                          LinkedIn ID :{" "}
                          <sup className="text-red-500 font-bold">*</sup>
                        </label>
                        <input
                          type="text"
                          id="linkedInId"
                          value={linkedin}
                          onChange={(e) => setLinkedInId(e.target.value)}
                          className="flex shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                        {linkedinError && (
                          <p className="text-red-500 text-sm">
                            {linkedinError}
                          </p>
                        )}
                      </div>

                      <div className="mb-4 flex flex-wrap">
                        <label
                          htmlFor="approach"
                          className=" block text-white font-normal mb-1"
                        >
                          Approach for the solution :{" "}
                          <sup className="text-red-500 font-bold">*</sup>
                        </label>
                        <textarea
                          id="approach"
                          value={approach}
                          onChange={(e) => setApproach(e.target.value)}
                          className="flex shadow appearance-none resize-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                        {approachError && (
                          <p className="text-red-500 text-sm">
                            {approachError}
                          </p>
                        )}
                      </div>

                      <div className="mb-4 flex flex-wrap">
                        <label
                          htmlFor="relatedFile"
                          className="block text-white font-normal mb-1"
                        >
                          Related Files :
                          {/* <sup className="text-red-500 font-bold">*</sup> */}
                        </label>
                        <input
                          type="file"
                          id="relatedFile"
                          onChange={(e) => setFile(e.target.files?.[0] || null)}
                          className="border border-gray-200 px-1 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                        />
                        {fileError && (
                          <p className="text-red-500 text-sm">{fileError}</p>
                        )}
                      </div>

                      <div className="py-2">
                        <label
                          htmlFor="termsAccepted"
                          className="flex items-center gap-1 font-normal"
                        >
                          <input
                            type="checkbox"
                            id="termsAccepted"
                            name="termsAccepted"
                            // checked={formData.termsAccepted}
                            //onChange={handleChange}
                            // required
                            checked={termsAccepted}
                            onChange={(e) => setTermsAccepted(e.target.checked)}
                            className="h-4 w-4"
                          />
                          <Link
                            href={"/bountytandc"}
                            className=" hover:text-blue-500"
                            target="_blank"
                          >
                            Accept Terms and Conditions
                          </Link>
                        </label>
                        {termsError && (
                          <p className="text-red-500 text-sm text-start">
                            {termsError}
                          </p>
                        )}
                      </div>

                      <div className="items-center justify-between pt-4">
                        <button
                          type="submit"
                          className="bg-white text-indigo-900 rounded-l hover:bg-indigo-500 hover:text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-all duration-200"
                          disabled={loading}
                        >
                          {loading ? "Applying..." : "Apply"}
                        </button>
                      </div>
                    </form>
                  </DialogContent>
                </Dialog>
              </div>
            ) : (
              <div className="text-amber-400 font-medium">
                {isCompleted ? "Completed" : "Expired"}
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default ApplyBounty;
