"use client";
import React, { useState } from "react";
import { Newspaper, FolderSearch, FileSearch, BookOpenIcon , TrendingUp } from "lucide-react";
import { FaChartLine } from "react-icons/fa";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

const BlogSidebar = ({ className, onFilterChange = () => {} }: { className?: string, onFilterChange: (tag: string) => void }) => {
  const pathname = usePathname();
  const [selectedTags, setSelectedTags] = useState<string>("");

  const handleFilter = (tag: string) => {
    setSelectedTags(tag);
    onFilterChange(tag); 
  };

  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedMenu, setSelectedMenu] = useState("all");

  const handleSelectChange = (value: string) => {
    setSelectedMenu(value);
    handleFilter(value === "all" ? "" : value); 
  };

  const menus = [
    { name: "All Blogs", id: 1, icon: Newspaper },
    { name: "casestudy", id: 3, icon: FileSearch },
    { name: "development", id: 2, icon: TrendingUp },
    { name: "learning", id: 4, icon: BookOpenIcon },
    { name: "research", id: 2, icon: FolderSearch },
  ];

  // const handleSearch = (query: string) => {
  //   setSearchQuery(query.toLowerCase());
  // };

  return (
    <div className={cn(`pb-8 md:mr-4 ${className} ${pathname === "/blog" ? "block" : "hidden lg:block"}`)}>
      {/* mobile */}
      <div className="md:hidden flex justify-center gap-2">
        <div className="relative w-fit">
          <button
            className="bg-transparent border border-white text-white py-2 rounded w-20 text-sm"
            onClick={() => setSelectedMenu((prev) => (prev === "open" ? "all" : "open"))}
          >
            {menus.find((menu) => menu.name === selectedMenu)?.name || "Select"}
          </button>
          {selectedMenu === "open" && (
            <div className="absolute right-0 bg-indigo-900 mt-2 rounded w-32 z-10">
              {menus.map((menu, i) => (
                <div
                  key={menu.id}
                  onClick={() => handleSelectChange(menu.name)}
                  className="flex items-center p-2 hover:bg-indigo-700 cursor-pointer rounded "
                >
                  <div>{React.createElement(menu.icon, { size: "20", className: "mr-2" })}</div>
                  <span className="first-letter:uppercase">{menu.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {/* laptops */}
      <div className="hidden md:flex flex-col gap-4 relative">
        <h2 className="text-lg font-bold text-indigo-200 mx-4 mt-4">Read Blogs</h2>
        {menus.map((menu, i) => (
          <button
            key={menu.id}
            onClick={() => handleFilter(menu.name)}
            className={`mt-2 group flex items-center text-lg gap-3.5 p-2 hover:bg-indigo-900 rounded-md border-b border-cyan-500 shadow-lg shadow-indigo-900 ${
              selectedTags === menu.name ? "bg-indigo-900 text-white" : ""
            }`}
          >
            <div>{menu.icon && React.createElement(menu.icon, { size: "20" })}</div>
            <h3 style={{ transitionDelay: `${i + 3}00ms` }} className="whitespace-pre duration-500 first-letter:uppercase">
              {menu.name}
            </h3>
          </button>
        ))}
      </div>
    </div>
  );
};

export default BlogSidebar;
