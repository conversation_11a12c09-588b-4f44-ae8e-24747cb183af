"use client";

import { Query<PERSON><PERSON>da<PERSON>, TQueryValidator } from "@/lib/validators/query-validator";
import { Product , User} from "@/server/payload-types";
import Link from "next/link";
import ProductListing from "./ProductListing";
import { useEffect, useRef, useState } from "react";
import axios from "axios";

import { z } from 'zod';
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ProductReelProps {
  title: string
  subtitle?: string
  href?: string
  filters?: { [key: string]: string[] };
  query: TQueryValidator;
  user: User
}

const FALLBACK_LIMIT = 4;

const ProductReel = (props: ProductReelProps) => {
  const { title, subtitle, href, query, filters, user } = props;
  // const [user, setUser] = useState();
  const [data, setData] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef(null);

  // const handleItemClick = (data) => {
  //   setSelectedItem(data); // Set the selected item
  // };
  const handleScroll = (direction) => {
    if (scrollRef.current) {
      const scrollAmount = 300; // Adjust scroll amount as needed
      if (direction === "left") {
        scrollRef.current.scrollBy({ left: -scrollAmount, behavior: "smooth" });
      } else if (direction === "right") {
        scrollRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
      }
    }
  };



  useEffect(() => {
    setIsLoading(true);
    const fetchProducts = async () => {
      try {
        const result = await axios.post(`/api2/products`, {
          query: {
            ...query,
            filters: {
              ...filters,
            }
          }, 
          limit: query.limit ?? FALLBACK_LIMIT,
        });
        setData(result.data);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, [query, filters]);

  const products = data ? data?.items : [];

  let map: (Product | null)[] = [];
// console.log("map=map",map);
// console.log("productsmap",products);



  if (products && products.length) {
    map = products;
  } else if (isLoading) {
    map = new Array<null>(query.limit ?? FALLBACK_LIMIT).fill(null);
  }

  if (!isLoading && products.length === 0) {
    return (
      <div className="text-center text-white py-6">
        No items available, try another filter.
      </div>
    );
  }
  return (
    <section className="md:py-14 py-10">
      <div className="flex mb-4 justify-between items-center">
        <div className="max-w-2xl lg:max-w-4xl lg:px-0">
          {title ? (
            <h3 className="text-3xl md:text-2xl font-bold sm:text-3xl">{title}</h3>
          ) : null}
          {subtitle ? (
            <p className="mt-2 text-sm text-muted-foreground">{subtitle}</p>
          ) : null}
        </div>

        {href ? (
          <Link
          href="/marketplace?displayProducts=true"
            className="bg-white/20  hover:bg-white/40 text-white font-semibold py-1 px-2 md:px-3 rounded-full flex items-center gap-1 min-w-fit text-sm md:text-md"
          >
            View All
            <span className="mb-[2px]">&rarr;</span>
          </Link>
        ) : null}
      </div>

      {/* <div className="relative">
        <div className="mt-6 flex items-center w-full">
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-10 sm:gap-x-6 md:grid-cols-3 lg:grid-cols-4 md:gap-y-10 lg:gap-x-8">
            {map.map((product, i) => (
              <ProductListing
                key={`product-${i}`}
                product={product}
                index={i}
                user={user}
              />
            ))}
          </div>
        </div>
      </div> */}



<div className="relative border-t border-muted-foreground">
        <div className="mt-2 flex items-center w-full">
          {/* Scrollable Area */}
          <div
            className="grid grid-flow-col mt-2 px-1 gap-x-3 md:gap-x-4 w-full overflow-x-auto py-2"
            id="xscroll"
            ref={scrollRef}
          >
            {map.map((product, i) => (
               <div
               key={`rapp-${i}`}
               className=" rounded-lg shadow-lg w-[300px] cursor-pointer"
              //  onClick={() => handleItemClick(rapp)}
             >
              <ProductListing
                key={`product-${i}`}
                product={product}
                index={i}
                user={user}
              />
              </div>

            ))}
          </div>

          {/* Left and Right Buttons */}
          <div className="absolute -bottom-12 right-2 flex gap-2 z-10">
            {/* Left Button */}
            <button
              onClick={() => handleScroll("left")}
              className="p-2 rounded-full shadow-md bg-white text-indigo-600 hover:opacity-90"
            >
              <ChevronLeft size={24} />
            </button>

            {/* Right Button */}
            <button
              onClick={() => handleScroll("right")}
              className="p-2 rounded-full shadow-md bg-white text-indigo-600 hover:opacity-90"
            >
              <ChevronRight size={24} />
            </button>
          </div>
        </div>

        {/* Selected Item Display */}
        {/* {selectedItem && (
          <div className="mt-4 p-4 bg-gray-100 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold">Selected Item</h3>
            <p>{JSON.stringify(selectedItem)}</p>
          </div>
        )} */}
      </div>


    </section>
  );
};

export default ProductReel;
