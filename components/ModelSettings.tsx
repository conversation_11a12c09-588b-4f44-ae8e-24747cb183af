// export default ModelSettings;
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Settings } from "lucide-react"; // Assuming you have this component
import DynamicFormField from "./ui/DynamicFormField";
import { Each } from "./each";
import RangeSliderInput from "./ui/RangeSliderInput";
import { Checkbox } from "./ui/checkbox";
import { Label } from "./ui/label";
import { Input } from "./ui/input";

interface Model {
  id: string;
  name: string;
  description: string;
  modelType: string;
  cost: number;
  commission: number;
  provider: string;
  examples: { example: string; id: string }[];
  settings: any;
}

interface ModelSettingsProps {
  model: Model;
  settings: Record<string, string | number>;
  onSettingsChange: (updatedSettings: Record<string, string | number>) => void;
  showAdvancedSettings: boolean;
}

interface FieldConfig {
  type: "select" | "range" | "text" | "checkbox";
  options?: { value: string; label: string }[];
  min?: number;
  max?: number;
}

const ModelSettings: React.FC<ModelSettingsProps> = ({
  settings,
  model,
  onSettingsChange,
  showAdvancedSettings,
}) => {
  const [localSettings, setLocalSettings] =
    useState<Record<string, string | number>>(settings);
  // const [selectedOptions, setSelectedOptions] = useState<string | null>(null);
  const [selectedOptions, setSelectedOptions] = useState<{
    [key: string]: any;
  }>({});

  useEffect(() => {
    if (!model) return;

    const filterRelevantSettings = (
      allSettings: Record<string, any> = {},
      allowedKeys?: string[]
    ) => {
      if (!allowedKeys) return {};
      return Object.fromEntries(
        Object.entries(allSettings)
          .flatMap(([key, value]) =>
            typeof value === "object" && value !== null
              ? Object.entries(value).filter(([innerKey]) =>
                  allowedKeys.includes(innerKey)
                )
              : [[key, value]]
          )
          .filter(([key]) => allowedKeys.includes(key))
      );
    };

    const relevantSettings = filterRelevantSettings(
      model.modelType === "audio"
        ? model.settings.ModelAudioFalSettings
        : model.settings.ModelImageReplicateSettings,
      model.modelType === "audio"
        ? model.settings.falaudiosettings
        : model.settings.replicateimagesettings
    );

    setLocalSettings(relevantSettings);
  }, [model]);

  const handleFieldChange = (name?: string, value?: any, type?: string) => {
    let updatedSettings = { ...localSettings };

    // if (type === "select" && name) {
    //   // For select/tag options, toggle selection
    //   const updatedSelection = selectedOptions === value ? null : value;
    //   console.log("updatedSelection insdie",updatedSelection)
    //   setSelectedOptions(updatedSelection); // Update selected option
    //   updatedSettings = { ...updatedSettings, [name]: updatedSelection }; // Add to settings
    // } else {
    //   // For other types (string, integer, float, boolean)
    //   updatedSettings = { ...updatedSettings, [name]: value };
    // }
    if (type === "select") {
      const currentValue = selectedOptions[name];
      const updatedSelection = currentValue === value ? null : value;
  // console.log("updatedSelection",updatedSelection)
  // console.log("currentValue",currentValue)

      // Update the selectedOptions map
      const updatedSelectedOptions = {
        ...selectedOptions,
        [name]: updatedSelection,
      };
  // console.log("updatedSelectedOptions",updatedSelectedOptions)
      setSelectedOptions(updatedSelectedOptions);
  
      // Update model/local settings too
      updatedSettings[name] = updatedSelection;
    } else {
      // Handle other types (string, number, etc.)
      updatedSettings[name] = value;
    }
    setLocalSettings(updatedSettings);
    onSettingsChange(updatedSettings);
  };

  const generateFieldConfig = (setting) => {
    switch (setting.type) {
      case "integer":
        return {
          type: "range",
          min: setting.min || 0, // If min exists, use it; otherwise, default to 0
          max: setting.max || 100, // If max exists, use it; otherwise, default to 100
        };
      case "string":
        return {
          type: "text",
        };
      case "boolean":
        return {
          type: "checkbox",
        };
      case "select":
        return {
          type: "select",
          options: setting.options || [], // If options exist, use them
        };
      default:
        return {
          type: "text", // Fallback type
        };
    }
  };

  const renderSettingInput = (setting: any) => {
    const { name, description, type, options, allowMultiple } = setting;

    switch (type) {
      case "integer":
      case "float":
        return (
          <div className="mb-4 w-full" key={name}>
            <RangeSliderInput
              key={name}
              setting={setting}
              value={model.settings[name]}
              onChange={handleFieldChange}
              // onChange={(value) => handleFieldChange(name, value)}
            />
          </div>
        );
      case "boolean":
        return (
          <div className="mb-4 flex items-center gap-2" key={name}>
            <Checkbox
              className="h-6 w-6"
              checked={model.settings[name] || false}
              // onCheckedChange={(checked) =>
              // setFormData((prev) => ({
              //   ...prev,
              //   settings: {
              //     ...prev.settings,
              //     [name]: checked,
              //   },
              // }))
              // }
              onCheckedChange={() => handleFieldChange()}
            />
            <div className="flex flex-col items-start">
              <Label className="text-lg">{name}</Label>
              {description && (
                <p className="italic text-sm text-yellow-400/[0.6] break-words whitespace-normal">
                  {description || "Click on an option to select it."}
                </p>
              )}
            </div>
          </div>
        );
      case "select":
        return (
          <div className="mb-4 w-full" key={name}>
            <div className="flex flex-col items-start">
              <Label className="text-lg">{name}</Label>
              {description && (
                <p className="italic text-sm text-yellow-400/[0.6] break-words whitespace-normal">
                  {description || "Click on an option to select it."}
                </p>
              )}
            </div>

            <div className="flex flex-wrap gap-2 mt-2">
              {options?.map((opt, index) => (
                <div
                  key={opt.id || index}
                  onClick={() => handleFieldChange(name, opt, "select")}
                  className={`cursor-pointer border rounded-lg px-3 py-2 bg-gradient-to-br from-black/[0.3] via-black/[0.1] to-black/[0.4] break-normal whitespace-normal break-all ${
                    selectedOptions[name] === opt
                      ? "bg-indigo-900 border-muted-foreground border-opacity-40 text-white"
                      : "bg-indigo-700"
                  }`}
                >
                  {opt}
                </div>
              ))}
            </div>
          </div>
        );

      case "string":
      default:
        return (
          <div className="mb-4 w-full" key={name}>
            <div className="flex flex-col items-start">
              <Label className="text-lg">{name}</Label>

              {description && (
                <p className="italic text-sm text-yellow-400/[0.6] break-words whitespace-normal">
                  {description || "Click on an option to select it."}
                </p>
              )}
            </div>
            <Input
              type="text"
              name={name}
              value={localSettings[name] || ""}
              // onChange={handleFieldChange}
              onChange={(e) => handleFieldChange(name, e.target.value)}
            />
          </div>
        );
    }
  };

  const fieldConfigs: Record<string, FieldConfig> =
    model.modelType === "audio"
      ? Object.fromEntries(
          model.settings.map((setting) => [
            setting.name,
            generateFieldConfig(setting),
          ])
        )
      : {
          output_format: {
            type: "select",
            options: [
              { value: "jpg", label: "JPG" },
              { value: "png", label: "PNG" },
              { value: "webp", label: "WEBP" },
            ],
          },
          aspect_ratio: {
            type: "select",
            options: [
              { value: "1_1", label: "1:1" },
              { value: "16_9", label: "16:9" },
              { value: "21_9", label: "21:9" },
              { value: "4_3", label: "4:3" },
            ],
          },
        };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="border border-indigo-500 rounded-xl p-8 shadow-xl bg-gradient-to-br from-indigo-900 to-indigo-700 mt-8 mb-4"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Settings className="text-yellow-300 w-8 h-8" />
          <h3 className="text-3xl font-bold text-yellow-300">
            Advanced Settings
          </h3>
        </div>
      </div>

      {/* Dynamic Form Fields */}
      {model.modelType && (
        <div className="grid grid-cols-1 gap-6">
          {/* {model.settings?.map((setting, i) => (
            <motion.div
              key={i}
              className="p-4 bg-indigo-800 rounded-xl shadow-lg"
            >
              <label className="block text-lg font-semibold text-white mb-2">
                {setting.replace(/_/g, " ")}:
              </label>
              <DynamicFormField
                setting={setting}
                value={localSettings[setting] || ""}
                config={fieldConfigs[setting] || { type: "text" }}
                onChange={handleFieldChange}
              />
            </motion.div>
          ))} */}
          {/* <Each
            of={model.settings || []} // ✅ Directly iterate over the array
            render={(setting, i) => ( 
              <motion.div
                key={i}
                className="p-4 bg-indigo-800 rounded-xl shadow-lg"
              >
                <label className="block text-lg font-semibold text-white mb-2">
                 
                  {setting.name}:
                </label>
                <DynamicFormField
                  setting={setting}
                  value={localSettings[setting] || ""}
                  config={setting || { type: "text" }}
                  onChange={handleFieldChange}
                />
              
              </motion.div>
            )}
          /> */}

          {model.settings?.map((setting, i) => {
            // Find matching config from fieldConfigs
            const matchingConfig = fieldConfigs[setting.name] || {
              type: "text",
            };

            return (
              <motion.div
                key={i}
                className="p-4 bg-indigo-800 rounded-xl shadow-lg"
              >
                {/* <label className="block text-lg font-semibold text-white mb-2">
                  {setting.name.replace(/_/g, " ")}:
                </label> */}
                <div key={setting.name}>{renderSettingInput(setting)}</div>
                {/* <DynamicFormField
                  setting={setting.name}
                  value={localSettings[setting.name] || ""}
                  config={matchingConfig} // Pass the correct config dynamically
                  onChange={handleFieldChange}
                /> */}
              </motion.div>
            );
          })}
        </div>
      )}
    </motion.div>
  );
};

export default ModelSettings;
