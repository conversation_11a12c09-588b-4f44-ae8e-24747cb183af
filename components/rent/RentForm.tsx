"use client";

import React, { useEffect, useRef, useState } from "react";
import axios from "axios";
import { toast } from "sonner";
import ReactMarkdown from "react-markdown";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import { Copy, Download, Pencil, Zap } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

import { User } from "@/server/payload-types";
import RentModal from "./RentModal";
import Link from "next/link";
import { jsPDF } from "jspdf";
import html2pdf from "html2pdf.js";
import showdown from "showdown";
import { saveAs } from "file-saver";
// import { Document, Packer, Paragraph, TextRun } from "docx";
import { GrDocumentPdf } from "react-icons/gr";
import { SpaceModal } from "@/components/SpaceModal";
import { FaRegFileWord } from "react-icons/fa";
import ImageWithAspectRatio from "@/components/ImageWithAspectRatio";
// import { Textarea } from "../ui/textarea";
import { UpdatedTextArea } from "../ui/updatedTextArea";

interface RentFormProps {
  rapp: any;
  user: User;
}

const RentForm = ({ rapp, user }: RentFormProps) => {
  const [output, setOutput] = useState<string | undefined>();
  const [data, setData] = useState<string | undefined>();
  const [spinLoading, setSpinLoading] = useState<boolean>(false);
  // const [imageFile, setImageFile] = useState<String | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [audioKey, setAudioKey] = useState(0);
  const [videoOutput, setVideoOutput] = useState("");
  const [audioFile, setAudioFile] = useState<string | null>(null);
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const [selectedOptions, setSelectedOptions] = useState<
    Record<string, string | string[]>
  >({});
  const [modelSettings, setModelSettings] = useState<
    Record<string, number | string>
  >({});

  const rappsPurchasesCount = rapp?.purchases?.length;

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageFile(e.target.files?.[0] || null);
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const ImageUrl = reader.result as string;
        setBase64Image(ImageUrl);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleOnSubmit = async (e: any) => {
    e.preventDefault();
    if (!user) {
      toast.error("Please login to use this app");
      return;
    }
    if (user.coinBalance <= rapp.totalCost) {
      toast.error("Insufficient Credits to Run this app");
      return;
    }
    try {
      setSpinLoading(true);
      toast.loading("Running...");

      const formData = new FormData(e.target);
      // Add selected options to FormData
      // Convert selected options to a comma-separated string
      Object.entries(selectedOptions).forEach(([name, option]) => {
        if (Array.isArray(option)) {
          formData.append(name, option.join(", ")); // Convert array to comma-separated string
        } else {
          formData.append(name, option); // Single value remains the same
        }
      });

      const formObject: Record<string, any> = Object.fromEntries(
        formData.entries()
      );

      const { data: runResponse } = await axios.post(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/run/${rapp.id}`,
        {
          obj: formObject,
          url: base64Image,
        }
      );
      if (runResponse.success === false) {
        toast.error(runResponse.message);
        return;
      }

      //audio generation model    
      const { output } = runResponse;
      if (output.error) {
        toast.error("Currently Model are not available, Please try after some time.");
        const emailResponse = await fetch("/api/model-error-email-sent", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ errorMessage: output.error }),
        });
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rappsPurchases/purchaseRapps`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            RappsId: rapp.id,
          }),
        }
      );

      const result = await response.json();
      toast.dismiss();

      if (response.ok) {
        toast.success("App Run Successful and Credits have been deducted.");
      } else {
        toast.error(result.message);
      }

      if (rapp.modelType === "text" || rapp.modelType === "vision") {
        if (rapp?.modelName.toLowerCase() === 'deepseek-r1') {
          let output = runResponse.output?.result || runResponse.output[0];
          output = output.replace(/<\/?think>/g, '');
          output = output.trim();
          setOutput(output);
        }
        else if (rapp?.modelName.toLowerCase() === 'meta/llama-4-maverick-instruct') {
          const textOutput = runResponse.output
            ? runResponse.output.filter(Boolean).join(' ')
            : 'No response from model';
          setOutput(textOutput);
        }
        else {
          setOutput(runResponse.output?.result || runResponse.output[0])
        }
        // setOutput(runResponse.output.result || runResponse.output[0]);
        setData(runResponse.output);
      } else if (rapp.modelType === "audio") {
        const newAudioFile = runResponse?.output;
        setAudioFile(newAudioFile);
        setAudioKey((prevKey) => prevKey + 1);
        setOutput(runResponse.output)
      } else if (rapp.modelType === "video") {
        // setVideoOutput(runResponse?.output?.video?.url);
        setVideoOutput(runResponse?.output?.videoUrl);
        setOutput(runResponse.output)

      }

      else {
        // const imageUrl = runResponse.output?.imageUrl || runResponse.output.result || runResponse.output[0] || runResponse.output;
        const imageUrl = runResponse.output?.imageUrl || runResponse.output.result || runResponse.output;
        const responseimage = await fetch(imageUrl);
        const contentType = responseimage.headers.get("content-type");

        const blob = await responseimage.blob();
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64Image = reader.result as string;
          setOutput(base64Image);
        };
        reader.readAsDataURL(blob);
        setOutput(runResponse.output || runResponse.output.result || runResponse.output[0]);
        setData(runResponse.output);
      }


    } catch (error) {
      toast.dismiss();
      console.log("Error caught in catch block:", error); // Check if it enters here

      if (error.response) {
        toast.error(error.response.data.message);
      } else {
        toast.error("An error occurred. Please try again.");
      }
    } finally {
      toast.dismiss();
      setSpinLoading(false);
    }
  };



  const handleCopy = () => {
    setSpinLoading(true);
    if (output) {
      // Strip basic markdown symbols like ##, **, *, etc.
      const plainText = output
        .replace(/[#*_`>-]/g, "") // remove markdown symbols
        .replace(/\n{2,}/g, "\n"); // collapse extra line breaks
  
      navigator.clipboard.writeText(plainText).then(
        () => {
          toast.success("Output copied to clipboard");
          setSpinLoading(false);
        },
        (err) => {
          toast.error("Failed to copy output");
          console.error("Could not copy text: ", err);
          setSpinLoading(false);
        }
      );
    }
  };
  
  const [text, setText] = useState("");
  const [showOptions, setShowOptions] = useState(false);



  const handleDownloadPdf = () => {
    if (!output) return;

    const converter = new showdown.Converter();
    const html = converter.makeHtml(output);

    const container = document.createElement("div");

    // Create <style> tag for list numbering and append to HEAD of the temporary HTML
    const style = document.createElement("style");
    style.textContent = `
      ol {
        list-style-type: decimal;
        margin-left: 20px;
      }
      ul {
        list-style-type: disc;
        margin-left: 20px;
      }
      li {
        margin-bottom: 5px;
      }
    `;

    const styledHtml = `
      <div style="
        color: black;
        background-color: white;
         padding: 10px 20px;
        max-width: 800px;
        font-family: Arial, sans-serif;
        line-height: 1.6;
      ">
        ${html}
      </div>
    `;

    container.innerHTML = styledHtml;
    container.style.position =  "absolute";
    container.style.top = "-9999px";
    document.body.appendChild(container);
    container.appendChild(style); // append style after content is there

    setTimeout(() => {
      const contentToPrint = container.firstElementChild;

      if (!contentToPrint) {
        toast.error("Failed to render content for PDF");
        document.body.removeChild(container);
        return;
      }

      html2pdf()
        .set({
          margin: [15, 10, 15, 10],
          filename: 'output.pdf',
          image: { type: 'jpeg', quality: 1 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            backgroundColor: "#ffffff"
          },
          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        })
        .from(contentToPrint)
        .save()
        .then(() => {
          document.body.removeChild(container);
          setShowOptions(false);
        });
    }, 300);
  };

 

  const handleDownloadWord = () => {

    const converter = new showdown.Converter();
    const html = converter.makeHtml(output); // output is your Markdown string

    const header = `
      <html xmlns:o='urn:schemas-microsoft-com:office:office' 
            xmlns:w='urn:schemas-microsoft-com:office:word' 
            xmlns='http://www.w3.org/TR/REC-html40'>
      <head><meta charset='utf-8'></head><body>`;
    const footer = `</body></html>`;

    const sourceHTML = header + html + footer;

    const blob = new Blob([sourceHTML], {
      type: "application/msword;charset=utf-8",
    });
    saveAs(blob, "output.doc");
    setShowOptions(false);
  };

  const handleSelectOption = (
    allowMultiple: boolean,
    variableName: string,
    option: string
  ) => {
    setSelectedOptions((prev) => {
      const currentSelection = prev[variableName] || [];

      if (allowMultiple === true) {
        // Toggle the option in the array
        const selectionArray = Array.isArray(currentSelection)
          ? currentSelection
          : [];

        const newSelection = selectionArray.includes(option)
          ? selectionArray.filter((item) => item !== option) // Remove if already selected
          : [...selectionArray, option]; // Add if not selected

        return { ...prev, [variableName]: newSelection };
      } else {
        return { ...prev, [variableName]: option }; // Single select case
      }
    });
  };

  const optionsRef = useRef(null);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (optionsRef.current && !optionsRef.current.contains(event.target)) {
        setShowOptions(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDownload = async () => {
    setSpinLoading(true);
    if (output) {
      try {
        const response = await axios.get(output, { responseType: "blob" });
        const blob = new Blob([response.data], {
          type: response.headers["content-type"] || "image/png",
        });

        const url = window.URL.createObjectURL(blob);

        const link = document.createElement("a");

        const format = modelSettings.output_format || "png";
        link.href = url;
        link.download = `output.${format}`;
        document.body.appendChild(link);
        link.click();
        setSpinLoading(false);
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        toast.error("Download failed: ", error);
        setSpinLoading(false);
      }
    } else {
      toast.error("No output URL provided for download.");
    }
  };

  const handleImageClick = () => {
    setIsModalOpen(true);
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    e.target.style.height = "auto"; // Reset height to auto to calculate new height
    e.target.style.height = `${e.target.scrollHeight}px`; // Set the height to the scroll height
  };

  function closeModal(): void {
    setIsModalOpen(false);
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-2 px-4">
        <h2 className="text-xl font-bold">Try this App </h2>
        {user && rapp.creatorId === user?.id && (
          <a
            href={`https://rentprompts.ai/dashboard/projects`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Pencil size={23} />
          </a>
        )}
      </div>
      <form className="flex flex-col" onSubmit={handleOnSubmit}>
        <div className="px-4">
          {rapp ? (
            <div>
              {/* System Variables */}
              <h2 className="text-xl font-semibold mb-3">Input Variables</h2>

              {rapp.systemVariables?.length > 0 && (
                <>
                  {rapp.systemVariables.map((variable, index) => (
                    <div key={index} className="mb-4">
                      <div className="flex gap-1 items-center">
                        <Label className="block font-semibold text-lg mb-1 break-words whitespace-normal truncate">
                          {variable.displayName}
                        </Label>
                      </div>

                      {variable.type === "string" && (
                        <>
                          <UpdatedTextArea
                            name={variable.name}
                            className="w-full p-2 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70"
                            placeholder={
                              variable.placeholder ||
                              `Enter ${variable.displayName}`
                            }
                            required={true}
                            rows={1}
                          />
                          <p className="text-sm text-slate-300">{variable.description}</p>

                        </>
                      )}

                      {variable.type === "number" && (
                        <>
                          <Input
                            type="number"
                            name={variable.name}
                            className="w-full p-2 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70"
                            placeholder={
                              variable.placeholder ||
                              `Enter ${variable.displayName}`
                            }
                            required={true}
                          />
                          <p className="text-sm text-slate-300">{variable.description}</p>

                        </>
                      )}
                      {variable.type === "select" && (
                        <div className="flex flex-col w-full">
                          <div className="flex flex-wrap gap-2">
                            {variable.options?.map((option, index) => (
                              <div
                                key={index}
                                onClick={() =>
                                  handleSelectOption(
                                    variable.allowMultiple,
                                    variable.name,
                                    option
                                  )
                                }
                                // onClick={() => handleSelectOption("system", variable, option)}
                                className={`cursor-pointer border rounded-lg px-3 py-2 bg-gradient-to-br from-black/[0.3] via-black/[0.1] to-black/[0.4] ${Array.isArray(selectedOptions[variable.name])
                                  ? selectedOptions[variable.name].includes(
                                    option
                                  ) // Check for multiple selections
                                    ? "bg-indigo-900 border-muted-foreground text-white"
                                    : "bg-indigo-700"
                                  : selectedOptions[variable.name] === option // Single selection case
                                    ? "bg-indigo-900 border-muted-foreground text-white"
                                    : "bg-indigo-700"
                                  }`}
                              >
                                {option}
                              </div>
                            ))}
                          </div>
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}

              {/* User Variables */}
              {rapp.userVariables?.length > 0 && (
                <>
                  {rapp.userVariables.map((variable, index) => (
                    <div key={index} className="mb-4">
                      <div className="flex gap-1 items-center">
                        <Label className="block font-semibold text-lg mb-1 break-words whitespace-normal truncate">
                          {variable.displayName}
                        </Label>
                      </div>

                      {variable.type === "string" && (
                        <>
                          <UpdatedTextArea
                            name={variable.name}
                            className="w-full p-2 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70"
                            placeholder={
                              variable.placeholder ||
                              `Enter ${variable.displayName}`
                            }
                            required={true}
                            rows={1}
                          />
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </>
                      )}

                      {variable.type === "number" && (
                        <>
                          <Input
                            type="number"
                            name={variable.name}
                            className="w-full p-2 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70"
                            placeholder={
                              variable.placeholder ||
                              `Enter ${variable.displayName}`
                            }
                            required={true}
                          />
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </>
                      )}

                      {variable.type === "select" && (
                        <div className="flex flex-col w-full">
                          <div className="flex flex-wrap gap-2">
                            {variable.options?.map((option, index) => (
                              <div
                                key={index}
                                onClick={() =>
                                  handleSelectOption(
                                    variable.allowMultiple,
                                    variable.name,
                                    option
                                  )
                                }
                                // onClick={() => handleSelectOption("system", variable, option)}
                                className={`cursor-pointer border rounded-lg px-3 py-2 bg-gradient-to-br from-black/[0.3] via-black/[0.1] to-black/[0.4] ${Array.isArray(selectedOptions[variable.name])
                                  ? selectedOptions[variable.name].includes(
                                    option
                                  ) // Check for multiple selections
                                    ? "bg-indigo-900 border-muted-foreground text-white"
                                    : "bg-indigo-700"
                                  : selectedOptions[variable.name] === option // Single selection case
                                    ? "bg-indigo-900 border-muted-foreground text-white"
                                    : "bg-indigo-700"
                                  }`}
                              >
                                {option}
                              </div>
                            ))}
                          </div>
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}

              {/* Negative Variables */}
              {rapp.negativeVariables?.length > 0 && (
                <>
                  {rapp.negativeVariables.map((variable, index) => (
                    <div key={index} className="mb-4">
                      <div className="flex gap-1 items-center">
                        <Label className="block font-semibold text-lg mb-1 break-words whitespace-normal truncate">
                          {variable.displayName}
                        </Label>
                      </div>

                      {variable.type === "string" && (
                        <>
                          <UpdatedTextArea
                            name={variable.name}
                            className="w-full p-2 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70"
                            placeholder={
                              variable.placeholder ||
                              `Enter ${variable.displayName}`
                            }
                            required={true}
                            rows={1}
                          />
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </>
                      )}

                      {variable.type === "number" && (
                        <>
                          <Input
                            type="number"
                            name={variable.name}
                            className="w-full p-2 bg-dash border border-slate-300 rounded-lg text-lg placeholder:text-base placeholder:opacity-70"
                            placeholder={
                              variable.placeholder ||
                              `Enter ${variable.displayName}`
                            }
                            required={true}
                          />
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </>
                      )}

                      {variable.type === "select" && (
                        <div className="flex flex-col w-full">
                          <div className="flex flex-wrap gap-2">
                            {variable.options?.map((option, index) => (
                              <div
                                key={index}
                                onClick={() =>
                                  handleSelectOption(
                                    variable.allowMultiple,
                                    variable.name,
                                    option
                                  )
                                }
                                // onClick={() => handleSelectOption("system", variable, option)}
                                className={`cursor-pointer border rounded-lg px-3 py-2 bg-gradient-to-br from-black/[0.3] via-black/[0.1] to-black/[0.4] ${Array.isArray(selectedOptions[variable.name])
                                  ? selectedOptions[variable.name].includes(
                                    option
                                  ) // Check for multiple selections
                                    ? "bg-indigo-900 border-muted-foreground text-white"
                                    : "bg-indigo-700"
                                  : selectedOptions[variable.name] === option // Single selection case
                                    ? "bg-indigo-900 border-muted-foreground text-white"
                                    : "bg-indigo-700"
                                  }`}
                              >
                                {option}
                              </div>
                            ))}
                          </div>
                          <p className="text-sm text-slate-300">{variable.description}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}
            </div>
          ) : (
            <p className="text-gray-300">Loading...</p>
          )}
        </div>

        {rapp.imageinput ? (
          <div className="mb-4 px-4">
            <Label className="block text-lg font-medium ml-1 mb-1">
              {"Image"}:
            </Label>

            <Input
              type="file"
              required={true}
              name={"image"}
              accept="image/*"
              onChange={handleImageFileChange}
              className="w-full p-2 bg-dash rounded-lg text-lg"
              placeholder="Add image"
            />
          </div>
        ) : null}
        <div className="w-full text-center px-4">
          <button
            type="submit"
            className="bg-gradient-to-r from-amber-500 to-pink-500 hover:bg-gradient-to-4 hover:from-amber-600 hover:to-pink-600 hover:bg-purple-700
             text-white font-bold px-10 py-4 mb-2 rounded w-full mt-4 focus:ring-offset-2 text-center transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20"
          >
            {spinLoading ? <div>Generating...</div> :
              // <div>Generate   <Zap className="fill-yellow-400" /></div>
              <div className="flex items-center justify-center text-center space-x-2">
                <span>Generate</span>
                <Zap className="fill-yellow-500 stroke-2 w-6 h-6" />
              </div>
            }
          </button>
        </div>

        <div className="bg-dash px-4 py-4 min-h-80 h-fit rounded">
          <div>
            {output ? (
              <div>
                {rapp.modelType === "text" || rapp.modelType === "vision" ? (
                  <div className="text-sm rounded-lg h-fit relative">
                    <div className="bg-dash-foreground px-2 py-1 z-10 w-full absolute">
                      <h2 className="text-lg mt-1 absolute">Output Section:</h2>

                      <div className="flex items-center justify-end space-x-2">
                        <div
                          className="flex items-center mx-2 p-1 right-0 shadow-lg rounded cursor-pointer"
                          onClick={handleCopy}
                        >
                          <Copy className="" />
                        </div>

                        <div
                          className="flex items-center p-1 bp-1 shadow-lg rounded cursor-pointer "
                          onClick={() => setShowOptions(!showOptions)}
                        >
                          <Download className="text-white" />
                        </div>
                      </div>

                      {showOptions && (
                        <div
                          ref={optionsRef}
                          className="absolute right-0 bg-indigo-700 shadow-lg rounded border p-3"
                        >
                          <div
                            className="flex items-center space-x-3 cursor-pointer p-2 hover:bg-indigo-600 rounded"
                            onClick={handleDownloadPdf}
                          >
                            <GrDocumentPdf className="text-white" />
                            <span>Download as PDF</span>
                          </div>
                          <div
                            className="flex items-center space-x-3 cursor-pointer p-2 hover:bg-indigo-600 rounded"
                            onClick={handleDownloadWord}
                          >
                            <FaRegFileWord className="text-white" />
                            <span>Download as Word</span>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="pt-12">
                      <div id="content-to-print"

                        className="overflow-x-auto thin-scrollbar "
                      >
                        {/* <ReactMarkdown
                          rehypePlugins={[rehypeSanitize]}
                          remarkPlugins={[remarkGfm]}
                          className="prose lg:prose-xl text-lg font-medium leading-relaxed break-words"
                        >
                          {output}
                        </ReactMarkdown> */}
                        <ReactMarkdown
                          className="prose prose-invert max-w-none 
                prose-p:text-white prose-p:my-2
                prose-headings:text-white prose-headings:font-semibold prose-headings:my-4
                prose-strong:text-white prose-strong:font-semibold
                prose-em:text-white prose-em:italic
                prose-code:text-slate-300 prose-code:bg-slate-800 prose-code:px-1 prose-code:rounded
                prose-ul:my-2 prose-li:my-1 prose-li:marker:text-slate-400
                prose-blockquote:border-l-4 prose-blockquote:border-slate-400 prose-blockquote:pl-4
                prose-table:border-collapse prose-td:border prose-td:border-slate-600 prose-td:px-3 prose-td:py-2
                leading-relaxed text-sm md:text-base"
                          remarkPlugins={[remarkGfm]}
                          components={{
                            // Custom component overrides
                            a: ({ node, ...props }) => (
                              <a className="text-blue-400 hover:text-blue-300 underline" {...props} />
                            ),
                            ul: ({ node, ...props }) => (
                              <ul className="list-disc pl-5" {...props} />
                            ),
                            ol: ({ node, ...props }) => (
                              <ol className="list-decimal pl-5" {...props} />
                            ),
                            // Handle custom bullet points (•)
                            li: ({ node, children, ...props }) => {
                              if (typeof children === 'string' && children.startsWith('•')) {
                                return (
                                  <li className="flex items-start">
                                    <span className="mr-2">•</span>
                                    <span>{children.replace('•', '').trim()}</span>
                                  </li>
                                );
                              }
                              return <li {...props}>{children}</li>;
                            }
                          }}
                        >
                          {output || ''}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </div>
                ) : null}

                {rapp.modelType === "image" ? (
                  <>
                    <h1 className="text-xl font-bold mb-4">Output:</h1>
                    <div className="relative flex-1">
                      <div
                        className="absolute top-0 right-0 m-2 cursor-pointer p-2 bg-indigo-600 hover:bg-indigo-800 rounded-full"
                        onClick={handleDownload}
                      >
                        <Download />
                      </div>

                      {/* Image Component */}
                      <ImageWithAspectRatio imageSrc={output} />
                    </div>
                  </>
                ) : null}

                {rapp.modelType === "video" ? (
                  <div className="rounded-lg relative h-fit">
                    <div
                      className="absolute m-2 p-1 right-0 shadow-lg bg-indigo-600 rounded cursor-pointer"
                      onClick={handleDownload}
                    >
                      <Download className="" />
                    </div>
                    <video
                      className="rounded-lg shadow-lg"
                      controls
                      width="640"
                      height="360"
                    >
                      <source src={videoOutput || output} type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                ) : null}

                {rapp.modelType === "audio" ? (
                  <div className="relative flex flex-col justify-between h-full">
                    <div className="relative flex-1">
                      <h2 className="text-lg font-semibold text-yellow-400 mb-4">
                        Audio Output:
                      </h2>
                      <audio key={audioKey} controls className="w-full">
                        <source src={audioFile} type="audio/mpeg" />
                        Your browser does not support the audio tag.
                      </audio>
                    </div>
                  </div>
                ) : null}
              </div>
            ) : (
              <p className="text-2xl font-bold text-center mt-28 animate-pulse">
                Running this app will deduct {"  "}
                {rapp.totalCost + rapp.price}
                <img
                 src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                  alt="Coin"
                  style={{
                    width: "25px",
                    height: "27px",
                    display: "inline",
                    margin: "0px 4px 4px 2px",
                  }}
                  loading="lazy"
                />
                from your account.
              </p>
            )}
          </div>
        </div>
      </form>

      <RentModal isOpen={isModalOpen} onClose={closeModal}>
        <img
          src={output}
          alt="Output in modal"
          className="w-full h-auto object-contain"
          loading="lazy"
        />
      </RentModal>
    </div>
  );
};

export default RentForm;
