import { useEffect, useState } from "react";
import { toast } from "sonner";
import { SpaceModal } from "@/components/SpaceModal";
import { Button } from "@/components/ui/button";
import coinImage from "../../public/img/coin-png.png";
import { useRouter } from "next/navigation";
import { Play } from "lucide-react";

interface RunRappProps {
  rentproduct: Rapp;
}
interface Rapp {
  price: number;
  id: string;
}

interface User {
  coinBalance: number;
  // Add other properties if needed
}

const RunRapp = ({ rentproduct }: RunRappProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);

  const router = useRouter();

  // get the login user
  useEffect(() => {
    fetchUser();
  }, []);


  // const fetchUser = async () => {
  //   try {
  //     const res = await fetch(
  //       `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`
  //     );
  //     const data = await res.json();
  //     setUser(data?.user);
  //   } catch (e) {
  //     console.log(e);
  //     toast.error("Unexpected Error");
  //   }
  // };

  //----------------- Fetching Logged inUser with custom endpoint -----------------
  const fetchUser = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`,             
        {
          method: "GET",
        }
      );
      if (!response.ok) {
         throw new Error("Network response was not ok");
      }
      const data = await response.json();                                      
      setUser(data.data);
    } catch (error) {
      console.error("Error fetching user data:", error);
    } 
  };

  const handleRunNow = () => {
    if (user) {
      if (user.coinBalance >= rentproduct.price) {
        setIsModalOpen(true);
      } else {
        toast.error("Insufficient Credits to buy the product");
      }
    } else {
      toast.error("Please login to Buy Product");
    }
  };

  const handleConfirmPurchase = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/purchases/purchaseRapps`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            rentproductId: rentproduct.id,
          }),
        }
      );

      const result = await response.json();

      if (response.ok) {
        setIsModalOpen(false);
        toast.success("Product Purchase successful!");
        if (!router.push) {
          toast.loading("Loading...!");
        } else {
          toast.dismiss();
          router.push(
            `${process.env.NEXT_PUBLIC_SERVER_URL}/thank-you?purchaseId=${result.purchase.id}`
          );
        }
      } else {
        toast.error(result.message);

        // throw new Error(result.message);
      }
    } catch (error) {
      toast.error(`An error occurred while purchasing the product`);
    }
  };

  return (
    <>
      <button
        onClick={handleRunNow}
        type="submit"
        className="bg-gradient-to-br from-indigo-700 to-indigo-800 text-white py-4 px-8 rounded w-fit mt-4 focus:ring-offset-2 text-center transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none"
      >
        <Play className="fill-white" />
      </button>
      {isModalOpen && (
        <SpaceModal
          title=""
          description=""
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setErrorMessage(null); // Reset error message on close
          }}
        >
          <div className="space-y-3 py-2 pb-2">
            <p className="text-2xl font-bold">
              The amount of {rentproduct.price}
              <img
                src={coinImage.src}
                alt="Coin"
                style={{
                  width: "25px",
                  height: "27px",
                  display: "inline",
                  margin: "0px 4px 4px 2px",
                }}
                 loading="lazy"
              />
              will be deducted from your account for running this Rapp.
            </p>
            {errorMessage && <p className="text-red-500">{errorMessage}</p>}
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Close
              </Button>
              <Button variant="outline" onClick={handleConfirmPurchase}>
                Confirm
              </Button>
            </div>
          </div>
        </SpaceModal>
      )}
    </>
  );
};

export default RunRapp;
