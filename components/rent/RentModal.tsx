import { X } from 'lucide-react';
import React from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const RentModal = ({ isOpen, onClose, children }: ModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      <div className="relative bg-indigo-600/[0.4] p-2 backdrop-blur-md rounded-lg overflow-hidden w-11/12 h-11/12 md:w-3/4 md:h-[90vh]">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 lg:right-6 bg-indigo-600 text-white rounded-lg p-1"
        >
          <X/>
        </button>
        <div className="p-4 h-full w-full overflow-auto">{children}</div>
      </div>
    </div>
  );
};

export default RentModal;
