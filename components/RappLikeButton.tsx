"use client";

import { useState, useEffect } from "react";
import { Heart } from "lucide-react";
import { toast } from "sonner";

interface LikeButtonProps {
  productId: string;
  userId: string | null;
  initialLikes: number;
  isInitiallyLiked: boolean;
}

const LikeButton = ({
  productId,
  userId,
  initialLikes,
  isInitiallyLiked,
}: LikeButtonProps) => {
  const [likes, setLikes] = useState<number>(initialLikes);
  const [isLike, setIsLike] = useState<boolean>(isInitiallyLiked);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLikes(initialLikes);
    setIsLike(isInitiallyLiked);
  }, [initialLikes, isInitiallyLiked]);

  const handleLikeClick = async () => {
    if (!userId) {
      toast.error("You are not logged in.");
      return;
    }
  
    setLoading(true);
  
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/rappsLikes/${productId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );
  
      if (!result.ok) {
        console.error("Error:", result.status, result.statusText);
        setLoading(false);
        return;
      }
  
      const body = await result.json();
      setLikes(body.likeCount);
      setIsLike(body.isLikeUpdated);
      setLoading(false);
    } catch (error) {
      console.error("Error during request:", error);
    } finally {
      setLoading(false);
    }
  };
  

  return (
    <div
      className="flex items-center gap-2 rounded-xl bg-white/30 px-2 py-1 w-fit cursor-pointer"
      onClick={handleLikeClick}
    >
      <Heart
        className={`h-6 w-6 ${isLike ? "fill-red-600 text-red-600" : ""} ${
          loading ? "animate-pulse" : ""
        }`}
      />
      <p className="text-base md:text-xl font-bold">{likes}</p>
    </div>
  );
};

export default LikeButton;
