"use client";
import React, { useEffect, useState } from "react";
import { Skeleton } from "./ui/skeleton";
import Link from "next/link";
import { cn } from "@/lib/utils";
import ImageSlider from "./ImageSlider";
import Image from "next/image";
import { Heart, Star } from "lucide-react";
import coinImage from "../public/img/coin-png.png";
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerDescription,
} from "@/components/ui/drawer";
import { GlowingStarsBackgroundCard } from "./ui/glowing-stars";
import { toast } from "sonner";
import Container from "./ui/container";
import { useRouter } from "next/navigation";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import ReactMarkdown from "react-markdown";
import StarRating from "./RatingStar";
import TotalRating from "./TotalRating";
import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";

interface RappListingProps {
  rapp: any | null;
  index: number;
  user?: any;
}

const RappListing = ({ rapp, index, user }: RappListingProps) => {
  const validUrls = rapp?.images
    .map(({ image }: any) => (typeof image === "string" ? image : image.url))
    .filter(Boolean) as string[];

  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeImage, setActiveImage] = useState(
    validUrls
      ? validUrls[0]
      : "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/placeholder-09.webp"
  );
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [likes, setLikes] = useState(rapp?.likes_length ?? 0);
  const [isLike, setIsLike] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [buttonLoading, setbuttonLoading] = useState<boolean>(false);
  const [totalRating, setTotalRating] = useState<number>(0);

  const handleRatingFetched = (rating: number) => {
    setTotalRating(rating); // Store the total rating in parent state
  };

  const router = useRouter();
  useEffect(() => {
    if (rapp?.likes_id && user?.id) {
      setIsLike(
        rapp.likes_user_id?.includes(user.id) // Check if user.id is in likes_user_id array
      );
    }

    if (rapp?.likes_id) {
      setLikes(rapp.likes_length);
    }
  }, [rapp, user]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, index * 75);

    return () => clearTimeout(timer);
  }, [index]);

  if (!rapp || !isVisible) return <RappPlaceholder />;

  const handleImageClick = (imageSrc: string) => setActiveImage(imageSrc);

  const toggleDescription = () =>
    setIsDescriptionExpanded(!isDescriptionExpanded);

  // New function to handle arrow clicks
  const handleArrowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleLikeClick = async (rappId: string) => {
    if (!user?.id) {
      toast.error("You are not logged in.");
      return;
    }

    setLoading(true);
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/rappsLikes/${rappId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
        }
      );

      if (!result.ok) {
        console.error("Error:", result.status, result.statusText);
        setLoading(false);
        return;
      }

      const body = await result.json();
      setLikes(body.likeCount);
      setIsLike(body.isLikeUpdated);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching like data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLinkClick = () => {
    setbuttonLoading(true);
    // router.push(`/rent/${rapp.slug}`);
    window.location.href = `/ai-apps/${rapp.slug}`;
  };

  const getRelativeTime = (dateString: string): string => {
    const createdAt = new Date(dateString).getTime(); // Convert to timestamp (number)
    const now = new Date().getTime(); // Convert to timestamp (number)
    const diffInMs = now - createdAt; // Now both are numbers

    const diffInSec = Math.floor(diffInMs / 1000);
    const diffInMin = Math.floor(diffInSec / 60);
    const diffInHours = Math.floor(diffInMin / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInMonths / 12);

    if (diffInYears > 0)
      return ` ${diffInYears} Year${diffInYears > 1 ? "s" : ""} ago`;
    if (diffInMonths > 0)
      return ` ${diffInMonths} Month${diffInMonths > 1 ? "s" : ""} ago`;
    if (diffInDays > 0)
      return ` ${diffInDays} Day${diffInDays > 1 ? "s" : ""} ago`;
    if (diffInHours > 0)
      return ` ${diffInHours} Hour${diffInHours > 1 ? "s" : ""} ago`;
    if (diffInMin > 0)
      return ` ${diffInMin} Minute${diffInMin > 1 ? "s" : ""} ago`;
    return "Created just now";
  };
  return (
    <React.Fragment>
      <GlowingStarsBackgroundCard className="relative">
        {/* <div className="absolute top-0 right-0 font-bold rounded z-20">
          <p className="px-2 py-1 text-sm rounded bg-indigo-400 text-white font-bold ">
            {rapp.modelType}
          </p>
        </div> */}

        <Link
          className={cn(
            "invisible h-full w-full cursor-pointer rounded-t-lg group/main",
            {
              "visible animate-in fade-in-5": isVisible,
            }
          )}
          href={""}
          onClick={(e) => {
            e.preventDefault(); // Prevents the default navigation behavior
            setIsDrawerOpen(true); // Execute your desired function
          }}
        >
          <div className="relative">
            {rapp.isFeatured === true && (
              <div className="absolute -top-4 -left-6 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-sm rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                  FEATURED
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                </div>
              </div>
            )}
          </div>

          {/* Image Slider */}
          <div className="relative">
            <ImageSlider urls={validUrls} onArrowClick={handleArrowClick} />

            {/* Like Button and Likes Count */}
            <div
              className={`absolute z-10 bottom-1 backdrop-blur-sm left-1 flex items-center justify-center ${totalRating ? "gap-2" : "gap-0"} bg-black bg-opacity-50 rounded-md px-2 py-[2px]`}
            >
              <div className="flex gap-1 rounded-lg items-center">
                <Heart
                  className={`w-4 h-4 cursor-pointer ${
                    isLike ? "text-red-500 fill-red-500" : ""
                  }${loading ? "animate-pulse text-red-500 fill-red-500" : ""}`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLikeClick(rapp?.id);
                  }}
                />
                <p className="font-semibold text-white text-sm">{likes}</p>
              </div>
              <div className="hover:cursor-default rounded-lg">
                <TotalRating
                  rappId={rapp.id}
                  user={rapp}
                  onRatingFetched={handleRatingFetched}
                  totalRating={rapp?.rating}
                />
              </div>
            </div>
          </div>

          {/* <ImageSlider urls={validUrls} onArrowClick={handleArrowClick} /> */}
          <div className="flex items-start gap-4 md:gap-8 justify-between mt-4">
            {/* <div className="flex items-center gap-1">
              <Heart
                className={`cursor-pointer ${
                  isLike ? "text-red-500 fill-red-500" : ""
                }${loading ? "animate-pulse text-red-500 fill-red-500" : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleLikeClick(rapp?.id);
                }}
              />
              <p className="font-bold">{likes}</p>
            </div> */}
            <div className="">
              <p className="px-2 py-[2px] flex items-center text-sm rounded bg-indigo-400 text-white font-semibold uppercase">
                {rapp.modelType}
              </p>
            </div>
            <div className="flex gap-1 ">
              <p className="px-2 flex items-center text-xs rounded bg-white text-indigo-600 font-bold border uppercase">
                {rapp.modelName}
              </p>

              <div className=" w-auto">
                <p
                  className={`rounded px-1 pl-[5px] ${rapp.totalCost + rapp.price === 0 ? "bg-green-500 pl-[7px] tracking-wider" : "bg-white/30"} text-white flex items-center justify-center font-bold`}
                >
                  <span className="mb-[1px] text-md pr-[2px]">
                    {rapp.totalCost + rapp.price === 0
                      ? "FREE"
                      : rapp.totalCost + rapp.price}
                  </span>
                  {rapp.totalCost + rapp.price === 0 ? (
                    ""
                  ) : (
                    <img
                      src={coinImage.src}
                      alt="Coin"
                      style={{ width: "20px", height: "20px" }}
                      loading="lazy"
                    />
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col-2 items-start gap-4 md:gap-6 justify-between mt-1 w-full">
            <div className="w-[78%]">
              <h3 className="font-medium truncate text-lg md:text-md capitalize">
                {rapp.name}
              </h3>
            </div>
          </div>
          <h3 className="mt-1 font-medium break-words line-clamp-2 text-md md:text-sm first-letter:uppercase">
            {rapp.description}
          </h3>
        </Link>
        {/* <div className="hover:cursor-default">
          <TotalRating rappId={rapp.id} user={rapp} />
        </div> */}
      </GlowingStarsBackgroundCard>

      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <DrawerContent className="max-h-[80vh] md:max-h-[70vh] py-2 justify-between">
          <Container>
            <div className="flex flex-col md:flex md:flex-row mx-4 gap-2">
              <div className="flex flex-col md:grid md:grid-cols-2 gap-4 md:w-1/2">
                <Image
                  src={
                    validUrls[0]
                      ? validUrls[0]
                      : "https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/placeholder-09.webp"
                  }
                  className="h-40 md:h-60 lg:h-60 object-cover object-center rounded-lg cursor-pointer"
                  height="400"
                  width="400"
                  alt="thumbnail"
                  onClick={() => handleImageClick(validUrls[0])} // Open full image popup
                />
                <div className="md:grid flex md:grid-cols-2 overflow-x-scroll whitespace-nowrap gap-2 md:w-fit no-scrollbar">
                  {validUrls.slice(1).map((imageSrc, index) => (
                    <div key={index} onClick={() => handleImageClick(imageSrc)}>
                      <Image
                        src={imageSrc}
                        className={`w-24 h-20 md:w-28 md:h-28 object-cover object-center rounded-lg cursor-pointer`}
                        height="100"
                        width="100"
                        alt="thumbnail"
                      />
                    </div>
                  ))}
                </div>
              </div>
              <div className=" md:w-1/2 flex-grow relative">
                <div className="flex justify-between items-center mb-2 sm:mb-2 lg:mb-3">
                  <DrawerTitle className="text-xl mb-1 capitalize">
                    {rapp.name}
                  </DrawerTitle>
                  <div className="flex rounded-xl items-center bg-white/30 px-2 py-1 gap-2">
                    <Heart
                      className={`cursor-pointer ${
                        isLike ? "text-red-500 fill-red-500" : ""
                      }`}
                      onClick={(e) => {
                        handleLikeClick(rapp.id);
                      }}
                    />
                    <p className="font-bold">{likes}</p>
                  </div>
                </div>
                <DrawerDescription className="mb-1 max-h-20 md:max-h-48 overflow-y-auto">
                  <ReactMarkdown
                    components={{
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline first-letter:uppercase"
                        >
                          {children}
                        </a>
                      ),
                    }}
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                    className="inline"
                  >
                    {isDescriptionExpanded
                      ? capitalizeFirstLetter(rapp.description)
                      : capitalizeFirstLetter(
                          `${rapp.description?.substring(0, 100)}...`
                        )}
                  </ReactMarkdown>
                  <span
                    className="text-indigo-500 cursor-pointer"
                    onClick={toggleDescription}
                  >
                    {isDescriptionExpanded ? " Show less" : " Show more"}
                  </span>
                </DrawerDescription>

                <div className="flex flex-col gap-2"></div>
                <DrawerTitle className="mb-3 hover:text-indigo-400">
                  <Link href={`/users/${rapp?.creator?.user_name}`}>
                    @{" "}
                    {capitalizeFirstLetter(
                      rapp?.creator?.user_name || "Anonymous"
                    )}
                  </Link>
                </DrawerTitle>

                <div className="flex justify-between ">
                  <div
                    className={`flex flex-row rounded ${rapp.totalCost + rapp.price === 0 ? "bg-green-500 pl-[7px] tracking-wider" : "bg-white/30"} w-fit px-2 items-center`}
                  >
                    <p className="text-md font-bold">
                      {" "}
                      {rapp.totalCost + rapp.price === 0
                        ? "FREE"
                        : rapp.totalCost + rapp.price}
                    </p>
                    {rapp.totalCost + rapp.price === 0 ? (
                      ""
                    ) : (
                      <img
                        src={coinImage.src}
                        alt="Coin"
                        style={{ width: "23px", height: "23px" }}
                        loading="lazy"
                      />
                    )}
                  </div>
                  <div>
                    <div className="text-sm bg-white/30 text-white px-2 py-1 rounded">
                      {getRelativeTime(rapp.newest)}
                    </div>
                  </div>
                </div>
                <p className="pb-16 lg:pb-16">
                  {rapp.affiliated_with ? (
                    <div>
                      Affiliated With :{" "}
                      <a
                        href={rapp.affiliated_with}
                        className="text-green-500 underline "
                        target="_blank"
                      >
                        {rapp.affiliated_with.substring(0, 30) + "..."}
                      </a>
                    </div>
                  ) : (
                    ""
                  )}
                </p>

                <div className="absolute bottom-2 w-full">
                  <button
                    className="bg-gradient-to-br from-indigo-600 to-indigo-700 w-full text-white rounded-md h-10 font-medium text-center align-middle"
                    onClick={handleLinkClick}
                  >
                    {buttonLoading ? <div>Loading...</div> : "Try Now"}
                  </button>
                </div>
              </div>
            </div>
          </Container>
        </DrawerContent>
      </Drawer>
    </React.Fragment>
  );
};

const RappPlaceholder = () => {
  return (
    <div className="flex flex-col w-full">
      <div className="relative bg-zinc-100 aspect-square w-full overflow-hidden rounded-xl">
        <Skeleton className="h-full w-full" />
      </div>
      <Skeleton className="mt-4 w-2/3 h-4 rounded-lg" />
      <Skeleton className="mt-2 w-full h-8 rounded-lg" />
    </div>
  );
};

export default RappListing;
function setData(data: any) {
  toast.error("Function not implemented.");
}
