"use client";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  ModalTrigger,
} from "./ui/animated-modal";
import { motion } from "framer-motion";
import Image from "next/image";
import { toast } from "sonner";
import Link from "next/link";
import { Plus } from "lucide-react";

import { AnimatePresence } from "framer-motion";
import { PlusIcon, SquareIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import RatingStar from "./RatingStar";
import TotalRating from "./TotalRating";
//import { cn } from "@/cuicui/utils/cn/cn";

const menuCategories = [
  { label: "Comment", slug: "", menuWidth: 320, menuHeight: 240 },
] as const;

export function AnimatedModalDemo({ user, rappId, rapp }) {
  const [comments, setComments] = useState("");
  const [isPurchased, setIsPurchased] = useState(false);
  const [userComments, setUserComments] = useState([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedComments, setExpandedComments] = useState(new Set());
  const [isReviewOpen, setIsReviewOpen] = useState(false);

  const [isOpen, setIsOpen] = useState(false);
  const [subMenuSelected, setSubMenuSelected] = useState("dimensions");

  const handleOpenSettings = () => {
    setIsOpen(!isOpen);
  };

  const toggleExpanded = (commentId) => {
    setExpandedComments((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(commentId)) {
        newSet.delete(commentId);
      } else {
        newSet.add(commentId);
      }
      return newSet;
    });
  };

  const handleReviewClick = () => {
    setIsReviewOpen(true);
    setIsEditing(false);
  };
  const handleAddComment = async () => {
    if (!user?.id || !rappId) {
      toast.error("You are not logged in.");
      return;
    }
    setLoading(true);
    try {
      await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/comment/${rappId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ comments }),
        }
      );
      toast.success("Comment added successfully!");
      fetchComments();
      setComments("");
      setIsOpen(false); 
    } catch (error) {
      toast.error("Failed to add comment. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const fetchComments = async () => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/comment/${rappId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      const data = await result.json();
      if (data.success) {
        setUserComments(data.comments);
      }
    } catch (error) {
      console.error("Error submitting comment:", error);
      toast.error("Something went wrong. Please try again.");
    }
  };

  const purchasedStatus = async () => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/rappPurchased/${rappId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      const data = await result.json();
      setIsPurchased(data.isPurchased);
    } catch (error) {
      console.error("Error submitting comment:", error);
      toast.error("Something went wrong. Please try again.");
    }
  };

  useEffect(() => {
    fetchComments();
  }, []);

  useEffect(() => {
    purchasedStatus();
  }, []);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setActiveIndex((prevIndex) =>
        prevIndex === userComments.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000);

    return () => clearInterval(intervalId);
  }, [userComments.length]);

  const handleEditComment = (comment) => {
    setComments(comment.comment);
    setIsEditing(true);
  };

  const userHasComments = userComments.some(
    (commentData) => commentData.user.id === user?.id
  );
  const handleEditClick = (commentData) => {
    setComments(commentData.comment);
    setIsEditing(true);
  };

  function NavigationMenu({
    setSubMenuSelected,
    subMenuSelected,
  }: Readonly<{
    setSubMenuSelected: (slug: string) => void;
    subMenuSelected: string;
  }>) {
    return (
      <nav className="flex flex-row">
        {menuCategories.map((button, _index) => (
          <button
            className={cn(
              "relative inline-flex w-fit transform-gpu whitespace-nowrap rounded-md px-2 py-1 font-medium text-indigo-200 text-sm transition-colors duration-300 hover:text-neutral-50",
              subMenuSelected === button.slug && "text-neutral-200"
            )}
            key={button.label}
            onClick={() => setSubMenuSelected(button.slug)}
            type="button"
          >
            <p className="z-20">{button.label}</p>
            <AnimatePresence>
              {subMenuSelected === button.slug && (
                <motion.div
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute top-0 right-0 bottom-0 left-0 z-10 rounded-md bg-neutral-700 dark:bg-neutral-800"
                  exit={{ opacity: 0, scale: 0.9 }}
                  initial={{ opacity: 0, scale: 0.95 }}
                  layout={true}
                  layoutId="focused-element"
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                />
              )}
            </AnimatePresence>
          </button>
        ))}
      </nav>
    );
  }

  return (
    <>
      <div className="flex flex-col mt-24 mb-4 px-3 md:px-0">
        <div className="flex items-center justify-between text-lg">
          <h2 className="text-lg">Reviews</h2>
          <span className="text-lg"> Comments </span>
        </div>

        <div className="border-t-2 border-gray-300 my-2"></div>
        <div className="flex items-center justify-between">
          <Modal>
            <span
              onClick={handleReviewClick}
              className="text-center transition duration-500 w-full"
            >
              <span className="flex gap-1">
                <motion.div
                  animate={{
                    height: isOpen ? 220 : 52,
                  }}
                  className={cn(
                    "items-center justify-between overflow-hidden rounded-lg bg-black/30 text-indigo-400 p-2 border border-violet-400 hover:border-neo-foreground shadow-lg",
                    "w-full"
                  )}
                  transition={{
                    type: "spring",
                    duration: 0.6,
                  }}
                  onClick={handleOpenSettings}
                >
                  <div className="h-full" >
                    <div className="flex h-full flex-col justify-between gap-2 transition-all duration-300">
                      <div className="group flex items-center justify-between gap-2 transition-all duration-300 cursor-pointer">
                        {isOpen ? (
                          <div className="flex gap-2 items-center">
                            <button
                              className="flex gap-2 p-2 font-medium text-indigo-200 text-sm transition-all group-hover:text-indigo-100"
                              onClick={() => setIsOpen(false)}
                            >
                              Add Review
                            </button>
                            {isPurchased && (
                              <div className="bg-indigo-200 text-indigo-800 p-1 font-bold rounded-lg text-sm">
                                Purchased
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="flex gap-2 items-center">
                            <button
                              className="flex gap-2 p-2 font-medium text-indigo-200 text-sm transition-all group-hover:text-indigo-100"
                              onClick={() => setIsOpen(true)}
                            >
                              Add Review
                            </button>
                            {isPurchased && (
                              <div className="bg-indigo-200 text-indigo-800 p-1 font-bold rounded-lg text-sm">
                                Purchased
                              </div>
                            )}
                          </div>
                        )}
                        <button
                          className="size-8 transform-gpu text-neutral-400 transition-colors duration-500 hover:text-neutral-300"
                          onClick={() => setIsOpen(false)} // Change to just close modal
                          type="button"
                        >
                          <PlusIcon
                            className={cn(
                              "text-indigo-200 transform-gpu transition-transform duration-300",
                              isOpen ? "rotate-45" : "rotate-0"
                            )}
                          />
                        </button>
                      </div>

                      <motion.div
                        animate={{ opacity: 1, filter: "blur(0px)" }}
                        className="flex h-full flex-col justify-between gap-4"
                        initial={{ opacity: 0, filter: "blur(4px)" }}
                        transition={{ duration: 1.2, type: "spring" }}
                      >
                        <textarea
                          value={comments}
                          onChange={(e) => setComments(e.target.value)}
                          onClick={(e) => e.stopPropagation()} // Add this line
                          className="rounded-[6px] h-28 bg-indigo-900 px-2 mx-2 py-[6px] text-sm text-white placeholder:text-white/30"
                          placeholder="Add a new comment"
                        />
                        <div className="flex justify-between">
                          <div className="ml-2 flex items-center gap-2">
                            {userComments.map((commentData) =>
                              commentData.user.id === user?.id ? (
                                <button
                                  key={commentData.id}
                                  onClick={(e) => { e.stopPropagation();
                                    handleEditClick(commentData)}}
                                  className="text-white underline text-end mb-2"
                                >
                                  Edit
                                </button>
                              ) : null
                            )}
                          </div>
                          <button
                            className="rounded-lg bg-indigo-900 px-2 py-1 font-medium text-indigo-200"
                            onClick={() => {
                              handleAddComment();
                              // handleOpenSettings();
                            }}
                            type="submit"
                          >
                            Comment
                          </button>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </span>
            </span>

            {isReviewOpen && (
              <ModalBody className="bg-indigo-800">
                <ModalContent>
                  <div className="flex flex-col mt-4">
                    {userComments.map((commentData) =>
                      commentData.user.id === user?.id ? (
                        <button
                          key={commentData.id}
                          onClick={() => handleEditClick(commentData)}
                          className="text-white underline text-end mb-2"
                        >
                          Edit Comment
                        </button>
                      ) : null
                    )}
                  </div>
                  <>
                    <div className="w-full mt-2">
                      <h2 className="text-xl font-semibold mb-4">
                        {isEditing
                          ? "Edit Your Comment Here..."
                          : "Comment Here..."}
                      </h2>
                      <textarea
                        value={comments}
                        onChange={(e) => setComments(e.target.value)}
                        onInput={(e) => {
                          const target = e.target as HTMLTextAreaElement;
                          target.style.height = "auto";
                          target.style.height = `${target.scrollHeight}px`;
                        }}
                        placeholder="Write your comment here..."
                        className="w-full max-h-40 h-32 p-2 border bg-indigo-700 rounded mb-4 outline-none text-white resize-none overflow-y-auto"
                      />
                    </div>
                    <button
                      onClick={handleAddComment}
                      disabled={loading}
                      className="bg-black/30 text-white dark:bg-white dark:text-black text-sm px-2 py-1 rounded-md border border-black/30 w-28"
                    >
                      {loading
                        ? isEditing
                          ? "Editing..."
                          : "Commenting..."
                        : isEditing
                          ? "Save"
                          : "Comment"}
                    </button>
                  </>
                </ModalContent>
              </ModalBody>
            )}
          </Modal>
        </div>
      </div>

      <div className="px-3 md:px-0">
        {userComments.length > 0 ? (
          <div className="w-full flex mt-4">
            <div className="relative w-full rounded-lg p-0 overflow-hidden">
              {/* <div
                className="overflow-y-auto h-64  rounded-lg shadow-md"
                style={{ height: "400px" }}
              > */}
              {userComments.map((commentData, index) => (
                <div
                  key={commentData.id}
                  className="flex flex-col mb-4 p-4 bg-black/30 rounded-lg"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Image
                      src={
                        commentData.user.profileImage?.url ||
                        "https://t4.ftcdn.net/jpg/02/29/75/83/360_F_229758328_7x8jwCwjtBMmC6rgFzLFhZoEpLobB6L8.jpg"
                      }
                      alt={`${commentData.user.user_name}'s profile`}
                      className="w-10 h-10 rounded-full shadow-md border-2 border-indigo-600"
                      width="500"
                      height="500"
                    />
                    <Link
                      href={`/users/${commentData.user.user_name}`}
                      // target="_blank"
                      className=" text-white font-semibold py-1 rounded first-letter:uppercase"
                    >
                      {/* <p className="text-indigo-600 font-medium text-base"> */}
                      {commentData.user.user_name}
                      {/* </p> */}
                    </Link>
                  </div>
                  <p className="text-indigo-200 text-sm italic break-words">
                    {expandedComments.has(commentData.id)
                      ? commentData.comment
                      : `${commentData.comment.slice(0, 500)}`}
                  </p>

                  {commentData.comment.length > 500 && (
                    <button
                      onClick={() => toggleExpanded(commentData.id)}
                      className="text-blue-500 text-sm mt-2 focus:outline-none"
                    >
                      {expandedComments.has(commentData.id)
                        ? "Show Less"
                        : "Show More"}
                    </button>
                  )}
                </div>
              ))}
              {/* </div> */}
            </div>
          </div>
        ) : (
          <div className="text-center text-white font-semibold">
            No comments yet.
          </div>
        )}
      </div>
    </>
  );
}
