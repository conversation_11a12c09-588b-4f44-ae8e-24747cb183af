"use client";

import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import Hero from "./hero";
import Slider from "./SlideWiget";

interface HeroSliderProps {
    // Add your props here if needed
}
const HeroSlider: React.FC<HeroSliderProps> = () => {
    const containerRef = useRef<HTMLDivElement>(null);

    const { scrollYProgress } = useScroll({
        target: containerRef,
        offset: ["start start", "end start"],
    });

    const opacity = useTransform(scrollYProgress, [0, 0.4], [1, 0]);
    const scale = useTransform(scrollYProgress, [0, 0.4], [1, 0.8]);
    const display = useTransform(scrollYProgress, 
        [0, 0.4, 0.41], 
        ["block", "block", "none"]
    );

    const fadeZoomInVariants = {
        visible: { opacity: 1, scale: 1 },
        hidden: { opacity: 0, scale: 0.8 },
    };

    // className="hero-slider relative h-[120vh] sm:h-[160vh] md:h-[180vh] xl:h-[175vh] 2xl:h-[145vh]"

    return (
        <div ref={containerRef} className="hero-slider relative h-[64rem] sm:h-[74rem] md:h-[77rem] lg:h-[85rem]">
            <motion.div
                variants={fadeZoomInVariants}
                initial="hidden"
                animate="visible"
                style={{ opacity, scale, display }}
                className="sticky top-0 left-0 right-0 h-fit"
            >
                <Hero />
            </motion.div>
            <div className="absolute bottom-0 w-full">
                <Slider />
            </div>
        </div>
    );
};
export default HeroSlider;