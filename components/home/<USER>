'use client';
import Image from 'next/image';
import { ProgressSlider, SliderBtn, SliderBtnGroup, SliderContent, SliderWrapper } from '../ui/progress-slider';
import { FeatureWidget } from './featuredWiget';
import { Icons } from '../Icons';

const items = [
  {
    img:`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/aiapp_updated.jpg`,
    title: 'AI Apps',
    desc: 'Create, share, and monetize AI apps in seconds.',
    sliderName: 'Aiapp',
  },
  {
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/marketplace_updated.jpg`,
    title: 'Marketplace',
    desc: 'Discover, buy, and sell AI prompts for all your needs.',
    sliderName: 'Marketplace',
  },
  {
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/GENERATE_UPDATED.jpg`,
    title: 'Generate',
    desc: 'Access leading models to effortlessly create text, images, audio, and videos in one place.',
    sliderName: 'Generate',
  },
  {
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/bounty 2.png`,
    title: 'Bounty',
    sliderName: 'Bounty',
    desc: 'Collaborate with AI engineers and bring your use cases to life.',
  },
];
export default function Slider() {
  return (
    <>
      <ProgressSlider vertical={false} activeSlider='bridge flex justify-center items-center'>
        <SliderContent className='flex px-2 justify-center items-center'>
          {items.map((item, index) => (
            <SliderWrapper key={index} value={item?.sliderName}>
              <Image
                className=' rounded-t-xl px-2 pb-0 pt-4 xl:h-[640px] w-auto bg-black/[0.3] transition-all duration-300 ease-in-out hover:-translate-y-1 rounded-md outline outline-8 outline-[#1C1B1A]/25 object-bottom object-scale-down'
                src={item.img}
                width={1900}
                height={1080}
                alt={item.desc}
              />
            </SliderWrapper>
          ))}
        </SliderContent>
        <SliderBtnGroup className='relative h-fit text-white bg-black/40 backdrop-blur-md overflow-hidden grid grid-cols-2 md:grid-cols-4 rounded-md'>
          {items.map((item, index) => (
            <SliderBtn
              key={index}
              value={item?.sliderName}
              className='text-left p-3 border-r'
              progressBarClass='bg-dash-foreground h-full'
            >
              <h2 className='relative px-4 rounded-full w-fit bg-gradient-to-r from-amber-500 to-pink-500 text-white mb-2'>
                {item.title}
              </h2>
              <p className='text-sm font-medium line-clamp-1 md:line-clamp-2'>{item.desc}</p>
            </SliderBtn>
          ))}
        </SliderBtnGroup>
      </ProgressSlider>
    </>
  );
}