"use client";

const ButtonRotatingBackgroundGradient = ({ children }: { children?: React.ReactNode }) => {
  return (
    <button className="relative max-md:w-80 inline-flex overflow-hidden rounded-full p-[2px] focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-50 motion-ease-spring-bouncy motion-scale-x-in-[30%] motion-scale-y-in-90">
      <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#a2aeff_0%,#3749be_50%,#a2aeff_100%)] dark:bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]" />
      <span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-[#070e41] px-4 py-1 text-lg font-medium text-gray-50 backdrop-blur-3xl">
        {children}
      </span>
    </button>
  );
};

export default ButtonRotatingBackgroundGradient;
