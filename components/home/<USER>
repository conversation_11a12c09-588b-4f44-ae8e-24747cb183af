import React from 'react';
import { Icons } from '../Icons';
import { FaHandshake } from 'react-icons/fa';

interface ContainerProps {
    children?: React.ReactNode;
    className?: string;
    backgroundColor?: string;
  }
  
  const AnimatedDiv: React.FC<ContainerProps> = ({
    children,
  }) => {
    return (
        <div className="relative text-4xl md:text-5xl">
          <div className="motion-duration-[0.2s] hover:motion-text-loop-[#f8ff8c]">{children}</div>
        <div className=" hidden absolute -left-16 -top-0 md:flex -rotate-45">
          <div className="motion-rotate-loop-[360deg]/reset motion-duration-2000 motion-ease-linear text-3xl sm:text-4xl">⚙️</div>
          <div className=" -ml-2 text-4xl sm:text-3xl -motion-rotate-loop-[360deg]/reset motion-duration-2000 motion-ease-linear">⚙️</div>
        </div>
        <div className="hidden md:block absolute text-5xl md:-top-16 md:-right-40 motion-preset-pulse-sm motion-duration-500 justify-self-center right-28 -top-96"><FaHandshake/></div>
        <div className="hidden md:block absolute -top-12 left-1/2 translate-x-8 rotate-12 motion-preset-float motion-translate-y-loop-25">🛸</div>
  
        <div className="max-md:hidden absolute -right-12 top-1/2 -translate-y-1/2 motion-rotate-loop-45 motion-duration-1000 motion-delay-500 motion-ease-bounce">🔨</div>
        <div className="hidden md:block absolute w-fit -right-16 -top-60 md:-top-16 md:-left-48 translate-y-8 rotate-12 motion-preset-float motion-translate-y-loop-25"><Icons.meta/></div>
  
        <div className="hidden md:block absolute text-5xl -z-10 -bottom-16 -left-4 -rotate-12 motion-opacity-loop-50 motion-duration-[1s]">💡</div>
  
        <div className="hidden md:block absolute text-5xl -bottom-16 right-2 motion-preset-pulse-sm motion-duration-200">💣</div>
      </div>
    );
  };
  
export default AnimatedDiv;