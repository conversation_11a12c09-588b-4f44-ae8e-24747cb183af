"use client";

import classNames from "classnames";


export default function ExampleWrapper({
  children,
}: {
  children: React.ReactNode
}) {

  return (
    <div className="w-full h-full items-center justify-center relative">
      <div
        className={classNames(
          "rounded w-full h-[50vmin] flex items-center justify-center"
        )}
      >
        <div className='absolute -z-10 bottom-0 left-0 right-0 top-0 bg-[linear-gradient(to_right,#808BF7_1px,transparent_1px),linear-gradient(to_bottom,#808BF7_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]'></div>
        { children}
      </div>
    </div>
  );
}