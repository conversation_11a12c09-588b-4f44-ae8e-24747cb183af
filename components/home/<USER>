import Image from "next/image";
import { Icons } from "../Icons";
import MaxWidthWrapper from "../MaxWidthWrapper";
import { <PERSON><PERSON> } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import { AlignEndHorizontal, Bot, Code2, Database, Grid, Library, Sparkles, Store, Wand2 } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs";
import Link from "next/link";
import MagneticButton from "./MagnaticButton";
import ButtonRotatingBackgroundGradient from "./moderngradiant";
import PortalSvg from "./portalSvg";
import {
  useMotionTemplate,
  useMotionValue,
  motion,
  animate,
} from "framer-motion";
import { BounceCard, CardTitle } from "./BounceCard";


const WhoWeAre = () => {
  return (
    <>
      <MaxWidthWrapper className="px-0 md:px-4 rounded md:rounded-lg">       
      <div className="relative pt-6">
          <div
            className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
              style={{
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            />
          </div>
          <section className="relative md:px-4 py-16 md:py-16">
        <div className="mx-auto max-w-6xl">
          <div className="grid gap-12 md:grid-cols-2 md:gap-16">
            <div className="flex flex-col justify-center">
              <h2 className="mb-6 text-4xl font-bold tracking-tight md:text-6xl">
                The Ultimate AI
                <span className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent">
                  {" "}
                  Marketplace & Studio
                </span>
              </h2>
              <p className="mb-8 text-lg text-gray-300 md:text-xl">
                Discover, create, and monetize AI assets. From prompts to full applications, rentprompts is
                your one-stop platform for generative AI innovation.
              </p>
              <div className="flex flex-row gap-4">
                <Link href="/marketplace">
                <Button variant="white">
                  Explore Marketplace
                </Button>
                </Link>
                <Link href="https://rentprompts.ai/">
                <Button variant="gradient">
                  Launch AI Studio
                </Button>
                </Link>
              </div>
            </div>
            <div className="relative flex flex-col items-center justify-center aspect-square md:aspect-auto">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative h-64 w-64 md:h-96 md:w-96">
                  <div className="absolute inset-0 animate-pulse rounded-full bg-purple-500/20 blur-3xl" />
                  <div className="absolute inset-0 animate-pulse rounded-full bg-pink-500/20 blur-3xl animation-delay-1000" />
                </div>
              </div>
              <PortalSvg className="w-3/4 h-3/4"/>
            </div>
          </div>
        </div>
      </section>
          <div
            className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
              style={{
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            />
          </div>
        </div>

        {/* Feature section */}

 
        {/* Testimonial section */}
        <div className="my-12 sm:max-w-7xl">
          <div className="relative overflow-hidden px-4 shadow-xl sm:rounded-3xl md:px-10  lg:px-20">
            <div className="absolute -z-10 inset-0 bg-dash mix-blend-multiply" />
            <div
              className="absolute -left-80 -top-56 transform-gpu blur-3xl -z-10"
              aria-hidden="true"
            >
              <div
                className="aspect-[1097/845] w-[68.5625rem] bg-gradient-to-r from-[#ff4694] to-[#776fff] opacity-[0.45]"
                style={{
                  clipPath:
                    "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                }}
              />
            </div>
            <section className="px-4">
        <div className="mx-auto max-w-7xl">
          <div className="w-full flex flex-col justify-center items-center text-center gap-2 my-8">
          <h2 className="text-center md:max-w-3xl text-gray-300 text-3xl font-bold md:text-3xl">
          Two Platforms, One Unified AI Ecosystem.
          </h2>
          <p className="hidden md:block text-center text-gray-300 text-xl">Revolutionize how you innovate and collaborate with AI.

          </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            {/* rentprompts.com */}
            <Card className="bg-white/5 backdrop-blur-lg border border-white/10">
              <CardContent className="p-6">
                <h3 className="mb-4 text-2xl font-semibold text-center">rentprompts.com</h3>
                <p className="mb-6 text-center text-gray-300">The Ultimate AI Marketplace Transform your AI experience with</p>
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <Store className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">AI Marketplace</h4>
                      <p className="text-sm text-gray-300">Buy, sell, and trade prompts, models, and apps to power your projects.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Grid className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">AI App Store</h4>
                      <p className="text-sm text-gray-300">Discover pre-built apps to generate impactful content and save iterations.

                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Database className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">Generate Hub</h4>
                      <p className="text-sm text-gray-300">Access free and premium text, image, audio, video models without subscriptions.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Sparkles className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">Community</h4>
                      <p className="text-sm text-gray-300">Join an expert network, gain rewards, and grow your skills with curated resources.

                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <p className="md:hidden text-center text-gray-300 text-xl">Revolutionize how you innovate and collaborate with AI.

</p>
            {/* rentprompts.ai */}
            <Card className="bg-white/5 backdrop-blur-lg border border-white/10">
              <CardContent className="p-6">
                <h3 className="mb-4 text-2xl font-semibold text-center">rentprompts.ai</h3>
                <p className="mb-6 text-center text-gray-300">Your AI App Studio Build smarter solutions without coding</p>
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <Code2 className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">AI App Studio</h4>
                      <p className="text-sm text-gray-300">Design public or private apps with real-time previews and no-code.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Bot className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">AI Agents</h4>
                      <p className="text-sm text-gray-300">Create intelligent, data-driven agents tailored to your needs.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Wand2 className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">Collaborate</h4>
                      <p className="text-sm text-gray-300">Work with top AI engineers and share projects seamlessly.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Library className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">API Integration</h4>
                      <p className="text-sm text-gray-300">Effortlessly integrate AI apps into your workflows.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <AlignEndHorizontal className="h-6 w-6 text-purple-400 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold mb-2">Easy Onboarding</h4>
                      <p className="text-sm text-gray-300">Easily onboard and manage your enterprise members.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="mt-6 text-center">
            <p className="mb-8 text-lg text-gray-300">
            Unleash the power of AI with RentPrompts—innovate, create, and grow. Start your journey today!
            </p>
            
          </div>
        </div>
      </section>

            <div
              className="hidden md:absolute md:bottom-16 md:left-[50rem] md:block md:transform-gpu md:blur-3xl -z-10"
              aria-hidden="true"
            >
              <div
                className="aspect-[1097/845] w-[68.5625rem] bg-gradient-to-r from-[#ff4694] to-[#776fff] opacity-25"
                style={{
                  clipPath:
                    "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                }}
              />
            </div>
          </div>
        </div>

        {/* Info section */}
        <section className="mx-auto max-w-7xl px-4 py-12 text-indigo-400">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-end md:px-8">
        <h2 className="bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent text-4xl text-center font-bold md:text-5xl w-full">
          Grow faster
          <span className="text-primary/90"> with our all in one </span>
          solution
        </h2>
        {/* <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="whitespace-nowrap rounded-lg bg-slate-900 px-4 py-2 font-medium text-white shadow-xl transition-colors hover:bg-slate-700"
        >
          Learn more
        </motion.button> */}
      </div>
      <div className="mb-4 grid grid-cols-12 gap-4">
      <BounceCard className="col-span-12 md:col-span-4 relative group overflow-hidden">
  <CardTitle>Pay-as-you Go</CardTitle>
  <div className="absolute bottom-0 left-4 right-4 top-16 translate-y-8 rounded-t-2xl bg-gradient-to-br from-violet-400 to-indigo-400 p-4 transition-transform duration-[250ms] group-hover:translate-y-4 group-hover:rotate-[2deg]">
    {/* Responsive Background Image */}
    <Image
      fill
      src="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/vz1.png"
      alt="image"
      className="-z-10 object-cover object-top rounded-t-2xl transition-transform duration-[250ms]"
    />
    {/* Text Overlay with Background Change */}
    
  </div>
  <div className="absolute bottom-4 left-4 right-4 pb-6 translate-y-8 rounded-t-2xl bg-gradient-to-br from-violet-400 p-2 to-indigo-400 transition-transform duration-[250ms] group-hover:translate-y-6 group-hover:rotate-[-2deg]">
            <span className="block text-center font-semibold text-emerald-50">
            Buy and sell prompts, models, and complete AI applications
            </span>
          </div>
  {/* <span className="absolute -bottom-1 left-2 right-2 text-center group-hover:scale-110 transition-all duration-200 font-semibold text-indigo-600 bg-white/70 backdrop-blur-sm p-2 rounded-md  duration-[250ms] group-hover:opacity-100 group-hover:text-indigo-800 ">
      Buy and sell prompts, models, and complete AI applications
    </span> */}
</BounceCard>
    <BounceCard className="col-span-12 md:col-span-8">
          <CardTitle>Launch your Ideas with a One click</CardTitle>
          <div className="absolute bottom-0 left-4 right-4 top-16 translate-y-8 rounded-t-2xl bg-gradient-to-br from-violet-400 to-indigo-400 p-4 transition-transform duration-[250ms] group-hover:translate-y-4 group-hover:rotate-[2deg]">
    {/* Responsive Background Image */}
    <Image
      fill
      src="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/aiclick.png"
      alt="image"
      className="-z-10 object-cover object-right-top rounded-t-2xl transition-transform duration-[250ms]"
    />
    {/* Text Overlay with Background Change */}
    
  </div>
  <div className="absolute bottom-4 left-4 right-4 pb-6 translate-y-8 rounded-t-2xl bg-gradient-to-br from-amber-400 to-orange-400 transition-transform duration-[250ms] group-hover:translate-y-6 group-hover:rotate-[-2deg]">
            <span className="block text-center font-semibold text-emerald-50">
            Create and deploy AI applications with our powerful tools
            </span>
          </div>
         
        </BounceCard>
      </div>
      <div className="grid grid-cols-12 gap-4">
        <BounceCard className="col-span-12 md:col-span-8">
          <CardTitle>Share your Work. Get Paid.</CardTitle>
          <div className="absolute bottom-0 left-4 right-4 top-16 translate-y-8 rounded-t-2xl bg-gradient-to-br from-violet-400 to-indigo-400 p-4 transition-transform duration-[250ms] group-hover:translate-y-4 group-hover:rotate-[2deg]">
          <Image
      fill
      src="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/shareEarn.png"
      alt="image"
      className="-z-10 object-cover object-right-top rounded-t-2xl transition-transform duration-[250ms]"
    /> 
          </div>
          <div className="absolute bottom-4 left-4 right-4 pb-6 translate-y-8 rounded-t-2xl bg-gradient-to-br from-green-400 to-emerald-400 transition-transform duration-[250ms] group-hover:translate-y-6 group-hover:rotate-[-2deg]">
            <span className="block text-center font-semibold text-emerald-50">
            Build and train AI agents for your specific needs
            </span>
          </div>
        </BounceCard>
        <BounceCard className="col-span-12 md:col-span-4">
          <CardTitle>Seamless Integration</CardTitle>
          <div className="absolute bottom-0 left-4 right-4 top-16 translate-y-8 rounded-t-2xl bg-gradient-to-br from-gray-950 to-indigo-400 p-4 transition-transform duration-[250ms] group-hover:translate-y-4 group-hover:rotate-[2deg]">
          <Icons.easyIng className="w-full"/>
          </div>
          <div className="absolute bottom-4 pb-6 left-4 right-4 translate-y-8 rounded-t-2xl bg-gradient-to-br from-pink-400 to-red-400 transition-transform duration-[250ms] group-hover:translate-y-6 group-hover:rotate-[-2deg]">
          
            <span className="block text-center font-semibold text-red-50">
            Integrate AI capabilities directly into your applications
            </span>
          </div>
        </BounceCard>
      </div>
    </section>
      {/* CTA Section */}
      <section className="">
        <div className="mx-auto">
          <Card className="overflow-hidden bg-transparent backdrop-blur-xl transition-all">          
            <CardContent className="flex flex-col items-center gap-6 py-20 text-center">
            <Icons.communityLogo />
            <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative h-64 w-64 md:h-96 md:w-96">
                  <div className="absolute inset-0 animate-pulse rounded-full bg-amber-500/20 blur-3xl" />
                  <div className="absolute inset-0 animate-pulse rounded-full bg-pink-500/20 blur-3xl animation-delay-1000" />
                </div>
          </div>
              <h2 className="text-3xl font-bold md:text-4xl">
                Ready to Start Your AI Journey?
              </h2>
              <p className="max-w-2xl text-lg text-gray-300">
                Join our community of creators and innovators. Start building, sharing, and monetizing your
                AI assets, Apps and Workflows today.
              </p>
              <Link href="https://discord.gg/kPkYbzMvN3" target="_blank">
                <MagneticButton>
                  <Button className="bg-gradient-to-r from-amber-500 to-pink-500 font-bold px-2 py-1 sm:py-2 sm:px-3 rounded text-xs sm:text-lg transform transition duration-200 hover:scale-105 hover:shadow-xl text-white">
                   Get Started Now
                   </Button>
                </MagneticButton>
                </Link>
            </CardContent>
          </Card>
        </div>
      </section>
      </MaxWidthWrapper>
    </>
  );
};


export default WhoWeAre;
