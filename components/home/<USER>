"use client"

import { useState, use<PERSON><PERSON><PERSON>, Mouse<PERSON><PERSON>, useRef, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowRight, Image as ImageIcon, Type, Redo } from "lucide-react"
import Image from "next/image"

interface Node {
  id: string
  type: "text-to-text" | "text-to-image" | "image-to-image"
  position: { x: number; y: number }
  content: {
    input?: string
    output?: string
    image?: string
  }
  zIndex: number
}

export default function NodeUI() {
  const [nodes, setNodes] = useState<any>([
    {
      id: "node1",
      type: "text-to-text",
      position: { x: 20, y: 20 },
      content: {
        input: "Write a description of a cozy house",
        output: "The living room is a cozy, inviting space filled with warmth and character. Soft, plush furnishings in earthy tones create a comfortable atmosphere, while a crackling fireplace serves as the room's focal point. Large windows allow natural light to flood in, illuminating the carefully curated decor that includes family photos, vintage books, and handmade quilts. The overall ambiance is one of relaxation and homeliness, perfect for unwinding after a long day."
      },
      zIndex: 1
    },
    {
      id: "node2",
      type: "text-to-image",
      position: { x: 20, y: 300 },
      content: {
        input: "A cozy craftsman house with a porch",
        image: "/placeholder.svg?height=200&width=300"
      },
      zIndex: 1
    }
  ])

  const [draggedNode, setDraggedNode] = useState<string | null>(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const containerRef = useRef<HTMLDivElement>(null)
  const nodeRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  const handleMouseDown = useCallback((event: MouseEvent, id: string) => {
    const node = nodes.find(n => n.id === id)
    if (node) {
      setDraggedNode(id)
      setDragOffset({
        x: event.clientX - node.position.x,
        y: event.clientY - node.position.y
      })
      setNodes(prevNodes => 
        prevNodes.map(n => ({
          ...n,
          zIndex: n.id === id ? Math.max(...prevNodes.map(node => node.zIndex)) + 1 : n.zIndex
        }))
      )
    }
  }, [nodes])

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (draggedNode && containerRef.current && nodeRefs.current[draggedNode]) {
      const containerRect = containerRef.current.getBoundingClientRect()
      const nodeRect = nodeRefs.current[draggedNode]!.getBoundingClientRect()

      setNodes(prevNodes =>
        prevNodes.map(node =>
          node.id === draggedNode
            ? {
                ...node,
                position: {
                  x: Math.max(0, Math.min(event.clientX - dragOffset.x, containerRect.width - nodeRect.width)),
                  y: Math.max(0, Math.min(event.clientY - dragOffset.y, containerRect.height - nodeRect.height))
                }
              }
            : node
        )
      )
    }
  }, [draggedNode, dragOffset])

  const handleMouseUp = useCallback(() => {
    setDraggedNode(null)
  }, [])

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect()
        setNodes(prevNodes =>
          prevNodes.map(node => {
            if (nodeRefs.current[node.id]) {
              const nodeRect = nodeRefs.current[node.id]!.getBoundingClientRect()
              return {
                ...node,
                position: {
                  x: Math.min(node.position.x, containerRect.width - nodeRect.width),
                  y: Math.min(node.position.y, containerRect.height - nodeRect.height)
                }
              }
            }
            return node
          })
        )
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black p-4 overflow-hidden"
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      <svg className="absolute inset-0 pointer-events-none">
        <path
          d="M220 220 L220 300"
          stroke="rgb(45, 212, 191)"
          strokeWidth="2"
          fill="none"
        />
      </svg>
      
      {nodes.map((node) => (
        <Card 
          key={node.id}
          ref={(el: HTMLDivElement | null) => {
            nodeRefs.current[node.id] = el
          }}
          className="absolute w-[400px] bg-gray-900 border-gray-800 text-white cursor-move"
          style={{
            left: node.position.x,
            top: node.position.y,
            zIndex: node.zIndex
          }}
          onMouseDown={(e) => handleMouseDown(e, node.id)}
        >
          <div className="p-4 space-y-4">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              {node.type === "text-to-text" ? (
                <Type className="w-4 h-4" />
              ) : (
                <ImageIcon className="w-4 h-4" />
              )}
              {node.type}
            </div>
            
            <div className="space-y-2">
              {node.content.input && (
                <div className="p-2 bg-gray-800 rounded text-sm">
                  {node.content.input}
                </div>
              )}
              
              {node.content.output && (
                <div className="p-2 bg-gray-800 rounded text-sm">
                  {node.content.output}
                </div>
              )}
              
              {node.content.image && (
                <div className="relative h-[200px] rounded overflow-hidden">
                  <Image
                    src={node.content.image}
                    alt="Generated image"
                    fill
                    className="object-cover"
                  />
                </div>
              )}
            </div>

            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                size="icon"
                className="bg-gray-800 border-gray-700 hover:bg-gray-700"
              >
                <Redo className="w-4 h-4" />
              </Button>
              
              <Button className="bg-teal-500 hover:bg-teal-600">
                Generate
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  )
}