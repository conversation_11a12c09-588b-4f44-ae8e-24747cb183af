// "use client";

// import { cn } from "@/lib/utils";
// import ExampleWrapper from "./ExampleWrapper";
// import { motion, HTMLMotionProps } from "framer-motion";
// import React from "react";


// export const Drag = React.forwardRef<
// HTMLDivElement,
// HTMLMotionProps<"span">
// >(({ className, ...props }, ref) => (
// <motion.span
//             initial={{ opacity: 1, scale: 0.8 }}
//             animate={{
//               opacity: 0.8,
//               transition: { delay: 0.25, duration: 0.1 },
//               scale: 1,
//             }}
//             whileHover={{ scale: 1.05 }}
//             whileDrag={{ scale: 1.25, transition: { duration:2 } }}
//             key={Math.random()}

//             className= {cn("cursor-pointer font-semibold", className)}
//             drag
//             {...props}
//             ref={ref}
//           >
//             {props.children}
//     </motion.span>
// ))
