"use client"

import { motion } from "framer-motion";

export const BounceCard = ({ className, children }) => {
    return (
      <motion.div
        whileHover={{ scale: 0.95, rotate: "-1deg" }}
        className={`group relative min-h-[300px] cursor-pointer overflow-hidden rounded-2xl bg-slate-100 p-8 ${className}`}
      >
        {children}
      </motion.div>
    );
  };
  
export const CardTitle = ({ children }) => {
    return (
      <h3 className="mx-auto bg-gradient-to-r from-amber-500 to-pink-500 bg-clip-text text-transparent text-center text-3xl font-semibold">{children}</h3>
    );
  };