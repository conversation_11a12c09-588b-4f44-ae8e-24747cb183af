import React from 'react';
import Goo from 'gooey-react'
import { Radar, Search } from 'lucide-react';

interface LoaderProps {
  size?: string; // Dynamically control size
  speed?: string; // Dynamically control speed
}

const Bot: React.FC<LoaderProps> = ({ size = '20%', speed = '3.5s' }) => {
  return (
    <div
      className="loaders motion-preset-seesaw "
      style={{
        '--loader-size': size,
        '--speed': speed,
      } as React.CSSProperties}
    >
      <Radar style={{ width: '20%', height: '20%' }} size={48} className="absolute opacity-70 z-10 top-[40%] left-[40%] motion-preset-pulse  motion-rotate-in-90 -rotate-90" />
      <Goo> 
      {[...Array(10)].map((_, idx) => (
        <span
          key={idx}
          style={{
            '--idx': idx,
          } as React.CSSProperties}
        ></span>
      ))}
      </Goo>
    </div>
  );
};

export default Bot;