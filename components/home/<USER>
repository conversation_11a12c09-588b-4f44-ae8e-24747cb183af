// "use client";

// import ExampleWrapper from "./ExampleWrapper";
// import { motion } from "framer-motion";
// import Goo from "gooey-react";
// import { useRef, useState } from "react";
// import {Drag} from "./DragElement";
// import { Skeleton } from "../ui/skeleton";
// import { Card } from "../ui/card";
// import { Button } from "../ui/button";
// import { ArrowRight, ImageIcon, Redo, Type } from "lucide-react";
// import Image from "next/image";

// interface Node {
//   id: string
//   type: "text-to-text" | "text-to-image" | "image-to-image"
//   position: { x: number; y: number }
//   content: {
//     input?: string
//     output?: string
//     image?: string
//   }
//   zIndex: number
// }


// export default function DragHomeWiget( {
//   children,
// }: {
//   children?: React.ReactNode
// }) {
//   const constraintsRef = useRef(null);
//   const traintsRef = useRef(null);
//   const [nodes, setNodes] = useState<any>([
//     {
//       id: "node1",
//       type: "text-to-text",
//       position: { x: 20, y: 20 },
//       content: {
//         input: "Write a description of a cozy house",
//         output: "The living room is a cozy, inviting space filled with warmth and character. Soft, plush furnishings in earthy tones create a comfortable atmosphere, while a crackling fireplace serves as the room's focal point. lluminating the carefully curated decor that includes family photos, vintage books, and handmade quilts."
//       },
//       zIndex: 1
//     },
//     {
//       id: "node2",
//       type: "text-to-image",
//       position: { x: 200, y: 300 },
//       content: {
//         input: "A cozy craftsman house with a porch",
//         image: "/placeholder.svg?height=200&width=300"
//       },
//       zIndex: 1
//     }
//   ])
  
//   return (
//     <ExampleWrapper
//     >
//       <div
//         ref={constraintsRef}
//         className="w-full h-full flex gap-8"
//       >
//         <svg
//         xmlns="http://www.w3.org/2000/svg"
//         version="1.1"
//         className="absolute"
//         >
//         <defs>
//           <filter id="goo">
//             <feGaussianBlur
//               in="SourceGraphic"
//               stdDeviation="12"
//               result="blur"
//             />
//             <feColorMatrix
//               in="blur"
//               mode="matrix"
//               values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 20 -10"
//               result="goo"
//             />
//             <feComposite in="SourceGraphic" in2="goo" operator="atop" />
//           </filter>
//         </defs>
//       </svg>
//         <motion.div
//         style={{
//           filter: "url(#goo)",
//         }}
//         layout
//         className="flex"
//       >

//          {nodes.map((node) => (
//           <Drag key={node} ref={constraintsRef} >
//           <Card 
//           key={node.id}
//           ref={constraintsRef}
//           className="absolute w-[240px] bg-gray-900 border-gray-800 text-white cursor-move"
//           // onMouseDown={(e) => handleMouseDown(e, node.id)}
//         >
//           <div className="p-4 space-y-2">
//             <div className="flex items-center gap-2 text-sm text-gray-400">
//               {node.type === "text-to-text" ? (
//                 <Type className="w-4 h-4" />
//               ) : (
//                 <ImageIcon className="w-4 h-4" />
//               )}
//               {node.type}
//             </div>
            
//             <div className="space-y-2">
//               {node.content.input && (
//                 <div className="p-2 bg-gray-800 rounded text-sm">
//                   {node.content.input}
//                 </div>
//               )}
              
//               {node.content.output && (
//                 <div className="p-2 bg-gray-800 rounded text-sm">
//                   {node.content.output}
//                 </div>
//               )}
              
//               {node.content.image && (
//                 <div className="relative h-[200px] rounded overflow-hidden">
//                   <Image src="/mailly.png" fill alt="email sent image" />
//                 </div>
//               )}
//             </div>

//             <div className="flex justify-between items-center">
//               <Button
//                 variant="outline"
//                 size="icon"
//                 className="bg-gray-800 border-gray-700 hover:bg-gray-700"
//               >
//                 <Redo className="w-4 h-4" />
//               </Button>
              
//               <Button className="bg-teal-500 hover:bg-teal-600">
//                 Generate
//                 <ArrowRight className="w-4 h-4 ml-2" />
//               </Button>
//             </div>
//           </div>
//         </Card>
//         </Drag>
//       ))}
//         {/* <Drag className="p-10" constraintsRef={constraintsRef}>
//         hi
//         </Drag> */}
//         {/* <Drag className="h-fit" constraintsRef={constraintsRef}>
//               yo
//         </Drag> */}
//         </motion.div>
//       </div>
      
//     </ExampleWrapper>
//   );
// }