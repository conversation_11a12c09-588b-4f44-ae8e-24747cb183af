
const SVGComponent = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" 
    width={640} 
    height={520} 
    viewBox="0 0 640 520" 
    textRendering="geometricPrecision" 
    shapeRendering="geometricPrecision" 
    style={{ background: "#00000000", 
        transform: "rotate(90deg)", 
    }} 
    {...props} >
      <style>
        {
          "\n @keyframes a43_t{0%{transform:translate(-135px,257.0295px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(205px,257.0295px)}}@keyframes a43_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a44_t{0%{transform:translate(-139px,253.1745px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(213px,253.1745px)}}@keyframes a44_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a45_t{0%{transform:translate(-101px,239px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(229px,239px)}}@keyframes a45_o{0%{opacity:.8;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a46_t{0%{transform:translate(-140px,208px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(222px,208px)}}@keyframes a46_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a47_t{0%{transform:translate(-140px,120.41px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(228.122px,120.41px)}}@keyframes a47_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes line-right-5_o{0%{opacity:.1;animation-timing-function:cubic-bezier(.55,.055,.675,.19)}50%{opacity:.2;animation-timing-function:cubic-bezier(.215,.61,.355,1)}to{opacity:.1}}@keyframes line-right-4_o{0%{opacity:.1;animation-timing-function:cubic-bezier(.55,.055,.675,.19)}50%{opacity:.2;animation-timing-function:cubic-bezier(.215,.61,.355,1)}to{opacity:.1}}@keyframes line-right-3_o{0%{opacity:.1;animation-timing-function:cubic-bezier(.55,.055,.675,.19)}50%{opacity:.2;animation-timing-function:cubic-bezier(.215,.61,.355,1)}to{opacity:.1}}@keyframes line-right-2_o{0%{opacity:.1;animation-timing-function:cubic-bezier(.55,.055,.675,.19)}50%{opacity:.2;animation-timing-function:cubic-bezier(.215,.61,.355,1)}to{opacity:.1}}@keyframes line-right-1_o{0%{opacity:.1;animation-timing-function:cubic-bezier(.55,.055,.675,.19)}50%{opacity:.2;animation-timing-function:cubic-bezier(.215,.61,.355,1)}to{opacity:.1}}@keyframes a48_t{0%{transform:translate(719.5px,329.5px) scaleX(-1) translate(-65px,-79.95px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(349.5px,329.5px) scaleX(-1) translate(-65px,-79.95px)}}@keyframes a48_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a49_t{0%{transform:translate(734.5px,299.5px) scaleX(-1) translate(-65px,-41px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(364.5px,299.5px) scaleX(-1) translate(-65px,-41px)}}@keyframes a49_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a50_t{0%{transform:translate(704.5px,269.5px) scaleX(-1) translate(-65px,-30px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(384.5px,269.5px) scaleX(-1) translate(-65px,-30px)}}@keyframes a50_o{0%{opacity:.8;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}@keyframes a51_t{0%{transform:translate(724.5px,238.5px) scaleX(-1) translate(-65px,-30px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(364.5px,238.5px) scaleX(-1) translate(-65px,-44px)}}@keyframes a52_t{0%{transform:translate(719.5px,208.5px) scaleX(-1) translate(-65px,-84.8329px);animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6666%,to{transform:translate(355.579px,208.5px) scaleX(-1) translate(-65px,-84.8329px)}}@keyframes a52_o{0%{opacity:1;animation-timing-function:cubic-bezier(.645,.045,.355,1)}66.6667%,to{opacity:0}}\n  "
        }
      </style>
      <defs>
        <linearGradient
          id="Gradient-2"
          x1={320}
          y1={0}
          x2={320}
          y2={520}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.25} />
          <stop offset={1} stopColor="#fff" stopOpacity={0.15} />
        </linearGradient>
        <linearGradient
          id="Gradient-4"
          x1={476}
          y1={68}
          x2={564.249}
          y2={-19.751}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" />
          <stop offset={0.2} stopColor="#fff" stopOpacity={0.1} />
          <stop offset={0.4} stopColor="#fff" stopOpacity={0} />
          <stop offset={0.6} stopColor="#fff" stopOpacity={0} />
          <stop offset={0.8} stopColor="#fff" stopOpacity={0} />
          <stop offset={1} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-5"
          x1={136}
          y1={448}
          x2={224.249}
          y2={360.249}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" />
          <stop offset={0.2} stopColor="#fff" stopOpacity={0.1} />
          <stop offset={0.4} stopColor="#fff" stopOpacity={0} />
          <stop offset={0.6} stopColor="#fff" stopOpacity={0} />
          <stop offset={0.8} stopColor="#fff" stopOpacity={0} />
          <stop offset={1} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-6"
          x1={120}
          y1={60}
          x2={0}
          y2={60}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.84} />
          <stop offset={0.385} stopColor="#fff" stopOpacity={0.28} />
          <stop offset={0.7} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-9"
          x1={0}
          y1={24.75}
          x2={98.319}
          y2={24.75}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.84} />
          <stop offset={0.385} stopColor="#fff" stopOpacity={0.28} />
          <stop offset={0.7} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-11"
          x1={16}
          y1={12.5}
          x2={0}
          y2={12.5}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.84} />
          <stop offset={0.875} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-13"
          x1={16}
          y1={12.5}
          x2={0}
          y2={12.5}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.84} />
          <stop offset={0.875} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-14"
          x1={199.5}
          y1={268.5}
          x2={-0.5}
          y2={268.5}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.5} />
          <stop offset={1} stopColor="#fff" />
        </linearGradient>
        <linearGradient
          id="Gradient-15"
          x1={199.5}
          y1={208.5}
          x2={-0.5}
          y2={208.5}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.5} />
          <stop offset={1} stopColor="#fff" />
        </linearGradient>
        <linearGradient
          id="Gradient-16"
          x1={199.5}
          y1={148.5}
          x2={-0.5}
          y2={148.5}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.5} />
          <stop offset={1} stopColor="#fff" />
        </linearGradient>
        <linearGradient
          id="Gradient-17"
          x1={130}
          y1={60}
          x2={0}
          y2={60}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#3ceeae" />
          <stop offset={0.425} stopColor="#3ceeae" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-18"
          x1={130}
          y1={30}
          x2={0}
          y2={30}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#3dc5fa" />
          <stop offset={0.425} stopColor="#3dc5fa" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-19"
          x1={120}
          y1={30}
          x2={0}
          y2={30}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" />
          <stop offset={0.425} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-20"
          x1={130}
          y1={30}
          x2={0}
          y2={30}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#3ceeae" />
          <stop offset={0.425} stopColor="#3ceeae" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-21"
          x1={130}
          y1={60}
          x2={0}
          y2={60}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#9d72ff" />
          <stop offset={0.425} stopColor="#9d72ff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-22"
          x1={440}
          y1={269}
          x2={640}
          y2={269}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.5} />
          <stop offset={1} stopColor="#fff" />
        </linearGradient>
        <linearGradient
          id="Gradient-23"
          x1={440}
          y1={209}
          x2={640}
          y2={209}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.5} />
          <stop offset={1} stopColor="#fff" />
        </linearGradient>
        <linearGradient
          id="Gradient-24"
          x1={440}
          y1={149}
          x2={640}
          y2={149}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" stopOpacity={0.5} />
          <stop offset={1} stopColor="#fff" />
        </linearGradient>
        <linearGradient
          id="Gradient-25"
          x1={130}
          y1={60}
          x2={0}
          y2={60}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#9d72ff" />
          <stop offset={0.425} stopColor="#9d72ff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-26"
          x1={130}
          y1={30}
          x2={0}
          y2={30}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#3ceeae" />
          <stop offset={0.425} stopColor="#3ceeae" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-27"
          x1={90}
          y1={30}
          x2={0}
          y2={30}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#fff" />
          <stop offset={0.425} stopColor="#fff" stopOpacity={0} />
        </linearGradient>
        <linearGradient
          id="Gradient-28"
          x1={130}
          y1={30}
          x2={0}
          y2={30}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#3dc5fa" />
          <stop offset={0.425} stopColor="#3dc5fa" stopOpacity={0} />
        </linearGradient>
        <mask
          id="mask0_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path fill="url(#Gradient-3)" d="M0 0h640v520H0z" />
        </mask>
        <mask
          id="mask5_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            d="M0 389c120 0 79.5-120 199.5-120"
            stroke="#000"
            strokeWidth={1}
            fill="none"
          />
        </mask>
        <mask
          id="mask6_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            d="M0 328.5c120 0 79.5-60 199.5-60"
            stroke="#000"
            strokeWidth={1}
            fill="none"
          />
        </mask>
        <mask
          id="mask"
          style={{
            maskType: "alpha",
          }}
        >
          <rect
            width={219.125}
            height={1}
            fill="#fff"
            rx={0}
            transform="translate(-10.624 268.25)"
          />
        </mask>
        <mask
          id="mask8_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            d="M0 208.5c120 0 79.5 60 199.5 60"
            stroke="#fff"
            strokeWidth={1}
            fill="none"
          />
        </mask>
        <mask
          id="mask9_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            fillRule="evenodd"
            strokeWidth={1}
            clipRule="evenodd"
            d="M64.772 167.45C49.868 156.237 29.954 148.75 0 148.75v-.5c30.046 0 50.07 7.513 65.072 18.8 14.988 11.276 24.945 26.306 34.876 41.296l.01.016c9.941 15.005 19.857 29.968 34.77 41.188 14.904 11.213 34.818 18.7 64.772 18.7v.5c-30.046 0-50.07-7.513-65.072-18.8-14.988-11.276-24.945-26.306-34.876-41.296l-.01-.016c-9.942-15.005-19.857-29.968-34.77-41.188"
          />
        </mask>
        <mask
          id="mask10_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            d="M639.5 389.5c-120 0-79.5-120-199.5-120"
            stroke="#000"
            strokeWidth={1}
            fill="none"
          />
        </mask>
        <mask
          id="mask11_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            d="M639.5 329c-120 0-79.5-60-199.5-60"
            stroke="#000"
            strokeWidth={1}
            fill="none"
          />
        </mask>
        <mask
          id="mask12_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <rect
            width={200.125}
            height={2}
            fill="#fff"
            rx={0}
            transform="translate(439.375 268.75)"
          />
        </mask>
        <mask
          id="mask13_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            d="M639.5 209c-120 0-79.5 60-199.5 60"
            stroke="#fff"
            strokeWidth={2}
            fill="none"
          />
        </mask>
        <mask
          id="mask14_1362_541"
          style={{
            maskType: "alpha",
          }}
        >
          <path
            fillRule="evenodd"
            strokeWidth={2}
            clipRule="evenodd"
            d="M574.728 167.95c14.904-11.213 34.818-18.7 64.772-18.7v-.5c-30.046 0-50.07 7.513-65.072 18.8-14.988 11.276-24.945 26.306-34.876 41.296l-.01.016c-9.941 15.005-19.857 29.968-34.77 41.188-14.904 11.213-34.818 18.7-64.772 18.7v.5c30.046 0 50.07-7.513 65.072-18.8 14.988-11.276 24.945-26.306 34.876-41.296l.01-.016c9.941-15.005 19.857-29.968 34.77-41.188"
          />
        </mask>
      </defs>
      <g id="lights-left" transform="translate(-140 120.41)">
        <path
          id="line-left-5"
          d="M0 388.5c120 0 79.5-120 199.5-120"
          stroke="url(#Gradient-14)"
          strokeWidth={0.5}
          opacity={0.1}
          transform="translate(140 -120.41)"
          style={{
            animation: "2s linear 1.1s infinite both line-left-5_o",
          }}
          fill="none"
          strokeDasharray="1,1.2"
        />
        <path
          id="line-left-4"
          d="M0 328.5c120 0 79.5-60 199.5-60"
          stroke="url(#Gradient-14)"
          strokeWidth={0.5}
          opacity={0.2}
          fill="none"
          transform="translate(140 -120.41)"
          style={{
            animation: "2s linear 1.3s infinite both line-left-4_o",
          }}
          strokeDasharray="1,1"
        />
        <path
          id="line-left-3"
          d="M0 268.5h199.5"
          stroke="url(#Gradient-14)"
          strokeWidth={0.5}
          opacity={0.15}
          transform="translate(140 -120.41)"
          fill="none"
          strokeDasharray="1,1"
          style={{
            animation: "2s linear 1.7s infinite both line-left-3_o",
          }}
        />
        <path
          id="line-left-2"
          d="M0 208.5c120 0 79.5 60 199.5 60"
          stroke="url(#Gradient-15)"
          strokeWidth={0.5}
          opacity={0.2}
          fill="none"
          transform="translate(140 -120.41)"
          style={{
            animation: "2s linear .6s infinite both line-left-2_o",
          }}
          strokeDasharray="1,1"
        />
        <path
          id="line-left-1"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M64.772 167.45C49.868 156.237 29.954 148.75 0 148.75v-.5c30.046 0 50.07 7.513 65.072 18.8 14.988 11.276 24.945 26.306 34.876 41.296l.01.016c9.941 15.005 19.857 29.968 34.77 41.188 14.904 11.213 34.818 18.7 64.772 18.7v.5c-30.046 0-50.07-7.513-65.072-18.8-14.988-11.276-24.945-26.306-34.876-41.296l-.01-.016c-9.942-15.005-19.857-29.968-34.77-41.188"
          fill="url(#Gradient-16)"
          strokeDasharray="1,1"
          opacity={0.3}
          transform="translate(140 -120.41)"
          style={{
            animation: "2s linear 1s infinite both line-left-1_o",
          }}
        />
        <g
          id="light-left-green-2"
          mask="url(#mask5_1362_541)"
          transform="translate(140 -120.41)"
        >
          <path
            fill="url(#Gradient-17)"
            transform="translate(-135 257.03)"
            style={{
              animation:
                "3s linear 3s infinite both a43_t,3s linear 3s infinite both a43_o",
            }}
            d="M0 0h130v143.941H0z"
          />
        </g>
        <g
          id="light-left-blue"
          mask="url(#mask6_1362_541)"
          transform="translate(140 -120.41)"
        >
          <path
            fill="url(#Gradient-18)"
            transform="translate(-139 253.174)"
            style={{
              animation:
                "3s linear 2.5s infinite both a44_t,3s linear 2.5s infinite both a44_o",
            }}
            d="M0 0h130v91.651H0z"
          />
        </g>
        <g
          id="light-left-white"
          mask="url(#mask)"
          transform="translate(140 -120.41)"
        >
          <path
            opacity={0.8}
            fill="url(#Gradient-19)"
            transform="translate(-101 239)"
            style={{
              animation:
                "3s linear 2s infinite both a45_t,3s linear 2s infinite both a45_o",
            }}
            d="M0 0h91v60H0z"
          />
        </g>
        <g
          id="light-left-green-1"
          mask="url(#mask8_1362_541)"
          transform="translate(140 -120.41)"
        >
          <path
            fill="url(#Gradient-20)"
            transform="translate(-140 208)"
            style={{
              animation:
                "3s linear 1.5s infinite both a46_t,3s linear 1.5s infinite both a46_o",
            }}
            d="M0 0h130v60H0z"
          />
        </g>
        <g
          id="light-left-purple"
          mask="url(#mask9_1362_541)"
          transform="translate(140 -120.41)"
        >
          <path
            fill="url(#Gradient-21)"
            transform="translate(-140 120.41)"
            style={{
              animation:
                "3s linear 1s infinite both a47_t,3s linear 1s infinite both a47_o",
            }}
            d="M0 0h130v175.18H0z"
          />
        </g>
      </g>
      <g id="lights-right" transform="translate(300 123.668)">
        <path
          id="line-right-5"
          d="M639.5 389c-120 0-79.5-120-199.5-120"
          stroke="url(#Gradient-22)"
          fill="none"
          strokeWidth={0.5}
          opacity={0.5}
          transform="translate(-440 -123.667)"
          style={{
            animation: "2s linear .9s infinite both line-right-5_o",
          }}
          strokeDasharray="1,1"
        />
        <path
          id="line-right-4"
          d="M639.5 329c-120 0-79.5-60-199.5-60"
          stroke="url(#Gradient-22)"
          fill="none"
          strokeWidth={0.5}
          opacity={0.4}
          transform="translate(-440 -123.667)"
          style={{
            animation: "2s linear 1.6s infinite both line-right-4_o",
          }}
          strokeDasharray="1,1"
        />
        <path
          id="line-right-3"
          d="M639.5 269H440"
          stroke="url(#Gradient-22)"
          fill="none"
          strokeWidth={0.5}
          opacity={0.2}
          transform="translate(-440 -123.667)"
          style={{
            animation: "2s linear .5s infinite both line-right-3_o",
          }}
          strokeDasharray="1,1"
        />
        <path
          id="line-right-2"
          d="M639.5 209c-120 0-79.5 60-199.5 60"
          stroke="url(#Gradient-23)"
          fill="none"
          strokeWidth={0.5}
          opacity={0.3}
          transform="translate(-440 -123.667)"
          style={{
            animation: "2s linear 2s infinite both line-right-2_o",
          }}
          strokeDasharray="1,1"
        />
        <path
          id="line-right-1"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M574.728 167.95c14.904-11.213 34.818-18.7 64.772-18.7v-.5c-30.046 0-50.07 7.513-65.072 18.8-14.988 11.276-24.945 26.306-34.876 41.296l-.01.016c-9.941 15.005-19.857 29.968-34.77 41.188-14.904 11.213-34.818 18.7-64.772 18.7v.5c30.046 0 50.07-7.513 65.072-18.8 14.988-11.276 24.945-26.306 34.876-41.296l.01-.016c9.941-15.005 19.857-29.968 34.77-41.188"
          fill="url(#Gradient-24)"
          opacity={0.2}
          transform="translate(-440 -123.667)"
          style={{
            animation: "2s linear 1s infinite both line-right-1_o",
          }}
        />
        <g
          id="light-right-purple-2"
          mask="url(#mask10_1362_541)"
          transform="translate(-440 -123.667)"
        >
          <path
            fill="url(#Gradient-25)"
            transform="matrix(-1 0 0 1 784.5 249.55)"
            style={{
              animation:
                "3s linear 2.8s infinite both a48_t,3s linear 2.8s infinite both a48_o",
            }}
            d="M0 0h130v159.9H0z"
          />
        </g>
        <g
          id="light-right-green"
          mask="url(#mask11_1362_541)"
          transform="translate(-440 -123.667)"
        >
          <path
            fill="url(#Gradient-26)"
            transform="matrix(-1 0 0 1 799.5 258.5)"
            style={{
              animation:
                "3s linear 1.5s infinite both a49_t,3s linear 1.5s infinite both a49_o",
            }}
            d="M0 0h130v82H0z"
          />
        </g>
        <g
          id="light-right-white"
          mask="url(#mask12_1362_541)"
          transform="translate(-440 -123.667)"
        >
          <path
            opacity={0.8}
            fill="url(#Gradient-27)"
            transform="matrix(-1 0 0 1 769.5 239.5)"
            style={{
              animation:
                "3s linear 2s infinite both a50_t,3s linear 2s infinite both a50_o",
            }}
            d="M0 0h110v60H0z"
          />
        </g>
        <g
          id="light-right-blue"
          mask="url(#mask13_1362_541)"
          transform="translate(-440 -123.667)"
        >
          <path
            fill="url(#Gradient-28)"
            transform="matrix(-1 0 0 1 789.5 208.5)"
            style={{
              animation:
                "3s linear 1s infinite both a51_t,3s linear 1s infinite both a51_o,3s linear 1s infinite both a51_h",
            }}
            d="M0 0h130v60H0z"
          />
        </g>
        <g
          id="light-right-purple-1"
          mask="url(#mask14_1362_541)"
          transform="translate(-440 -123.667)"
        >
          <path
            fill="url(#Gradient-25)"
            transform="matrix(-1 0 0 1 784.5 123.667)"
            style={{
              animation:
                "3s linear infinite both a52_t,3s linear infinite both a52_o",
            }}
            d="M0 0h130v175.833H0z"
          />
        </g>
      </g>
    </svg>
  );
  export default SVGComponent;