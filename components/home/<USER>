"use client";

import React from 'react';
import ButtonRotatingBackgroundGradient from './moderngradiant';
import MorphingText from './morphingtext';
import AnimatedDiv from './AnimatedDiv';
import GetStarted from '../GetStarted';
import Link from 'next/link';
import { Button } from '../ui/button';
import { Icons } from '../Icons';
import Image from 'next/image';


const Hero = () => {
    return (
        <section className="text-white w-full h-fit bg-transparent grid place-content-center ">
        <div className="absolute -z-10 opacity-60 -bottom-40 left-0 right-0 top-0 bg-[linear-gradient(to_right,#808BF7_1px,transparent_1px),linear-gradient(to_bottom,#808BF7_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_0%,#000_70%,transparent_100%)]"></div>
        <div className="flex flex-col items-center justify-center mt-40 md:mt-[115px] ">
          <div className='px-4'>
          <ButtonRotatingBackgroundGradient>
            Turn Ideas into Revenue by Bridging Human-AI Collaboration
          </ButtonRotatingBackgroundGradient>
          </div>
          <div className="relative text-4xl"> 
            <div className="max-sm:hidden absolute top-8 -left-16 md:-left-28 md:-top-12 flex -rotate-45">
              <div className=" motion-preset-stretch motion-duration-2000">
              <Image
              src="https://pub-9991e1a416ba46d0a4bef06e046435a1.r2.dev/coin-png.png"
              width={64}
              height={64}
              alt="Coin"
              />
              </div>
            </div>
            <div className="max-sm:hidden absolute -top-2 -right-24 rotate-12 motion-preset-spin motion-duration-2000">
            <Image
              src="https://pub-8e6c4510cd754e5f87d370aeac8e4579.r2.dev/chat-gpt-logo.svg"
              width={48}
              height={48}
              alt="open ai"
              />
            </div>

            <div className="hidden md:block absolute text-2xl motion-preset-pulse-sm rotate-12 motion-duration-2000 -top-24 -right-8 md:-right-24 md:top-1/2 -translate-y-1/2  ">
             no-code
            </div>

            <div className="hidden md:block absolute text-2xl max-md:-top-28 md:bottom-20 -left-10 md:-left-32 -rotate-12 motion-opacity-loop-50 motion-duration-[1s]">
             LoRAs
            </div>
            <div className="select-none text-center md:w-[40vw]">
              <h1 className="mt-6 mb-16 md:mb-10 w-full text-2xl text-gray-200">
                The Next-Gen AI platform for{" "}
                <span><MorphingText className='text-[35pt] lg:text-[5rem] mt-4' texts={[
                    "Enthusiasts",
                    "Developers",
                    "Creators",
                    "Businesses",
                    "Researchers",
                  ]}/></span>
                {/* to efficiently Create, Explore, and Monetize AI. */}
                <span className='w-full'><MorphingText className='font-normal text-[17pt] text-center h-16 lg:text-[1.5rem]' texts={[
                    "To learn, and generate text, image, audio, video at one place without any subscription.",
                    "To develop and optimize AI solutions for Free with live preview and no-code.",
                    "Fastest and affordable way to turn any AI use case into reality.",
                    "To collaborate, create and empower your team with cutting edge AI tools to drive growth.",
                    "To access all models, compare and research seamlessly.",
                  ]}/></span>
              </h1>
            </div>                
          </div>
          
          <AnimatedDiv>
          <div className="mt-4 flex items-center gap-x-6 sm:gap-x-8">
            <GetStarted />
            <Link href="/community">
              <Button
                className=" py-7 motion-preset-seesaw text-xs lg:text-lg font-extrabold text-primary-muted bg-gradient-to-r from-amber-500 to-pink-500 text-white rounded z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none"
              >
                LEARN MORE →
              </Button>
            </Link>
          </div>
          </AnimatedDiv>
          {/* <MorphingText className="my-20 md:my-10" texts={[
                    "Anything to Everything",
                    "Everthing to Anything",
                  ]}/> */}
          <Icons.herobg className="absolute -z-10 w-11/12 top-44"/>
        </div>
      </section>
    );
};

export default Hero;