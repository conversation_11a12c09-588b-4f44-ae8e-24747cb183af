import React from 'react';
import { cn } from "@/lib/utils"
import Goo from 'gooey-react';

interface ContainerProps {
  children?: React.ReactNode;
  className?: string;
  backgroundColor?: string;
}

const FeatureWidget: React.FC<ContainerProps> = ({
  children,
  backgroundColor = 'bg-purple-500', // Default color
  className,
}) => {
  return (
    <div className={cn("group z-10 backdrop-blur-lg relative w-auto cursor-pointer overflow-hidden bg-black/[0.3] transition-all duration-300 ease-in-out hover:-translate-y-1 rounded-md outline outline-4 outline-[#1C1B1A]/25", className)}>
      {children}
    </div>
  );
};

const FeatureBg: React.FC<ContainerProps> = ({
  className,
}) => {
  return (
      <div className={cn("absolute inset-0 -z-10 rounded-2xl opacity-0 blur-md transition-opacity duration-300 group-hover:opacity-50", className)}></div>
  );
};

export {FeatureWidget, FeatureBg};
