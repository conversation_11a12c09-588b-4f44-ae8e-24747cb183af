"use client";
import React, { useEffect, useState } from "react";
import { ChevronDown } from "lucide-react";
import { useForm, SubmitHandler } from "react-hook-form";
import { SpaceModal } from "@/components/SpaceModal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import BountyCard from "@/components/BountyCard";
import SearchBar from "@/components/ui/searchbar";
import { toast } from "sonner";
import Link from "next/link";
import { Icons } from "@/components/Icons";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { Plus } from "lucide-react";

const CreateBountyForm = () => {
  const [data, setData] = useState<Bounty[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [sortBy, setSortBy] = useState("");
  const [bountyStatus, setBountyStatus] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [userId, setUserId] = useState("");
  const [rupees, setRupees] = useState("");
  const [myBounties, setMyBounties] = useState<Bounty[]>([]);
  const [filteredBounties, setFilteredBounties] = useState<Bounty[]>([]);
  const [bountiesToShow, setBountiesToShow] = useState(6);
  const [currentBounty, setCurrentBounty] = useState<Bounty | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleTermsClick = () => {
    setTermsAccepted((prevState) => !prevState);
  };

  const handleShowMore = () => {
    setBountiesToShow((prev) => prev + 6);
  };

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
    watch,
  } = useForm<FormData>();

  const today = new Date().toISOString().split("T")[0];

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchBounties = async () => {
      try {
        console.log("try function");
        const res = await fetch(`/api2/bounties`);
        const getdata = await res.json();
        console.log("getData:", getdata);
        setData(getdata?.data);
      } catch (error) {
        //console.log("catch function");
        console.error("Error fetching bounties:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchBounties();
  }, []);

  //----------------- Fetching Logged in User -----------------
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`
        );
        const data = await res.json();

        if (!data) {
          setLoading(false);
          return;
        }

        setUser(data?.user);
        setUserId(data?.user?.id);
        setMyBounties(data?.user?.bounties || []);
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const onSubmit: SubmitHandler<FormData> = async (formData, event) => {
    formData.user = userId;

    try {
      const userCoinBalance = user?.coinBalance ?? 0;
      const estimatedPrice = parseFloat(formData.estimatedPrice);

      if (userCoinBalance < estimatedPrice) {
        toast.error("Insufficient balance. Please recharge your account.");
        return;
      }

      const formDataToSend = new FormData();
      formDataToSend.append("title", formData.title);
      formDataToSend.append("content", formData.content);
      formDataToSend.append("completionDate", formData.completionDate);
      formDataToSend.append("estimatedPrice", formData.estimatedPrice);
      formDataToSend.append("bountyType", formData.bountyType);
      formDataToSend.append("user", formData.user);

      formData.tags.forEach((tag) => {
        formDataToSend.append("tags", tag);
      });

      // Check if the form contains 'product_files'
      if (formData.product_files && formData.product_files.length > 0) {
        const fileFormData = new FormData();

        // Convert FileList to Array
        Array.from(formData.product_files).forEach((file) => {
          fileFormData.append("file", file);
        });

        try {
          const mediaRes = await fetch(
            `${process.env.NEXT_PUBLIC_SERVER_URL}/api/product_files`,
            {
              method: "POST",
              credentials: "include",
              body: fileFormData,
            }
          );

          const mediaData = await mediaRes.json();

          if (mediaRes.ok) {
            const uploadedFilesIds = mediaData.doc.id;
            formDataToSend.append("product_files", uploadedFilesIds);

            try {
              const bountyRes = await fetch(
                `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties`,
                {
                  method: "POST",
                  credentials: "include",
                  body: formDataToSend,
                }
              );

              const bountyData = await bountyRes.json();

              if (bountyRes.ok) {
                setIsModalOpen(false);
                toast.success(
                  "Bounty Created successfully and set for approval."
                );
                reset();
                setRupees("");
                window.location.reload();
              } else {
                console.error("Error:", bountyData);
                toast.error(bountyData.errors[0].message);
              }
            } catch (bountyError) {
              console.error("Error creating bounty:", bountyError);
              toast.error("Failed to create bounty. Please try again.");
            }
          } else {
            console.error("Error uploading media:", mediaData);
            toast.error("Media upload failed. Please try again.");
          }
        } catch (mediaError) {
          console.error("Media upload error:", mediaError);
          toast.error("Media upload failed. Please try again.");
        }
      } else {
        // Creating the new bounty without 'product_files'
        try {
          const res = await fetch(
            `${process.env.NEXT_PUBLIC_SERVER_URL}/api/bounties`,
            {
              method: "POST",
              credentials: "include",
              body: formDataToSend,
            }
          );

          const data = await res.json();

          if (res.ok) {
            setIsModalOpen(false);
            toast.success("Bounty Created Successfully and Set for approval.");
            reset();
            setRupees("");
            window.location.reload();
          } else {
            console.error("Error:", data);
            toast.error(data.errors[0].message);
          }
        } catch (error) {
          console.error("Error creating bounty:", error);
          toast.error("Failed to create bounty. Please try again.");
        }
      }
    } catch (err) {
      console.log(err);
      toast.error("Bounty Creation Failed, Please try again");
    }
  };

  const handleValueChange = (value: string) => {
    setBountyStatus(value);
    if (value === "myBounty") {
      setFilteredBounties(myBounties);
    } else {
      setFilteredBounties(data);
    }
  };

  useEffect(() => {
    const filtered =
      bountyStatus === "myBounty"
        ? myBounties
        : data
            .filter((bounty) => {
              if (
                selectedCategory !== "all" &&
                !bounty?.title
                  .toLowerCase()
                  .includes(selectedCategory.toLowerCase())
              ) {
                return false;
              }
              if (
                bountyStatus !== "all" &&
                bountyStatus &&
                bounty.status !== bountyStatus
              ) {
                return false;
              }
              if (
                searchQuery &&
                !bounty.title.toLowerCase().includes(searchQuery)
              ) {
                return false;
              }
              return true;
            })
            .sort((a, b) => {
              if (sortBy === "reward") {
                const priceA =
                  typeof a.estimatedPrice === "string"
                    ? parseFloat(a.estimatedPrice.replace("$", ""))
                    : a.estimatedPrice;
                const priceB =
                  typeof b.estimatedPrice === "string"
                    ? parseFloat(b.estimatedPrice.replace("$", ""))
                    : b.estimatedPrice;
                return priceB - priceA;
              }
              if (sortBy === "posting_date") {
                const dateA = new Date(a.createdAt);
                const dateB = new Date(b.createdAt);
                return dateB.getTime() - dateA.getTime();
              }
              return 0;
            });

    setFilteredBounties(filtered);
  }, [data, selectedCategory, bountyStatus, searchQuery, sortBy]);

  const tagOptions = [
    { value: "ai/ml", label: "AI/ML" },
    { value: "prompt", label: "Prompt" },
    { value: "finetuning", label: "Finetuning" },
    { value: "blockchain", label: "Blockchain" },
    { value: "frontend", label: "Frontend" },
    { value: "backend", label: "Backend" },
    { value: "ui/ux", label: "UI/UX" },
    { value: "fullstack", label: "Fullstack" },
    { value: "design", label: "Design" },
    { value: "testing", label: "Testing" },
    { value: "assets", label: "Assets" },
    { value: "bug", label: "Bug" },
    { value: "chatbot", label: "Chatbot" },
  ];

  const handleInputChange = (event: any) => {
    const value = event.target.value;
    setRupees(value);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query.toLowerCase());
  };

  return (
    <>
      {/* ---------------------- Hero Section ---------------------------- */}

      <div className="w-11/12 mx-auto py-10 md:py-20 md:pt-28 rounded-md overflow-hidden flex flex-col items-center justify-center">
        <BackgroundBeams className="flex items-center flex-col justify-center px-2 md:px-10 w-full h-full"></BackgroundBeams>
        <h2 className="text-white text-4xl md:text-6xl lg:w-[80%] font-bold text-center pt-7 md:pt-0">
          Generative AI Bounties: Where Creativity Meets Challenges
        </h2>
        <p className="text-white md:text-lg lg:max-w-[80%] mt-6 text-center ">
          Be part of a vibrant Bounty Program where every challenge is a chance
          for innovation. Let&apos;s advance generative AI together!
        </p>
        <div className="flex flex-row items-center gap-4 mt-8">
          <Button
            size="lg"
            className="text-sm lg:text-xl font-semibold text-primary-muted bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent text-white rounded z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-2 md:py-3 px-4 md:px-6 dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
            onClick={() => {
              if (user) {
                setIsModalOpen(true);
                setIsEditMode(false);
                setCurrentBounty(null);
              } else {
                toast.error("Please login to create a bounty");
              }
            }}
          >
            Create a Bounty
            <Plus size={20} />
          </Button>

          <a
            className="py-2 md:py-3 px-4 md:px-6 text-sm lg:text-xl font-extrabold text-primary-muted bg-white text-indigo-600 rounded z-10 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-gray-500/20 focus:scale-[1.02] focus:opacity-[0.85] focus:shadow-none active:scale-100 active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none hover:cursor-pointer"
            href="https://discord.gg/kPkYbzMvN3"
            target="_blank"
            rel="noopener noreferrer"
          >
            Join Discord →
          </a>
        </div>
      </div>

      {/*------------------------ Space Modal----------------------------- */}

      <SpaceModal
        title={isEditMode ? "Edit Bounty" : "New Bounty"}
        description={
          isEditMode ? "Edit your bounty here" : "Create Your Bounty Here"
        }
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      >
        <div className="space-y-4 py-2 pb-4">
          <form
            onSubmit={handleSubmit(onSubmit)}
            encType="multipart/form-data"
            className="space-y-1 h-[400px] overflow-auto no-scrollbar"
          >
            <div className="py-2 col-span-12 lg:col-span-10">
              <label>
                Bounty Title : <sup className="text-red-500 font-bold">*</sup>
              </label>
              <input
                className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                placeholder="Name your bounty"
                type="text"
                {...register("title", {
                  required: "Title is required",
                  minLength: {
                    value: 5,
                    message: "Title must be at least 5 characters long",
                  },
                })}
              />
              {errors.title && (
                <p className="text-yellow-300 text-sm">
                  {errors.title.message}
                </p>
              )}
            </div>
            <div className="py-2 col-span-12 lg:col-span-10">
              <label>
                Bounty Description :{" "}
                <sup className="text-red-500 font-bold">*</sup>
              </label>
              <textarea
                className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                placeholder="Your bounty description"
                rows={5}
                {...register("content", {
                  required: "Description is required",
                  minLength: {
                    value: 10,
                    message: "Description must be at least 10 characters long",
                  },
                })}
              />
              {errors.content && (
                <p className="text-yellow-300 text-sm">
                  {errors.content.message}
                </p>
              )}
            </div>
            <div className="py-2 col-span-12 lg:col-span-10">
              <label>
                Bounty Completion Date :{" "}
                <sup className="text-red-500 font-bold">*</sup>
              </label>
              <input
                className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                placeholder="Bounty completion date"
                type="date"
                min={today}
                {...register("completionDate", {
                  required: "Completion date is required",
                })}
              />
              {errors.completionDate && (
                <p className="text-yellow-300 text-sm">
                  {errors.completionDate.message}
                </p>
              )}
            </div>
            <div className="py-2 col-span-12 lg:col-span-10">
              <label>
                Bounty Estimate Credits :{" "}
                <sup className="text-red-500 font-bold">*</sup>
              </label>
              <input
                className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                placeholder="Bounty amount in Coins"
                type="number"
                min={0}
                {...register("estimatedPrice", {
                  required: "Estimated price is required",
                  validate: (value) =>
                    Number(value) > 0 || "Price must be greater than zero",
                })}
                onChange={handleInputChange}
                disabled={isEditMode}
              />

              {errors.estimatedPrice && (
                <p className="text-yellow-300 text-sm">
                  {errors.estimatedPrice.message}
                </p>
              )}

              <p className="text-sm text-green-400">
                Estimated price in INR :{" "}
                {isEditMode ? currentBounty?.estimatedPrice : rupees}{" "}
              </p>
            </div>

            <div className="py-2 col-span-12 lg:col-span-10">
              <label>
                Bounty Related to:{" "}
                <sup className="text-red-500 font-bold">*</sup>
              </label>
              <Select
                {...register("bountyType", {
                  required: "Bounty type is required",
                })}
                onValueChange={(value) => {
                  setValue("bountyType", value);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Bounty Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="web">Web</SelectItem>
                  <SelectItem value="mobile">Mobile</SelectItem>
                  <SelectItem value="blockchain">Blockchain</SelectItem>
                  <SelectItem value="ai">AI</SelectItem>
                  <SelectItem value="ml">ML</SelectItem>
                </SelectContent>
              </Select>
              {errors.bountyType && (
                <p className="text-yellow-300">{errors.bountyType.message}</p>
              )}
            </div>

            <div className="py-2 col-span-12 lg:col-span-10">
              <label>Related Files :</label>
              <input
                className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                placeholder="Name your bounty"
                type="file"
                multiple
                {...register("product_files", {
                  validate: (value) => {
                    if (value && value.length > 5) {
                      return "You can upload up to 5 files";
                    }
                    return true;
                  },
                })}
              />
              {errors.product_files && (
                <p className="text-yellow-300">
                  {errors.product_files.message}
                </p>
              )}
            </div>

            <div className="py-2 col-span-12 lg:col-span-10">
              <label>
                Tags: <sup className="text-red-500 font-bold">*</sup>
              </label>
              <select
                className="border border-gray-200 px-2 py-1 focus-visible:ring-0 focus-visible:ring-transparent w-full bg-transparent outline-none"
                {...register("tags", {
                  required: "At least one tag is required",
                })}
                multiple
              >
                {/* Map through tagOptions to generate option elements */}
                {tagOptions.map((option, index) => (
                  <option key={index} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.tags && (
                <p className="text-yellow-300">{errors.tags.message}</p>
              )}
            </div>

            <div className=" py-2">
              <label
                htmlFor="termsAccepted"
                className="flex items-center gap-1 font-normal"
              >
                <input
                  type="checkbox"
                  id="termsAccepted"
                  name="termsAccepted"
                  {...register("termsAccepted", {
                    required: "You must accept the terms and conditions",
                  })}
                  className="h-4 w-4"
                />
                <label onClick={handleTermsClick}>
                  <Link
                    href={"/bountytandc"}
                    className=" hover:text-blue-500"
                    target="_blank"
                  >
                    Accept Terms and Conditions
                  </Link>
                </label>
                {errors.termsAccepted && (
                  <p className="text-yellow-300">
                    {errors.termsAccepted.message}
                  </p>
                )}
              </label>
            </div>

            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">{isEditMode ? "Update" : "Create"}</Button>
            </div>
          </form>
        </div>
      </SpaceModal>

      {/* ---------------------Search Bar and Filters ----------------*/}

      <div className="px-4 md:px-0 md:w-9/12 mx-auto py-10 md:py-20">
        <div className="flex flex-col md:flex-row items-center pb-5 justify-between gap-3 md:gap-8 w-full">
          <div className="w-full md:w-[50%] md:-mt-6">
            <SearchBar mainheader={false} onSearch={handleSearch} />
          </div>

          <div>
            <form className="flex gap-3 md:justify-end">
              <div className="py-2 col-span-12 lg:col-span-10">
                <Select onValueChange={(value) => setSelectedCategory(value)}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sort By Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Web">Web</SelectItem>
                    <SelectItem value="Mobile">Mobile</SelectItem>
                    <SelectItem value="Blockchain">Blockchain</SelectItem>
                    <SelectItem value="AI">AI</SelectItem>
                    <SelectItem value="ML">ML</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="py-2 col-span-12 lg:col-span-10">
                <Select onValueChange={(value) => setSortBy(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recommended">Recommended</SelectItem>
                    <SelectItem value="posting_date">Posting Date</SelectItem>
                    <SelectItem value="reward">Reward</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {user ? (
                <div className="py-2 col-span-12 lg:col-span-10">
                  <Select onValueChange={handleValueChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Bounties" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bounties</SelectItem>
                      <SelectItem value="myBounty">My Bounties</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                ""
              )}
            </form>
          </div>
        </div>

        <div className="grid gap-5 mt-5">
          {loading ? (
            <div className="fixed flex inset-0 items-center justify-center bg-black/[0.2] p-2 backdrop-blur-md z-50 ">
              <Icons.logo className="w-[15%] md:w-[10%] lg:w-[5%] fill-white animate-pulse" />
            </div>
          ) : filteredBounties.length === 0 ? (
            <p className="text-white text-center">No bounties present</p>
          ) : (
            filteredBounties.slice(0, bountiesToShow).map((bounty, index) => (
              <div key={index}>
                <BountyCard
                  key={index}
                  bounty={bounty}
                  index={index}
                  user={user}
                  bountyStatus={bountyStatus}
                />
              </div>
            ))
          )}
        </div>

        <div className="pt-12 text-lg flex justify-center">
          {filteredBounties.length > bountiesToShow && (
            <button
              className="hover:text-indigo-500 flex justify-center items-center gap-1 w-fit"
              onClick={handleShowMore}
            >
              Show More
              <ChevronDown />
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default CreateBountyForm;
