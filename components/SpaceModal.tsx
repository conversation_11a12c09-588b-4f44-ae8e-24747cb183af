"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { X } from "lucide-react";

interface ModalProps {
  title: string;
  description: string;
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  component?: string;
}

export const SpaceModal: React.FC<ModalProps> = ({
  title,
  description,
  isOpen,
  onClose,
  children,
  component,
}) => {
  const onChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={onChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>{description}</DialogDescription>
            {component !== "UserJourney" ? (
              <button
                onClick={() => onChange(false)}
                className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity"
              >
                <X size={15} />
              </button>
            ) : (
              ""
            )}
          </DialogHeader>
          <div className="first-letter:uppercase">{children}</div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
