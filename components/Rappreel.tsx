"use client";

import { TQ<PERSON>yValidator } from "@/lib/validators/query-validator";
import { Rapp, User } from "@/server/payload-types";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import axios from "axios";
import RappListing from "./RappListing";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface RappReelProps {
  title: string;
  subtitle?: string;
  href?: string;
  query: TQueryValidator;
  user: User;
  description?: string;
}

const FALLBACK_LIMIT = 4;

const RappReel = (props: RappReelProps) => {
  const { title, subtitle, href, query, user, description } = props;
  // const [user, setUser] = useState();
  const [data, setData] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef(null); // Reference for scrolling
  const [selectedItem, setSelectedItem] = useState(null); // State for selected item

  const handleScroll = (direction) => {
    if (scrollRef.current) {
      const scrollAmount = 300; // Adjust scroll amount as needed
      if (direction === "left") {
        scrollRef.current.scrollBy({ left: -scrollAmount, behavior: "smooth" });
      } else if (direction === "right") {
        scrollRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
      }
    }
  };

  const handleItemClick = (rapp) => {
    setSelectedItem(rapp); // Set the selected item
  };

  //----------------- Fetching Logged in User -----------------
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);

        const result = await axios.post(`/api2/rapps`, {
          query,
          limit: query.limit,
          sort: query.sort,
        });

        setData(result.data);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, []);

  const rapps = data ? data?.items : [];
  // console.log("rapps+++",rapps);

  let map: (Rapp | null)[] = [];

  if (rapps && rapps.length) {
    map = rapps;
  } else if (isLoading) {
    map = new Array<null>(query.limit ?? FALLBACK_LIMIT).fill(null);
  }

  return (
    <section className="md:py-14 py-10">
      <div className="flex mb-4 justify-between items-center gap-3">
        <div className="max-w-2xl lg:max-w-4xl lg:px-0 flex items-start flex-col">
          {title ? (
            <h3 className="text-3xl md:text-2xl font-bold sm:text-3xl">
              {title}
            </h3>
          ) : null}
          {subtitle ? (
            <p className="text-sm text-gray-400 max-w-sm md:max-w-md">{subtitle}</p>
          ) : null}

        </div>

        {href ? (
          <Link
            href="/marketplace"
            className="bg-white/20 hover:bg-white/40 text-white font-semibold py-1 px-2 md:px-3 rounded-full flex items-center gap-1 min-w-fit text-sm md:text-md"
          >
            View All
            <span className="mb-[2px]">&rarr;</span>
          </Link>
        ) : null}
      </div>

      <div className="relative border-t border-muted-foreground">
        <div className="mt-2 flex items-center w-full">
          {/* Scrollable Area */}
          <div
            className="grid grid-flow-col mt-2 px-1 gap-x-3 md:gap-x-4 w-full overflow-x-auto py-2"
            id="xscroll"
            ref={scrollRef}
          >
            {map.map((rapp, i) => (
              <div
                key={`rapp-${i}`}
                className=" rounded-lg shadow-lg w-[300px] cursor-pointer"
                onClick={() => handleItemClick(rapp)}
              >
                <RappListing rapp={rapp} index={i} user={user} />
              </div>
            ))}
          </div>

          {/* Left and Right Buttons */}
          <div className="absolute -bottom-12 right-2 flex gap-2 z-10">
            {/* Left Button */}
            <button
              onClick={() => handleScroll("left")}
              className="p-2 rounded-full shadow-md bg-white text-indigo-600 hover:opacity-90"
            >
              <ChevronLeft size={24} />
            </button>

            {/* Right Button */}
            <button
              onClick={() => handleScroll("right")}
              className="p-2 rounded-full shadow-md bg-white text-indigo-600 hover:opacity-90"
            >
              <ChevronRight size={24} />
            </button>
          </div>
        </div>

        {/* Selected Item Display */}
        {/* {selectedItem && (
          <div className="mt-4 p-4 bg-gray-100 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold">Selected Item</h3>
            <p>{JSON.stringify(selectedItem)}</p>
          </div>
        )} */}
      </div>
    </section>
  );
};

export default RappReel;
