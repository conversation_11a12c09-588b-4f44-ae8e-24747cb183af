"use client";

import React, { useRef, useState, useEffect } from "react";
import "tui-image-editor/dist/tui-image-editor.css";
import ImageEditor from "@toast-ui/react-image-editor";
import customTheme from "./customTheme";

interface ImageWithEditorProps {
  imageSrc: string;
}

const FullScreenImageEditor: React.FC<ImageWithEditorProps> = ({ imageSrc }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [editedImage, setEditedImage] = useState<string | null>(null);
  const editorRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false); // Loading state

  const [isMobile, setIsMobile] = useState(false);


  // Fallback image if imageSrc is null
  const fallbackImage = "/hero.png";
  const validImageSrc = imageSrc || fallbackImage;

  // Open modal only if imageSrc is valid
  const openModal = () => {
    if (!imageSrc) {
      alert("Please provide a valid image URL.");
      return;
    }
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  const handleSaveAndDownload = () => {
    if (editorRef.current) {
      const editorInstance = editorRef.current.getInstance();
      if (editorInstance) {
        try {
          const dataURL = editorInstance.toDataURL();
          setEditedImage(dataURL);

          // Trigger download
          const link = document.createElement("a");
          link.href = dataURL;
          link.download = "edited-image.png";
          link.click();
        } catch (error) {
          console.error("Error saving edited image:", error);
          alert("Failed to save the edited image.");
        }
      } else {
        console.error("Editor instance is not available.");
      }
    } else {
      console.error("Editor ref is not available.");
    }
    closeModal();
  };

  // Detect screen size and update `isMobile` state
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768); // Adjust the breakpoint as needed
    };

    // Initial check
    handleResize();

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [])

  useEffect(() => {
    if (isOpen && imageSrc) {
      setIsLoading(true); // Show loading spinner
      setTimeout(() => {
      if (editorRef.current) {
        const editorInstance = editorRef.current.getInstance();
        if (editorInstance) {
          editorInstance
            .loadImageFromURL(imageSrc, "Editable Image")
            .then(() => {
              setIsLoading(false); // Hide loading spinner
            })
            .catch((error) => {
              console.error("Error loading image:", error);
              alert("Failed to load the image. Please check the URL.");
              setIsLoading(false); // Hide loading spinner
            });
        } else {
          console.error("Editor instance is not available.");
          setIsLoading(false); // Hide loading spinner
        }
      } else {
        console.error("Editor ref is not available.");
        setIsLoading(false); // Hide loading spinner
      } },500)
    } else {
      setIsLoading(true); // Show spinner if imageSrc is null or undefined
    }
  }, [isOpen, imageSrc]);

  return (
    <div className="">
      <div className="flex flex-col items-center mb-4">
        <img
          src={editedImage || imageSrc || fallbackImage}
          alt="Preview"
          className="border rounded shadow-lg w-[500px] h-auto"
          loading="lazy"
        />
        <button
          onClick={openModal}
          type="button"
          className="hidden md:block mt-4 bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-700"
        >
          Open in Editor
        </button>
      </div>

      {isOpen && (
        <div
          className="fixed top-0 left-0 w-full h-full bg-indigo-900 bg-opacity-70 z-50 flex items-center justify-center"
          onClick={closeModal}
        >
          <div
            className="fixed top-0 left-0 w-full h-full bg-indigo-900 bg-opacity-70 z-50 flex items-center justify-center"
            onClick={closeModal}
          >
            <div
              className="relative w-full h-full bg-indigo-800 p-4 rounded-lg shadow-lg flex flex-col overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Responsive Header (Visible on sm and md, Hidden on lg) */}
              <div className="flex justify-between items-center bg-indigo-600 p-2 rounded-t-lg shadow-md md:hidden">
                {/* "Edit Image" Label */}
                <div className="text-white text-sm font-medium p-2">Edit Image</div>

                {/* Buttons */}
                <div className="flex items-center">
                  <button
                    onClick={handleSaveAndDownload}
                    className="bg-indigo-800 text-white px-4 py-2 rounded text-sm hover:bg-indigo-700"
                  >
                    Save & Download
                  </button>
                  <button
                    onClick={closeModal}
                    className="text-white bg-red-600 hover:bg-red-500 ml-2 px-2 py-1 rounded"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-6 h-6"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 7.586l4.293-4.293a1 1 0 111.414 1.414L11.414 9l4.293 4.293a1 1 0 11-1.414 1.414L10 10.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 9 4.293 4.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Save & Download Button (Visible on lg) */}
              <button
                onClick={handleSaveAndDownload}
                className="absolute top-8 right-20 bg-indigo-800 text-white px-4 py-2 rounded text-sm hover:bg-indigo-700 z-10 hidden md:block"
              >
                Save & Download
              </button>

              {/* Cross Button (Visible on lg) */}
              <button
                onClick={closeModal}
                className="absolute top-10 right-8 text-white bg-red-600 hover:bg-red-500 z-10 hidden md:block"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-6 h-6"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 7.586l4.293-4.293a1 1 0 111.414 1.414L11.414 9l4.293 4.293a1 1 0 11-1.414 1.414L10 10.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 9 4.293 4.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>

              {/* Image Editor Container */}
              <div className="w-full h-full">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-500"></div>
                  </div>
                ) : (
                  <ImageEditor
                    ref={editorRef}
                    includeUI={{
                      loadImage: {
                        path: validImageSrc,
                        name: "Editable Image",
                      },
                      theme: customTheme,
                      menu: ["crop", "rotate", "flip", "filter", "text", "shape", "icon", "mask", "draw"],
                      uiSize: {
                        width: "100%",
                        height: "100%",
                      },
                      menuBarPosition: "bottom",
                      toolbar: {
                        menu: ['crop', 'flip', 'rotate', 'draw', 'shape', 'text', 'filter'],
                      },
                    }}
                    cssMaxHeight={700}
                    cssMaxWidth={1000}
                    selectionStyle={{
                      cornerSize: 20,
                      rotatingPointOffset: 70
                    }}
                    usageStatistics={true}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FullScreenImageEditor;