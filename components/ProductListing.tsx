"use client";

import { useEffect, useState } from "react";
import { Skeleton } from "./ui/skeleton";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { PRODUCT_CATEGORIES } from "@/constants";
import ImageSlider from "./ImageSlider";
import Image from "next/image";
import { Heart, Star } from "lucide-react";


import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerDescription,
} from "@/components/ui/drawer";

import { GlowingStarsBackgroundCard } from "./ui/glowing-stars";
import BuyNowProducts from "./BuyNowProducts";
import { toast } from "sonner";
import Container from "./ui/container";
import { useRouter } from "next/navigation";
import { Button } from "./ui/button";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import ReactMarkdown from "react-markdown";
import ProductTotalRating from "./ProductTotalRating";
import { log } from "node:console";
import { capitalizeFirstLetter } from "@/lib/capitalizeFirstLetter";

interface ProductListingProps {
  product: any | null;
  index: number;
  user?: any;
}

const ProductListing = ({ product, index, user }: ProductListingProps) => {
  const validUrls = product?.images
    .map(({ image }: any) => (typeof image === "string" ? image : image?.url))
    .filter(Boolean) as string[];

  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeImage, setActiveImage] = useState(validUrls ? validUrls[0] : "");
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [likes, setLikes] = useState(product?.likes_length ?? 0);
  const [isLike, setIsLike] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [buttonLoading, setbuttonLoading] = useState<boolean>(false);
  const router = useRouter();
  const [totalRating, setTotalRating] = useState<number>(0);

  const handleRatingFetched = (rating: number) => {
    setTotalRating(rating); // Store the total rating in parent state
  };

  useEffect(() => {
    // if (product?.likes_id && user?.id) {
    //   setIsLike(product.likes_id.some((like: any) => product.likes_user_id === user.id));
    // }

    if (product?.likes_id && user?.id) {
      setIsLike(
        product?.likes_user_id?.includes(user.id) // Check if user.id is in likes_user_id array
      );
    }
    if (product?.likes_id) {
      setLikes(product.likes_length);
    }
  }, [product, user]);

  // console.log("product:", product);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, index * 75);
    return () => clearTimeout(timer);
  }, [index]);

  if (!product || !isVisible) return <ProductPlaceholder />;

  // const label = PRODUCT_CATEGORIES.find(
  //   ({ value }) => value === product.category
  // )?.label;
  // console.log("product?.productCategory", product?.productCategory);
  const categoryLabel = Array.isArray(product?.productCategory)
    ? product?.productCategory[0]?.label
    : product?.productCategory?.label || "";

  const listingCategory = product?.listingCategory || "";

  const handleImageClick = (imageSrc: string) => setActiveImage(imageSrc);

  const toggleDescription = () =>
    setIsDescriptionExpanded(!isDescriptionExpanded);

  // New function to handle arrow clicks
  const handleArrowClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleLikeClick = async (productId: string) => {
    if (!user?.id) {
      toast.error("Please login to like this product");
      return;
    }

    setLoading(true);

    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/products/likes/${productId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
          credentials: "include",
        }
      );

      if (!result.ok) {
        console.log("Server error. Are you online?", result);
        setLoading(false);
        return;
      }

      const body = await result.json();
      // console.log("body", body);
      setLikes(body.likeCount);
      setIsLike(body.isLikeUpdated);
    } catch (error) {
      console.error("Error liking product:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLinkClick = () => {
    setbuttonLoading(true);
    router.push(`/product/${product.slug}`);
  };

  const getRelativeTime = (dateString: string): string => {
    const createdAt = new Date(dateString).getTime(); // Convert to timestamp (number)
    const now = new Date().getTime(); // Convert to timestamp (number)
    const diffInMs = now - createdAt; // Now both are numbers

    const diffInSec = Math.floor(diffInMs / 1000);
    const diffInMin = Math.floor(diffInSec / 60);
    const diffInHours = Math.floor(diffInMin / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInMonths / 12);

    if (diffInYears > 0)
      return ` ${diffInYears} Year${diffInYears > 1 ? "s" : ""} ago`;
    if (diffInMonths > 0)
      return ` ${diffInMonths} Month${diffInMonths > 1 ? "s" : ""} ago`;
    if (diffInDays > 0)
      return ` ${diffInDays} Day${diffInDays > 1 ? "s" : ""} ago`;
    if (diffInHours > 0)
      return ` ${diffInHours} Hour${diffInHours > 1 ? "s" : ""} ago`;
    if (diffInMin > 0)
      return ` ${diffInMin} Minute${diffInMin > 1 ? "s" : ""} ago`;
    return "Created just now";
  };

  const userName =
  product?.user?.user_name ??
  product?.user_name ??
  (product?.user?.user_email || product?.user_email
    ? (product?.user?.user_email || product?.user_email).split("@")[0]
    : "Anonymous");

  return (
    <>
      <GlowingStarsBackgroundCard>
        <Link
          className={cn(
            "invisible h-full w-full cursor-pointer rounded-t-lg group/main",
            {
              "visible animate-in fade-in-5": isVisible,
            }
          )}
          href={""}
          onClick={(e) => {
            e.preventDefault(); // Prevents the default navigation behavior
            setIsDrawerOpen(true); // Execute your desired function
          }}
        >
          <div className="relative">
            {product.isFeatured === true && (
              <div className="absolute -top-4 -left-6 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-sm rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                  FEATURED
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent to-indigo-600 opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                </div>
              </div>
            )}
          </div>

          {/* Image Slider */}
          <div className="relative">
            <ImageSlider urls={validUrls} onArrowClick={handleArrowClick} />

            {/* Like Button and Likes Count */}
            <div
              className={`absolute z-10 backdrop-blur-sm bottom-1 left-1 flex ${totalRating ? "gap-2" : "gap-0"} items-center bg-black bg-opacity-50 rounded-md px-2 py-[2px]`}
            >
              <div className="flex gap-1 rounded-lg items-center">
                <Heart
                  className={`cursor-pointer h-4 w-4  ${
                    isLike ? "text-red-500 fill-red-500" : ""
                  }${loading ? "animate-pulse text-red-500 fill-red-500" : ""}`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLikeClick(product?.id);
                  }}
                />
                <p className="font-semibold text-sm">{likes}</p>
              </div>
              <div className="hover:cursor-default">
                <ProductTotalRating
                  productId={product.id}
                  user={user}
                  onRatingFetched={handleRatingFetched}
                />
              </div>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex gap-1 items-center justify-between">
              {/* {categoryLabel && (
                <p className="px-2 text-sm py-[2px] rounded bg-pink-500 text-white font-semibold">
                  {categoryLabel || ""}
                </p>
              )} */}
              <div className=" w-auto">
                <p
                  className={`rounded px-1 pl-[5px] ${product.price === 0 ? "bg-green-500 pl-[7px] tracking-wider" : "bg-white/30"} text-white flex items-center justify-center font-bold`}
                >
                  <span className="mb-[1px] text-sm pr-[2px]">
                    {product.price === 0 ? "FREE" : product.price}
                  </span>
                  {product.price === 0 ? (
                    ""
                  ) : (
                    <img
                      src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                      alt="Coin"
                      style={{ width: "20px", height: "20px" }}
                      loading="lazy"
                    />
                  )}
                </p>
              </div>
              <div>
                <p className="rounded px-1 py-1 pl-[5px] bg-white/30 text-white flex items-center justify-center font-bold uppercase text-sm">
                  {listingCategory}
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col-2 items-start gap-4 md:gap-6 justify-between mt-1 w-full">
            <div className="w-[78%]">
              <h3 className="font-medium truncate text-lg md:text-md capitalize">
                {product.name}
              </h3>
            </div>
          </div>
          <h3 className="mt-1 font-medium truncate text-md md:text-sm first-letter:uppercase">
            {product.description}
          </h3>
          {/* <div className="hover:cursor-default">
            <ProductTotalRating productId={product.id} user={user} />
          </div> */}
        </Link>
      </GlowingStarsBackgroundCard>

      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <DrawerContent className="max-h-[80vh] md:max-h-[70vh] pb-4 lg:pb-4 justify-between">
          <Container>
            <div className="flex flex-col md:flex md:flex-row mx-4 gap-2">
              <div className="flex flex-col md:grid md:grid-cols-2 gap-4 md:w-1/2 h-fit">
                <Image
                  src={validUrls[0]}
                  className="h-40 md:h-60 lg:h-60 object-cover object-center rounded-lg"
                  height="400"
                  width="400"
                  alt="thumbnail"
                />
                <div className="md:grid flex  md:grid-cols-2 overflow-x-scroll whitespace-nowrap gap-2 md:w-fit no-scrollbar">
                  {validUrls.slice(1).map((imageSrc, index) => (
                    <div key={index} onClick={() => handleImageClick(imageSrc)}>
                      <Image
                        src={imageSrc}
                        className={`w-24 h-20 md:w-28 md:h-28 object-cover object-center rounded-lg ${
                          activeImage === imageSrc
                            ? "border-2 border-indigo-500"
                            : ""
                        }`}
                        height="100"
                        width="100"
                        alt="thumbnail"
                      />
                    </div>
                  ))}
                </div>
              </div>
              <div className="md:w-1/2 flex-grow relative">
                <div className="flex justify-between items-center mb-2 sm:mb-2 lg:mb-3">
                  <DrawerTitle className="text-xl capitalize">
                    {product.name}
                  </DrawerTitle>
                  <div className="flex rounded-xl items-center bg-white/30 px-2 py-1 gap-2">
                    <Heart
                      className={`cursor-pointer ${
                        isLike ? "text-red-500 fill-red-500" : ""
                      }${
                        loading ? "animate-pulse text-red-500 fill-red-500" : ""
                      }`}
                      onClick={(e) => {
                        handleLikeClick(product.id);
                      }}
                    />
                    <p className="font-bold">{likes}</p>
                  </div>
                </div>

                <DrawerDescription className="mb-3 max-h-20 md:max-h-48 overflow-y-auto">
                  <ReactMarkdown
                    components={{
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline"
                        >
                          {children}
                        </a>
                      ),
                    }}
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                  >
                    {isDescriptionExpanded
                      ? capitalizeFirstLetter(product.description)
                      : capitalizeFirstLetter(
                          `${product?.description?.substring(0, 100)}...`
                        )}
                  </ReactMarkdown>
                  <span
                    className="cursor-pointer text-gray-300"
                    onClick={toggleDescription}
                  >
                    {isDescriptionExpanded ? " Show less" : " Show more"}
                  </span>
                </DrawerDescription>

                <DrawerTitle className="mb-3 hover:text-indigo-400">
                  <Link
                    href={`/users/${product?.user?.user_name ?? product?.user_name}`}
                    className="first-letter:uppercase"
                  >
                    @{" "}{capitalizeFirstLetter(userName)}
                 
                  </Link>
                </DrawerTitle>
                <div className="flex justify-between items-center mb-2 lg:mb-3">
                  <p
                    className={`text-xl font-bold flex gap-[2px] items-center rounded px-3 ${product.price === 0 ? "bg-green-500 pl-[7px] tracking-wider" : "bg-white/30"} justify-center`}
                  >
                    <span className="mb-[1px]">
                      {product.price === 0 ? "FREE" : product.price}
                    </span>
                    {product.price === 0 ? (
                      ""
                    ) : (
                      <img
                        src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                        alt="Coin"
                        style={{ width: "23px", height: "23px" }}
                        loading="lazy"
                      />
                    )}
                  </p>
                  <p className="flex items-center gap-[2px] fade-in-right">
                    <>
                      <span className="text-xl font-semibold">1</span>
                      <img
                        src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                        alt="Coin"
                        style={{ width: "20px", height: "20px" }}
                        loading="lazy"
                      />{" "}
                      = <span className="text-xl font-semibold">1</span>
                      <span className="font-medium mt-[2px]">₹</span>
                    </>
                  </p>
                </div>

                <div className="pb-16 lg:pb-20 flex justify-between">
                  <div>
                    {product.affiliated_with ? (
                      <div>
                        Affiliated With :{" "}
                        <a
                          href={product.affiliated_with}
                          className="text-green-500 underline "
                          target="_blank"
                        >
                          {product.affiliated_with.substring(0, 30) + "..."}
                        </a>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="text-sm bg-white/30 text-white px-2 py-1 rounded">
                    {" "}
                    {getRelativeTime(product.createdAt)}
                  </div>
                </div>
                <div className="flex gap-2 absolute bottom-2 w-full">
                  <BuyNowProducts product={product} user={user} />
                  <button
                    onClick={handleLinkClick}
                    className="bg-gradient-to-br from-indigo-600 to-indigo-700 w-full text-white rounded-md font-medium flex items-center justify-center"
                  >
                    {buttonLoading ? <div>Loading...</div> : "View Detail"}
                  </button>
                </div>
              </div>
            </div>
          </Container>
        </DrawerContent>
      </Drawer>
    </>
  );
};

const ProductPlaceholder = () => {
  return (
    <div className="flex flex-col w-full">
      <div className="relative bg-zinc-100 aspect-square w-full overflow-hidden rounded-xl">
        <Skeleton className="h-full w-full" />
      </div>
      <Skeleton className="mt-4 w-2/3 h-4 rounded-lg" />
      <Skeleton className="mt-2 w-full h-8 rounded-lg" />
    </div>
  );
};

export default ProductListing;
