"use client";

import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSpace } from "@/app/hooks/use-space";
import { SpaceModal } from "@/components/SpaceModal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { formSchema } from "../app/(space)/space/constants";
import { Input } from "@/components/ui/input";

const SpaceForm = () => {
  const { isOpen, onClose } = useSpace();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      type: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    console.log(values);
  };

  return (
    <SpaceModal
      title="New Space"
      description="Create Your Magical Space Here"
      isOpen={isOpen}
      onClose={onClose}
    >
      <div className="space=y-4 py-2 pb-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="">
            <FormField
              name="name"
              render={({ field }) => (
                <FormItem className="col-span-12 lg:col-span-10">
                  <FormLabel>Name</FormLabel>
                  <FormControl className="m-0 p-0">
                    <Input
                      className="border-2 border-gray-200 px-2 focus-visible:ring-0 focus-visible:ring-transparent"
                      placeholder="Name your prompt space"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="type"
              render={({ field }) => (
                <FormItem className="py-2 col-span-12 lg:col-span-10">
                  <FormLabel>Prompt Types</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Prompt Type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="GPTs/LLMs">GPT</SelectItem>
                      <SelectItem value="Image">Image</SelectItem>
                      <SelectItem value="3D">3D</SelectItem>
                      <SelectItem value="Music">Music</SelectItem>
                      <SelectItem value="Video">Video</SelectItem>
                    </SelectContent>
                    <FormDescription>
                      Select the type of prompt you want to rent
                    </FormDescription>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="pt-6 space-x-2 flex items-center justify-end">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">Continue</Button>
            </div>
          </form>
        </Form>
      </div>
    </SpaceModal>
  );
};

export default SpaceForm;
