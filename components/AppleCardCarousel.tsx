"use client";
import React, { useState, useEffect } from "react";
import { Carousel, Card } from "../components/ui/apple-cards-carousel";

export function AppleCardsCarouselDemo({ user, rappId }) {
  const [userComments, setUserComments] = useState([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleText = () => {
    setIsExpanded((prevState) => !prevState);
  };
  const fetchComments = async () => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/rapps/comment/${rappId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      const data = await result.json();
      if (data.success) {
        setUserComments(data.comments);
      }
    } catch (error) {
      console.error("Error fetching comments:", error);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [rappId]);

  // Automatically cycle through comments in the carousel
  useEffect(() => {
    const intervalId = setInterval(() => {
      setActiveIndex((prevIndex) =>
        prevIndex === userComments.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000);

    return () => clearInterval(intervalId);
  }, [userComments.length]);

  const cards = userComments.map((commentData, index) => (
    <Card
      key={commentData.id}
      card={{
        category: "",
        title: commentData.user.user_name,
        src:
          commentData.user.profileImage?.url ||
          "https://t4.ftcdn.net/jpg/02/29/75/83/360_F_229758328_7x8jwCwjtBMmC6rgFzLFhZoEpLobB6L8.jpg",
        content: (
          <div className="dark:bg-neutral-800">
            <div className="mt-0 border border-gray-300 dark:border-gray-700 rounded-lg bg-white overflow-y-auto h-32 dark:bg-neutral-900 w-[400px] max-w-md p-4">
              <p
                className={`text-black dark:text-neutral-400 text-lg font-sans`}
              >
                {commentData.comment}
              </p>
            </div>
          </div>
        ),
      }}
      index={index}
    />
  ));

  return (
    <div className="w-full h-full py-20">
      {userComments.length > 0 ? (
        <Carousel items={cards} />
      ) : (
        <p className="text-center text-neutral-600">No comments available.</p>
      )}
    </div>
  );
}
