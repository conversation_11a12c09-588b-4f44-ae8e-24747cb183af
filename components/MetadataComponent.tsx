'use client';

import { useEffect, useState } from 'react';
import { Metadata } from 'next';
import { usePathname } from 'next/navigation';

const generateRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

function DynamicSVG({ title }: { title: string }) {
  const backgroundColor = generateRandomColor();
  const generateSVG = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630">
              <rect width="1200" height="630" fill="${backgroundColor}" />
              <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="white" font-size="48">${title}</text>
            </svg>`;
  };

  const [metadata, setMetadata] = useState<Metadata | null>(null);

  useEffect(() => {
    setMetadata({
      openGraph: {
        images: [
          {
            url: `data:image/svg+xml;base64,${Buffer.from(generateSVG()).toString('base64')}`,
            width: 1200,
            height: 630,
            type: 'image/svg+xml'
          }
        ]
      }
    });
  }, [title]);

  if (!metadata) return null;

  return (
    <>
      {/* You can use the metadata object as needed */}
    </>
  );
}

export default function MetadataComponent() {
  const pathname = usePathname();
  const title = pathname === '/' ? 'Home' : `Page: ${pathname}`;
  return (
    <>
      <DynamicSVG title={title} />
    </>
  );
}
