"use client";

import Link from "next/link";
import React from "react";
import CountUp from "react-countup";
import { motion } from "framer-motion";
import { Users, PenLine, HeartHandshake } from "lucide-react";

const cardsData = [
  {
    icon: <Users size={50} className="text-indigo-200" />,
    number: 2000,
    suffix: "+",
    description: "Active Members",
  },
  {
    icon: <PenLine size={50} className="text-indigo-200" />,
    number: 3500,
    suffix: "+",
    description: "Posts",
  },
  {
    icon: <HeartHandshake size={50} className="text-indigo-200" />,
    number: 20,
    suffix: "+",
    description: "Partners",
  },
];

const CommunityDiscussion = () => {
  return (
    <div className=" mx-auto py-10 px-8 rounded-xl">
      {/* -------------------- Discussion Header ------------------------ */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex items-center justify-between mb-10"
      >
        <div className="text-white text-3xl md:text-4xl font-medium">
            Discussion
          </div>
          <Link href={"https://discord.gg/kPkYbzMvN3"} target="_blank">
            <button className="bg-gradient-to-r from-amber-500 to-pink-500 text-white font-semibold py-2 px-4 rounded-full text-sm md:text-md">
              Start a discussion →
            </button>
          </Link>
      </motion.div>

      {/* -------------------- Stats Section ------------------------ */}
      <div className="grid gap-6 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1">
        {cardsData.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.2 }}
            className="flex flex-row gap-4 p-6 justify-center items-center rounded-lg backdrop-blur-lg shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl bg-black/25"
          >
            <div className=" p-4 rounded-full">{item.icon}</div>
            <div>
            <div className="text-4xl font-bold bg-gradient-to-br from-amber-500 to-pink-500 bg-clip-text text-transparent">
              <CountUp start={0} end={item.number} duration={2} suffix={item.suffix} />
            </div>
            <div className="text-lg text-indigo-200 tracking-wide">{item.description}</div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default CommunityDiscussion;
