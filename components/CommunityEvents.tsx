"use client";

import Link from "next/link";
import React from "react";
import { StickyScroll } from "@/components/ui/sticky-scroll-reveal";
import Image from "next/image";

const content = [
  {
    title: "Community Meetups",
    description:
      "Join us for regular meet-up sessions to stay connected with fellow community members and stay updated on the latest developments in generative AI. Enjoy a casual setting, and take advantage of networking opportunities with fellow professionals.",
    content: (
      <div className="h-full w-full flex items-center justify-center text-white">
        <Image
          src="/img/TechEvents3.jpg"
          width={500}
          height={500}
          className="h-full w-full object-cover"
          alt="Community Meetup"
        />
      </div>
    ),
  },
  {
    title: "Interactive Workshops",
    description:
      "Join our workshops to take your skills to the next level and stay ahead of the curve in the rapidly evolving world of generative AI. Receive expert guidance, and gain practical experience in building and fine-tuning generative models.",
    content: (
      <div className="h-full w-full  flex items-center justify-center text-white">
        <Image
          src="/img/TechEvents7.jpg"
          width={500}
          height={500}
          className="h-full w-full object-cover"
          alt="Interactive Workshops"
        />
      </div>
    ),
  },
  {
    title: "Hackathons",
    description:
      "Participate in our hackathons and unleash your creativity and technical skills in a supportive and collaborative environment. Show off your projects and take home recognition and prizes.",
    content: (
      <div className="h-full w-full  flex items-center justify-center text-white">
        <Image
          src="/img/TechEvents1.jpg"
          width={500}
          height={500}
          className="h-full w-full object-cover"
          alt="Hackathon"
        />
      </div>
    ),
  },
  {
    title: "Webinars",
    description:
      "Join webinars where our expert speaker will share strategies and techniques for building advanced generative models. Learn how to overcome common challenges and optimize your model performance. From understanding the limitations of your data to techniques for model interpretation, get ready to take your generative modeling skills to the next level.",
    content: (
      <div className="h-full w-full  flex items-center justify-center text-white">
        <Image
          src="/img/TechEvents4.jpg"
          width={500}
          height={500}
          className="h-full w-full object-cover"
          alt="Webinar"
        />
      </div>
    ),
  },
];

const CommunityEvents = () => {
  return (
    <>
      {/* --------------------Academy Section------------------------ */}
      <div className="w-11/12 mx-auto pb-24">
        <div className="flex items-center justify-between mb-10">
          <div className="text-white text-3xl md:text-4xl font-medium">
            Events
          </div>
          <Link href={"https://discord.gg/kPkYbzMvN3"} target="_blank">
            <button className="bg-gradient-to-r from-amber-500 to-pink-500 text-white font-semibold py-2 px-4 rounded-full text-sm md:text-md">
              VIEW MORE →
            </button>
          </Link>
        </div>

        <StickyScroll content={content} />
      </div>
    </>
  );
};

export default CommunityEvents;
