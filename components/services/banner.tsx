"use client";
import Link from "next/link";
import { Icons } from "../Icons";
import Image from "next/image";

const ServiceBanner = () => {
  
  return (
    <>
      <div className="flex md:mt-20 bg-white justify-center lg:justify-between md:rounded-xl rounded-b-xl h-60 md:h-96">
        <div className="flex relative items-center text-center lg:text-left md:px-12">
          <div >
            <h1 className="text-2xl font-semibold text-gray-800 md:text-4xl flex items-center">
              <span>
                {" "}
                <Icons.logo className="h-8 w-auto me-2" />
              </span>
              RENTPROMPTS <span className="text-indigo-600 ms-2">SERVICES</span>
            </h1>
            <p className="mt-2 text-sm text-gray-600 md:text-base">
              TRANSFORMING IDEAS INTO REALITY
            </p>
            <div className="flex justify-center lg:justify-start mt-6">
              <Link
                className="px-4 py-3 bg-gray-900 bg-gradient-to-br from-indigo-600 to-indigo-700 text-white text-xs font-semibold rounded hover:bg-gray-800"
                href="/bounties"
              >
                Bounties
              </Link>
              <Link
                className="mx-4 px-4 py-3 bg-gray-300 text-gray-900 text-xs font-semibold rounded hover:bg-gray-400"
                href="#connect-us"
              >
                Contact US
              </Link>
            </div>
          </div>
          {/* <div className="absolute z-40 pt-8 left-0 right-0">
          <BlurryBlob
          className="rounded-4xl opacity-50"
          firstBlobColor="bg-purple-400"
          secondBlobColor="bg-blue-400"
          />
          </div> */}
        </div>
        <div
          className="hidden lg:block lg:w-1/2 rounded-r-xl"
          style={{ clipPath: "polygon(10% 0, 100% 0%, 100% 100%, 0 100%)" }}
        >
          <div className="rounded-tl rounded-r-xl relative h-full">
            <Image
              className="rounded-tl rounded-r-xl"
              src="asset.rentprompts.com/Chroma_Glass_codetoform00004.png"
              alt="Background Image"
              layout="fill"
              objectFit="cover"
              quality={80}
            />
            <div className="absolute inset-0 bg-black rounded-r-xl opacity-25"></div>
          </div>
        </div>
      </div>
    </>
  );
};
export default ServiceBanner;
