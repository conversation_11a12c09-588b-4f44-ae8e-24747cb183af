import Image from "next/image";
import React from "react";
import MaxWidthWrapper from "../MaxWidthWrapper";

export default function WhyrentPromts() {
  const sections = [
    {
      title: "Collaborative community of prompt engineers?",
      description:
        "Rentprompts boasts a thriving community of generative AI engineers, facilitating the sharing of knowledge and resources. This collaborative environment accelerates innovation and problem-solving capabilities.",
    },
    {
      title: "Rapid MVP development?",
      description:
        "The platform can deliver a proof of concept in a week and a minimum viable product in a month. This includes building complex conversational chatbots and recommendation systems rapidly..",
    },
    {
      title: "Affordable and Relevant generative AI solutions?",
      description:
        "Rentprompts offers cost-effective AI applications that are both user-friendly and easy to integrate into your workflow. This makes advanced generative AI technology accessible to a wider audience, including individuals and businesses alike.",
    },
  ];

  return (
    <>
      <MaxWidthWrapper className="md:px-10 rounded md:rounded-lg">
        <div className="my-100 md:mt-4 mb-12 text-center">
          <h2 className="text-3xl mb-10 font-bold tracking-tight sm:text-3xl">
            Why Rentprompts?
          </h2>
          <p className=" mb-4">
            At Rentprompts, we believe that the transformative power of
            generative AI should be accessible to everyone. Our platform is
            designed to offer affordable, user-friendly, and accurate AI
            solutions that cater to both novices and experts alike. Whether you
            re an individual creator or a large enterprise, Rentprompts empowers
            you to harness advanced AI technology with ease.
          </p>

          <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 relative mt-6 border border-transparent text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-3 px-6 dark:focus:ring-offset-gray-800 transition-all">
            <div className="absolute inset-0 z-0 hidden md:block">
              <Image
                height={400}
                width={1100}
                src="/img/aitoolimg/mounten.svg"
                alt=""
                className="object-cover w-full h-full z-0"
              />
            </div>
            <div className="mx-auto max-w-2xl pb-8 sm:px-6 sm:pb-5 md:grid md:max-w-7xl md:grid-cols-1 md:gap-x-8 md:px-8">
              {/* Product images */}
              <div className="md:self-center z-10 h-full">
                {/* <div className="aspect-square rounded-lg">
                  <Icons.logo className="h-[200px] fill-transparent w-60 stroke-4 dark:stroke-white stroke-white" />
                </div> */}
                <div className="max-w-xl mx-auto z-10">
                  <div className="grid divide-y divide-neutral-200 max-w-xl mx-auto mt-2">
                    {sections.map((section, index) => (
                      <div key={index} className="py-5">
                        <details className="group relative">
                          <summary className="flex justify-between font-sans text-base font-medium text-start cursor-pointer list-none">
                            <span className="pr-[8px]">{section.title}</span>
                            <span className="transition group-open:rotate-180 absolute right-0">
                              <svg
                                fill="none"
                                height="24"
                                shapeRendering="geometricPrecision"
                                stroke="currentColor"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="1.5"
                                viewBox="0 0 24 24"
                                width="24"
                             
                              >
                                <path d="M6 9l6 6 6-6"></path>
                              </svg>
                            </span>
                          </summary>
                          <p className="text-indigo-300 text-base text-start mt-3 group-open:animate-fadeIn">
                            {section.description}
                          </p>
                        </details>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </MaxWidthWrapper>
    </>
  );
}
