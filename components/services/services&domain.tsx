"use client";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import type SwiperType from "swiper";
import { useEffect, useState } from "react";
import { Pagination } from "swiper/modules";
import SliderComponent from "./slidercomponent";
import { Domainservices } from "./domain";
import MaxWidthWrapper from "../MaxWidthWrapper";

// import { url } from "inspector";
// import { cn, formatPrice } from "@/lib/utils";
// import { ChevronLeft, ChevronRight } from "lucide-react";
// import { url } from "inspector";

const Urls = [
  {
    title: "The Dawn of Innovation",
    description: "Explore the birth of groundbreaking ideas and inventions.",
    // header: <Skeleton />,
    src: "/img/projectimg/one.jpg",
    // className: "md:col-span-2",
    // icon: <IconClipboardCopy className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Digital Revolution",
    description: "Dive into the transformative power of technology.",
    src: "/img/projectimg/two.jpg",

    className: "md:col-span-1",
    // icon: <IconFileBroken className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Art of Design",
    description: "Discover the beauty of thoughtful and functional design.",
    src: "/img/projectimg/two.jpg",
    className: "md:col-span-1",
    // icon: <IconSignature className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Power of Communication",
    description:
      "Understand the impact of effective communication in our lives.",
    src: "/img/projectimg/two.jpg",
    className: "md:col-span-2",
    // icon: <IconTableColumn className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Power of Communication",
    description:
      "Understand the impact of effective communication in our lives.",
    src: "/img/projectimg/two.jpg",
    className: "md:col-span-2",
    // icon: <IconTableColumn className="h-4 w-4 text-neutral-500" />,
  },
];

const ServicesandDomain = () => {
  return (
    <>
      <MaxWidthWrapper>
        <SliderComponent />
      </MaxWidthWrapper>
    </>
  );
};
export default ServicesandDomain;
