"use client";
import React, { useState } from "react";

const ExpandableBox = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={`bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 p-3 transition-all duration-300 ${
        isExpanded ? "max-h-full" : "max-h-32"
      } overflow-hidden cursor-pointer`}
      onClick={toggleExpand}
    >
      <div className="text-white text-xl font-bold mb-2">
        Lorem ipsum dolor sit amet.
      </div>
      <div
        className={`text-indigo-300 text-sm transition-all duration-300 ${
          isExpanded ? "line-clamp-none" : "line-clamp-2"
        }`}
      >
        {/* {content} */}
        Lorem, ipsum dolor sit amet consectetur adipisicing elit. Itaque maiores
        debitis eos quae laboriosam aspernatur hic modi culpa magnam at
        architecto recusandae ea vero aliquam tenetur fuga saepe harum minus
        obcaecati facere maxime, laborum, eveniet consectetur nisi! Fugit
        suscipit itaque cupiditate tenetur dolorem pariatur provident. Esse qui
        ducimus ex eum illo. Vitae commodi molestias laboriosam asperiores
        doloremque! Dolorum sint veniam quae temporibus id numquam, fugit, ullam
        obcaecati facilis hic eligendi, ea ipsa libero! Libero cupiditate ut
        voluptas. Incidunt quam libero placeat quod debitis, eum accusantium,
        vitae quaerat molestiae a omnis itaque quidem in tempore eaque facilis
        saepe odit dolores rerum quisquam hic? Quia illo animi nesciunt ex
        veritatis ipsa deserunt quasi perspiciatis eos odit, quis, beatae sint
        vero temporibus laborum voluptate neque optio odio adipisci fuga minus
        itaque porro incidunt! Sunt voluptatem doloribus voluptatibus a
        voluptatum, accusantium reiciendis, modi, itaque odio ipsam fugit
        deleniti esse aperiam quis nesciunt recusandae nemo quasi? Eos, ducimus
        optio? Ipsum corrupti praesentium saepe facilis quia animi ipsam beatae
        dignissimos, labore ab dolore laboriosam voluptatum dicta deleniti
        delectus reprehenderit accusantium magnam ex maxime excepturi. Quisquam
        voluptates hic quod, explicabo quidem exercitationem perferendis sed
        velit error officia, provident aliquid pariatur atque, id suscipit
        corrupti veniam? Architecto, dolor.
      </div>
      <div className="text-indigo-300 text-sm mt-2 text-right">
        {isExpanded ? "Show Less" : "Read More"}
      </div>
    </div>
  );
};

export default ExpandableBox;
