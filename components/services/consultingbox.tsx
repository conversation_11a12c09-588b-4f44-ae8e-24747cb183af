"use Client";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";

const urls = [
  {
    title: " Initial Consulting Services for Free",
    description:
      "Receive expert guidance on your first Ai project at no cost, ensuring you get the best advice to move forward effectively.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-helping"><path d="M11 12h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 14"/><path d="m7 18 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9"/><path d="M2 13 6 6"/></svg>',
  },
  {
    title: "Contextual AI App's",
    description:
      "Develop AI applications tailored to specific contexts and needs, bringing more relevance and effectiveness to your solutions.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fullscreen"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><rect width="10" height="8" x="7" y="8" rx="1"/></svg>',
  },
  {
    title: "Model Fine Tuning",
    description:
      "Enhance the performance of your AI models through meticulous fine-tuning, optimizing them for both accuracy and efficiency.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-platter"><path d="M12 3V2"/><path d="M5 10a7.1 7.1 0 0 1 14 0"/><path d="M4 10h16"/><path d="M2 14h12a2 2 0 1 1 0 4h-2"/><path d="m15.4 17.4 3.2-2.8a2 2 0 0 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2L5 18"/><path d="M5 14v7H2"/></svg>',
  },
  {
    title: "Prompt Engineering",
    description:
      "Craft precise and effective prompts to drive better results from generative AI applications and improve overall outcomes.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-pen"><rect width="8" height="4" x="8" y="2" rx="1"/><path d="M10.4 12.6a2 2 0 0 1 3 3L8 21l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/><path d="M13 8H7"/><path d="M17 12H7"/></svg>',
  },
  {
    title: "Agents Development",
    description:
      "Create intelligent AI agents capable of performing complex tasks and automations, driving efficiency and fostering innovation.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot-message-square"><path d="M12 6V2H8"/><path d="m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z"/><path d="M2 12h2"/><path d="M9 11v2"/><path d="M15 11v2"/><path d="M20 12h2"/></svg>',
  },
  {
    title: "Chat Bots Development",
    description:
      "Design and implement advanced chatbots for improved customer interactions and service automation across various platforms.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square-text"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/><path d="M13 8H7"/><path d="M17 12H7"/></svg>',
  },
  {
    title: "Workflow Automation",
    description:
      "Automate your business workflows using generative AI to boost productivity and streamline operations efficiently.",
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square-text"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/><path d="M13 8H7"/><path d="M17 12H7"/></svg>',
  },
];

const CunsultingBox = () => {
  return (
    <>
      <div className="my-[100px]">
        <div className=" mb-10 text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-3xl">
            Gen AI Consulting Services
          </h2>
        </div>
        {/* <Swiper
            modules={[Navigation, Pagination, A11y, Autoplay]}
            spaceBetween={30}
            slidesPerView={4}
            autoplay={{
              delay: 2500,
              disableOnInteraction: false,
            }}
            breakpoints={{
              280: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              500: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              640: {
                slidesPerView: 2,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 40,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 50,
              },
              1200: {
                slidesPerView: 3,
                spaceBetween: 50,
              },
            }}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
            // pagination={{ clickable: true }}
            onSwiper={(swiper) => console.log(swiper)}
            onSlideChange={() => console.log("slide change")}
          >
            {urls.map((url, i) => (
              <SwiperSlide key={i} className="-z-10 relative h-full w-full">
                <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 p-3 min-h-[220px]">
                  <div className="rounded-lg h-58 bg-indigo-700 flex flex-col gap-3 p-4 items-center justify-center">
                    <div className="max-w-lg rounded">
                      <div
                        dangerouslySetInnerHTML={{ __html: url.icon }}
                        className="mx-auto mb-4 flex justify-center bg-white rounded-full p-3 text-indigo-600"
                      />
                    </div>
                    <div className="font-bold text-white text-xl ">
                      {url.title}
                    </div>
                    <p className="text-indigo-300 text-sm">{url.description}</p>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          
            <button
              className="active:scale-[0.97] grid hover:scale-105 absolute top-1/2 -translate-y-1/2 aspect-square h-8 w-8 z-50 place-items-center rounded-full border-2 bg-white border-zinc-300 right-3 transition hover:bg-primary-300 text-primary-800 opacity-100"
              aria-label="previous image"
            >
              <ChevronRight className="h-4 w-4 text-zinc-700" />
            </button>
            <button
              className="active:scale-[0.97] grid hover:scale-105 absolute top-1/2 -translate-y-1/2 aspect-square h-8 w-8 z-50 place-items-center rounded-full border-2 bg-white border-zinc-300 left-3 transition hover:bg-primary-300 text-primary-800 opacity-100"
              aria-label="next image"
            >
              <ChevronLeft className="h-4 w-4 text-zinc-700" />
            </button>
          </Swiper> */}
        <div className="grid gap-4  sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {urls.map((url, i) => (
            <div key={i} className="relative h-full w-full">
              <div className="shadow dark:shadow-none dark:border-white/[10.2] group-hover/bento:translate-x-2 transition duration-200 hover:shadow-xl bg-[#FFFFFF0F] rounded-lg border border-white hover:border-transparent p-3 min-h-[220px]">
                <div className="h-58 flex flex-col gap-3 p-3 items-center justify-center">
                  <div className="max-w-lg rounded">
                    <div
                      dangerouslySetInnerHTML={{ __html: url.icon }}
                      className="mx-auto flex justify-center bg-white rounded-full p-3 text-indigo-600"
                    />
                  </div>
                  <h3 className="font-bold text-white font-sans">
                    {url.title}
                  </h3>
                  <p className="text-indigo-300 text-sm text-center">{url.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};
export default CunsultingBox;
