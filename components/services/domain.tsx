"use client";

import React from "react";
import { AiTools } from "../ui/aitoollist";

export function Domainservices() {
  return (
    <>
      <div className="rounded-md flex my-3 md:my-0 flex-col antialiased dark:bg-black dark:bg-grid-white/[0.05] items-center justify-center relative overflow-hidden w-full max-full">
        <AiTools
          items={testimonials}
          direction="left"
          speed="slow"
          infobox={true}
        />
      </div>
    </>
  );
}

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/dall-e-logo.jpg`,
  },
  {
    name: "Midjourney",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/midjoy.png`,
  },
  {
    name: "ChatGpt",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/chat-gpt-logo.svg`,
  },
  {
    name: "Stable Diffusion",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/stable-diffusion-logo.png`,
  },
  {
    name: "Gemini",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/gemini.gif`,
  },
  {
    name: "Leonardo",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/leonardo-logo.svg`,
  },
  {
    name: "ClaudeAi",
    img: `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/claude_app_icon.png`,
  },
];
