"use client";

import React from "react";
import { AiTools } from "../ui/aitoollist";

export function Domainservices() {
  return (
    <>
      <div className="rounded-md flex my-3 md:my-0 flex-col antialiased dark:bg-black dark:bg-grid-white/[0.05] items-center justify-center relative overflow-hidden w-full max-full">
        <AiTools
          items={testimonials}
          direction="left"
          speed="slow"
          infobox={true}
        />
      </div>
    </>
  );
}

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    img: "/img/aitoolimg/dall-e-logo.jpg",
  },
  {
    name: "Midjourney",
    img: "/img/aitoolimg/midjoy.png",
  },
  {
    name: "ChatGpt",
    img: "/img/aitoolimg/chat-gpt-logo.svg",
  },
  {
    name: "Stable Diffusion",
    img: "/img/aitoolimg/stable-diffusion-logo.png",
  },
  {
    name: "<PERSON>",
    img: "/img/aitoolimg/gemini.gif",
  },
  {
    name: "<PERSON>",
    img: "/img/aitoolimg/leonardo-logo.svg",
  },
  {
    name: "Claude<PERSON>i",
    img: "https://claude.ai/images/claude_app_icon.png",
  },
];
