"use Client";
import Link from "next/link";
import MaxWidthWrapper from "../MaxWidthWrapper";
import { GetInTouch } from "./getintouch";
import { MapPin, MailOpenIcon, BookOpen } from "lucide-react";

const WriteToUs = ({showFields}) => {
  return (
    <>
      <MaxWidthWrapper className="px-5">
        <div
          id="connect-us"
          className="text-white flex flex-col items-center pt-20 sm:justify-center sm:pt-0"
        >
          <div className="my-4 md:my-8 mb-12 text-center"></div>
          <div className="grid grid-cols-1 md:flex md:flex-row gap-6 w-full">
            {/* First Column: Form (8/12 width on medium screens and above) */}
            <div className="relative w-full md:w-8/12 mx-auto bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 shadow-[20px_0_20px_20px] shadow-slate-500/10 dark:shadow-white/20 rounded-md border-white/20 border-l-white/20 border-r-white/20 sm:shadow-sm lg:shadow-none">
              {/* <div className="relative -mb-px h-px w-full bg-gradient-to-r from-transparent via-sky-300 to-transparent"></div> */}
              <GetInTouch showFields={showFields}/>
            </div>
            {/* Second Column: Image (4/12 width on medium screens and above) */}
            <div className="relative md:md:w-4/12 w-full mx-auto bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 sm:border-t-white/20 shadow-[20px_0_20px_20px] shadow-slate-500/10 dark:shadow-white/20 rounded-lg border-white/20 border-l-white/20 border-r-white/20 sm:shadow-sm lg:shadow-none">
              {/* <!-- Add your image here --> */}
              <div className="w-3/4 h-full m-auto py-4 md:py-8">
                <div className="mb-3">
                  <h2 className="text-3xl font-bold tracking-tight sm:text-3xl text-center mb-8">
                    Connect to us
                  </h2>
                </div>
                <div className="mb-3">
                  <Link href="mailto:<EMAIL>" target="_blank">
                    <div className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-5 px-5 cursor-pointer">
                      <MailOpenIcon width={20} height={29} />
                      <div className="flex flex-col">
                        <h2>
                          <span className="inline-flex font-semibold bg-clip-text text-transparent text-white">
                            Mail
                          </span>
                        </h2>
                        <span className="text-sm text-muted-foreground break-words overflow-wrap break-word"
                         style={{ wordBreak: "break-word" }}>
                          <EMAIL>
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
                <div className="mb-3">
                  <div className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-5 px-5">
                    <MapPin width={40} height={32} />
                    <div className="flex flex-col">
                      <h2>
                        <span className="inline-flex  font-semibold bg-clip-text text-transparent text-white ">
                          Address
                        </span>
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        VF-29, Treasure Town, IDA State, Indore,
                        MP, India - 452012
                      </p>
                    </div>
                  </div>
                </div>
                <Link  href="https://discord.gg/kPkYbzMvN3" target="_blank" className="bg-indigo-800 flex gap-3 m-auto rounded-lg py-5 px-5">
                  <BookOpen width={24} height={32} />

                  <div className="flex flex-col">
                    <h2>
                      <span className="inline-flex font-semibold bg-clip-text text-transparent text-white ">
                        For Enquiry
                      </span>
                    </h2>
                    <span
                     
                      className="text-sm text-muted-foreground hover:text-indigo-500"
                    >
                      Join Discord
                    </span>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </MaxWidthWrapper>
    </>
  );
};
export default WriteToUs;
