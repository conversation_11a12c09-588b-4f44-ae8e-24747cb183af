"use Client";
import Image from "next/image";
import { Navigation, Pagination, A11y, Autoplay } from "swiper/modules";

import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import { useEffect, useState } from "react";
// import { A11y, Pagination } from "swiper/modules";
// import { cn, formatPrice } from "@/lib/utils";
// import { ChevronLeft, ChevronRight } from "lucide-react";
import { url } from "inspector";
import { ArrowBigRight, ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
// import { Scrollbar } from "@radix-ui/react-scroll-area";

const urls = [
  {
    title: "The Dawn of Innovation",
    description: "Explore the birth of groundbreaking ideas and inventions.",
    // header: <Skeleton />,
    src: "/img/4.png",
    className: "md:col-span-2",
    // icon: <IconClipboardCopy className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Digital Revolution",
    description: "Dive into the transformative power of technology.",
    src: "/img/2.png",

    className: "md:col-span-1",
    // icon: <IconFileBroken className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Art of Design",
    description: "Discover the beauty of thoughtful and functional design.",
    src: "/img/3.png",
    // className: "md:col-span-1",
    // icon: <IconSignature className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Power of Communication",
    description:
      "Understand the impact of effective communication in our lives.",
    src: "/img/4.png",
    // className: "md:col-span-2",
    // icon: <IconTableColumn className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "The Power of Communication",
    description:
      "Understand the impact of effective communication in our lives.",
    src: "/img/5.png",
    // className: "md:col-span-2",
    // icon: <IconTableColumn className="h-4 w-4 text-neutral-500" />,
  },
];

const SliderComponent = () => {
  return (
    <>
      <div className="container ">
        <div className="mt-[100px]">
          <div className="mt-2 md:mt-4 mb-12 text-center">
            <h1 className="text-3xl font-bold tracking-tight sm:text-3xl">
              Services we provided
            </h1>
          </div>
          <Swiper
            modules={[Navigation, Pagination, A11y, Autoplay]}
            spaceBetween={30}
            slidesPerView={4}
            autoplay={{
              delay: 2500,
              disableOnInteraction: false,
            }}
            breakpoints={{
              500: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              640: {
                slidesPerView: 2,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 40,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 50,
              },
              1200: {
                slidesPerView: 4,
                spaceBetween: 50,
              },
            }}
            navigation
            // pagination={{ clickable: true }}
            onSwiper={(swiper) => console.log(swiper)}
            onSlideChange={() => console.log("slide change")}
          >
            {urls.map((url, i) => (
              <SwiperSlide key={i} className="-z-10 relative h-full w-full">
                <div className="row-span-1 bg-card min-h-[300px] rounded-xl group/bento hover:shadow-xl transition duration-200 p-4 dark:bg-black border-transparent justify-between flex flex-col space-y-4">
                  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-200 dark:from-neutral-900 dark:to-neutral-800 to-neutral-100">
                    <Image
                      width={300}
                      height={200}
                      src={url.src}
                      alt=""
                      className="w-full h-full object-cover rounded-xl"
                    />
                  </div>

                  {/* {header} */}
                  <div className="group-hover/bento:translate-x-2 transition duration-200">
                    <div className="text-sm text-navy-700 dark:text-white font-semibold mb-2 mt-2 text-center">
                      {url.title}
                    </div>
                    <div className="text-base font-medium text-slate-400 flex justify-center">
                      {/* {url.description} */}
                      Explore <ArrowRight className="w-[16px] ml-2 text-navy-700" />
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>



        
      </div>
    </>
  );
};
export default SliderComponent;
