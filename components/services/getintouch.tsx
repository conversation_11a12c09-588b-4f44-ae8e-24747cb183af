"use client";
import React, { useState } from "react";
import { Label } from "../ui/label";
import { cn } from "@/lib/utils";
import { Input } from "./servicesinput";
import { toast } from "sonner";
import { getintouchValidation } from "../../lib/validators/getintouch-validator";
import { z } from "zod";

interface FormData {
  username: string;
  phoneNumber: string;
  email: string;
  organizationName: string;
  query: string;
}

export function GetInTouch({ showFields }) {
  const [formData, setFormData] = useState<FormData>({
    username: "",
    phoneNumber: "",
    email: "",
    organizationName: "",
    query: "",
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    let numericValue: any;
    if (name === "phoneNumber") {
      numericValue = value.replace(/[^0-9]/g, "").slice(0, 10);
    } else {
      numericValue = value;
    }
    setFormData((prevData) => ({
      ...prevData,
      [name]: numericValue,
    }));

    // Clear error when the user starts typing
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
  };

  // const validateForm = () => {
  //   try {
  //     setErrors({});
  //     getintouchValidation.parse(formData);
  //     return true;
  //   } catch (err) {
  //     if (err instanceof z.ZodError) {
  //       const fieldErrors: Partial<Record<keyof FormData, string>> = {};

  //       const firstError = err.errors[0];
  //       if (firstError) {
  //         fieldErrors[firstError.path[0] as keyof FormData] =
  //           firstError.message;
  //       }

  //       setErrors(fieldErrors);
  //       return false;
  //     }
  //     return false;
  //   }
  // };
  const validateForm = () => {
    const fieldErrors: Partial<Record<keyof FormData, string>> = {};

    if (!formData.username) {
      fieldErrors.username = "User Name is required.";
    }
    if (!formData.phoneNumber) {
      fieldErrors.phoneNumber = "Phone Number is required.";
    } else if (formData.phoneNumber.length < 10) {
      fieldErrors.phoneNumber = "Phone Number must be 10 digits.";
    }
    if (!formData.email) {
      fieldErrors.email = "Email Address is required.";
    }
    if (showFields) {
      if (!formData.organizationName) {
        fieldErrors.organizationName = "Organization Name is required.";
      }
    }
    if (!formData.query) {
      fieldErrors.query = "Query is required.";
    }

    setErrors(fieldErrors);

    return Object.keys(fieldErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    const formDataToSend = {
      userName: formData.username,
      phoneNumber: formData.phoneNumber,
      email: formData.email,
      organizationName: formData.organizationName,
      query: formData.query,
    };
    // console.log("Sending data:", formDataToSend);
    setIsLoading(true);
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL}/api/contactInfo`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json", // Ensure this matches the format of your body
          },
          body: JSON.stringify(formDataToSend), // Send the form data as a JSON string
        }
      );

      const data = await res.json();

      if (!res.ok) {
        const errorText = data.errors?.[0]?.message || "Unknown error";
        console.error("Error response text:", errorText);
        toast.error(errorText);
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: data.errors?.find((error: any) => error.field === "email")
            ? "Email must be unique"
            : prevErrors.email,
        }));
        toast.error(`HTTP error! status: ${res.status}`);
        // throw new Error(`HTTP error! status: ${res.status}`);
      } else {
        toast.success("Form submitted successfully");
        setFormSubmitted(true);
        setFormData({
          username: "",
          phoneNumber: "",
          email: "",
          organizationName: "",
          query: "",
        });
      }

      // console.log("Response data:", data);
      // console.log("Form submitted", formData);
    } catch (error) {
      console.error("There was a problem with the fetch operation:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <>
        <div className="w-full mx-auto rounded-none md:rounded-md pt-10 px-4 md:p-8">
          <h2 className="text-3xl font-bold tracking-tight sm:text-3xl text-center">
            Write to us
          </h2>

          <form className="my-8" onSubmit={handleSubmit}>
            <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 mb-4">
              <LabelInputContainer>
                <Label htmlFor="username">User Name</Label>
                <Input
                  id="username"
                  name="username"
                  className="bg-transparent placeholder:text-slate-400 focus:text-slate-400 mb-2 md:mb-0"
                  placeholder="Enter your name"
                  type="text"
                  value={formData.username}
                  onChange={handleChange}
                />
                {errors.username && (
                  <p className="text-yellow-300 text-sm">{errors.username}</p>
                )}
              </LabelInputContainer>
              <LabelInputContainer>
                <Label htmlFor="phoneNumber">Phone</Label>
                <Input
                  id="phoneNumber"
                  name="phoneNumber"
                  className="bg-transparent placeholder:text-slate-400 focus:text-slate-400"
                  placeholder="Enter your number"
                  type="text"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                />
                {errors.phoneNumber && (
                  <p className="text-yellow-300 text-sm">
                    {errors.phoneNumber}
                  </p>
                )}
              </LabelInputContainer>
            </div>
            <LabelInputContainer className="mb-4">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                className="bg-transparent placeholder:text-slate-400 focus:text-slate-400 mb-2 md:mb-0"
                placeholder="Enter your Email"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
              {errors.email && (
                <p className="text-yellow-300 text-sm">{errors.email}</p>
              )}
            </LabelInputContainer>
            <LabelInputContainer className="mb-4">
              <Label htmlFor="organizationName">Organization Name</Label>
              <Input
                id="organizationName"
                name="organizationName"
                className="bg-transparent placeholder:text-slate-400 focus:text-slate-400cmb-2 md:mb-0"
                placeholder="Organization name"
                type="text"
                value={formData.organizationName}
                onChange={handleChange}
              />
              
              {showFields && errors.organizationName && (
                <p className="text-yellow-300 text-sm">
                  {errors.organizationName}
                </p>
              )}
            
            </LabelInputContainer>
            <LabelInputContainer className="mb-4">
              <Label htmlFor="queries">Your Queries</Label>
              <textarea
                id="query"
                name="query"
                placeholder="Enter your comment here"
                className="mb-2 md:mb-0 flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background text-slate-400 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-400 focus:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formData.query}
                onChange={handleChange}
              ></textarea>
              {errors.query && (
                <p className="text-yellow-300 text-sm">{errors.query}</p>
              )}
            </LabelInputContainer>
            {formSubmitted && (
              <p className="items-center h-full pb-3">
                Thank you for contacting us, Our team will get back to you soon.
              </p>
            )}
            <button
              className="bg-background relative group/btn from-black dark:from-zinc-900 dark:to-zinc-900 to-neutral-600 block dark:bg-zinc-800 w-full text-white rounded-md h-10 font-medium shadow-[0px_1px_0px_0px_#ffffff40_inset,0px_-1px_0px_0px_#ffffff40_inset] dark:shadow-[0px_1px_0px_0px_var(--zinc-800)_inset,0px_-1px_0px_0px_var(--zinc-800)_inset]"
              type="submit"
            >
              {isLoading ? (
                <Loader />
              ) : (
                <>
                  Send &rarr;
                  <BottomGradient />
                </>
              )}
            </button>
          </form>
        </div>
      </>
    </>
  );
}
const Loader: React.FC = () => {
  return (
    <div className="flex justify-center">
      {/* <div className="loader"></div> */}
      <div>Submiting...</div>
    </div>
  );
};
const BottomGradient: React.FC = () => {
  return (
    <>
      <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
      <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
    </>
  );
};

const LabelInputContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn("flex flex-col space-y-2 w-full", className)}>
      {children}
    </div>
  );
};
