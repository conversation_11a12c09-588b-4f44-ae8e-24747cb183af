"use client";
import React, { useState } from "react";
import { BentoGrid, BentoGridItem } from "../ui/bento-grid";
import Image from "next/image";
import MaxWidthWrapper from "../MaxWidthWrapper";
import { Dialog, DialogContent, DialogTrigger } from "@radix-ui/react-dialog";
import { CardDescription } from "../ui/card";
import { X } from "lucide-react";
interface MenuItemProps {
  recentProject: boolean;
}

const Skeleton = ({ url }: { url: string }) => (
  <div className="flex w-full h-full hue-rotate-30 bg- object-cover min-h-[6rem] rounded-xl from-neutral-200 to-neutral-100">
    <Image
      width={400}
      height={400}
      src={url}
      alt="events"
      className="rounded-xl opacity-70 w-full h-full object-cover"
    />
  </div>
);

const items = [
  {
    title: "Prompt as a Service",
    description:
      "Harness the power of tailored prompts to generate high-quality AI outputs effortlessly and efficiently with ease.",
    imageUrl: "asset.rentprompts.com/image%20(6).webp",
  },
  {
    title: "Listing and Renting AI Applications (AI Apps)",
    description:
      "Explore, list, and rent a variety of generative AI applications (AI Apps) to reduce costs and enhance productivity.",
    imageUrl: "asset.rentprompts.com/image%20(13).webp",
  },
  {
    title: "Easy UI and APIs on Rent",
    description:
      "Integrate AI solutions into your workflows with our user-friendly interface and robust APIs, designed for simplicity.",
    imageUrl: "asset.rentprompts.com/image%20(8).webp",
  },
  {
    title: "AI Assets Listing and Lead Generation",
    description:
      "List AI assets for portfolio building or sale, generating leads and connections for brands with engineers.",
    imageUrl: "asset.rentprompts.com/image%20(14).webp",
  },
  {
    title: "AI Lab",
    description:
      "Our AI Lab offers a playground for learners and builders, featuring an intuitive no-code app builder for creating.",
    imageUrl: "asset.rentprompts.com/image%20(17).webp",
  },
  {
    title: "Community of Generative AI Engineers",
    description:
      "Join a thriving community of generative AI engineers to collaborate, learn, innovate, and stay ahead in AI advancements.",
    imageUrl: "asset.rentprompts.com/image%20(15).webp",
  },
  {
    title: "Academy and Learnings",
    description:
      "Gain insights and skills through our dedicated learning resources and training programs to expand your AI knowledge.",
    imageUrl: "asset.rentprompts.com/image%20(19).webp",
  },
  {
    title: "Bounties and Gigs",
    description:
      "Participate in bounties and gigs to showcase your expertise, solve real-world problems, and earn rewards in AI.",
    imageUrl: "asset.rentprompts.com/image%20(18).webp",
  },
];

export function OurPortfolio({ recentProject }: MenuItemProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const openDialog = (item) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setSelectedItem(null);
    setIsDialogOpen(false);
  };
  return (
    <MaxWidthWrapper>
      <div className="my-[100px]">
        <div className="mb-10 text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-3xl">
            Core Platform Services
          </h2>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={closeDialog}>
          <DialogTrigger asChild>
            <BentoGrid
              className={`mx-auto grid-cols-1 md:grid-cols-2 ${
                recentProject ? "md:grid-cols-3" : "md:grid-cols-4"
              }`}
            >
              {items.map((item, i) => (
                <BentoGridItem
                  onClick={() => openDialog(item)}
                  key={i}
                  title={item.title}
                  description={item.description}
                  header={<Skeleton url={item.imageUrl} />}
                  className={`rounded-lg cursor-pointer ${
                    recentProject
                      ? `${
                          i === 3 || i === 3 ? "md:col-span-2" : ""
                        } bg-translated`
                      : "bg-card"
                  }`}
                />
              ))}
            </BentoGrid>
          </DialogTrigger>
          {isDialogOpen && selectedItem && (
            <DialogContent>
              {/* <DialogTitle> </DialogTitle> */}
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
                <div className="relative bg-card p-6 rounded-lg shadow-lg max-w-md mx-auto">
                  <button
                    // onClick={() => setDialogOpen(false)}
                    onClick={closeDialog}
                    className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100"
                  >
                    <X size={15} />
                  </button>
                  <CardDescription className="text-white">
                    <h4 className="text-2xl font-bold">{selectedItem.title}</h4>
                    <p>{selectedItem.description}</p>
                  </CardDescription>
                </div>
              </div>
            </DialogContent>
          )}
        </Dialog>
      </div>
    </MaxWidthWrapper>
  );
}
