import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, RatIcon } from "lucide-react";
import CounterNum from "./counternum";
import MaxWidthWrapper from "../MaxWidthWrapper";

const Counter = () => {
  const listItems = [
    {
      number: 1500,
      headline: "Active Developers",
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rat"><path d="M17 5c0-1.7-1.3-3-3-3s-3 1.3-3 3c0 .8.3 1.5.8 2H11c-3.9 0-7 3.1-7 7v0c0 2.2 1.8 4 4 4"></path><path d="M16.8 3.9c.3-.3.6-.5 1-.7 1.5-.6 3.3.1 3.9 1.6.6 1.5-.1 3.3-1.6 3.9l1.6 2.8c.2.3.2.7.2 1-.2.8-.9 1.2-1.7 1.1 0 0-1.6-.3-2.7-.6H17c-1.7 0-3 1.3-3 3"></path><path d="M13.2 18a3 3 0 0 0-2.2-5"></path><path d="M13 22H4a2 2 0 0 1 0-4h12"></path><path d="M16 9h.01"></path></svg>',
    },
    {
      number: 40,
      headline: "Community Projects",
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart-pulse"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/><path d="M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27"/></svg>',
    },
    {
      number: 50,
      headline: "Workshops & Webinars",
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart-pulse"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/><path d="M3.22 12H9.5l.5-1 2 4.5 2-7 1.5 3.5h5.27"/></svg>',
    },
    {
      number: 100,
      headline: "Bounties",
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot"><path d="M12 8V4H8"/><rect width="16" height="12" x="4" y="8" rx="2"/><path d="M2 14h2"/><path d="M20 14h2"/><path d="M15 13v2"/><path d="M9 13v2"/></svg>',
    },
  ];

  const iconMap = {
    "Enter Headline 1": Star,
    "Enter Headline 2": Heart,
    "Enter Headline 3": CheckCircle,
  };

  return (
    <>
      <MaxWidthWrapper>
        <div className="relative font-inter antialiased mt-[50px]">
          <main className="relative flex flex-col justify-center overflow-hidden">
            <div className="mb-8 text-center">
              <h2 className="text-3xl mb-5 font-bold tracking-tight sm:text-3xl">
                Strong Community and Developers
              </h2>
              <p>
                  RentPrompt thrives on collaboration and innovation, evident in
                  our vibrant developer community. Together, we shape the future
                  of AI prompts through projects, workshops, and open-source
                  contributions. Join us in cultivating an environment where
                  creativity flourishes and boundaries are redefined.
                </p>
            </div>

            <div className="grid grid-cols-1 gap-4 md:gap-6">
              {/* First Box */}

              <div className="w-full mx-auto ">
               
                <section className="grid gap-4 grid-cols-2 md:grid-cols-4 md:gap-4 mt-5">
                  {listItems.map((item) => (
                    <article
                      key={item.number}
                      className="text-white w-full text-center m-auto bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 p-3 rounded-md p-4 min-h-[176px] md:min-h-auto"
                    >
                      <div>
                        <div
                          dangerouslySetInnerHTML={{ __html: item.icon }}
                          className="mx-auto mb-4 flex justify-center"
                        />
                        <h3>
                          <CounterNum
                            finalValue={item.number} // Example final value
                            duration={3000} // Example duration
                          />
                          <span className="inline-flex font-semibold mb-2">
                            {item.headline}
                          </span>
                        </h3>
                      </div>
                    </article>
                  ))}
                </section>
              </div>

              {/* Second Box */}
              {/* <div className="w-full max-w-5xl mx-auto d-none md:d-block px-4 md:px-6">
                <Image
                  src="/img/aitoolimg/stong.jpg"
                  alt=""
                  width={570}
                  height={220}
                />
              </div> */}
            </div>
          </main>
        </div>
      </MaxWidthWrapper>
    </>
  );
};

export default Counter;
