// components/Counter.js
"use client";
import { useState, useEffect } from "react";

interface CounterNumProps {
  finalValue: number;
  duration: number;
}

const CounterNum = ({ finalValue, duration }: CounterNumProps) => {
  const [count, setCount] = useState(1); // Start count from 1
  let requestId: number | null = null;
  let startTime: number | null = null;

  useEffect(() => {
    const increment = finalValue - 1; // Adjust increment calculation

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const elapsedTime = timestamp - startTime;
      const progress = Math.min(elapsedTime / duration, 1);
      const incrementCount = Math.floor(progress * increment + 1); // Adjust incrementCount calculation

      setCount(incrementCount);

      if (elapsedTime < duration) {
        requestId = requestAnimationFrame(animate);
      }
    };

    requestId = requestAnimationFrame(animate);

    return () => {
      if (requestId) {
        cancelAnimationFrame(requestId);
      }
    };
  }, [finalValue, duration]);

  return (
    <span className="flex justify-center tabular-nums text-white text-3xl font-extrabold mb-2">
      {count}+
    </span>
  );
};

export default CounterNum;


