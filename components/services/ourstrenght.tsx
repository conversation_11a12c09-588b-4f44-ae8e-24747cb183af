"use client";
import Image from "next/image";
import React, { useState } from "react";
import MaxWidthWrapper from "../MaxWidthWrapper";
import { Icons } from "../Icons";

const strengths = [
  {
    title: "Rapid Prototyping and Deployment",
    description:
      "Achieve POCs in a week and MVPs in a month with our rapid prototyping.",
  },
  {
    title: "Versatile AI Solutions",
    description:
      "Solutions range from simple chatbots to complex AI agents and workflow.",
  },
  {
    title: "Expert Community of AI Engineers",
    description:
      "Benefit from collective knowledge, collaboration, and staying updated with the latest AI.",
  },
  {
    title: "User-Friendly Tools and Integration",
    description:
      "Easy-to-use interface and robust APIs for seamless integration into existing workflows.",
  },
  {
    title: "Comprehensive AI Lab",
    description:
      "Intuitive no-code app builder and a playground for experimentation and development.",
  },
  {
    title: "Cost-Effective Solutions",
    description:
      "Marketplace for listing and renting AI applications, significantly reducing development costs.",
  },
  {
    title: "Commitment to Security and Ethics",
    description:
      "Adherence to best practices in data security and responsible AI implementation. Trust.",
  },
  {
    title: "Tailored Consulting Services",
    description:
      "Personalized guidance in various AI applications, provided at no additional cost. Receive.",
  },
];

const OurStrengths = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Filter the items with even indices for the first box
  const firstBoxItems = strengths.filter((_, index) => index % 2 === 0);

  // Filter the items with even indices for the third box starting from index 2
  const thirdBoxItems = strengths.filter((_, index) => index % 2 === 1);
  return (
    <MaxWidthWrapper>
      <div className="mt-24">
        {/* <div className="container mx-auto w-full h-full mt-100"> */}
        {/* <div className="container mx-auto w-full h-full mt-100 border border-white"> */}
          <div className="mb-8 text-center">
            <h2 className="text-3xl mb-4 font-bold tracking-tight sm:text-3xl">
              Our Strengths
            </h2>
            <p>
              Rentprompts stands out by providing rapid, versatile, and
              cost-effective generative AI solutions. Supported by a vibrant
              community of experts and user-friendly tools, we ensure that both
              novices and seasoned developers can swiftly bring innovative ideas
              to life. Our platform prioritizes security, ethics, and continuous
              learning, making Rentprompts the go-to choice for anyone looking
              to harness the power of AI efficiently and responsibly.
            </p>
          </div>
         
         <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
            {/* First Box */}
            <div className="rounded-lg grid grid-row-4 gap-2">
              {firstBoxItems.map((item, index) => (
                <div
                  key={index}
                  className={`order-1 bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 
                  border-muted-foreground hover:border-gray-300 rounded-md rounded-lg shadow-xl w-full p-2`}
                //   className={`order-1 bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 p-3 rounded-md p-4 rounded-lg shadow-xl w-full p-2 ${
                //     isExpanded ? "max-h-full" : "max-h-32"
                //   } overflow-hidden cursor-pointer`}
                //   onClick={toggleExpand}
                >
                  {item.description}
              
                {/* <div className="text-indigo-300 text-sm mt-2 text-right">
                    {isExpanded ? "Show Less" : "Read More"}
                  </div> */}
              </div>
            ))}
          </div>

          {/* Second Box with Image */}
          <div className="bg-transparent rounded-lg flex items-center justify-center">
            <Icons.logo className="absolute pt-4 h-full z-10 stroke-muted-foreground fill-muted-foreground/80 w-32 stroke-[0.25rem]" />
            <Image
                width={400}
                height={300}
                src="asset.rentprompts.com/Image.png"
                alt="Example"
                className="object-cover h-full w-full mix-blend-luminosity opacity-50 rounded-lg"
              />
            
          </div>

          {/* Third Box */}
          <div className="rounded-lg grid grid-row-4 gap-2">
            {thirdBoxItems.map((item, index) => (
              <div
                key={index}
                className={`bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg border-2 border-muted-foreground hover:border-gray-300 rounded-md rounded-lg shadow-xl w-full p-2`}
              >
                <div className="font-semibold text-white text-base ">
                  {item.title}
                </div>
                <p className="text-indigo-300 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
     </div> 
    </MaxWidthWrapper>
  );
};

export default OurStrengths;
