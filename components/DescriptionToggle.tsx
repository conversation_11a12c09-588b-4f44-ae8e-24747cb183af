// DescriptionToggle.jsx
'use client';

import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

const DescriptionToggle = ({ description }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleDescription = () => setIsExpanded(!isExpanded);

  // Limit description to 200 characters
  const shortDescription = description.slice(0, 200);

  return (
    <div className="space-y-4 text-base text-white">
      <ReactMarkdown
        components={{
          a: ({ href, children }) => (
            <a href={href} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
              {children}
            </a>
          ),
        }}
        className='first-letter:uppercase'
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
      >
        {isExpanded ? description : `${shortDescription}...`}
      </ReactMarkdown>
      
      <button
        onClick={toggleDescription}
        className="text-blue-500 hover:underline focus:outline-none"
      >
        {isExpanded ? 'Show less' : 'Show more'}
      </button>
    </div>
  );
};

export default DescriptionToggle;
