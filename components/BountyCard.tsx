"use client";
import Link from "next/link";

import creatorImage1 from "@/public/img/9c45a21219389871.6230b1b939ae4.jpg";




import { AnimatedTooltip } from "./ui/animated-tooltip";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Pencil, Star } from "lucide-react";
import { Trash2 } from "lucide-react";

import { Bounty } from "@/server/payload-types";

interface BountyCardProps {
  bounty: any;
  index: number;
  user: User | null;
  bountyStatus: string;
  onDelete?: (bounty: any) => void; // Callback function for deleting bounty
}
type User = {
  id: string;
  // Add other properties of the user if necessary
};

const BountyCard: React.FC<BountyCardProps> = ({
  bounty,
  index,
  user,
  bountyStatus,
}) => {
  const [isApplied, setIsApplied] = useState<boolean>(false);

  const calculateDaysLeft = () => {
    const completionDate = new Date(bounty?.completionDate as string);
    const currentDate = new Date();
    const timeDiff = completionDate.getTime() - currentDate.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    return daysLeft;
  };

  // const { createdAt, completionDate } = bounty;
  const daysLeft = calculateDaysLeft();
  const isExpired = daysLeft < 0;
  const isCompleted = bounty.status === "completed";
  return (
    <div>
      {/* ----------------------- First Variation ------------------------ */}
      {bountyStatus === "myBounty" ? (
        <div
          className="relative rounded-xl group hover:shadow-xl transition duration-200 shadow-input bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent hover:scale-[1.05] group p-4 gap-4 md:gap-6"
          key={index}
        >
          <div className="flex flex-col gap-3">
            <Link href={`/bounties/${bounty.slug}`}>
              <div className="flex justify-end">
                <div>
                  {bounty.isFeatured === true && (
                    <div className="absolute top-[-10px] -left-5 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                      <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                        FEATURED
                        <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <p className="absolute top-[-1px] right-0 w-auto px-2 z-20 text-sm font-bold bg-white text-indigo-700 rounded-xl flex justify-center items-center">
                {bounty?.status}
              </p>
              <div className="flex justify-between items-center">
                <div className="flex gap-2 items-baseline mt-2">
                  <span className="text-5xl font-extrabold flex gap-[2px] items-center">
                    {bounty?.estimatedPrice === 0 ? (
                      ""
                    ) : (
                      <img
                        src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                        alt="Coin"
                        style={{ width: "50px", height: "55px" }}
                        loading="lazy"
                      />
                    )}

                    {bounty.estimatedPrice === 0 ? (
                      <div className="px-4 py-2 rounded-full bg-gradient-to-r  text-white text-2xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                        FREE
                      </div>
                    ) : (
                      bounty.estimatedPrice
                    )}
                  </span>
                  {/* <span className="text-gray-300">{bounty.rupees_price}</span> */}
                </div>
                <div className="flex flex-col-reverse md:flex-row justify-between items-end md:items-center md:gap-2">
                  {/* <p className="text-sm text-gray-300">{bounty.due_date}</p> */}
                  <ul>
                    <li className="text-gray-300 text-sm list-disc">
                      {(() => {
                        const createdAtDate = new Date(bounty?.createdAt);
                        const currentDate = new Date();
                        const timeDifference =
                          currentDate.getTime() - createdAtDate.getTime();
                        const daysDifference = Math.floor(
                          timeDifference / (1000 * 3600 * 24)
                        );
                        if (daysDifference >= 7) {
                          const weeksDifference = Math.floor(
                            daysDifference / 7
                          );
                          return `${weeksDifference} Week${weeksDifference > 1 ? "s" : ""} ago`;
                        } else {
                          return `${daysDifference} Day${daysDifference > 1 ? "s" : ""} ago`;
                        }
                      })()}
                    </li>
                  </ul>

                  <div className="bg-indigo-900 px-2 py-1 rounded-full text-sm hover:bg-indigo-800">
                    <p className="bg-indigo-900 px-2 py-1 rounded-full text-sm hover:bg-indigo-800 text-indigo-200">
                      {isCompleted ? "Closed" : isExpired ? "Expired" : "Open"}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-2xl md:text-3xl font-medium capitalize">
                  {`${
                    bounty?.title?.length > 50
                      ? bounty?.title.substring(0, 50) + "..."
                      : bounty?.title
                  }`}
                </h3>
              </div>

              <div className="flex justify-between sm:items-center flex-col sm:flex-row gap-3 -mt-1 md:mt-2">
                <p className="text-sm text-gray-300 first-letter:uppercase">
                  {`${
                    bounty?.content?.length > 100
                      ? bounty?.content.substring(0, 120) + "..."
                      : bounty?.content
                  }`}
                </p>
                <div className="font-bold">
                  <div className="flex flex-row items-center justify-end ml-4-4">
                    {/* <AnimatedTooltip items={people} component={'bountypage'} /> */}
                    {bounty?.applicants > 0 ? (
                      <div>{bounty?.applicants} Applicants</div>
                    ) : (
                      <div> No Applicants</div>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
      ) : (
        <div
          className=" rounded-xl group hover:shadow-xl transition duration-200 shadow-input 
          bg-gradient-to-br from-indigo-600 to-indigo-700 border border-transparent hover:scale-[1.05] group 
           gap-4 md:gap-6 p-4 relative"
          key={index}
        >
          <div className="flex flex-col gap-3">
            <Link href={`/bounties/${bounty.slug}`}>
              <div className="flex justify-end">
                <div>
                  {/* {bounty.isFeatured === true && (
                    <div className="absolute top-0 right-0  text-yellow-500 rounded text-xs">
                      <Star className="w-6 h-6 text-yellow-500 fill-current bg-white rounded-full" />
                    </div>
                  )} */}
                  {bounty.isFeatured === true && (
                    <div className="absolute top-[-10px] -left-5 w-auto px-2 z-20 text-sm font-bold text-indigo-700 rounded-xl flex justify-center items-center">
                      <div className="relative bg-gradient-to-r from-amber-500 to-pink-500 text-white text-xs font-extrabold px-2 py-1 rounded-tl-lg rounded-br-lg shadow-lg overflow-hidden animate-pulse-glow">
                        FEATURED
                        <span className="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-80 w-full h-full transform translate-x-full animate-slide-glow" />
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex gap-2 items-baseline">
                  <span className="text-5xl font-extrabold flex items-center gap-[2px]">
                    {bounty?.estimatedPrice === 0 ? (
                      ""
                    ) : (
                      <img
                        src={`${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2}/coin-png.png`}
                        alt="Coin"
                        style={{ width: "50px", height: "55px" }}
                         loading="lazy"
                      />
                    )}

                    {bounty?.estimatedPrice === 0 ? (
                      <>
                        <div className="px-4 py-2 rounded-full bg-gradient-to-r  text-white text-2xl font-bold uppercase tracking-wider shadow-xl border-2 border-white transition-all duration-300 ease-in-out hover:scale-105  hover:shadow-2xl">
                          {"FREE"}
                        </div>
                      </>
                    ) : (
                      bounty?.estimatedPrice
                    )}
                  </span>
                  {/* <span className="text-gray-300">{bounty.rupees_price}</span> */}
                </div>
                <div className="flex flex-col-reverse md:flex-row justify-between items-end md:items-center md:gap-2">
                  {/* <p className="text-sm text-gray-300">{bounty.due_date}</p> */}
                  <ul>
                    <li className="text-gray-300 text-sm list-disc">
                      {(() => {
                        const createdAtDate = new Date(bounty.createdAt);
                        const currentDate = new Date();
                        const timeDifference =
                          currentDate.getTime() - createdAtDate.getTime();
                        const daysDifference = Math.floor(
                          timeDifference / (1000 * 3600 * 24)
                        );
                        if (daysDifference >= 7) {
                          const weeksDifference = Math.floor(
                            daysDifference / 7
                          );
                          return `${weeksDifference} Week${weeksDifference > 1 ? "s" : ""} ago`;
                        } else {
                          return `${daysDifference} Day${daysDifference > 1 ? "s" : ""} ago`;
                        }
                      })()}
                    </li>
                  </ul>

                  {/* <button onClick={registerUser}
                className=" bg-indigo-900 px-2 py-1 rounded-full text-sm hover:bg-indigo-800"> */}
                  {/* { 
                    bounty.status
                  } */}
                  <p className="bg-indigo-900 px-2 py-1 rounded-full text-sm hover:bg-indigo-800 text-indigo-200">
                    {isCompleted ? "Closed" : isExpired ? "Expired" : "Open"}
                  </p>
                  {/* </button> */}
                </div>
              </div>

              <div>
                <h3 className="text-2xl md:text-3xl font-medium line-clamp-2 leading-7 mt-1 capitalize">
                  {`${
                    bounty?.title.length > 50
                      ? bounty?.title.substring(0, 50) + "..."
                      : bounty?.title
                  }`}
                </h3>
              </div>

              <div className="flex justify-between sm:items-center flex-col sm:flex-row gap-3 mt-1 md:mt-2">
                <p className="text-sm text-gray-300 first-letter:uppercase">
                  {`${
                    bounty?.content.length > 100
                      ? bounty?.content.substring(0, 120) + "..."
                      : bounty?.content
                  }`}
                </p>
                <div className="font-bold">
                  <div className="flex flex-row items-center justify-end  ml-4">
                    {/* <AnimatedTooltip items={people} component={'bountypage'} /> */}
                    {bounty?.applicants > 0 ? (
                      <div>{bounty?.applicants} Applicants</div>
                    ) : (
                      <div>No Applicants</div>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default BountyCard;
