"use client";

import { Query<PERSON><PERSON>da<PERSON>, TQueryValidator } from "@/lib/validators/query-validator";
import { Product , User} from "@/server/payload-types";
import Link from "next/link";
import ProductListing from "./ProductListing";
import { useEffect, useRef, useState } from "react";
import axios from "axios";
import payload from 'payload';

import { z } from 'zod';
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ProductReelProps {
  title: string
  subtitle?: string
  href?: string
  filters?: { [key: string]: string[] };
  query: TQueryValidator;
  user: User
  component?: string;
}

const FALLBACK_LIMIT = 4;

const ProductReel = (props: ProductReelProps) => {
  const { title, subtitle, href, query, filters, user, component } = props;
  const [data, setData] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef(null);

  useEffect(() => {
    setIsLoading(true);
    const fetchProducts = async () => {
      try {
        const result = await axios.post(`/api2/products?limit=20`, {
          query: {
            ...query,
            filters: {
              ...filters,
            }
          }, 
        //  limit: query.limit ?? FALLBACK_LIMIT,
        limit: 0,
        });
        setData(result?.data);
      } 
      catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, [query, filters]);

  const products = data ? data?.items : [];

  let map: (Product | null)[] = [];
  if (products && products.length) {
    map = products;
  } else if (isLoading) {
    map = new Array<null>(query.limit ?? FALLBACK_LIMIT).fill(null);
  }

  if (!isLoading && products.length === 0) {
    return (
      <div className="text-center text-white py-6">
        No items available, try another filter.
      </div>
    );
  }
  return (
    <section className={`${component === "rentplacePro" || "marketplace" ? "md:py-0 py-0" : "md:py-14 py-10"}`}>
      <div className="flex mb-4 justify-between items-center">
        <div className="max-w-2xl lg:max-w-4xl lg:px-0">
          {title ? (
            <h3 className="text-3xl md:text-2xl font-bold sm:text-3xl">{title}</h3>
          ) : null}
          {subtitle ? (
            <p className="mt-2 text-sm text-muted-foreground">{subtitle}</p>
          ) : null}
        </div>

        {href ? (
          <Link
          href="/marketplace?displayProducts=true"
            className="bg-white/20  hover:bg-white/40 text-white font-semibold py-1 px-2 md:px-3 rounded-full flex items-center gap-1 min-w-fit text-sm md:text-md"
          >
            View All
            <span className="mb-[2px]">&rarr;</span>
          </Link>
        ) : null}
      </div>
 
    <div className="relative">
        <div className=" flex items-center w-full">
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-10 sm:gap-x-6 md:grid-cols-3 lg:grid-cols-4 md:gap-y-10 lg:gap-x-8">
            {map.map((product, i) => (
              <ProductListing
                key={`product-${i}`}
                product={product}
                index={i}
                user={user}
              />
            ))}
          </div>
        </div>
      </div> 

    </section>
  );
};

export default ProductReel;
