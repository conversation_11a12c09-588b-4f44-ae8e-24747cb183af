const knowledgeBase = {
//  Company Vision & Purpose, my profile section
"Why RentPrompts?": {
text: "RentPrompts is a community-driven Gen Al platform focused on Human and Al collaboration, dedicated to simplifying the production and monetization of generative Al applications. Our team of innovative Generative Al Engineers works to empower users through a user-friendly marketplace, central hub of prompts, Al apps and Agents.",
alternateQuestions: [],
},

"How can I view my profile?": {
text: "To view your profile, follow the below steps:\n• **Sign in** to your account.\n• Click on the **profile icon** located at the top-right corner of the page.\n• Select '**View Profile**' from the dropdown menu.\n\nYou will be directed to your profile page, where you can view your profile information.",
    link: "https://rentprompts.com/profile",
    linkText: "My Profile",
    alternateQuestions: [
      'how to see my profile page',
      'how to view my profile',
      'how to check my profile',
      'how to see my profile',
    ],
  },

  "How can I see my dashboard?": {
    text: "To see your dashboard, follow the below steps:\n• Sign in to your account.\n• Click on the **profile icon** located at the top-right corner of the page.\n• Select '**Dashboard**' from the dropdown menu.\n\n You'll be taken to your personalized dashboard.",
    link: "https://rentprompts.com/dashboard",
    linkText: "My Dashboard",
    alternateQuestions: [],
  },

  "How can I payout my account's Joules credit?": {
    text: "To payout your account joules, follow the below steps:\n• Sign in to your account.\n• Click on the **profile icon** at the top-right corner of the page.\n• Select '**Payout**' from the dropdown menu.\n• Enter your preferred payment method details.\n• Confirm the transaction.\n\n**🔔 Note**: Withdrawals are only available when your account balance is 500 Joules or more.\nYour payout will be processed according to the timeline of the selected payment method.",
    link: "https://rentprompts.com/payout",
    linkText: "Payout",
    alternateQuestions: [
      'How can I payout my money',
    ],
  },

  "Where can I see my purchases?": {
    text: "To view your Purchases, follow the below steps:\n• Sign in to your account.\n• Click on the **profile icon** at the top-right corner of the page.\n• Select '**Purchases**' from the dropdown menu.\n\n You will see a list of your product and course purchases.",
    alternateQuestions: [
      'how can i view my purchases',
      'how can i see my purchases',
      'how can i check my purchases',
    ],
  },

  // Authentication & Onboarding
  "How do I sign up?": {
    text: "Follow the below steps:\n• Click on the **Sign Up** button.\n• You can register using your Google or GitHub account, or manually with your email and password.\n\n**🔔 Note**: Earn free 25 credits on first Sign-up.",
    link: "https://rentprompts.com/sign-up?from=%2Faboutus",
    linkText: "SignUp",
    alternateQuestions: [
    "how to sign up",
    "how to create an account",
    "how to register",
],
  },

  "How do I sign in to my account?": {
    text: "Follow the below steps:\n • Click on the **Sign In** button.\n• You can login using your Google or GitHub account, or manually with your registered email and password.\n\n**🔔 Note**: If you have forgotten your password, click on the '**Reset Password**' button to recover it.",
    link: "https://rentprompts.com/sign-in?from=%2Faboutus",
    linkText: "SignIn",
    alternateQuestions: [
    "sign in",
    "SignIN",
    "Sign In",
    "login",
    "log in",
    "signin",
],
  },

  "What if I forgot my password?": {
    text: "Follow the below steps:\n• Click on the '**Reset Password**' button.\n• Enter your registered email address.\n• Click on '**Send Reset Link**'.\n• You will receive recovery instructions at your registered email address.\n\n**🔔 Note**: Follow the instructions to reset your password.",
    link: "https://rentprompts.com/sign-in?from=%2Faboutus",
    linkText: "Reset Password",
    alternateQuestions: [
    "how to create reset password",
],
  },

  "How can I logout of my account?": {
    text: "To **log out** of your account:\n• Click on your **profile icon** located at the top-right corner of the page.\n• Click '**Logout**'.\n\nThis will securely sign you out of your account from Rentprompts.",
    alternateQuestions: [
],
  },

  // Wallet & Joules
  "How can I recharge or buy joules for my account": {
    text: "To recharge, you need to follow the below steps:\n• Click on the '**Buy Joules**' button below or\n• Click on the **Joule icon** at the top-left corner of the page.\n• Choose a plan that suits your needs.\n• Enter the amount manually or use the slider to set the desired value.\n• Select your preferred payment method.\n• Complete the payment process.\n\n**🔔 Note**:In case payment failed contact us.",
    link: "https://rentprompts.com/pricing",
    linkText: "Buy Joules",
    alternateQuestions: [
    "joules section",
    "How can I recharge",
    "How can I recharge my account",
    "How can I recharge my joules",
    "How can I buy joule",
    "How can I recharge my joules account",
],
  },


  "What is the price of 1 Joule in INR?": {
    text: "The price of 1 Joule is equal to 1 INR.",
    alternateQuestions: [
    'one joule equal to how much',
    'price of 1 joule',
    'price of 1 joule in inr',
    'price of 1 joule in rupees',
    'price of 1 joule in indian rupees',
],
  },

  // AI Rapps
  "What are AI-Apps?": {
    text: "Al Applications are easy to build Al-powered mini applications on RentPrompts which can generate anything from text to images, videos or audio.",
    alternateQuestions: [
    'what is app',
    'what is ai rapps',
    'what is ai apps',
    'what is ai application',
],
  },

  "How can I create an AI-Apps?": {
    text: "To create an Al-App on Rentprompts:\n• Click on the '**AI Studio**' button in the navigation bar.\n• **Login** at **rentprompts.ai** using your RentPrompts credentials.\n\n Once logged in, you can create **public** or **private Al apps** as per your preference.",
    link: "https://rentprompts.com/dashboard",
    linkText: "Create AI-Apps",
    alternateQuestions: [
    "how to apply for rapps",
    "How to create an AI Apps"
],
  },

  "Where can I find my AI-Apps?": {
    text: "To view your created AI-Apps:\n• Click the **Sell** button at navbar.\n• Then Select **Al-Apps** on the left sidebar of the **dashboard**.\n\n**🔔 Note**: If No Apps available. Create your first AI App!",
    link: "https://rentprompts.com/dashboard/AI-Apps",
    linkText: "My Rapps",
    alternateQuestions: [
    'how check my ai apps',
    'how to see my ai apps',
    'how to view my ai apps',
    'how to find my ai apps',
],
  },

  "How can I explore all listed AI Apps?": {
    text: "To explore all listed AI-Apps:\n• Click '**Marketplace**' of navbar.\n\n On the marketplace page you all can see the listed ai apps.",
    link: "https://rentprompts.com/marketplace",
    linkText: "Explore AI-Apps",
    alternateQuestions: [
      "how to explore ai apps",
      "how to see all listed ai apps",


    ],
  },

  // Content Creation (Blog/Product)
  "What is Blog?": {
    text: "Blogs are informative articles that share updates, insights, and tips about AI, productivity, and latest technologies & development to help users stay informed and inspired.",
    alternateQuestions: [],
  },

  "How can I create a blog?": {
    text: "To create a blog:\n• Go to your dashboard.\n• Click '**Create Blog**'.\n• Add your content, images, and tags and then publish.\n\n Alse click on below button to create a blog.",
    link: "https://rentprompts.com/dashboard/create/blog",
    linkText: "Create Blog",
    alternateQuestions: [
     "how to apply for blog",
    "how to create a blog",
    "how to create blog",
    "how to create a blog post",
    ],
  },

  "Where are my blogs listed?": {
    text: "You can view your blogs on the dashboard.",
    link: "https://rentprompts.com/dashboard/blogs",
    linkText: "My Blogs",
    alternateQuestions: [
      "listed blogs",
    ],
  },

  "Where can I see all the products of Rentprompt?": {
    text: "To view all available products on RentPrompts:\n• Click the '**Explore Products**' button.\n• You'll be able to browse all listings in the marketplace.\n\n Alse click on below button to explore products.",
    link: "https://rentprompts.com/products",
    linkText: "Explore Products",
    alternateQuestions: [],
  },

  "How Do I create a product?": {
    text: "To create a product:\n• Go to the dashboard.\n• Click '**Create Product**'.\n• Fill in the product details, and submit.\n\n Once approved, your product will be listed in the marketplace.",
    link: "https://rentprompts.com/dashboard",
    linkText: "Create Product",
    alternateQuestions: [],
  },

  "Where can I find my products?": {
    text: "To manage your product listings:\n• Go to your dashboard.\n• Click on **products** on the sidebar to view and manage your listings.\n\n You can also click on the below button to view your products.",
    link: "https://rentprompts.com/dashboard/products",
    linkText: "My Products",
    alternateQuestions: [
      "where i can see my products",
      "where can i find my products",
      "where can i see my products",
      "where can i check my products",
    ],
  },

  //  Prompt Marketplace
  "How can I get a prompt?": {
    text: "Follow the below steps:\n• Go to the marketplace.\n• Open the ai-apps which prompt you want.\n• Then click the **get prompt** button.\n\n**🔔 Note**: Button will be visible if the creator wants to sell the prompt.",
    link: "https://rentprompts.com/marketplace",
    linkText: "Marketplace",
    alternateQuestions: [
    "How can I get a prompt",
    "How do I browse the prompt marketplace?",
    "How can I explore the prompt marketplace?",
    "How can I see the prompt marketplace?",
    "How can I purchase a prompt?",
   
],
  },

 
  "What happens if someone misuses my prompt?": {
    text: "Report any misuse via filling the form.",
    link: "https://rentprompts.com/aboutus?from=%2Faboutus",
    linkText: "Contact Us",
    alternateQuestions: [],
  },

  // Generative Capabilities
  "How can I generate text, images, audio, or video?": {
    text: "Follow the below steps:\n• Click on the **Generate** button.\n• Use our '**Generate**' page to access Al-powered generation for text, audio, video, image, 3D, and models.\n\nYou can also click on below button.",
    link: "https://rentprompts.com/generate",
    linkText: "Generate",
    alternateQuestions: [
      "how to generate text",
      "how to generate images",
      "how to generate audio",
      "how to generate video",
      "how to generate 3D",
      "how to generate models",
    ],
  },
"How can I use AI models ": {
    text: "Go to the **Generate** page via click on **generate link**.",
    link: "https://rentprompts.com/generate",
    linkText: "Generate",
    alternateQuestions: [],
  },

  // Events
  "How can I explore Events on RentPrompts?": {
    text: "Details for exploring Events on RentPrompts are not specified in the provided PDF. The existing link for 'Explore Events' in the knowledge base points to a Discord server.",
    link: "https://discord.com/invite/kPkYbzMvN3",
    linkText: "Explore Events",
    alternateQuestions: [],
  },

  // Bounty System
  "What is Bounty?": {
    text: "Bounty is task-based challenges where users can earn rewards by completing specific goals or contributing valuable content or solutions.",
    alternateQuestions: [],
  },

  "How do I create a Bounty?": {
    text: "To create a Bounty:\n• Go to your **dashboard**.\n• Click '**Create Bounty**'.\n• Fill in the challenge details, set a reward, and publish.\n\nYou can also Click on below button.",
    link: "https://rentprompts.com/dashboard/create/bounty",
    linkText: "Create Bounty",
    alternateQuestions: [
      
    ],
  },
 
  "Where can I view my Bounty?": {
    text: "Follow the below steps:\n• Go to **dashboard**.\n• Click on '**Bounty**' at sidebar.\n\n**🔔 Note**: Post Your Challenges and Needs, Set Bounties, and Crowdsource AI-Driven Solutions From Global Community of AI Experts!",
    link: "https://rentprompts.com/dashboard/bounties",
    linkText: "Bounty",
    alternateQuestions: [
      "where can I see my bounty",
      "how check my bounty"
    ],
  },
  

  "How can I apply in a Bounty?": {
    text: "To apply in a Bounty:\n• Go to the Bounty page via click on **‘Bounty’** at navbar.\n• Open that bounty where you want to apply.\n• Then click the apply button.\n\n Fill in the necessary information for applying.",
    alternateQuestions: [],
  },

  "How are winners chosen?": {
    text: "Winners are chosen based on mentioned conditions, which you can read at the apply time on a bounty.",
    alternateQuestions: [],
  },


  "Can I make private Bounties?": {
    text: "Follow the below steps:\n• Click on **create bounty**.\n• Select 'Private' when creating a bounty.\n\n**🔔 Note**: Private Bounties are only visible to the creator and selected participants.",
    link: "https://rentprompts.com/bounties",
    linkText: "Create Bounty",
    alternateQuestions: [
      "how make bounty private",
      "how to make bounty private",  
      "how to create private bounty",
      "how to create private bounties",
    ],
  },

  "How do I view past Bounty results?": {
    text: "Follow the below steps:\n• Go to applied bounties section.\n• Click that bounty.\n\nYoc can see all the historical submissions.",
    alternateQuestions: [],
  },

  // Community & Support
  "How can I join the RentPrompts community?": {
    text: "Click on **Join Community** button to Visit our community page to engage in discussions or join our Discord server.",
    link: "https://rentprompts.com/community",
    linkText: "Join Community",
    alternateQuestions: [],
  },

  "How can I start a discussion in the Community?": {
    text: "To start a discussion in the Community:\n• Click on **Join Community** button.\n• Enter your question or idea, and post.\n\nYou can also click on below button",
    link: "https://rentprompts.com/community",
    linkText: "Join Community",
    alternateQuestions: [],
  },

  "How can I post/submit a query?": {
    text: "To submit a Query:\n• Go to **contact us** page.\n• Submit your query on '**write to us**' form.\n\nYou can also click on the below button.",
    link: "https://rentprompts.com/aboutus#write-to-us",
    linkText: "Write to us",
    alternateQuestions: [],
  },

  // ai-apps
  "How can I explore RentPrompts AI Apps?": {
    text: "To explore RentPrompts Al Apps:\n• Click on the **button below**\n• Browse various Al-powered applications designed for different purposes.\n• Select the one that fits your needs, and start using it instantly.\n\n**🔔 Note**: To see all listed AI-Apps, you can also click '**Marketplace**' on the navbar.",
    link: "https://rentprompts.com/marketplace",
    linkText: "Explore AI Apps",
    alternateQuestions: [],
  },

  "How can I create AI Apps on RentPrompts?": {
    text: "To create Al Apps on RentPrompts:\n• Click on the '**Create AI App**' button.\n• Fill in the details.\n• Once done, submit it for review.\n\n Your Al App will be created based on your inputs and will be available for use or sharing.",
    link: "https://rentprompts.ai/dashboard",
    linkText: "Create AI Apps",
    alternateQuestions: [],
  },

  // Learning & Development
  "What type of academy courses are available on RentPrompts.": {
    text: "We offer courses for students, professionals, businesses, and educators. Visit our academy page to view the detailed information.",
    link: "https://rentprompts.com/academy",
    linkText: "Academy",
    alternateQuestions: [
      "academy courses",
],
  },
   "How can I enroll in a course?": {
    text: "To enroll in a courses:\n• Go to the **academy** page.\n• Choose a course.\n• Click on the **Join** button.\n\nFill the required informtion to join.",
    link: "https://rentprompts.com/academy",
    linkText: "Academy",
    alternateQuestions: [],
  },



  // RentPrompts Accounts (Social Media)
  "How can I join RentPrompts LinkedIn account?": {
    text: "Click on below button to join rentprompts on linkedIn.",
    link: "https://www.linkedin.com/company/crex-rentprompts/",
    linkText: "Join LinkedIn",
    alternateQuestions: [],
  },

  "How can I join RentPrompts Twitter account?": {
    text: "Click on below button to join rentprompts on twitter.",
    link: "https://x.com/i/flow/login?redirect_after_login=%2FRentPrompts",
    linkText: "Visit Twitter",
    alternateQuestions: [],
  },

  "How can I join RentPrompts Instagram account?": {
    text: "Click on below button to join rentprompts on Instagram.",
    link: "https://www.instagram.com/rentprompts/",
    linkText: "Visit Instagram",
    alternateQuestions: [],
  },

  "How can I join RentPrompts Facebook account?": {
    text: "Click on below button to join rentprompts on Facebook.",
    link: "https://www.facebook.com/people/Crex-Rentprompts/pfbid0kcy6HsosYYRtQGkGe1uaCP6eZRwQKguvmaF1GwCDW1Tj1WAghHQYmHL1yMhDMLbxl/?mibextid=qi2Omg&rdid=Nve0cW1kPhWB22o9&share_url=https%3A%2F%2Fwww.facebook.com%2Fshare%2FTxVFGpBgLVKkNLgS%2F%3Fmibextid%3Dqi2Omg",
    linkText: "Visit Facebook",
    alternateQuestions: [],
  },

  // privacy & policy
  "Where can I see the Privacy & Policy of RentPrompts?": {
    text: "Click on below button to see privacy policy of rentprompts.",
    link: "https://rentprompts.com/privacypolicy",
    linkText: "View Privacy & Policy",
    alternateQuestions: [],
  },

  // terms & conditions
  "Where can I see the Terms & Conditions of RentPrompts?": {
    text: "Click on below button to see terms & conditions of rentprompts.",
    link: "https://rentprompts.com/termsandconditions",
    linkText: "View Terms & Conditions",
    alternateQuestions: [],
  },

  // refund policy
  "Where can I see the Refund Policy of RentPrompts?": {
    text: "Click on below button to see refund policy of rentprompts.",
    link: "https://rentprompts.com/refundpolicy",
    linkText: "View Refund Policy",
    alternateQuestions: [],
  },
};

const friendlyResponses = {
  'hi': "Hello! 👋 How can I assist you today?",
  'hello': "Hi there! 😊 What can I help you with?",
  'hey': "Hey! 👋 Need any assistance?",
  'how are you': "I'm great, thanks for asking! How can I help you today?",
  'how are you doing': "Doing well! 🚀 What can I do for you?",
  'good morning': "Good morning! ☀ Hope you're having a great day!",
  'good afternoon': "Good afternoon! ☀ How can I assist you?",
  'good evening': "Good evening! 🌙 What can I help you with?",
  'whats up': "Not much, just here to help you! 🤗 How can I assist?",
  'what’s up': "Not much, just here to help you! 🤗 How can I assist?",
  'sup': "Hey! ✨ Need any help today?",
  'yo': "Yo! 👋 What can I do for you?",
  'howdy': "Howdy partner! 🤠 How can I assist you?",
  'greetings': "Greetings! 🌟 How may I help you today?",
  'how is it going': "Everything’s great! Thanks for asking. 😊",
  'how’s it going': "All good here! 🌟 How can I help you?",
  'nice to meet you': "Nice to meet you too! 👋 How can I assist?",
  'pleased to meet you': "Likewise! 🤝 Let’s get started!",
  'good to see you': "Good to see you too! 👀 How can I help?",
  'long time no see': "Feels like ages! 😂 How can I assist you today?",
  'how’s your day': "My day’s going great, thanks! 🌈 How can I help you?",
  'what are you doing': "Just waiting to assist you! 💬",
  'are you there': "Always here for you! 👀 Ask me anything!",
  'can you help me': "Of course! 🤝 What do you need help with?",
  'i need help': "I'm here to help! 💡 What's your question?",
  'help me': "I'm ready! 🚀 Tell me what you need assistance with.",
  'i have a question': "Sure! ❓ Go ahead, I’m listening.",
  'are you real': "I’m a virtual assistant! 🤖 Real enough to help you!",
  'who are you': "I'm RentPrompts Assistant! 🤖 Here to support you.",
  'can you talk': "I can chat all day! 💬 What’s on your mind?",
  'tell me a joke': "Why don't scientists trust atoms? Because they make up everything! 😄",
  'tell me something funny': "Sure! Why did the scarecrow win an award? Because he was outstanding in his field! 😂",
  'goodbye': "Goodbye! 👋 Hope to chat again soon!",
  'bye': "Bye! 👋 Take care!",
  'see you later': "See you! 👀 Have a great day!",
  'talk to you later': "Talk soon! 💬",
  'have a nice day': "Thank you! 🌈 You too!",
  'thanks': "You're welcome! 🙏 Happy to help!",
  'thank you': "No problem! 😊 Let me know if you need anything else!",
  'no problem': "Awesome! ✨ Let’s keep going!",
  'cool': "Cool indeed! 😎 How can I assist you?",
  'awesome': "Glad you think so! 🌟 Anything you need?",
  'great': "That’s great! 🙌 How can I assist you today?",
  'amazing': "You’re amazing too! 😍",
  'okay': "Okay! 👍 Let’s do this!",
  'ok': "Okie dokie! ✅ What’s next?",
  'are you human': "I'm an AI assistant built to help you! 🤖",
  'are you a robot': "You guessed it! 🤖 A friendly robot at your service!",
  'can you help me find something': "Absolutely! 🔎 What are you looking for?",
  'what do you do': "I help answer your questions and guide you! 🎯",
  'what can you do': "I can answer questions, assist with information, and guide you! 🚀",
  'do you sleep': "Nope! I’m awake 24/7 just for you. 🌙✨",
  'are you awake': "Always! 🌟 Ready when you are!",
  'happy birthday': "Thank you! 🎂 Even though I don't age, I appreciate the sentiment!",
  'merry christmas': "Merry Christmas! 🎄 Wishing you joy and happiness!",
  'happy new year': "Happy New Year! 🎉 Cheers to new beginnings!",
};

export { knowledgeBase, friendlyResponses };
