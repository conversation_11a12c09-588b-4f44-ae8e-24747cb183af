"use client";

import React, { useState } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const FormSchema = z.object({
  category: z.string({
    required_error: "Please select a category",
  })
});

interface FiltersProps {
  onFilterChange: (category: string) => void;
}

const Filters: React.FC<FiltersProps> = ({ onFilterChange }) => {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      category: 'image', // Default value to ensure it's never undefined
    }
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    onFilterChange(data.category);
  }

  return (
    <div>
     
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-2 gap-3 grid grid-cols-2">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel></FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="all">all</SelectItem>
                    <SelectItem value="gpt">gpt</SelectItem>
                    <SelectItem value="image">image</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )} 
          />
          <Button className='max-w-20' type="submit">Submit</Button>
        </form>
      </Form>

    </div>
  );
};

export default Filters;
