'use client'

import { PRODUCT_CATEGORIES } from '@/constants'
import { Button } from './ui/button'
import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'

type Category = (typeof PRODUCT_CATEGORIES)[number]

interface MenuItemProps {
  category: Category
  handleOpen: () => void
  close: () => void
  isOpen: boolean
  isAnyOpen: boolean
  sidebarnav: boolean
}

const MenuItem = ({
  isAnyOpen,
  category,
  handleOpen,
  close,
  isOpen,
  sidebarnav,
}: MenuItemProps) => {
  return (
    <div className={`${sidebarnav ? "" : "flex "}`}>
    <div
      className={` ${sidebarnav ? "w-full" : "items-center relative flex"} `}
      id="menu-button"
      aria-expanded="true"
      aria-haspopup="true"
    >
      <Button
        className={`gap-1.5 font-semibold ps-3 ${sidebarnav ? "w-full justify-start ps-2" : " "} `}
        onClick={handleOpen}
        variant={isOpen ? "secondary" : "ghost"}
      >
        {category.label}
        <ChevronDown
          className={cn("h-4 w-4 transition-all ", {
            "-rotate-180": isOpen,
          })}
        />
      </Button>
    </div>

    {isOpen ? (
      <>
      {/* for sidebar menu start */}
        {category.featured.map((item, index) => (
          <div
            key={index} 
            className="z-10 my-1 w-full md:hidden block origin-top-right rounded-md hover:bg-secondary/80 hover:shadow-lg "
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="menu-button"
            tabIndex={-1} 
          >
            <div className="py-1 font-bold px-4 " role="none">
              <a
                href={item.href}
                className=" block text-base text-slate-200 font-medium"
                role="menuitem"
                tabIndex={-1} 
                id={`menu-item-${index}`} 
              >
                {item.name}
                
              </a>
              <p className="text-xs text-slate-400">Shop Now</p>
            </div>
          </div>
        ))}
      {/* for sidebar menu end */}


        <div
          onClick={() => close()}
          className={cn(
            "absolute inset-x-0 hidden md:block top-full text-sm text-muted-foreground",
            {
              "animate-in fade-in-10 slide-in-from-top-5": !isAnyOpen,
            }
          )}
        >
          <div className="absolute inset-0 top-1/2" aria-hidden="true" />

          <div className="relative z-50 py-20 bg-black/[0.4] p-2 backdrop-blur-md dark:bg-black/[0.6]">
            <div className="mx-auto max-w-7xl px-8 block">
              <div className="grid grid-cols-4 gap-x-8 gap-y-10 py-8">
                <div className="col-span-4 col-start-1 grid grid-cols-3 gap-x-8">
                  {category.featured.map((item) => (
                    <div
                      onClick={() => close}
                      key={item.name}
                      className="group relative text-base sm:text-sm"
                    >
                      {sidebarnav && (
                        <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-100 group-hover:opacity-75">
                          <Image
                            src={item.imageSrc}
                            alt="product category image"
                            fill
                            className="object-cover object-center"
                          />
                        </div>
                      )}

                      <Link
                        href={item.href}
                        className="mt-6 block font-medium text-primary"
                      >
                        {item.name}
                      </Link>
                      <p className="mt-1" aria-hidden="true">
                        Shop now
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    ) : null}
  </div>
  )
}

export default MenuItem