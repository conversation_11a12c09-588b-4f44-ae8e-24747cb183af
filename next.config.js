/** @type {import('next').NextConfig} */

const nextConfig = {
  transpilePackages: ['@radix-ui/react-slider'],
  experimental: {
    esmExternals: false, // disables externalizing ESM packages during build
  },
  staticPageGenerationTimeout: 120,

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
      {
        protocol: "https",
        hostname: "dev.rentprompts.com",
      },
    ],

    unoptimized: true,
    // domains: ['api.dicebear.com'],
    // dangerouslyAllowSVG: true,
    // contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  async rewrites() {
    return [
      {
        source: "/socket.io/:path*",
        destination: "http://localhost:3000/socket.io/:path*",
      },
    ];
  },
	typescript: {
		ignoreBuildErrors: true,
	},
	experimental: {
		// Optimize large packages
	optimizePackageImports: [
    'lucide-react', 
    'date-fns', 
    'lodash-es',
    '@mui/material',
    '@mui/icons-material',
    '@tabler/icons-react',
    'react-icons'
  ],
	},
    swcMinify: true,
    cacheHandler: require.resolve('./cache-handler.js'),
    cacheMaxMemorySize: 50, // in MB,
    // Optimize on-demand entries
    onDemandEntries: {
        maxInactiveAge: 60 * 1000, // 60 seconds
  			pagesBufferLength: 4,
    },
	fastRefresh: true,
};

module.exports = nextConfig;
