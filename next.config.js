/** @type {import('next').NextConfig} */

// Extract domain from the full URL
const getCDNDomain = () => {
	const fullUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2;
	if (!fullUrl) {
		// Fallback based on environment
		const env = process.env.NODE_ENV || 'development';
		return env === 'production' ? 'assets.rentprompts.com' : 'testassets.rentprompts.com';
	}
	
	try {
		// Extract hostname from full URL
		const url = new URL(fullUrl);
		return url.hostname;
	} catch (error) {
		console.warn('Invalid NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2 URL, using fallback');
		const env = process.env.NODE_ENV || 'development';
		return env === 'production' ? 'assets.rentprompts.com' : 'testassets.rentprompts.com';
	}
};

const cdnDomain = getCDNDomain();

const nextConfig = {
	images: {
		// Dynamic CDN domain extracted from environment variable
		domains: [cdnDomain],
		
		remotePatterns: [
			{
				protocol: 'https',
				hostname: cdnDomain,
			},
			{
				protocol: 'https',
				hostname: 'testassets.rentprompts.com',
			},
			{
				// Keep this for other external images
				protocol: 'https',
				hostname: '**',
			},
		],
		
		// Use custom loader for Cloudflare optimization
		loader: 'custom',
		loaderFile: './lib/imageLoader.js',
		
		// Image optimization settings
		formats: ['image/webp', 'image/avif'],
		deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
		imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
		minimumCacheTTL: 3600, // Cache for 1 hour since images are optimized at CDN level
		
		// Uncomment if you need SVG support
		// dangerouslyAllowSVG: true,
		// contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
	},
	
	// Enable caching headers
	async headers() {
		return [
			{
				// Apply security headers to all routes
				source: '/(.*)',
				headers: [
					{
						key: 'X-DNS-Prefetch-Control',
						value: 'on'
					},
					{
						key: 'X-XSS-Protection',
						value: '1; mode=block'
					},
					{
						key: 'X-Frame-Options',
						value: 'SAMEORIGIN'
					},
					{
						key: 'X-Content-Type-Options',
						value: 'nosniff'
					},
					{
						key: 'Referrer-Policy',
						value: 'origin-when-cross-origin'
					}
				]
			},
			{
				// Cache static assets for 1 year
				source: '/_next/static/(.*)',
				headers: [
					{
						key: 'Cache-Control',
						value: 'public, max-age=31536000, immutable',
					}
				]
			},
			{
				// Cache Cloudflare images for longer since they're optimized at CDN
				source: '/_next/image(.*)',
				headers: [
					{
						key: 'Cache-Control',
						value: 'public, max-age=2592000, stale-while-revalidate=86400', // 30 days
					}
				]
			},
			{
				// Cache API routes for 5 minutes
				source: '/api/(.*)',
				headers: [
					{
						key: 'Cache-Control',
						value: 'public, max-age=300, stale-while-revalidate=60',
					}
				]
			}
		]
	},
	
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	}
};

module.exports = nextConfig;