{"name": "rent-prompts-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "devturbo": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts nodemon --turbo", "sdev": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts nodemon", "serve": "cross-env PAYLOAD_CONFIG_PATH=dist/payload.config.js NODE_ENV=production node dist/expressServer.js", "build:next": "cross-env PAYLOAD_CONFIG_PATH=dist/payload.config.js NEXT_BUILD=true node dist/expressServer.js", "build:server": "tsc --project tsconfig.server.json", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts payload generate:types", "build:payload": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts payload build", "build": "cross-env NODE_ENV=production yarn build:payload && yarn build:server && yarn copyfiles && yarn build:next", "start": "cross-env PAYLOAD_CONFIG_PATH=dist/payload.config.js NODE_ENV=production node dist/expressServer.js", "create-app": "mkdirp dist/app", "copyfiles-app": "copyfiles -u 1 \"app/**/*.{html,css,scss,ttf,woff,woff2,eot,svg,jpg,jpeg,webp,png}\" dist/app/", "copyfiles-otherOne": "copyfiles -u 1 \"payload/**/*.{html,css,scss,ttf,woff,woff2,eot,svg,jpg,jpeg,webp,png}\"  dist/payload/", "copyfiles-otherTwo": "copyfiles -u 1  \"components/**/*.{html,css,scss,ttf,woff,woff2,eot,svg,jpg,jpeg,webp,png}\" dist/components/", "copyfiles": "yarn create-app && yarn copyfiles-app && yarn copyfiles-otherOne && yarn copyfiles-otherTwo", "lint": "next lint", "prepare": "husky"}, "dependencies": {"-": "^0.0.1", "@ai-sdk/react": "^1.2.0", "@aws-sdk/client-s3": "^3.645.0", "@aws-sdk/lib-storage": "^3.614.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fal-ai/client": "^1.0.4", "@heroicons/react": "v1", "@hookform/resolvers": "^3.3.2", "@langchain/community": "^0.3.39", "@langchain/groq": "^0.0.13", "@langchain/openai": "^0.2.0", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.9", "@mui/styled-engine-sc": "^6.1.9", "@nanostores/react": "0.8.0", "@payloadcms/bundler-webpack": "^1.0.5", "@payloadcms/db-mongodb": "^1.0.6", "@payloadcms/live-preview-react": "^0.2.0", "@payloadcms/plugin-cloud-storage": "^1.1.3", "@payloadcms/plugin-seo": "^2.3.2", "@payloadcms/richtext-lexical": "^3.15.1", "@payloadcms/richtext-slate": "^1.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@react-email/components": "^0.0.13", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@runwayml/sdk": "^2.0.1", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.82", "@tabler/icons-react": "^3.1.0", "@tanstack/react-query": "^4.36.1", "@toast-ui/react-image-editor": "^3.15.2", "ai": "^2.2.37", "aws-crt": "^1.21.3", "axios": "^1.6.2", "body-parser": "^1.20.2", "browserify-zlib": "^0.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dotenv": "^16.4.0", "dotenv-safe": "^8.2.0", "draft-js": "^0.11.7", "embla-carousel-react": "^8.0.2", "express": "^4.18.2", "file-saver": "^2.0.5", "framer-motion": "^11.9.0", "fuse.js": "^7.1.0", "gooey-react": "^1.1.0", "groq-sdk": "^0.9.1", "gsap": "^3.12.5", "html-to-draftjs": "^1.5.0", "html2pdf.js": "^0.10.3", "ioredis": "^5.6.1", "jspdf": "^2.5.2", "jszip": "^3.10.1", "jwt-decode": "3.1.2", "lenis": "^1.1.16", "lodash.debounce": "^4.0.8", "lucide-react": "^0.487.0", "mongodb": "^6.3.0", "next": "13.5.6", "next-auth": "^4.24.8", "next-cookies": "^2.0.3", "next-themes": "^0.2.1", "node-cache": "^5.1.2", "node-loader": "^2.0.0", "openai": "^4.19.0", "payload": "^2.0.0", "pdf-parse": "^1.1.1", "pre-commit": "^1.2.2", "process": "^0.11.10", "razorpay": "^2.9.4", "react": "^18", "react-code-blocks": "^0.1.6", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-element-to-jsx-string": "^15.0.0", "react-email": "^3.0.3", "react-hook-form": "^7.48.2", "react-icons": "^5.3.0", "react-joyride": "^2.9.3", "react-loader-spinner": "^6.1.6", "react-markdown": "^9.1.0", "react-modal": "^3.16.1", "react-photo-editor": "^2.1.4", "react-quill": "^2.0.0", "react-resizable-panels": "^2.0.3", "react-router-dom": "^5.3.4", "react-spinners": "^0.14.1", "react-textarea-autosize": "^8.5.3", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "replicate": "^0.29.4", "resend": "^3.1.0", "sha256": "^0.2.0", "showdown": "^2.1.0", "simplex-noise": "^4.0.1", "slugify": "^1.6.6", "sonner": "^1.5.0", "stream-browserify": "^3.0.0", "stripe": "^14.13.0", "styled-components": "^6.1.13", "swiper": "^11.2.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-motion": "^0.4.1-beta", "tsconfig-paths": "^4.2.0", "tslib": "^2.6.2", "tui-image-editor": "^3.15.3", "url": "^0.11.4", "vaul": "^0.9.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/escape-html": "^1.0.4", "@types/express": "^4.17.20", "@types/file-saver": "^2.0.7", "@types/i18next": "^13.0.0", "@types/node": "^20.11.30", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/sha256": "^0.2.2", "autoprefixer": "^10.4.20", "copyfiles": "^2.4.1", "css-loader": "^7.1.2", "eslint": "^8", "eslint-config-next": "13.5.6", "husky": "^9.1.7", "nodemon": "^3.0.1", "postcss": "^8", "postcss-loader": "^8.1.1", "tailwindcss": "^3", "ts-node": "^10.9.2", "typescript": "5.4.5"}}