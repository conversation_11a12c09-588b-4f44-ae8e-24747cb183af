
import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import { redirect, useRouter } from 'next/navigation';
import payload from "payload";
import crypto from 'crypto';
import { getPayloadClient } from "../../../server/get-payload";
import express, { Request, Response } from "express";
import { serialize } from 'cookie';
import { toast } from 'sonner';
import User<PERSON><PERSON><PERSON> from '@/components/UserJourney';


export default NextAuth({ 
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({ user, account}:any) {
      const payload = await getPayloadClient();
      const {docs: users} = await payload.find({
        collection: "users",
        where: {
          email: {
            equals: user.email,
          },
        },
      });

      if (users.length > 0) {
        const credentials = {
          email: user.email,
          InputCred: 'R8f$2nZx&9pQ@6jT!mD',
          authProvider: users[0].AuthProvider
        };
        const encodedCredentials = encodeURIComponent(JSON.stringify(credentials));

        // Add it to the URL
        return `/sign-in?credentials=${encodedCredentials}`;
       
      }
      else{
        await payload.create({
          collection: "users",
          data: {
            email:user.email,
            password: 'R8f$2nZx&9pQ@6jT!mD',
            role: "user",
            followers: [],
            following: [],
            coinBalance: 25,
            _verified: true,
            user_name: user.name,
            AuthProvider: account.provider,
            tokens:0
          },
        });
        
        const credentials = {
          email: user.email,
          InputCred: 'R8f$2nZx&9pQ@6jT!mD',
          UserJourney:true,
          authprovider: account.provider,
        };
        const encodedCredentials = encodeURIComponent(JSON.stringify(credentials));

        // Add it to the URL
        return `/sign-in?credentials=${encodedCredentials}`;
      }
      
    },
    async session({ session, token, user, }) {
    user.id = token.sub;
      return session;
    },
    },
  },
);