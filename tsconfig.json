{
  "compilerOptions": {
    "esModuleInterop": true,
    "target": "es5",

    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],

    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    "noEmit": true,
    "module": "commonjs",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {

      "@/*": [
        "./*"
      ],
      // "@/*" : [
      //   "./src/*"
      // ]
      "socket.io": ["./node_modules/socket.io"]
    },
    "strictNullChecks": false

  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "node_modules/webhooks.ts",

    "",
    "payload/ForceThemeProvider.jsx",
    "types", "src"
  ],
  "exclude": [
    "node_modules"
  ],
  "ts-node": {
    "transpileOnly": true,
    "swc": true,
    "require": [
      "tsconfig-paths/register"
    ]

  }
}