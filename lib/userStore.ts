import { create } from 'zustand';

interface CoinStore {
  coinBalance: number;
  isLoading: boolean;
  error: string | null;
  setCoinBalance: (amount: number) => void;
  incrementCoinBalance: (amount: number) => void;
  decrementCoinBalance: (amount: number) => void;
  fetchCoinBalance: () => Promise<void>;
} 

export const useCoinStore = create<CoinStore>((set) => ({
  coinBalance: 0,
  isLoading: false,
  error: null,
  setCoinBalance: (amount) => set({ coinBalance: amount }),
  incrementCoinBalance: (amount) => 
    set((state) => ({ coinBalance: state.coinBalance + amount })),
  decrementCoinBalance: (amount) => 
    set((state) => ({ coinBalance: Math.max(0, state.coinBalance - amount) })),
  fetchCoinBalance: async () => {
    set({ isLoading: true, error: null });
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/meapi`);
      if (!res.ok) throw new Error('Failed to fetch coin balance');
      const data = await res.json();
      set({ coinBalance: data.data.coinBalance, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },
}));

// Convenience hooks with proper TypeScript return types
export const useCoinBalance = (): number => useCoinStore((state) => state.coinBalance);
export const useSetCoinBalance = (): ((amount: number) => void) => useCoinStore((state) => state.setCoinBalance);
export const useIncrementCoinBalance = (): ((amount: number) => void) => useCoinStore((state) => state.incrementCoinBalance);
export const useDecrementCoinBalance = (): ((amount: number) => void) => useCoinStore((state) => state.decrementCoinBalance);
export const useFetchCoinBalance = (): (() => Promise<void>) => useCoinStore((state) => state.fetchCoinBalance);