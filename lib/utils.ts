import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
 
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(
  price: number | string,
  options: {
    currency?: 'USD' | 'EUR' | 'GBP' | 'BDT'
    notation?: Intl.NumberFormatOptions['notation']
  } = {}
) {
  const { currency = 'USD', notation = 'compact' } = options

  const numericPrice =
    typeof price === 'string' ? parseFloat(price) : price

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    notation,
    maximumFractionDigits: 2,
  }).format(numericPrice)
}

// Function to get the status color class based on the status type
export const getStatusColorClass = (
  value: string,
  type: 'status' | 'approvedForSale'
): string => {
  const formattedValue = value?.toLowerCase();

  switch (formattedValue) {
    case 'pending':
      return 'bg-yellow-600 text-white hover:bg-yellow-400 active:scale-95 shadow-md transition-all';

    case 'completed':
      return 'bg-green-600 text-white hover:bg-green-700 active:scale-95 shadow-md transition-all';

    case 'approved':
      return 'bg-blue-600 text-white hover:bg-blue-700 active:scale-95 shadow-md transition-all';

    case 'rejected':
      case 'expired':
        case 'denied':
          return 'bg-red-600 text-white hover:bg-red-700 active:scale-95 shadow-md transition-all';

    default:
      return 'bg-indigo-500 text-white hover:bg-indigo-600 active:scale-95 shadow-md transition-all';
  }
};
