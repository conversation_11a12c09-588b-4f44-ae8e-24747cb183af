import { Request, Response, NextFunction } from "express";
import { cacheQuery, cacheDocument } from "./cache-manager";

// Cache middleware for Express routes
export const cacheMiddleware = (options: {
  collection: string;
  ttl?: number;
  keyGenerator?: (req: Request) => string;
}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const { collection, ttl = 172800, keyGenerator } = options;
    
    try {
      // Generate custom cache key if provided
      const customCacheKey = keyGenerator ? keyGenerator(req) : undefined;
      
      // Extract query parameters
      const query = {
        ...req.query,
        ...req.body,
      };
      
      // Remove pagination and limit from cache key for better hit rate
      const { page, limit, cursor, ...cacheableQuery } = query;
      
      // Add pagination back to actual query
      const finalQuery: any = {
        ...cacheableQuery,
      };
      
      if (page) finalQuery.page = parseInt(page as string, 10) || 1;
      if (limit) finalQuery.limit = parseInt(limit as string, 10) || 10;
      if (cursor) finalQuery.cursor = cursor;
      
      // Cache the query
      const result = await cacheQuery(collection, finalQuery, {
        ttl,
        customCacheKey,
      });
      
      // Set cache headers
      res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
      res.setHeader("X-Cache", "HIT");
      
      return res.status(200).json({
        success: true,
        data: result,
      });
      
    } catch (error) {
      console.error(`Cache middleware error for ${collection}:`, error);
      // Continue to next middleware if cache fails
      next();
    }
  };
};

// Cache middleware for single document routes
export const cacheDocumentMiddleware = (options: {
  collection: string;
  ttl?: number;
  idParam?: string;
  depth?: number;
}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const { collection, ttl = 172800, idParam = "id", depth = 1 } = options;
    
    try {
      const id = req.params[idParam];
      
      if (!id) {
        return next();
      }
      
      const result = await cacheDocument(collection, id, {
        ttl,
        depth,
      });
      
      res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
      res.setHeader("X-Cache", "HIT");
      
      return res.status(200).json({
        success: true,
        data: result,
      });
      
    } catch (error) {
      console.error(`Cache document middleware error for ${collection}/${req.params[idParam]}:`, error);
      next();
    }
  };
};

// Smart cache middleware that auto-detects collection from route
export const smartCacheMiddleware = (options: {
  ttl?: number;
  collectionMap?: Record<string, string>;
} = {}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const { ttl = 172800, collectionMap = {} } = options;
    
    try {
      // Auto-detect collection from route path
      const pathSegments = req.path.split('/').filter(Boolean);
      let collection = pathSegments[0]; // First segment after /api2/
      
      // Use custom mapping if provided
      if (collectionMap[collection]) {
        collection = collectionMap[collection];
      }
      
      // Skip if no collection detected
      if (!collection) {
        return next();
      }
      
      // Check if it's a document request (has ID in path)
      const potentialId = pathSegments[1];
      const isDocumentRequest = potentialId && potentialId.length === 24; // MongoDB ObjectId length
      
      if (isDocumentRequest) {
        // Use document cache middleware
        return cacheDocumentMiddleware({
          collection,
          ttl,
          idParam: "id",
        })(req, res, next);
      } else {
        // Use query cache middleware
        return cacheMiddleware({
          collection,
          ttl,
          keyGenerator: (req) => {
            const { page = 1, limit = 10, ...query } = { ...req.query, ...req.body };
            return `${collection}:page:${page || 1}:limit:${limit || 10}:query:${JSON.stringify(query)}`;
          },
        })(req, res, next);
      }
      
    } catch (error) {
      console.error("Smart cache middleware error:", error);
      next();
    }
  };
};

// Cache invalidation middleware (for POST, PUT, DELETE requests)
export const cacheInvalidationMiddleware = (collection: string) => {
  return (_req: Request, res: Response, next: NextFunction) => {
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(data: any) {
      // Only invalidate cache on successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Import here to avoid circular dependency
        import("./cache-manager").then(({ invalidateCollectionCache }) => {
          invalidateCollectionCache(collection);
        });
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };
    
    next();
  };
};

// Utility function to create route-specific cache middleware
export const createRouteCache = (collection: string, options: {
  ttl?: number;
  depth?: number;
  customKeyGenerator?: (req: Request) => string;
} = {}) => {
  const { ttl = 172800, depth = 1, customKeyGenerator } = options;
  
  return {
    // For GET requests (read operations)
    read: cacheMiddleware({
      collection,
      ttl,
      keyGenerator: customKeyGenerator,
    }),
    
    // For GET /:id requests (single document)
    readOne: cacheDocumentMiddleware({
      collection,
      ttl,
      depth,
    }),
    
    // For POST, PUT, DELETE requests (write operations)
    invalidate: cacheInvalidationMiddleware(collection),
  };
};
