import { Resend } from 'resend';

const resend = new Resend('re_g6hutZCM_HW51dVW2cA23c1qRs9fZD8dp');

const resendTransport = {
  sendMail: async (options) => {
    const { to, subject, html } = options;
    try {
      const data = await resend.emails.send({
        from: 'RentPrompts <<EMAIL>>',
        to: to,
        subject: subject,
        html: html,
      });
      // console.log('Email sent successfully', data);
    } catch (error) {
      console.error('Error sending email', error);
      throw error;
    }
  },
  verify: () => {
    // No-op verify method
    console.log('Transport verification skipped');
  },
};

export const resendEmailConfig = {
  fromName: 'RentPrompts',
  fromAddress: '<EMAIL>',
  transport: resendTransport,
};
