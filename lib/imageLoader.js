// lib/imageLoader.js

// Get CDN base URL from environment variable
const getCDNBaseUrl = () => {
  // Check for the Payload environment variable first
  const payloadUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2;
  
  if (payloadUrl) {
    // Remove trailing slash if present
    return payloadUrl.replace(/\/$/, '');
  }
  
  // Fallback based on environment
  const env = process.env.NODE_ENV || 'development';
  
  if (env === 'production') {
    return `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2 || 'https://assets.rentprompts.com'}`;
  } else {
    return `${process.env.NEXT_PUBLIC_CLOUDFLARE_PUBLIC_R2 || 'https://testassets.rentprompts.com'}`;
  }
};

// Extract domain from URL for comparison
const getCDNDomain = () => {
  const baseUrl = getCDNBaseUrl();
  try {
    const url = new URL(baseUrl);
    return url.hostname;
  } catch (error) {
    console.warn('Invalid CDN URL, using fallback domain');
    const env = process.env.NODE_ENV || 'development';
    return env === 'production' ? 'assets.rentprompts.com' : 'testassets.rentprompts.com';
  }
};

export default function cloudflareLoader({ src, width, quality }) {
  const baseUrl = getCDNBaseUrl();
  const cdnDomain = getCDNDomain();
  
  // If the image is already a full URL from your Cloudflare domain, return as is
  if (src.startsWith(`https://${cdnDomain}`)) {
    return src;
  }
  
  // If it's a full URL from other domains (external images), return as is
  if (src.startsWith('http')) {
    return src;
  }
  
  // For relative paths, construct Cloudflare R2 URL
  
  // Remove leading slash if present
  const cleanSrc = src.startsWith('/') ? src.slice(1) : src;
  
  // Build optimization parameters for Cloudflare Image Resizing
  const params = [`w=${width}`];
  
  if (quality) {
    params.push(`q=${quality}`);
  }
  
  // Option 1: Using Cloudflare Image Resizing (recommended)
  // This requires Cloudflare Image Resizing to be enabled on your domain
  // return `https://testassets.rentprompts.com/cdn-cgi/image/${params.join(',')}/${cleanSrc}`;
  
  // Option 2: Direct serving from R2 (fallback)
  // This doesn't use Cloudflare's image optimization but still serves from CDN
  return `${baseUrl}/${cleanSrc}`;
}

// Alternative advanced loader with more optimization features
export function advancedCloudflareLoader({ src, width, quality }) {
  const baseUrl = getCDNBaseUrl();
  
  if (src.startsWith('http')) {
    return src;
  }
  
  const cleanSrc = src.startsWith('/') ? src.slice(1) : src;
  
  // Advanced Cloudflare Image Resizing parameters
  const params = [
    `w=${width}`,
    `f=auto`, // Automatic format selection (WebP/AVIF)
    `fit=scale-down`, // Never upscale images
    `metadata=none`, // Strip metadata for smaller files
  ];
  
  if (quality) {
    params.push(`q=${quality}`);
  } else {
    params.push(`q=85`); // Default quality
  }
  
  // Using Cloudflare Image Resizing
  return `${baseUrl}/cdn-cgi/image/${params.join(',')}/${cleanSrc}`;
}