/**
 * Utility functions for manual balance updates
 * इन functions को सिर्फ user actions के बाद call करें
 */

// Client-side function to trigger balance update
export const triggerBalanceUpdate = () => {
  // यह function client-side components में use करें
  // Context के updateBalance function को call करने के लिए
  if (typeof window !== 'undefined') {
    // Dispatch a custom event that components can listen to
    window.dispatchEvent(new CustomEvent('updateCoinBalance'));
  }
};

// Server-side function to get fresh balance
export const getFreshUserBalance = async (userId: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch user balance');
    }
    
    const userData = await response.json();
    return userData.coinBalance;
  } catch (error) {
    console.error('Error fetching fresh balance:', error);
    return null;
  }
};

// Types for balance update scenarios
export type BalanceUpdateScenario = 
  | 'purchase' 
  | 'recharge' 
  | 'refund' 
  | 'bounty_payment' 
  | 'bounty_refund';

export const logBalanceUpdate = (
  scenario: BalanceUpdateScenario, 
  amount: number, 
  newBalance: number
) => {
  const emoji = {
    purchase: '🛒',
    recharge: '💳',
    refund: '💰',
    bounty_payment: '🎯',
    bounty_refund: '🔄'
  };
  
  console.log(
    `${emoji[scenario]} Balance Update - ${scenario.toUpperCase()}:`, 
    `Amount: ${amount}, New Balance: ${newBalance}`
  );
};
