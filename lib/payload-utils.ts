import { User } from '../server/payload-types'
import { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies'
import { NextRequest } from 'next/server'

export const getServerSideUser = async (
  cookies: NextRequest['cookies'] | ReadonlyRequestCookies
) => {
  const token = cookies.get('payload-token')?.value;

  if (!token) {
    return {
      status: 401,
      message: 'Authentication token missing',
    };
  }
  // // Simulate an expired session
  // const meRes = new Response(null, { status: 401 });
  const meRes = await fetch(
    `${process.env.NEXT_PUBLIC_SERVER_URL}/api/users/me`,
    {
      headers: {
        Authorization: `JWT ${token}`,
      },
    }
  );

  if (meRes.status === 401) {
    return {
      status: 401,
      message: 'Unauthorized: Session expired',
    };
  }

  const { user } = (await meRes.json()) as {
    user: User | null
  };

  return {
    status: 200,
    user,
  };
};
