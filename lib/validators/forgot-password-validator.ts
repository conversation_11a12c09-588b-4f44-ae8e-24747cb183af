import { z } from "zod";

// Existing validators
export const AuthCredentialsValidator = z.object({
  email: z.string().email({ message: "Please enter a valid Email" }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters long.",
  }),
});

export const userprofile = z.object({
  email: z.string().email({ message: "Please enter a valid Email" }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters long.",
  }),
});

// New validator for "Forgot Password" feature
export const ForgotPasswordValidator = z.object({
  email: z.string().email({ message: "Please enter a valid Email" }),
});

// Inferred types
export type TAuthCredentialsValidator = z.infer<typeof AuthCredentialsValidator>;
export type TUserProfileValidator = z.infer<typeof userprofile>;
export type TForgotPasswordValidator = z.infer<typeof ForgotPasswordValidator>;