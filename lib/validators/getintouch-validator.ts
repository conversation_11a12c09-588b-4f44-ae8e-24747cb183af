import { z } from "zod";

export const getintouchValidation = z.object({
    username: z.string().nonempty("Username is required"), 
    phoneNumber: z.string()
        .regex(/^\+?[1-9]\d{1,14}([ \-\(\)\d]{0,20})$/, "Invalid phone number")
        .refine((value) => {
            const digitsOnly = value.replace(/\D/g, "");
            return digitsOnly.length === 10;
        }, "Phone number must contain 10 digits"),
    email: z.string().email("Invalid email address"),
    organizationName: z.string().nonempty("Organization name is required"), 
    query: z.string().nonempty("Query is required") 
});
