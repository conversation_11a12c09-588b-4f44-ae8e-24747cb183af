import { z } from 'zod'

export const QueryValidator = z.object({
  //category: z.string().optional(),
  category: z.enum(['createdAt' , 'likes' , 'price']).optional(),
  sort: z.enum(['asc', 'desc' , 'createdAt' , 'likes' , 'price','-createdAt' , '-likes' , '-price']).optional(),
  limit: z.number().optional(),
  offset: z.number().optional(),
  filters: z.record(z.array(z.string())).optional(),
})

export type TQueryValidator = z.infer<typeof QueryValidator>

