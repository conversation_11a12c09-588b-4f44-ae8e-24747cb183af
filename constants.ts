import { MessageSquare, ImageIcon, Box, Music, VideoIcon } from "lucide-react";

export const MAX_FREE_COUNTS = 5;

export const tools = [
  {
    label: "Text Generation",
    icon: MessageSquare,
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
    href: "/generate/chatgpt",
    disc: "Crafting narratives and ideas through the power of words.",
    available: "available",
  },
  {
    label: "Image Generation",
    icon: ImageIcon,
    color: "text-red-500",
    bgColor: "bg-red-500/10",
    href: "/generate/image",
    disc: "Transforming thoughts into visual masterpieces.",
    available: "available",
  },
  {
    label: "Video Generation",
    icon: VideoIcon,
    color: "text-indigo-500",
    bgColor: "bg-indigo-500/10",
    href: "/generate/video",
    available: "available",
    disc: "Bringing stories to life through cinematic sorcery.",
  },
  {
    label: "Music Generation",
    icon: Music,
    href: "/generate/audio",
    color: "text-green-500",
    bgColor: "bg-emerald-500/10",
    available: "available",
    disc: "Composing symphonies from the whispers of inspiration.",
  },
  // {
  //   label: "Video Generation",
  //   icon: VideoIcon,
  //   color: "text-indigo-500",
  //   bgColor: "bg-indigo-500/10",
  //   href: "/generate/animatediff",
  //   disc: "Bringing stories to life through cinematic sorcery.",
  // },
  {
    label: "3D Generation",
    icon: Box,
    href: "#",
    color: "text-violet-500",
    bgColor: "bg-violet-500/10",
    disc: "Shaping worlds with the magic of polygons and pixels.",
  },
];

export const spaces = [
  {
    label: "Text Generation",
    icon: MessageSquare,
    color: "text-green-700",
    bgColor: "bg-green-700/10",
    href: "/generate/chatgpt",
    // disc: 'Crafting narratives and ideas through the power of words.'
  },
  {
    label: "Image Generation",
    icon: ImageIcon,
    color: "text-pink-700",
    bgColor: "bg-pink-700/10",
    href: "/generate/image",
    // disc: 'Crafting narratives and ideas through the power of words.'
  },
  {
    label: "Video Generation",
    icon: VideoIcon,
    color: "text-orange-700",
    bgColor: "bg-orange-700/10",
    href: "/generate/animatediff",
    // disc: 'Crafting narratives and ideas through the power of words.'
  },
  {
    label: "Music Generation",
    icon: Music,
    href: "/generate/audio",
    color: "text-emerald-500",
    bgColor: "bg-emerald-500/10",
    // disc: 'Crafting narratives and ideas through the power of words.'
  },
  {
    label: "3D Generation",
    icon: Box,
    href: "/generate/shape",
    color: "text-violet-500",
    bgColor: "bg-violet-500/10",
    // disc: 'Crafting narratives and ideas through the power of words.'
  },
];

export const Product_Type = [
  {
    label: "Prompt",
    value: "prompt" as const,
  },
  {
    label: "Asset",
    value: "asset" as const,
  },
  {
    label: "Datasets",
    value: "datasets" as const,
  },
  {
    label: "Models",
    value: "models" as const,
  },
  {
    label: "Automation",
    value: "automation" as const,
  },
];

export const Generation_Type = [
  {
    label: "Text",
    value: "text" as const,
  },
  {
    label: "Image",
    value: "image" as const,
  },

  {
    label: "Video",
    value: "video" as const,
  },

  {
    label: "Music",
    value: "music" as const,
  },
  {
    label: "3D",
    value: "3d" as const,
  },
];

export const Model_type = [
  {
    label: "3D",
    value: "3D" as const,
  },
  {
    label: "GPT",
    value: "gpt" as const,
  },
  {
    label: "Image",
    value: "image" as const,
  },
  {
    label: "Music",
    value: "music" as const,
  },
  {
    label: "Video",
    value: "video" as const,
  },
];

export const Bounty_Type = [
  {
    label: "Artificial Intelligence (AI)",
    value: "ai" as const,
  },
  {
    label: "Blockchain",
    value: "blockchain" as const,
  },

  {
    label: "Mobile Application",
    value: "mobile" as const,
  },

  {
    label: "Machine Learning (ML)",
    value: "ml" as const,
  },
  {
    label: "Web Application",
    value: "web" as const,
  },
];

export const PRODUCT_CATEGORIES = [
  {
    label: "All",
    value: "all" as const,
    featured: [
      {
        name: "Editor picks",
        href: `#`,
        imageSrc: "/img/1.png",
      },
      {
        name: "New Arrivals",
        href: "/products?category=ui_kits&sort=desc",
        imageSrc: "/img/2.png",
      },
      {
        name: "Bestsellers",
        href: "#",
        imageSrc: "/img/3.png",
      },
    ],
  },
  {
    label: "GPT",
    value: "gpt" as const,
    featured: [
      {
        name: "Favorite Picks",
        href: `#`,
        imageSrc: "/img/5.png",
      },
      {
        name: "New Arrivals",
        href: "/products?category=icons&sort=desc",
        imageSrc: "/img/4.png",
      },
      {
        name: "Bestselling",
        href: "#",
        imageSrc: "/img/3.png",
      },
    ],
  },
  {
    label: "Image",
    value: "image" as const,
    featured: [
      {
        name: "Favorite Picks",
        href: `#`,
        imageSrc: "/img/1.png",
      },
      {
        name: "New Arrivals",
        href: "/products?category=icons&sort=desc",
        imageSrc: "/img/3.png",
      },
      {
        name: "Bestselling",
        href: "#",
        imageSrc: "/img/6.png",
      },
    ],
  },
];

export const homeImages = [
  {
    img: "/img/prompt1.png",
    prompt:
      "Cube cutout of an isometric living room, isometric 3d render, pastel colors, soft lighting, high detail behance, ray tracing.",
  },
  {
    img: "/img/prompt2.png",
    prompt:
      "a hysteric news reporter, with exaggerated features and a flurry of wild gestures,Their eyes widen comically, comic style.",
  },
  {
    img: "/img/prompt3.png",
    prompt:
      "ultra futuristic minimal design bike Designed by 8k resolution,hyper realistic, detailed render, extremely complex and advanced chassis, natural dirt and debris detail, Teenage Engineering, Stunning details.",
  },
  {
    img: "/img/prompt4.png",
    prompt:
      "A smile arctic fox, illustrate by duncan beedie children book illustration, simple, cute, full colour, minimalist, detailed, vector illustration.",
  },
  {
    img: "/img/prompt5.png",
    prompt:
      "A father and son raccoon playing baseball while celebrating fathers day, life-like.",
  },
  {
    img: "/img/prompt6.png",
    prompt:
      "An imaginative illustration of a software developer exploring a new language. The illustration is vibrant and dynamic, with bold colors and clean lines. The developer is shown with a determined expression.",
  },
  {
    img: "/img/prompt7.png",
    prompt:
      "A 45 year old masculine man with trim beard in futuristic long coat shoots a sleek laser gun at a flying black patrol bot with robotic tentacles inside space port, realistic, futuristic, sci-fi.",
  },
  {
    img: "/img/prompt8.png",
    prompt:
      "1 woman, beautiful, 25 years old, short hair, standing in the rain, studio ghibli style illustration, wide angle.",
  },
  {
    img: "/img/prompt9.png",
    prompt:
      "A small hedgehog holding a piece of watermelon with its tiny paws, taking little bites, eyes closed in delight, cute ink sketch style illustration.",
  },
  {
    img: "/img/prompt10.png",
    prompt:
      "White purple Dragon Cat with wings the wizard, fantasy, anime 90 style, Profile, Steamer Logo.",
  },
  {
    img: "/img/prompt11.png",
    prompt:
      "colorful blue and purple background with An AI robot making connections with photos.",
  },
  {
    img: "/img/prompt12.png",
    prompt:
      "Indian man blogging and gaming with a background of software architecture in the style of a cartoon.",
  },
];

export const rentCardData = [
  {
    mainImage: "/img/prompt1.png",
    label: "Prompt 1",
    price: "$1299",
    category: "Category",
    rating: "4.5",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 1",
    prompt: "This is the prompt for Prompt 1",
    description: "This is a detailed description for Prompt 1",
  },
  {
    mainImage: "/img/prompt2.png",
    label: "Prompt 2",
    price: "$1399",
    category: "Category",
    rating: "4.7",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 2",
    prompt: "This is the prompt for Prompt 2",
    description: "This is a detailed description for Prompt 2",
  },
  {
    mainImage: "/img/prompt3.png",
    label: "Prompt 3",
    price: "$1499",
    category: "Category",
    rating: "4.6",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 3",
    prompt: "This is the prompt for Prompt 3",
    description: "This is a detailed description for Prompt 3",
  },
  {
    mainImage: "/img/prompt4.png",
    label: "Prompt 4",
    price: "$1599",
    category: "Prompt",
    rating: "4.8",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 4",
    prompt: "This is the prompt for Prompt 4",
    description: "This is a detailed description for Prompt 4",
  },
  {
    mainImage: "/img/prompt5.png",
    label: "Prompt 5",
    price: "$1599",
    category: "Image",
    rating: "4.4",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 5",
    prompt: "This is the prompt for Prompt 5",
    description: "This is a detailed description for Prompt 5",
  },
  {
    mainImage: "/img/prompt6.png",
    label: "Prompt 6",
    price: "$1599",
    category: "App",
    rating: "4.9",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 6",
    prompt: "This is the prompt for Prompt 6",
    description: "This is a detailed description for Prompt 6",
  },
  {
    mainImage: "/img/prompt7.png",
    label: "Prompt 7",
    price: "$1299",
    category: "Category",
    rating: "4.5",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 1",
    prompt: "This is the prompt for Prompt 1",
    description: "This is a detailed description for Prompt 1",
  },
  {
    mainImage: "/img/prompt8.png",
    label: "Prompt 8",
    price: "$1399",
    category: "Category",
    rating: "4.7",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 2",
    prompt: "This is the prompt for Prompt 2",
    description: "This is a detailed description for Prompt 2",
  },
  {
    mainImage: "/img/prompt9.png",
    label: "Prompt 9",
    price: "$1499",
    category: "Category",
    rating: "4.6",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 3",
    prompt: "This is the prompt for Prompt 3",
    description: "This is a detailed description for Prompt 3",
  },
  {
    mainImage: "/img/prompt10.png",
    label: "Prompt 10",
    price: "$1599",
    category: "Prompt",
    rating: "4.8",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 4",
    prompt: "This is the prompt for Prompt 4",
    description: "This is a detailed description for Prompt 4",
  },
  {
    mainImage: "/img/prompt11.png",
    label: "Prompt 11",
    price: "$1599",
    category: "Image",
    rating: "4.4",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 5",
    prompt: "This is the prompt for Prompt 5",
    description: "This is a detailed description for Prompt 5",
  },
  {
    mainImage: "/img/prompt12.png",
    label: "Prompt 12",
    price: "$1599",
    category: "App",
    rating: "4.9",
    additionalImages: ["/img/1.png", "/img/2.png", "/img/3.png"],
    username: "User 6",
    prompt: "This is the prompt for Prompt 6",
    description: "This is a detailed description for Prompt 6",
  },
];

export const Course_Types = [
  {
    label: "AI Assistants / Agents",
    value: "AI Assistants / Agents" as const,
  },
  {
    label: "Blockchain",
    value: "Basics of DL" as const,
  },
  {
    label: "Knowledge Graph",
    value: "Agent Graph" as const,
  },
  {
    label: "Prompt Engineering",
    value: "Advance Prompt Eng" as const,
  },

  {
    label: "RAG",
    value: "RAG" as const,
  },

  {
    label: "UI/UX",
    value: "Basics of Prompt Eng" as const,
  },
];

export const BLOG_TAGS = [
  {
    label: "Case Study",
    value: "casestudy" as const,
  },
  {
    label: "Development",
    value: "development" as const,
  },
  {
    label: "Learning",
    value: "learning" as const,
  },
  {
    label: "Research",
    value: "research" as const,
  },
];

export const BOUNTY_TAGS = [
  {
    label: "AI/ML",
    value: "ai/ml" as const,
  },
  {
    label: "Assets",
    value: "assets" as const,
  },
  {
    label: "Blockchain",
    value: "blockchain" as const,
  },
  {
    label: "Backend",
    value: "backend" as const,
  },
  {
    label: "Bug",
    value: "bug" as const,
  },

  {
    label: "Chatbot",
    value: "chatbot" as const,
  },
  {
    label: "Design",
    value: "design" as const,
  },
  {
    label: "Finetuning",
    value: "finetuning" as const,
  },

  {
    label: "Frontend",
    value: "frontend" as const,
  },
  {
    label: "Fullstack",
    value: "fullstack" as const,
  },

  {
    label: "Prompt",
    value: "prompt" as const,
  },
  {
    label: "Testing",
    value: "testing" as const,
  },
  {
    label: "UI/UX",
    value: "ui/ux" as const,
  },
];

export const User_Interests = [
  {
    label: "Prompt Engineering",
    value: "prompt engineering" as const,
  },
  {
    label: "Content Creation",
    value: "content creation" as const,
  },
  {
    label: "Assets",
    value: "assets" as const,
  },
  {
    label: "AI Applications",
    value: "ai applications" as const,
  },
  {
    label: "Learning and Courses",
    value: "learning and courses" as const,
  },
  {
    label: "Blogs",
    value: "blogs" as const,
  },
  {
    label: "Bounties",
    value: "bounties" as const,
  },
  {
    label: "Community Collaboration",
    value: "community collaboration" as const,
  },
];

export const Rapp_Category = [
  // { label: "All", value: "all" },
  { label: "3D", value: "3d" },
  { label: "Animal", value: "animal" },
  { label: "Art", value: "art" },
  { label: "Avatar", value: "avatar" },
  { label: "Business", value: "business" },
  { label: "Cartoon", value: "cartoon" },
  { label: "Chatbot", value: "chatbot" },
  { label: "Code", value: "code" },
  { label: "Drawing", value: "drawing" },
  { label: "Fashion", value: "fashion" },
  { label: "Finance", value: "finance" },
  { label: "Food", value: "food" },
  { label: "Games", value: "games" },
  { label: "Graphic Design", value: "graphic design" },
  { label: "Health", value: "health" },
  { label: "Logo", value: "logo" },
  { label: "Music", value: "music" },
  { label: "Nature", value: "nature" },
  { label: "Pattern", value: "pattern" },
  { label: "Photography", value: "photography" },
  { label: "Prompts", value: "prompts" },
  { label: "SEO", value: "seo" },
  { label: "Sport", value: "sport" },
  { label: "Study", value: "study" },
  { label: "Translate", value: "translate" },
  { label: "Travel", value: "travel" },
  { label: "Vehicle", value: "vehicle" },
  { label: "Wallpaper", value: "wallpaper" },
  { label: "Writing", value: "writing" },
  { label: "None", value: "none" },
];
