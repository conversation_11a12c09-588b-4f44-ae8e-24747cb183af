import express from "express";
import { getPayloadClient } from "./server/get-payload";
import { nextApp, nextHandler } from "./server/next-utils";
import webhookRouter from "./webhooks";
import getRoutes from "./routes/getRoute";
import userRouter from "./routes/userRoute";
import { downloadFiles } from "./routes/downloadFiles";

const app = express();
const PORT = Number(process.env.PORT) || 3000;

app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));

const start = async () => {
  const payload = await getPayloadClient({
    initOptions: {
      express: app,
      onInit: async (cms) => {
        cms.logger.info(`Admin URL: ${cms.getAdminURL()}`);
      },
    },
  });

  // Skip Next.js build logic - handled by separate command now

  await nextApp.prepare();

  app.use("/api2", webhookRouter);
  app.use("/api2", getRoutes);
  app.use("/api2/user", userRouter);
  app.use("/api2/downloadFile/:type/:id/:fileName/:userId", downloadFiles);

  app.all("*", (req, res) => nextHandler(req, res));

  app.listen(PORT, () => {
    payload.logger.info(`✅ App running at ${process.env.NEXT_PUBLIC_SERVER_URL}`);
  });
};

start();
