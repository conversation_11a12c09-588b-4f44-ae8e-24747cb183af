import express from "express";
import { getPayloadClient } from "./server/get-payload";
import { nextApp, nextHandler } from "./server/next-utils";
import webhookRouter from "./webhooks";
import nextBuild from "next/dist/build";
import path from "path";
import getRoutes from "./routes/getRoute";
import userRouter from "./routes/userRoute";
import { downloadFiles } from "./routes/downloadFiles";
import {downloadUploadedFile} from "./routes/downloadUploadedFile";
// dotenv.config({
//   path: path.resolve(__dirname, "../.env.production"),
// });
// console.log("config in expressServer", dotenv.config());
// console.log("Inside express server...next build ::", process.env.NEXT_BUILD);

const app = express();
const PORT = Number(process.env.PORT) || 3000;
// console.log("NEXT BUILD config in expressServer:: ", process.env.NEXT_BUILD);

// Set Body Parser Limits Here
app.use(express.json({ limit: '50mb' })); 
app.use(express.urlencoded({ limit: '50mb', extended: true })); 

const start = async () => {
  const payload = await getPayloadClient({
    initOptions: {
      express: app,
      onInit: async (cms) => {
        cms.logger.info(`Admin URL: ${cms.getAdminURL()}`);
      },
    },
  });

  // if (process.env.NEXT_BUILD) {
  //   app.listen(PORT, async () => {
  //     payload.logger.info("Next.js is building for production");
  //     await nextBuild(path.join(__dirname, "../"));
  //     process.exit();
  //   });
  //   return;
  // }
  if (process.env.NEXT_BUILD) {
    app.listen(PORT, async () => {
      payload.logger.info("Next.js is building for production");

      // @ts-expect-error
      await nextBuild(path.join(__dirname, "../"));
      process.exit();
    });
    return;
  }

  payload.logger.info("Next.js is going to start...setting up routes...");

  app.use("/api2", webhookRouter);
  app.use("/api2", getRoutes);
  app.use("/api2/user", userRouter);
  app.use("/api2/downloadFile/:type/:id/:fileName/:userId", downloadFiles);
  app.use("/api2/downloadUploadedFile/:fileName", downloadUploadedFile);

  payload.logger.info("Next.js is going to start...");
  app.use((req, res) => nextHandler(req, res));
  nextApp.prepare().then(() => {
    payload.logger.info("Next.js started");
    app.listen(PORT, async () => {
      payload.logger.info(`Next.js App URL: ${process.env.NEXT_PUBLIC_SERVER_URL}`);
    });
  });
};

start();
