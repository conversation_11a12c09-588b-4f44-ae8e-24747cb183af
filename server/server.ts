// import express from "express";
// import payload from "payload";
// import http from "http";
// import { initSocketServer } from "./socketServer";
// import dotenv from "dotenv";

// dotenv.config();

// const app = express();
// const server = http.createServer(app);

// // 👇 Setup Socket.IO on the same server (port 3000)
// initSocketServer(server);

// const start = async () => {
//   await payload.init({
//     express: app,
//     secret: process.env.PAYLOAD_SECRET!,
//     config: require("../payload.config").default,
//   });

//   // 👇 You don’t need this line below; it causes the `expressMiddleware` error
//   // app.use("/admin", payload.expressMiddleware());

//   // 👇 Just serve admin like this (already handled by Payload)
//   // app.use("/admin", payload.express?.router);

//   server.listen(3000, () => {
//     console.log("✅ Server + Socket.IO listening at http://localhost:3000");
//   });
// };

// start();
