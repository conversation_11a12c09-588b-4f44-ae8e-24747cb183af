import next from "next";
import dotenv from "dotenv";
import path from "path";

// dotenv.config({
//   path: path.resolve(__dirname, "../.env.production"),
// });
// console.log("config in next-utils", dotenv.config());
const PORT = Number(process.env.PORT) || 3000;

export const nextApp = next({
  dev: process.env.NODE_ENV !== "production",
  port: PORT,
});

export const nextHandler = nextApp.getRequestHandler();
