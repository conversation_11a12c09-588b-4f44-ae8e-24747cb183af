import type { InitOptions } from "payload/config";
import payload, { Payload } from "payload";
import { resendEmailConfig } from "../lib/resendEmailAdapter";


interface Args {
  initOptions?: Partial<InitOptions>;
}

let cached = (global as any).payload;
if (!cached) {
  cached = (global as any).payload = {
    client: null,
    promise: null,
  };
}

export const getPayloadClient = async ({
  initOptions,
}: Args = {}): Promise<Payload> => {
  if (!process.env.PAYLOAD_SECRET) {
    throw new Error("PAYLOAD_SECRET is missing");
  }

  if (cached.client) {
    return cached.client;
  }

  if (!cached.promise) {
    cached.promise = payload.init({
      email: resendEmailConfig,
      secret: process.env.PAYLOAD_SECRET,
      local: initOptions?.express ? false : true,
      ...(initOptions || {}),
    });
  }

  try {
    cached.client = await cached.promise;
  } catch (e: unknown) {
    cached.promise = null;
    throw e;
  }

  return cached.client;
};
// import type { InitOptions } from "payload/config";
// import payload, { Payload } from "payload";
// import nodemailer from "nodemailer";

// interface Args {
//   initOptions?: Partial<InitOptions>;
// }


// // dotenv.config({
// //   path: path.resolve(__dirname, "../.env.production"),
// // });
// // console.log("config in get-payload", dotenv.config());
// const transporter = nodemailer.createTransport({
//   host: "smtp.resend.com",
//   secure: true,
//   port: 465,
//   auth: {
//     user:"resend",
//     pass:"re_g6hutZCM_HW51dVW2cA23c1qRs9fZD8dp",
//   },
// });

// let cached = (global as any).payload;
// if (!cached) {
//   cached = (global as any).payload = {
//     client: null,
//     promise: null,
//   };
// }

// export const getPayloadClient = async ({
//   initOptions,
// }: Args = {}): Promise<Payload> => {
//   if (!process.env.PAYLOAD_SECRET) {
//     throw new Error("PAYLOAD_SECRET is missing");
//   }

//   if (cached.client) {
//     return cached.client;
//   }

//   if (!cached.promise) {
//     cached.promise = payload.init({
//       email: {
//         transport: transporter,
//         fromAddress: '<EMAIL>',
//         fromName: 'RentPrompts',
//       },
//       secret: process.env.PAYLOAD_SECRET,
//       local: initOptions?.express ? false : true,
//       ...(initOptions || {}),
//     });
//   }

//   try {
//     cached.client = await cached.promise;
//   } catch (e: unknown) {
//     cached.promise = null;
//     throw e;
//   }

//   return cached.client;
// };


// import type { InitOptions } from "payload/config";
// import payload, { Payload } from "payload";
// import { Resend } from 'resend';

// interface Args {
//   initOptions?: Partial<InitOptions>;
// }

// const resend = new Resend(process.env.RESEND_API_KEY);

// let cached = (global as any).payload;
// if (!cached) {
//   cached = (global as any).payload = {
//     client: null,
//     promise: null,
//   };
// }

// export const getPayloadClient = async ({
//   initOptions,
// }: Args = {}): Promise<Payload> => {
//   if (!process.env.PAYLOAD_SECRET) {
//     throw new Error("PAYLOAD_SECRET is missing");
//   }

//   if (cached.client) {
//     return cached.client;
//   }

//   if (!cached.promise) {
//     cached.promise = payload.init({
//       email: {
//         send: async (options) => {
//           const { to, subject, message } = options;
//           try {
//             const data = await resend.emails.send({
//               from: '<EMAIL>',
//               to: to,
//               subject: subject,
//               html: message,
//             });
//             console.log('Email sent successfully', data);
//           } catch (error) {
//             console.error('Error sending email', error);
//             throw error;
//           }
//         },
//         fromName: 'RentPrompts',
//         fromAddress: '<EMAIL>',
//       },
//       secret: process.env.PAYLOAD_SECRET,
//       local: initOptions?.express ? false : true,
//       ...(initOptions || {}),
//     });
//   }

//   try {
//     cached.client = await cached.promise;
//   } catch (e: unknown) {
//     cached.promise = null;
//     throw e;
//   }

//   return cached.client;
// };
