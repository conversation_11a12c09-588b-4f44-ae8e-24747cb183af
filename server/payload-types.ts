/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

export interface Config {
  collections: {
    users: User;
    products: Product;
    media: Media;
    product_files: ProductFile;
    orders: Order;
    models: Model;
    bounties: Bounty;
    blogs: Blog;
    rapps: Rapp;
    category: Category;
    tags: Tag;
    purchases: Purchase;
    rappsPurchases: RappsPurchase;
    privateRapps: PrivateRapp;
    prices: Price;
    paymentdetails: Paymentdetail;
    contactInfo: ContactInfo;
    courses: Course;
    wallet: Wallet;
    payout: Payout;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  globals: {};
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  user_name?: string | null;
  AuthProvider?: string | null;
  coinBalance: number;
  role: 'admin' | 'entAdmin' | 'entUser' | 'user';
  products?: (string | Product)[] | null;
  product_files?: (string | ProductFile)[] | null;
  purchases?: (string | Purchase)[] | null;
  rappsPurchases?: (string | RappsPurchase)[] | null;
  bounties?: (string | Bounty)[] | null;
  rapps?: (string | Rapp)[] | null;
  profileImage?: string | Media | null;
  coverImage?: string | Media | null;
  genInfo?: {
    education?: string | null;
    skills?: string | null;
    gender?: ('male' | 'female' | 'other') | null;
    age?: number | null;
    profession?: string | null;
    workExperience?: number | null;
    interests?:
      | (
          | 'prompt engineering'
          | 'content creation'
          | 'assets'
          | 'ai applications'
          | 'learning and courses'
          | 'blogs'
          | 'bounties'
          | 'community collaboration'
        )[]
      | null;
  };
  socialMediaLinks?: {
    facebook?: string | null;
    instagram?: string | null;
    twitter?: string | null;
    github?: string | null;
    discord?: string | null;
  };
  likes?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  following?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  followers?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  productListing?: number | null;
  userType?: ('individual' | 'enterprise' | 'student') | null;
  over18?: boolean | null;
  domain?: string | null;
  members?: (string | User)[] | null;
  associatedWith?: (string | null) | User;
  rappAccess?:
    | {
        rappId: string | PrivateRapp;
        getAccess?: ('read' | 'delete' | 'create' | 'update')[] | null;
        id?: string | null;
      }[]
    | null;
  privateRapps?: (string | PrivateRapp)[] | null;
  createRappPermission?: boolean | null;
  tokens: number;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  _verified?: boolean | null;
  _verificationToken?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: string;
  name?: string | null;
  description?: string | null;
  generationType: 'text' | 'image' | 'video' | 'music' | '3d';
  generationModel?: string | null;
  price?: number | null;
  rating?: number | null;
  userRatings?:
    | {
        user?: (string | null) | User;
        rating?: number | null;
        id?: string | null;
      }[]
    | null;
  userComment?:
    | {
        user: string | User;
        comment?: string | null;
        id?: string | null;
      }[]
    | null;
  listingCategory?: ('prompt' | 'asset' | 'datasets' | 'models' | 'automation') | null;
  product_files?: (string | null) | ProductFile;
  approvedForSale?: ('pending' | 'approved' | 'denied') | null;
  adminReview?: string | null;
  productCategory?: (string | null) | Category;
  isFeatured?: boolean | null;
  priceId?: string | null;
  stripeId?: string | null;
  images?:
    | {
        image?: string | Media | null;
        id?: string | null;
      }[]
    | null;
  needsApproval?: boolean | null;
  slug?: string | null;
  affiliated_with?: string | null;
  user?: (string | null) | User;
  likes?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product_files".
 */
export interface ProductFile {
  id: string;
  user?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "category".
 */
export interface Category {
  id: string;
  label: string;
  value: string;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  user?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    tablet?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "purchases".
 */
export interface Purchase {
  id: string;
  purchaseType: 'bounty' | 'assets' | 'course' | 'prompt';
  prompt?: (string | null) | Rapp;
  product?: (string | null) | Product;
  bounty?: (string | null) | Bounty;
  course?: (string | null) | Course;
  user: string | User;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "rapps".
 */
export interface Rapp {
  id: string;
  modelType: 'text' | 'image' | 'audio' | 'video';
  model: string | Model;
  key: 'test' | 'prod';
  systemprompt?: string | null;
  prompt?: string | null;
  negativeprompt?: string | null;
  rating: number;
  userRatings?:
    | {
        user: string | User;
        rating: number;
        id?: string | null;
      }[]
    | null;
  userComment?:
    | {
        user: string | User;
        comment: string;
        id?: string | null;
      }[]
    | null;
  status: 'approved' | 'denied' | 'pending';
  approvedDate?: string | null;
  adminReview?: string | null;
  needsApproval: boolean;
  isFeatured: boolean;
  imageinput: boolean;
  imageinputopt?: boolean | null;
  PDFinput: boolean;
  affiliated_with?: string | null;
  likes?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  purchases?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  category?: (string | null) | Category;
  tags?: (string | Tag)[] | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  name: string;
  slug?: string | null;
  description: string;
  priceapplicable: boolean;
  price?: number | null;
  totalCost: number;
  getprompt: boolean;
  promptcost?: number | null;
  promptpurchase?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  images?:
    | {
        image: string | Media;
        id?: string | null;
      }[]
    | null;
  creator: string | User;
  systemVariables?:
    | {
        name: string;
        identifier: string;
        displayName: string;
        description?: string | null;
        placeholder?: string | null;
        type: 'string' | 'number' | 'boolean' | 'select';
        allowMultiple: boolean;
        options?: string[] | null;
        id?: string | null;
      }[]
    | null;
  promptVariables?:
    | {
        name: string;
        identifier: string;
        displayName: string;
        description?: string | null;
        placeholder?: string | null;
        type: 'string' | 'number' | 'boolean' | 'select';
        allowMultiple: boolean;
        options?: string[] | null;
        id?: string | null;
      }[]
    | null;
  negativeVariables?:
    | {
        name: string;
        identifier: string;
        displayName: string;
        description?: string | null;
        placeholder?: string | null;
        type: 'string' | 'number' | 'boolean' | 'select';
        allowMultiple: boolean;
        options?: string[] | null;
        id?: string | null;
      }[]
    | null;
  settings?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "models".
 */
export interface Model {
  id: string;
  name: string;
  description: string;
  type: 'text' | 'image' | 'audio' | 'video' | 'vision';
  modelImage: {
    image: string | Media;
    id?: string | null;
  }[];
  organization: string;
  organizationImage: {
    image: string | Media;
    id?: string | null;
  }[];
  isFeatured?: boolean | null;
  imageinput: boolean;
  imageinputopt: boolean;
  provider: 'groq' | 'openai' | 'replicate' | 'falai' | 'runway';
  settings?:
    | {
        name: string;
        type: 'integer' | 'float' | 'string' | 'boolean' | 'select';
        minValue?: number | null;
        maxValue?: number | null;
        defaultValue?: number | null;
        description: string;
        options?: string[] | null;
        allowMultiple: boolean;
        id?: string | null;
      }[]
    | null;
  enablePrompt: boolean;
  systemprompt: boolean;
  negativeprompt: boolean;
  prodkeys?: {
    groq?: {
      modelname: string;
      apikey: string;
    };
    replicate?: {
      modelname: string;
      apikey: string;
    };
    openai?: {
      modelname: string;
      apikey: string;
    };
    falai?: {
      modelname: string;
      apikey: string;
    };
    runway?: {
      modelname: string;
      apikey: string;
    };
  };
  testkeys?: {
    groq?: {
      modelname: string;
      apikey: string;
    };
    replicate?: {
      modelname: string;
      apikey: string;
    };
    openai?: {
      modelname: string;
      apikey: string;
    };
    falai?: {
      modelname: string;
      apikey: string;
    };
    runway?: {
      modelname: string;
      apikey: string;
    };
  };
  cost: number;
  commissionapplicable: boolean;
  commision?: number | null;
  examples?:
    | {
        example?: string | null;
        id?: string | null;
      }[]
    | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags".
 */
export interface Tag {
  id: string;
  label: string;
  value: string;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bounties".
 */
export interface Bounty {
  id: string;
  title: string;
  slug?: string | null;
  content: string;
  completionDate: string;
  status?: ('pending' | 'approved' | 'denied' | 'completed') | null;
  adminReview?: string | null;
  needsApproval?: boolean | null;
  isFeatured?: boolean | null;
  estimatedPrice: number;
  bountyType: 'ai' | 'blockchain' | 'mobile' | 'ml' | 'web';
  product_files?: (string | null) | ProductFile;
  tags?:
    | (
        | 'ai/ml'
        | 'assets'
        | 'blockchain'
        | 'backend'
        | 'bug'
        | 'chatbot'
        | 'design'
        | 'finetuning'
        | 'frontend'
        | 'fullstack'
        | 'prompt'
        | 'testing'
        | 'ui/ux'
      )[]
    | null;
  user: string | User;
  applicants?:
    | {
        userId: string;
        userName?: string | null;
        linkedin: string;
        phone: number;
        approach: string;
        relatedFile?: (string | ProductFile)[] | null;
        id?: string | null;
      }[]
    | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses".
 */
export interface Course {
  id: string;
  title: string;
  description: string;
  cost: number;
  level?: ('beginner' | 'intermediate' | 'advance') | null;
  status?: ('pending' | 'approved' | 'denied') | null;
  needsApproval?: boolean | null;
  slug?: string | null;
  isFeatured?: boolean | null;
  attachment?: (string | Media)[] | null;
  time?: ('1day' | '2days' | '4days' | '1week' | '2week' | '1month' | 'above 1month') | null;
  type: (
    | 'AI Assistants / Agents'
    | 'Basics of DL'
    | 'Agent Graph'
    | 'Advance Prompt Eng'
    | 'RAG'
    | 'Basics of Prompt Eng'
  )[];
  pdf: string | ProductFile;
  user: string | User;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "rappsPurchases".
 */
export interface RappsPurchase {
  id: string;
  rapps?: (string | null) | Rapp;
  user: string | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "privateRapps".
 */
export interface PrivateRapp {
  id: string;
  modelType: 'text' | 'image';
  model: string | Model;
  key: 'test' | 'prod';
  systemprompt?: string | null;
  prompt?: string | null;
  negativeprompt?: string | null;
  status: 'pending' | 'approved' | 'denied';
  slug?: string | null;
  imageinput: boolean;
  imageinputopt?: boolean | null;
  purchases?:
    | {
        user: string | User;
        id?: string | null;
      }[]
    | null;
  name: string;
  description: string;
  price?: number | null;
  totalCost: number;
  tokens: number;
  image?: string | Media | null;
  creator: string | User;
  systemVariables?:
    | {
        name: string;
        identifier: string;
        displayName: string;
        description?: string | null;
        placeholder?: string | null;
        type: 'string' | 'number' | 'boolean' | 'select';
        allowMultiple: boolean;
        options?: string[] | null;
        id?: string | null;
      }[]
    | null;
  promptVariables?:
    | {
        name: string;
        identifier: string;
        displayName: string;
        description?: string | null;
        placeholder?: string | null;
        type: 'string' | 'number' | 'boolean' | 'select';
        allowMultiple: boolean;
        options?: string[] | null;
        id?: string | null;
      }[]
    | null;
  negativeVariables?:
    | {
        name: string;
        identifier: string;
        displayName: string;
        description?: string | null;
        placeholder?: string | null;
        type: 'string' | 'number' | 'boolean' | 'select';
        allowMultiple: boolean;
        options?: string[] | null;
        id?: string | null;
      }[]
    | null;
  access?:
    | {
        userId: string | User;
        getAccess: ('read' | 'delete' | 'update')[];
        id?: string | null;
      }[]
    | null;
  settings?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders".
 */
export interface Order {
  id: string;
  totalCoins: number;
  totalBasePrice: number;
  total: number;
  tax?: number | null;
  paymentMethod?: ('RAZORPAY' | 'PHONEPE') | null;
  paymentId?: (string | null) | Paymentdetail;
  razorPayOrderId?: string | null;
  status?: ('init' | 'pending' | 'completed' | 'rejected') | null;
  _isPaid: boolean;
  user: string | User;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "paymentdetails".
 */
export interface Paymentdetail {
  id: string;
  paymentMethod: string;
  providerTxId?: string | null;
  totalAmountPaid: number;
  tax?: number | null;
  status: 'init' | 'paid' | 'pending' | 'rejected';
  payload?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blogs".
 */
export interface Blog {
  id: string;
  title: string;
  slug?: string | null;
  content: string;
  richText: {
    [k: string]: unknown;
  }[];
  status?: ('pending' | 'approved' | 'denied') | null;
  adminReview?: string | null;
  needsApproval?: boolean | null;
  isFeatured?: boolean | null;
  time: number;
  images: {
    image: string | Media;
    id?: string | null;
  }[];
  tags: 'casestudy' | 'development' | 'learning' | 'research';
  user: string | User;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "prices".
 */
export interface Price {
  id: string;
  packageName: string;
  numberOfCoins: number;
  priceInDollars?: string | null;
  isTopUp: boolean;
  rate: number;
  tax: number;
  discount: number;
  active: boolean;
  benefits?: string | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "contactInfo".
 */
export interface ContactInfo {
  id: string;
  userName?: string | null;
  email?: string | null;
  phoneNumber?: number | null;
  organizationName?: string | null;
  query: string;
  courseName?: string | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "wallet".
 */
export interface Wallet {
  id: string;
  commisionFrom: 'rapps' | 'generate' | 'assets' | 'course' | 'bounty';
  totalComputeCostReceived: number;
  totalCommisionReceived: number;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payout".
 */
export interface Payout {
  id: string;
  username?: string | null;
  bankDetails?: {
    accountNumber?: number | null;
    confirmAccountNumber?: number | null;
    ifscCode?: string | null;
    withdrawAmountBank?: number | null;
  };
  upiDetails?: {
    upiAddress?: string | null;
    confirmUpiAddress?: string | null;
    withdrawAmountUpi?: number | null;
  };
  international?: {
    internationalDetails?: string | null;
    withdrawAmountInternational?: number | null;
  };
  status?: ('submitted' | 'processed' | 'completed' | 'denied') | null;
  user: string | User;
  createdBy?: string | null;
  updatedBy?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}