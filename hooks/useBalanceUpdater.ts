"use client";

import { useEffect } from 'react';
import { useCoinBalanceContext } from '@/components/coin-balance-initializer';
import { BalanceUpdateScenario, logBalanceUpdate } from '@/lib/balance-utils';

/**
 * Custom hook for manual balance updates
 * यह hook सिर्फ user actions के बाद use करें
 */
export const useBalanceUpdater = () => {
  const { updateBalance, setBalance, incrementBalance, decrementBalance } = useCoinBalanceContext();

  // Listen for custom balance update events
  useEffect(() => {
    const handleBalanceUpdate = () => {
      updateBalance();
    };

    window.addEventListener('updateCoinBalance', handleBalanceUpdate);
    return () => window.removeEventListener('updateCoinBalance', handleBalanceUpdate);
  }, [updateBalance]);

  // Manual balance update functions
  const updateBalanceAfterPurchase = (newBalance: number, purchaseAmount: number) => {
    setBalance(newBalance);
    logBalanceUpdate('purchase', purchaseAmount, newBalance);
  };

  const updateBalanceAfterRecharge = (newBalance: number, rechargeAmount: number) => {
    setBalance(newBalance);
    logBalanceUpdate('recharge', rechargeAmount, newBalance);
  };

  const updateBalanceAfterRefund = (newBalance: number, refundAmount: number) => {
    setBalance(newBalance);
    logBalanceUpdate('refund', refundAmount, newBalance);
  };

  const updateBalanceAfterBountyPayment = (newBalance: number, bountyAmount: number) => {
    setBalance(newBalance);
    logBalanceUpdate('bounty_payment', bountyAmount, newBalance);
  };

  const updateBalanceAfterBountyRefund = (newBalance: number, refundAmount: number) => {
    setBalance(newBalance);
    logBalanceUpdate('bounty_refund', refundAmount, newBalance);
  };

  // Generic update function
  const updateBalanceManually = (
    scenario: BalanceUpdateScenario,
    newBalance: number,
    amount: number
  ) => {
    setBalance(newBalance);
    logBalanceUpdate(scenario, amount, newBalance);
  };

  // Fetch fresh balance from server
  const refreshBalanceFromServer = async () => {
    await updateBalance();
  };

  return {
    updateBalanceAfterPurchase,
    updateBalanceAfterRecharge,
    updateBalanceAfterRefund,
    updateBalanceAfterBountyPayment,
    updateBalanceAfterBountyRefund,
    updateBalanceManually,
    refreshBalanceFromServer,
  };
};
