import type { Configuration } from "webpack";

const config: Configuration = {
  resolve: {
    fallback: {
      fs: false,
      url: false,
      zlib: false,
      net: false,
      util: false,
      dns: false,
      os: false,
      tls: false,
      child_process: false,
      assert: false,
      crypto: false,
      http: false,
      https: false,
      querystring: require.resolve("querystring-es3"),
      timers: require.resolve("timers-browserify"),
    },
  },
};

export default config;
