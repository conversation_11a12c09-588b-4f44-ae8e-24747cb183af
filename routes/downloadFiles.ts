
import { getPayloadClient } from '../server/get-payload'; 
import { PayloadRequest } from 'payload/types';
import express, { Request, Response } from "express";
import { getS3Client } from '../payload/lib/cloudflareS3Client';
import { GetObjectCommand } from '@aws-sdk/client-s3';


const s3Client = getS3Client();

export const downloadFiles = async (req: PayloadRequest, res: Response) => {

 const id = req.params.id;
 const fileName = req.params.fileName;
 const userId = req.params.userId;
 const type = req.params.type;
 

 try{

  const payload = await getPayloadClient();

  // Get the user
  const user:any = await payload.findByID({
    collection: 'users',
    id: userId,
  });


  if (!user) return false;

  // Check if user has purchased the product
  const { docs: purchases }:any = await payload.find({
    collection: 'purchases',
    depth: 0,
    where: {
      user: {
        equals: userId,
      },
    },
  });


  let purchasedIds;
  if (type === 'product') {
    purchasedIds = purchases.map((purchase) => purchase.product).flat();
  } else if (type === 'course') {
    purchasedIds = purchases.map((purchase) => purchase.course).flat();
  } else {
    return res.status(400).send('Invalid type parameter');
  }

 
  if (!purchasedIds.includes(id)) {
    return false;
  }

  const command = new GetObjectCommand({
    Bucket: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_BUCKET_NAME,
    Key: fileName,
  });

  // Get the object from the bucket
  const response = await s3Client.send(command);
  const fileStream = response.Body;

   // Set appropriate headers
   res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
   res.setHeader('Content-Type', 'application/octet-stream');

   // Pipe the file stream to the response
   fileStream.pipe(res);

} catch (error) {
  console.error("Error getting file from R2:", error);
  return res.status(403).json("Getting error while downloading file")
}

};


