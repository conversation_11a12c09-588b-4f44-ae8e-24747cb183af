import payload from "payload";
import { getPayloadClient } from "../server/get-payload";
import express, { Request, Response } from "express";
const router = express.Router();

const create = async (req: Request, res: Response) => {
  const { email, password } = req.body;
  const payload = await getPayloadClient();

  if (!email || !password) {
    return res.status(200).send({
      success: false,
      message: "Email or Password not received",
    });
  }

  try {
    // check if user already exists
    const { docs: users } = await payload.find({
      collection: "users",
      where: {
        email: {
          equals: email,
        },
      },
    });

    if (users.length !== 0)
      return res
        .status(200)
        .send({ success: false, message: "User already exists" });

    await payload.create({
      collection: "users",
      data: {
        email,
        password,
        role: "user",
        followers: [],
        following: [],
        coinBalance: 0,
        tokens:0,
        // user_name: email.split("@")[0],
      },
    });

    return res.status(201).send({ success: true, message: "User created" });
  } catch (e) {
    console.log("Error in create userRoute.ts", e.message);
    return res
      .status(500)
      .send({ success: false, message: "Internal Server Error" });
  }
};

const signIn = async (req: Request, res: Response) => {
  const { email, password } = req.body;
  const payload = await getPayloadClient();

  if (!email || !password) {
    return res.status(200).send({
      success: false,
      message: "Email or Password not received",
    });
  }

    // check if user already exists
    const { docs: users } = await payload.find({
      collection: "users",
      where: {
        email: {
          equals: email,
        },
      },
    });

  if (users[0].role === "entUser") {
    return res.status(200).send({
      success: false,
      message: "Enterprise user can't login here",
    });
  }

  try {
    const hel = await payload.login({
      collection: "users",
      data: {
        email,
        password,
      },
      res,
    });

    return res
      .status(200)
      .send({ success: true, message: "Logged in successfully" });


  } catch (err) {
    return res.status(500).send({ success: false, message: err.message });
  }
};

const verifyEmail = async (req: Request, res: Response) => {
  const { token } = req.params;

  // const payload = await getPayloadClient();

  try {
    const user = await payload.find({
      collection: "users",
      where: {  
      _verificationToken: {
          equals: token,
        },
      },
    })

    const getUser = user.docs[0]

    const req = await fetch(`${process.env.PAYLOAD_PUBLIC_SERVER_URL}/api/users/verify/${token}`, {
      method: "POST", 
      // credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
    })

    const data = await req.json();
    const status = req.ok;

    if(status){
      const updatedUser = await payload.update({
        collection: "users",
        id: getUser?.id,
        data: {
          coinBalance: 25
        }

      })
    }

    return res.json({ success: true, message: "Verified", status})
  } catch (err) {
    return res.json({ code: "UNAUTHORIZED", error: err.message });
  }
    // const isVerified = await payload.verifyEmail({
    //   collection: "users",
    //   token,
    // });

};


const forgotPassword = async (req: Request, res: Response) => {
  const { email } = req.body;
  const payload = await getPayloadClient();

  if (!email) {
    return res.status(400).json({
      success: false,
      error: "Email not received",
    });
  }

  try {
    // Check if user exists
    const { docs: users } = await payload.find({
      collection: "users",
      where: {
        email: {
          equals: email,
        },
      },
    });

    if (users.length === 0) {
      return res.status(404).json({ code: "NOT_FOUND" });
    }

    // Generate a password reset token
    const user = users[0];
    const resetToken = await payload.forgotPassword({
      collection: "users",
      data: {
        email,
      },
    });

    // Send the password reset email
    // const resetLink = `${process.env.NEXT_PUBLIC_SERVER_URL}/reset-password?token=${resetToken}`;
    // await payload.sendEmail({
    //   from: "RentPrompts <<EMAIL>>",
    //   to: email,
    //   subject: "Password Reset Request",
    //   html: `<div style="font-family: 'Helvetica', sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background: linear-gradient(135deg, #3730a3, #9d50bb);  color: #ffffff;">
    //   <h1 style="color: #ffffff; text-align: center;">Forgot Your Password?</h1>
    //   <p style="color: #f4f4f4; text-align: center; font-size: 16px; line-height: 1.6;">No worries! You can reset your password with a single click. Just hit the button below and follow the instructions.</p>
    //   <div style="text-align: center; margin: 30px 0;">
    //     <a href="${resetLink}" style="display: inline-block; padding: 15px 30px; background-color: #ff5e57; color: #ffffff; font-size: 18px; font-weight: bold; text-decoration: none; border-radius: 50px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);">Reset My Password</a>
    //   </div>
    //   <p style="color: #d1d1d1; text-align: center; font-size: 14px; margin-top: 20px;">If you didn’t request this, feel free to ignore this email.</p>
    //   <div style="text-align: center; margin-top: 40px;">
    //     <hr style="border: 0; height: 1px; background-color: #ffffff33; margin: 20px 0;" />
    //     <p style="font-size: 12px; color: #e0e0e0;">Having trouble? <a href="mailto:<EMAIL>" style="color: #ff5e57; text-decoration: none;">Contact Support</a></p>
    //     <p style="color: #ccc; font-size: 12px;">© 2024 RentPrompts. All rights reserved.</p>
    //   </div>
    // </div>`,
    // });

    return res.json({ success: true, message: "Password reset email sent" });
  } catch (e) {
    return res.status(500).json({ success: false, error: e.message });
  }
}

const resetPassword = async (req: Request, res: Response) => {
  const { token, password } = req.body;
  const payload = await getPayloadClient();

  if (!token || !password) {
    return res.status(400).json({
      success: false,
      error: "Token or password not received",
    });
  }

  try {
    await payload.resetPassword({
      collection: "users",
      data: {
        token,
        password,
      },
      overrideAccess: true
    });

    return res.json({ success: true, message: "Password reset successfully" });
  } catch (e) {
    return res.status(500).json({ success: false, error: e.message });
  }
};

const checkUsername = async (req: Request, res: Response) => {
  const { user_name } = req.body;
  const payload = await getPayloadClient();

  if (!user_name) {
    return res.status(200).send({
      success: false,
      message: "Username not received",
    });
  }

  try {
    // check if user already exists
    const { docs: users } = await payload.find({
      collection: "users",
      where: {
        user_name: {
          equals: user_name,
        },
      },
    });

    if (users.length !== 0) {
      return res
        .status(200)
        .send({ success: false, message: "username already exists" });
    } else {
      return res.status(201).send({ success: true, message: "username doest not exists" });
    }
  } catch (e) {
    console.log("Error in create userRoute.ts", e.message);
    return res
      .status(500)
      .send({ success: false, message: "Internal Server Error" });
  }
};


router.post("/create", create);
router.post("/verify-email/:token", verifyEmail);
router.post("/signin", signIn);
router.post("/forgot-password", forgotPassword);
router.post("/reset-password", resetPassword);
router.post("/check-username", checkUsername);

export default router;
