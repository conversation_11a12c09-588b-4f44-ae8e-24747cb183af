import { getPayloadClient } from "../server/get-payload";
import express, { Request, Response } from "express";
import { cacheQuery, cacheDocument } from "../lib/cache-manager";

const router = express.Router();

const products = async (req: Request, res: Response) => {
  try {
    const { query, cursor } = req.body;
    const { sort, filters, ...queryOpts } = query || {};
    const limit = req.body.query?.limit || Number(req.query.limit) || 10;
    const page = cursor && cursor > 0 ? cursor : 1;

    // Create cache key based on all filters
    const cacheKey = `products:${JSON.stringify({ sort, filters, queryOpts, limit, page })}`;

    // Use centralized cache with custom key for filtered results
    const result: any = await cacheQuery("products", {
      where: { approvedForSale: { equals: "approved" } },
      depth: 1,
      sort: sort || "-createdAt",
      limit: 1000, // Get all, filter in memory (faster than multiple DB calls)
    }, { customCacheKey: cacheKey });

    // Filter and process in memory (much faster than multiple DB calls)
    let filteredItems = result.docs;

    // Apply queryOpts filters
    const allowedFields = ["price", "status", "featured", "name"];
    Object.entries(queryOpts).forEach(([key, value]) => {
      if (allowedFields.includes(key)) {
        filteredItems = filteredItems.filter((item: any) => item[key] === value);
      }
    });

    // Apply filters
    if (filters) {
      for (const [key, values] of Object.entries(filters)) {
        const filterValues = Array.isArray(values) ? values : [values];

        if (key === "name") {
          const searchName = filterValues[0];
          filteredItems = filteredItems.filter((item: any) =>
            item.name?.toLowerCase().includes(searchName.toLowerCase())
          );
        } else if (key === "category") {
          filteredItems = filteredItems.filter((item: any) => {
            const itemCategory = item.productCategory?.value || item.productCategory?.name;
            return filterValues.includes(itemCategory);
          });
        } else if (key === "productType") {
          filteredItems = filteredItems.filter((item: any) =>
            filterValues.includes(item.productType)
          );
        } else if (key === "status") {
          filteredItems = filteredItems.filter((item: any) =>
            item.status === filterValues[0]
          );
        } else if (key === "price") {
          const isFreeSelected = filterValues.includes(0) || filterValues.includes("0");
          const isPaidSelected = filterValues.includes("gt_0");

          if (isFreeSelected && isPaidSelected) {
            // Show all items
          } else if (isFreeSelected) {
            filteredItems = filteredItems.filter((item: any) => item.price === 0);
          } else if (isPaidSelected) {
            filteredItems = filteredItems.filter((item: any) => item.price > 0);
          }
        } else if (key === "featured" || key === "isFeatured") {
          filteredItems = filteredItems.filter((item: any) => item.isFeatured === true);
        } else if (key === "tags") {
          filteredItems = filteredItems.filter((item: any) => {
            const itemTags = item.tags?.map((tag: any) => tag.value || tag.name) || [];
            return filterValues.some((tag: string) => itemTags.includes(tag));
          });
        }
      }
    }

    // Transform data
    const transformedItems = filteredItems.map((product: any) => ({
      id: product.id,
      length: product.length,
      createdAt: product.createdAt,
      images: product.images,
      likes_id: product.likes?.map((like: any) => like.id),
      likes_length: product.likes?.length || 0,
      likes_user_id: product.likes?.map((like: any) => like.user?.id),
      productCategory: product.productCategory,
      productType: product.productType,
      isFeatured: product.isFeatured,
      price: product.price,
      name: product.name,
      slug: product.slug,
      description: product.description,
      user_id: product.user?.id,
      user_name: product.user?.user_name,
      user_email: product.user?.email,
      affiliated_with: product.affiliated_with,
      tags: product.tags,
      listingCategory: product.listingCategory,
    }));

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedItems = transformedItems.slice(startIndex, startIndex + limit);
    const hasNextPage = startIndex + limit < transformedItems.length;

    const responseData = {
      items: paginatedItems,
      nextPage: hasNextPage ? page + 1 : null,
    };

    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.json(responseData);
  } catch (error) {
    console.error("Error fetching products:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

router.put("/products/:id", async (req: Request, res: Response) => {
  const { id } = req.params;
  const data = req.body;

  try {
    const payload = await getPayloadClient();

    // Update product - this will automatically trigger cache invalidation via Payload hooks
    await payload.update({
      collection: "products",
      id,
      data,
    });

    console.log(`✅ Product updated: ${id} - Cache automatically invalidated via Payload hooks`);
    return res.status(200).json({ message: "Product updated successfully" });
  } catch (error) {
    console.error("Error updating product:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
});

const rapps = async (req: Request, res: Response) => {
  try {
    const { query } = req.body;
    const { sort, model, category, tags, limit = 10, page, name = "", generationType = "" } = query || {};
    const currentPage = page && page > 0 ? page : 1;

    // Create cache key based on all filters
    const cacheKey = `rapps:${JSON.stringify({ sort, model, category, tags, limit, page: currentPage, name, generationType })}`;

    // Use centralized cache with custom key for filtered results
    const result: any = await cacheQuery("rapps", {
      where: { status: { equals: "approved" } },
      depth: 1,
      sort: "-approvedDate",
      limit: 1000, // Get all, filter in memory (faster than multiple DB calls)
    }, { customCacheKey: cacheKey });

    // Filter and process in memory (much faster than multiple DB calls)
    let filteredItems = result.docs;

    // Apply filters
    if (model && model !== "all") {
      const modelArray = Array.isArray(model) ? model : model.split(",").map((m: string) => m.trim());
      filteredItems = filteredItems.filter((item: any) =>
        modelArray.includes(item.model?.name)
      );
    }

    if (category && category !== "all") {
      const categoryArray = Array.isArray(category) ? category : category.split(",").map((c: string) => c.trim());
      filteredItems = filteredItems.filter((item: any) => {
        const itemCategories = Array.isArray(item.category)
          ? item.category.map((cat: any) => cat.value || cat.name)
          : [item.category?.value || item.category?.name];
        return categoryArray.some((cat: string) => itemCategories.includes(cat));
      });
    }

    if (tags && tags !== "all") {
      const tagArray = Array.isArray(tags) ? tags : tags.split(",").map((t: string) => t.trim());
      filteredItems = filteredItems.filter((item: any) => {
        const itemTags = item.tags?.map((tag: any) => tag.value || tag.name) || [];
        return tagArray.some((tag: string) => itemTags.includes(tag));
      });
    }

    if (name && name.length > 0) {
      const searchName = Array.isArray(name) ? name[0] : name;
      filteredItems = filteredItems.filter((item: any) =>
        item.name?.toLowerCase().includes(searchName.toLowerCase())
      );
    }

    if (generationType && generationType !== "all") {
      const validTypes = ["text", "audio", "video", "image"];
      const generationTypeArray = Array.isArray(generationType)
        ? generationType
        : generationType.split(",").map((g: string) => g.trim());

      const invalidTypes = generationTypeArray.filter((type: string) => !validTypes.includes(type));
      if (invalidTypes.length > 0) {
        return res.status(400).json({ error: `Invalid generation type(s): ${invalidTypes.join(", ")}` });
      }

      filteredItems = filteredItems.filter((item: any) =>
        generationTypeArray.includes(item.modelType)
      );
    }

    // Transform data
    const transformedItems = filteredItems.map((item: any) => ({
      likes_id: item.likes?.map((like: any) => like.id),
      likes_user_id: item.likes?.map((like: any) => like.user?.id),
      likes_length: item.likes?.length || 0,
      images: item.images,
      name: item.name,
      slug: item.slug,
      modelType: item.modelType,
      modelName: item.model?.name,
      id: item.id,
      description: item.description,
      price: item.price,
      totalCost: item.totalCost,
      creator: {
        user_name: item.creator?.user_name,
        id: item.creator?.id,
      },
      newest: item.createdAt,
      isFeatured: item.isFeatured,
      category: Array.isArray(item.category)
        ? item.category.map((cat: any) => cat.name)
        : item.category?.name,
      tags: item.tags?.map((t: any) => t.name),
      likesCount: item.likes?.length || 0,
      purchasesCount: item.purchases?.length || 0,
      rating: item.rating,
    }));

    // Apply sorting
    let sortedItems = transformedItems;
    if (sort === "likes") {
      sortedItems = transformedItems.sort((a: any, b: any) => b.likesCount - a.likesCount);
    } else if (sort === "purchases") {
      sortedItems = transformedItems.sort((a: any, b: any) => b.purchasesCount - a.purchasesCount);
    } else if (sort === "isFeatured") {
      sortedItems = transformedItems.filter((item: any) => item.isFeatured === true);
    }

    // Apply pagination
    const startIndex = (currentPage - 1) * limit;
    const paginatedItems = sortedItems.slice(startIndex, startIndex + limit);

    const responseData = {
      items: paginatedItems,
      nextPage: startIndex + limit < sortedItems.length ? currentPage + 1 : null,
    };

    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.json(responseData);
  } catch (error) {
    console.error("Error fetching rapps:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

router.put("/rapps/:id", async (req: Request, res: Response) => {
  const { id } = req.params;
  const data = req.body;

  try {
    const payload = await getPayloadClient();

    // Update rapp - this will automatically trigger cache invalidation via Payload hooks
    await payload.update({
      collection: "rapps",
      id,
      data,
    });

    console.log(`✅ Rapp updated: ${id} - Cache automatically invalidated via Payload hooks`);
    return res.status(200).json({ message: "Rapp updated successfully" });
  } catch (error) {
    console.error("Error updating rapp:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
});

const bounties = async (req: Request, res: Response) => {
  try {
    const { page, limit } = req.query;
    const pageNumber = parseInt(page as string, 10) || 1;
    const pageSize = parseInt(limit as string, 10) || 4;

    // Use centralized cache
    const result: any = await cacheQuery("bounties", {
      where: { status: { in: ["approved", "completed"] } },
      depth: 3,
      sort: "-updatedAt",
    });

    const Bounties = result.docs.map((bounty: any) => ({
      id: bounty.id,
      title: bounty.title,
      slug: bounty.slug,
      content: bounty.content,
      estimatedPrice: bounty.estimatedPrice,
      completionDate: bounty.completionDate,
      isFeatured: bounty.isFeatured,
      type: bounty.bountyType,
      status: bounty.status,
      profileImage: bounty?.user?.profileImage?.url,
      creator: bounty?.user?.user_name,
      applicants: bounty.applicants?.length,
      createdAt: bounty.createdAt,
    }));

    const totalItems = Bounties.length;
    const startIndex = (pageNumber - 1) * pageSize;
    const paginatedItems = Bounties.slice(startIndex, startIndex + pageSize);

    const responseData = {
      data: paginatedItems,
      hasMore: startIndex + pageSize < totalItems,
    };

    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching bounties:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Bounties update route removed - not used in frontend

const Courses = async (_req: Request, res: Response) => {
  try {
    // Use centralized cache
    const result: any = await cacheQuery("courses", {
      where: { status: { equals: "approved" } },
      depth: 2,
      sort: "-updatedAt",
    });

    const coursesData = result.docs.map((course: any) => ({
      id: course.id,
      title: course.title,
      description: course.description,
      cost: course.cost,
      level: course.level,
      time: course.time,
      pdf: course.pdf?.filename || null,
      type: course.type,
      isFeatured: course.isFeatured,
      image: course.attachment?.map((att: any) => att.url).filter(Boolean)[0] || null,
    }));

    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.status(200).json({ data: coursesData });
  } catch (error) {
    console.error("Error fetching courses:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Courses update route removed - not used in frontend

const Models = async (req: Request, res: Response) => {
  try {
    const { type } = req.query as { type?: string };

    // Create cache key based on type filter
    const cacheKey = `models:type:${type || "all"}`;

    // Use centralized cache with custom key
    const result: any = await cacheQuery("models", {
      depth: 1,
      limit: 0,
      sort: "-updatedAt",
    }, { customCacheKey: cacheKey });

    // Filter in memory (faster than DB filtering)
    let filteredItems = result.docs;

    if (type === "both") {
      filteredItems = result.docs.filter((item: any) =>
        item.type === "text" || item.type === "vision"
      );
    } else if (type) {
      filteredItems = result.docs.filter((item: any) => item.type === type);
    }

    const models = filteredItems.map((item: any) => ({
      id: item.id,
      name: item.name,
      provider: item.provider,
      description: item.description,
      cost: (item.cost || 0) + (item.commision || 0),
      type: item.type,
      modelImage: item.modelImage,
      isFeatured: item.isFeatured,
      organization: item.organization,
      organizationImage: item.organizationImage,
      imageinputopt: item.imageinputopt,
    }));

    const responseData = { data: models };
    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching models:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Models update route removed - not used in frontend

const ModelsById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { type } = req.query;

    if (!id) {
      return res.status(400).json({ error: "Model ID parameter is required." });
    }

    if (!type) {
      return res.status(400).json({ error: "Type parameter is required." });
    }

    // Use centralized cache for single document
    const model: any = await cacheDocument("models", id, { depth: 2 });

    if (!model) {
      return res.status(404).json({ error: "Model not found." });
    }

    if (model.type !== type) {
      return res.status(400).json({ error: "Model type does not match." });
    }

    const modelData = {
      id: model.id,
      name: model.name,
      description: model.description,
      modelType: model.type,
      organization: model.organization,
      cost: model.cost,
      commission: model.commision,
      provider: model.provider,
      settings: model.settings,
      examples: model.examples,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      enablePrompt: model.enablePrompt,
      imageinput: model.imageinput,
      imageinputopt: model.imageinputopt,
    };

    const responseData = { data: modelData };
    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching model by ID:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const Prompts = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Use centralized cache for single document (rapps collection)
    const rapp: any = await cacheDocument("rapps", id, { depth: 2 });

    if (!rapp) {
      return res.status(404).json({ error: "Prompt not found." });
    }

    const promptData = {
      id: rapp.id,
      promptcost: rapp.promptcost,
      modelType: rapp.modelType,
      prompt: rapp.prompt,
      negativeprompt: rapp.negativeprompt,
      systemprompt: rapp.systemprompt,
      getprompt: rapp.getprompt,
      promptpurchase: rapp.promptpurchase,
      settings: rapp.settings,
    };

    const responseData = { data: promptData };
    res.setHeader("Cache-Control", "s-maxage=172800, stale-while-revalidate");
    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching prompts:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

router.put("/prompts/:id", async (req: Request, res: Response) => {
  const { id } = req.params;
  const data = req.body;

  try {
    const payload = await getPayloadClient();
    await payload.update({
      collection: "rapps",
      id,
      data,
    });

    console.log(`✅ Prompt updated: ${id} - Cache automatically invalidated via Payload hooks`);
    return res.status(200).json({ message: "Prompt updated successfully" });
  } catch (error) {
    console.error("Error updating prompt:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
});

// Use new cache system for products
router.post("/products", products);
router.post("/rapps", rapps);
router.get("/bounties", bounties);
router.get("/courses", Courses);
router.get("/models", Models);
router.get("/models/:id", ModelsById);
router.get("/prompts/:id", Prompts);

export default router;