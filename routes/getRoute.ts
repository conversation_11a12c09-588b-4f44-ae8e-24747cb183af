//import { Organization } from "@runwayml/sdk/resources/organization";
import { getPayloadClient } from "../server/get-payload";
import express, { Request, Response } from "express";
import { modelType } from "@/payload/fields/modelType";

const router = express.Router();

const products = async (req: Request, res: Response) => {
  try {
    const { query, cursor } = req.body;
    const { sort, limit, filters, ...queryOpts } = query;
    const payload = await getPayloadClient();

    // Allowed extra query fields
    const allowedFields = ["price", "status", "featured", "name"];

    // Build parsedQueryOpts from extra query options
    const parsedQueryOpts: Record<string, any> = {};
    Object.entries(queryOpts).forEach(([key, value]) => {
      if (allowedFields.includes(key)) {
        parsedQueryOpts[key] = { equals: value };
      } else {
        console.error(`Invalid query field: ${key}`);
      }
    });

    // Process the filters object for multiple filter criteria
    if (filters) {
      for (const [key, values] of Object.entries(filters)) {
        const filterValues = Array.isArray(values) ? values : [values];

        if (key === "name") {
          // For text search on name
          parsedQueryOpts[key] = { contains: values[0] };
        } else if (key === "category") {
          // Convert category names to IDs
          const categories = await payload.find({
            collection: "category",
            where: { value: { in: values } },
            limit: 100,
          });

          const categoryIds = categories.docs.map((cat) => cat.id);

          parsedQueryOpts["productCategory"] = {
            in: categoryIds, // Use category IDs instead of names
          };
        } else if (key === "productType") {
          // Handle product type filtering (3d, image, gpt, music, video)
          parsedQueryOpts["product_type"] = {
            in: values,
          };
        } else if (key === "status") {
          parsedQueryOpts[key] = { equals: values[0] };
        } else if (key === "price") {
          // Handle price filter: free (0) or paid (greater than 0)
          const isFreeSelected =
            filterValues.includes(0) || filterValues.includes("0");
          const isPaidSelected = filterValues.includes("gt_0");
          if (isFreeSelected && isPaidSelected) {
            parsedQueryOpts[key] = { or: [{ equals: 0 }, { greater_than: 0 }] };
          } else if (isFreeSelected) {
            parsedQueryOpts[key] = { equals: 0 };
          } else if (isPaidSelected) {
            parsedQueryOpts[key] = { greater_than: 0 };
          }
        } else if (key === "featured" || key === "isFeatured") {
          parsedQueryOpts["isFeatured"] = { equals: true };
        } else if (key === "tags") {
          // Handle multiple tag selections
          parsedQueryOpts["tags"] = {
            in: values,
          };
        } else {
          // For any other filters, use the in operator
          parsedQueryOpts[key] = { in: values };
        }
      }
    }

    const page = cursor && cursor > 0 ? cursor : 1;
    const sortingOption = sort || "-createdAt";

    // Base query options
    // const queryOptions = {
    //   collection: "products",
    //   where: {
    //     approvedForSale: { equals: "approved" },
    //     ...parsedQueryOpts,
    //   },
    //   depth: 1,
    //   page,
    // };

    const {
      docs: items,
      hasNextPage,
      nextPage,
    } = await payload.find({
      collection: "products",
      where: {
        approvedForSale: {
          equals: "approved",
        },
        ...parsedQueryOpts,
      },
      sort,
      depth: 1,
      limit: 0,
      page,
    });

    const Products = items.map((product: any) => ({
      id: product.id,
      length: product.length,
      createdAt: product.createdAt,
      images: product.images,
      likes_id: product.likes?.map((like: any) => like.id),
      likes_length: product.likes?.length,
      likes_user_id: product.likes?.map((like: any) => like.user.id),
      productCategory: product.productCategory,
      productType: product.productType, // Include product type in response
      isFeatured: product.isFeatured,
      price: product.price,
      name: product.name,
      slug: product.slug,
      description: product.description,
      user_id: product.user.id,
      user_name: product.user?.user_name,
      user_email: product.user?.email,
      affiliated_with: product.affiliated_with,
      tags: product.tags,
      listingCategory: product.listingCategory,
    }));

    return res.json({
      items: Products,
      nextPage: hasNextPage ? nextPage : null,
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const rapps = async (req: Request, res: Response) => {
  try {
    const { query } = req.body;

    const {
      sort,
      model,
      category,
      tags,
      limit,
      page,
      name = "",
      generationType = "",
      ...queryOpts
    } = query;

    const payload = await getPayloadClient();
    // const page = cursor && cursor > 0 ? cursor : 1;
    const currentPage = page && page > 0 ? page : 1;
    let modelIdFilter = {}; // Default: No restriction on model
    let categoryIdFilter = {}; // Default: No restriction on category
    let tagsIdFilter = {}; // Default: No restriction on tags
    let nameFilter = {}; // Initialize name filter
    let generationTypeFilter = {}; // Initialize generation type filter

    // Step 1: Convert Model Name(s) → Model ID(s)
    if (model && model !== "all") {
      // If model is a comma-separated string, split it into an array.
      const modelArray =
        typeof model === "string"
          ? model.split(",").map((m) => m.trim())
          : model;

      const modelResult = await payload.find({
        collection: "models",
        where: { name: { in: modelArray } },
        depth: 1,
        limit: modelArray.length,
      });

      if (!modelResult.docs.length) {
        return res.status(404).json({ error: `Model(s) '${model}' not found` });
      }
      const modelIds = modelResult.docs.map((m: any) => m.id);
      modelIdFilter = { model: { in: modelIds } };
    }

    // Step 2: Convert Category Name(s) → Category ID(s)
    if (category && category !== "all") {
      // If category is a comma-separated string, split it into an array.
      const categoryArray =
        typeof category === "string"
          ? category.split(",").map((c) => c.trim())
          : category;

      const categoryResult = await payload.find({
        collection: "category",
        where: { value: { in: categoryArray } },
        depth: 1,
        limit: categoryArray.length,
      });

      if (!categoryResult.docs.length) {
        return res
          .status(404)
          .json({ error: `Category '${category}' not found` });
      }
      const categoryIds = categoryResult.docs.map((cat: any) => cat.id);
      categoryIdFilter = { category: { in: categoryIds } };
    }

    // Step 3: Convert Tag Names → Tag IDs
    if (tags && tags !== "all") {
      // If tags is a comma-separated string, split it into an array.
      const tagArray =
        typeof tags === "string" ? tags.split(",").map((t) => t.trim()) : tags; // Ensure it's an array
      const tagResults = await payload.find({
        collection: "tags",
        where: { value: { in: tagArray } },
        depth: 1,
        limit: tagArray.length,
      });

      if (!tagResults.docs.length) {
        return res.status(404).json({ error: `Tags '${tags}' not found` });
      }
      const tagIds = tagResults.docs.map((tag: any) => tag.id);
      tagsIdFilter = { tags: { in: tagIds } };
    }
    // Step 4: Apply the `name` Filter
    if (name.length > 0) {
      nameFilter = { name: { contains: name[0] } }; // Use the first element of the name array
    }
    // Step 5: Apply the `generationType` Filter
    if (generationType && generationType !== "all") {
      const validTypes = ["text", "audio", "video", "image"];
      const generationTypeArray =
        typeof generationType === "string"
          ? generationType.split(",").map((g) => g.trim())
          : generationType;

      const invalidTypes = generationTypeArray.filter(
        (type) => !validTypes.includes(type)
      );
      if (invalidTypes.length > 0) {
        return res.status(400).json({
          error: `Invalid generation type(s): ${invalidTypes.join(", ")}`,
        });
      }

      generationTypeFilter = { modelType: { in: generationTypeArray } }; // Apply valid types
    } else {
      generationTypeFilter = {}; // No filter applied if generationType is empty or "all"
    }
    // Step 6: Fetch `rapps` with filters applied
    // const fetchLimit = sort === "isFeatured" ? 100 : limit; // Fetch more items if sorting by isFeatured
    const {
      docs: items,
      // hasNextPage,
      // nextPage,
    } = await payload.find({
      collection: "rapps",
      where: {
        ...modelIdFilter, // Apply model filter
        ...categoryIdFilter, // Apply category filter
        ...tagsIdFilter, // Apply tags filter
        status: { equals: "approved" },
        ...nameFilter, // Apply name filter
        ...generationTypeFilter, // Apply generation type filter
        ...queryOpts,
      },
      depth: 1,
       sort: '-approvedDate',
      limit: 1000 // Use the provided limit (default to 10)
      // page:currentPage
    });

    let finalItems = items.map((item: any) => ({
      likes_id: item.likes?.map((like: any) => like.id),
      likes_user_id: item.likes?.map((like: any) => like.user.id),
      likes_length: item.likes?.length,
      images: item.images,
      name: item.name,
      slug: item.slug,
      modelType: item.modelType,
      modelName: item.model?.name,
      id: item.id,
      description: item.description,
      price: item.price,
      totalCost: item.totalCost,
      creator: {
        user_name: item.creator?.user_name,
        id: item.creator?.id,
      },
      newest: item.createdAt,
      isFeatured: item.isFeatured,
      // If item.category is an array, map to an array of names; otherwise, return a single name.
      category: Array.isArray(item.category)
        ? item.category.map((cat: any) => cat.name)
        : item.category?.name,
      tags: item.tags?.map((t: any) => t.name), // Return tag names instead of IDs
      likesCount: item.likes?.length || 0,
      purchasesCount: item.purchases?.length || 0,
      rating: item.rating,
    }));
    // Step 5: Additional Sorting
    if (sort) {
      if (sort === "likes") {
        finalItems = finalItems.sort(
          (a: any, b: any) => b.likesCount - a.likesCount
        );
      } else if (sort === "purchases") {
        finalItems = finalItems.sort(
          (a: any, b: any) => b.purchasesCount - a.purchasesCount
        );
      } else if (sort === "isFeatured") {
        finalItems = finalItems.filter((item: any) => item.isFeatured === true);
      }
    }

    const startIndex = (currentPage - 1) * limit;
    const paginatedItems = finalItems.slice(startIndex, startIndex + limit);

    return res.json({
      items: paginatedItems,
      nextPage: startIndex + limit < finalItems.length ? currentPage + 1 : null,
    });

    // return res.json({
    //   items: finalItems,
    //   nextPage: hasNextPage ? nextPage : null,
    // });
  } catch (error) {
    console.error("Error fetching rapps:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const bounties = async (req: Request, res: Response) => {
  try {
    const payload = await getPayloadClient();
    const { page, limit } = req.query;
    const pageNumber = parseInt(page as string, 10) || 1;
    const pageSize = parseInt(limit as string, 10) || 4;

    // Fetch all items that match the condition
    const result = await payload.find({
      collection: "bounties",
      where: {
        status: { in: ["approved", "completed"] },
      },
      depth: 3,
      // page: pageNumber,
      limit: 0,
    });

    // Process data
    const Bounties =  result.docs.map((bounty: any) => ({
      id: bounty.id,
      title: bounty.title,
      slug: bounty.slug,
      content: bounty.content,
      estimatedPrice: bounty.estimatedPrice,
      completionDate: bounty.completionDate,
      applyExpireDate: bounty.applyExpireDate,
      applicantsLimit: bounty.applicantsLimit,
      isFeatured: bounty.isFeatured,
      type: bounty.bountyType,
      tag: bounty.tag,
      status: bounty.status,
      profileImage: bounty?.user?.profileImage?.url,
      creator: bounty?.user?.user_name,
      applicants: bounty.applicants?.length,
      createdAt: bounty.createdAt,
      UseCases: bounty.useCases,
    }));

    // Paginate manually
    // const totalItems = items.length;
    // const startIndex = (pageNumber - 1) * pageSize;
    // const paginatedItems = Bounties.slice(startIndex, startIndex + pageSize);

    // return res.status(200).json({
    //   data: paginatedItems,
    //   hasMore: startIndex + pageSize < totalItems, // Properly calculate hasMore
    // });
    return res.status(200).json({
      data: Bounties,
      hasMore: result.hasNextPage, // use Payload's pagination metadata
    });
  } catch (error) {
    console.error("Error fetching:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const Courses = async (req: Request, res: Response) => {
  try {
    const payload = await getPayloadClient();

    const { docs: items } = await payload.find({
      collection: "courses",
      where: {
        status: { equals: "approved" },
      },
      depth: 2, // Ensure depth is enough to fetch attachment
    });

    // Mapping through courses safely
    const Courses = items.map((course: any) => ({
      id: course.id,
      title: course.title,
      description: course.description,
      cost: course.cost,
      level: course.level,
      time: course.time,
      pdf: course.pdf?.filename || null, // Ensure pdf exists
      type: course.type,
      isFeatured: course.isFeatured,
      image:
        course.attachment?.map((att: any) => att.url).filter(Boolean)[0] ||
        null, // Safely extract URL
    }));

    return res.status(200).json({
      data: Courses,
    });
  } catch (error) {
    console.error("Error fetching courses:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const Models = async (req: Request, res: Response) => {
  try {
    const payload = await getPayloadClient();

    // Fetch all models from the collection
    const { docs: items } = await payload.find({
      collection: "models",
      depth: 1,
      limit: 0,
    });

    const { type } = req.query as { type?: string }; // Extract type from the query
    //const filteredItems = items.filter((item) => item.type === type);

    // Filter items based on the type; include both text and vision if 'both' is requested
    const filteredItems =
      type === "both"
        ? items.filter((item) => item.type === "text" || item.type === "vision")
        : type
          ? items.filter((item) => item.type === type)
          : items; // If no type is specified, return all items

    // Map filtered items to the desired structure
    const models = filteredItems.map((item: any) => ({
      id: item.id,
      name: item.name,
      provider: item.provider,
      description: item.description,
      cost: item.cost + item.commision,
      type: item.type,
      modelImage: item.modelImage,
      isFeatured: item.isFeatured,
      organization: item.organization,
      organizationImage: item.organizationImage,
      imageinputopt: item.imageinputopt,
    }));
    return res.status(200).json({
      data: models,
    });
  } catch (error) {
    console.error("Error fetching models:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const ModelsById = async (req: Request, res: Response) => {
  try {
    const payload = await getPayloadClient();

    const { id } = req.params;
    const { type } = req.query;

    //console.log("Fetching vedio model with ID:", id, "and type:", type);

    if (!id) {
      return res.status(400).json({ error: "Model ID parameter is required." });
    }

    if (!type) {
      return res.status(400).json({ error: "Type parameter is required." });
    }

    // Fetch the model by ID
    const model = await payload.findByID({
      collection: "models",
      id,
      depth: 2,
    });

    if (!model) {
      return res.status(404).json({ error: "Model not found." });
    }

    if (model.type !== type) {
      return res.status(400).json({ error: "Model type does not match." });
    }

    const modelData = {
      id: model.id,
      name: model.name,
      description: model.description,
      modelType: model.type,
      organization: model.organization,
      cost: model.cost,
      commission: model.commision,
      provider: model.provider,
      settings: model.settings,
      examples: model.examples,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      enablePrompt: model.enablePrompt,
      imageinput: model.imageinput,
      imageinputopt: model.imageinputopt,
    };

    return res.status(200).json({
      data: modelData,
    });
  } catch (error) {
    console.error("Error fetching model by ID:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const Prompts = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const payload = await getPayloadClient();

    // const { docs: items,} = await payload.find({
    //   collection: "rapps",
    //   where: {
    //     status: { equals: "approved" },
    //   },
    //   depth: 2,
    // });

    const rapp = await payload.findByID({
      collection: "rapps",
      id,
      depth: 2,
    });

    const Prompts = {
      id: rapp.id,
      promptcost: rapp.promptcost,
      modelType: rapp.modelType,
      prompt: rapp.prompt,
      negativeprompt: rapp.negativeprompt,
      systemprompt: rapp.systemprompt,
      getprompt: rapp.getprompt,
      promptpurchase: rapp.promptpurchase,
      settings: rapp.settings,
      //  promptpurchase_user_id : rapp?.promptpurchase?.map(purchase => purchase.user?.id).filter(id => id !== undefined),
    };

    return res.status(200).json({
      data: Prompts,
    });
  } catch (error) {
    console.error("Error fetching :", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

router.post("/products", products);
router.post("/rapps", rapps);
router.get("/bounties", bounties);
router.get("/courses", Courses);
router.get("/models", Models);
router.get("/models/:id", ModelsById);
router.get("/prompts/:id", Prompts);

export default router;

// finalItems = finalItems.filter((item: any) => item.category === category);
// category: item.category?.map((c: any) => c.value),
