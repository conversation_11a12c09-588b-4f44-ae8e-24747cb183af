import { Request, Response } from "express";
import { getS3Client } from "../payload/lib/cloudflareS3Client";
import { GetObjectCommand } from "@aws-sdk/client-s3";

const s3Client = getS3Client();

export const downloadUploadedFile = async (req: Request, res: Response) => {
  const fileName = req.params.fileName;

  try {
    if (!fileName) {
      return res.status(400).json({ error: "File name is required" });
    }

    const command = new GetObjectCommand({
      Bucket: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_BUCKET_NAME,
      Key: fileName,
    });

    const response = await s3Client.send(command);

    const fileStream = response.Body;

    res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`);
    res.setHeader("Content-Type", response.ContentType || "application/octet-stream");

    fileStream.pipe(res);
  } catch (error) {
    console.error("Error downloading file:", error);
    return res.status(500).json({ error: "Failed to download file" });
  }
};