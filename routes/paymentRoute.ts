import { getPayloadClient } from "@/server/get-payload";
import express, { Response, Request } from "express";
import type <PERSON><PERSON> from "stripe";
import { stripe } from "@/lib/stripe";

const paymentRouter = express.Router();

const createSession = async (req: Request, res: Response) => {
  const { user } = req.body;
  let { productIds } = req.body;

  if (productIds.length === 0) {
    return res.json({ code: "BAD_REQUEST" });
  }

  const payload = await getPayloadClient();

  const { docs: products } = await payload.find({
    collection: "products",
    where: {
      id: {
        in: productIds,
      },
    },
  });

  const filteredProducts = products.filter((prod) => Boolean(prod.priceId));

  const order = await payload.create({
    collection: "orders",
    data: {
      _isPaid: false,
      // products: filteredProducts.map((prod: any) => prod.id),
      user: user.id,
      totalCoins: 0,
      totalBasePrice: 0,
      total: 0,
    },
  });

  const line_items: Stripe.Checkout.SessionCreateParams.LineItem[] = [];

  filteredProducts.forEach((product: any) => {
    line_items.push({
      price: product.priceId!,
      quantity: 1,
    });
  });

  line_items.push({
    price: "price_1OfibtSA5X4MA4CumdbzZLiI",
    quantity: 1,
    adjustable_quantity: {
      enabled: false,
    },
  });

  try {
    const stripeSession = await stripe.checkout.sessions.create({
      success_url: `${process.env.NEXT_PUBLIC_SERVER_URL}/thank-you?orderId=${order.id}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SERVER_URL}/cart`,
      payment_method_types: ["card", "paypal"],
      mode: "payment",
      metadata: {
        userId: user.id,
        orderId: order.id,
      },
      line_items,
    });

    return res.json({ url: stripeSession.url });
  } catch (err) {
    return res.json({ url: null });
  }
};

const orderStatus = async (req: Request, res: Response) => {
  const { orderId } = req.body;

  const payload = await getPayloadClient();

  const { docs: orders } = await payload.find({
    collection: "orders",
    where: {
      id: {
        equals: orderId,
      },
    },
  });

  if (!orders.length) {
    throw res.json({ code: "NOT_FOUND" });
  }

  const [order] = orders;

  return res.json({ isPaid: order._isPaid });
};

paymentRouter.post("/createSession", createSession);
paymentRouter.post("/orderStatus", orderStatus);
export default paymentRouter;
