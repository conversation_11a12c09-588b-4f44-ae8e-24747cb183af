# ---- Base builder ----
FROM node:20-alpine AS builder

WORKDIR /app

# Install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy all source files
COPY . .

# ✅ Set environment for build
ENV NODE_ENV=production
ENV PAYLOAD_CONFIG_PATH=payload.config.ts
ENV NEXT_BUILD=true

# ✅ Run full production build via script
RUN yarn build

# ---- Runtime image ----
FROM node:20-alpine AS runner

WORKDIR /app

# Copy only production dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile --production && yarn cache clean

# ✅ Copy only what you need to run the app
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/app ./app
COPY --from=builder /app/components ./components
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Default environment variables
ENV NODE_ENV=production
ENV PAYLOAD_CONFIG_PATH=dist/payload.config.js

EXPOSE 3000

CMD ["node", "dist/expressServer.js"]
