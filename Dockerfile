# Stage 1: Base image with dependencies
FROM node:20-alpine as base
RUN apk add --no-cache git

# Stage 2: Build stage
FROM base as builder

ENV NEXT_BUILD="true"

WORKDIR /home/<USER>/app

# First copy only package files for better caching
COPY package.json yarn.lock ./

# Install all dependencies (including devDependencies)
RUN yarn install --frozen-lockfile && yarn cache clean

# Copy the rest of the files
COPY . .

# Build the application
RUN yarn build

# Stage 3: Runtime image
FROM base as runtime

# Set environment variables
ENV NEXT_BUILD=""
ENV PAYLOAD_CONFIG_PATH=dist/payload.config.js
ENV NODE_ENV=production

WORKDIR /home/<USER>/app

# Copy package files first
COPY package.json yarn.lock ./

# Install only production dependencies
RUN yarn install --production --frozen-lockfile && yarn cache clean

# Copy built assets from builder stage
COPY --from=builder /home/<USER>/app/dist ./dist
COPY --from=builder /home/<USER>/app/build ./build
COPY --from=builder /home/<USER>/app/.next ./.next
COPY --from=builder /home/<USER>/app/public ./public

EXPOSE 3000

CMD ["node", "dist/expressServer.js"]
