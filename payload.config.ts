import { buildConfig } from "payload/config";
import { webpackBundler } from "@payloadcms/bundler-webpack";
import { mongooseAdapter } from "@payloadcms/db-mongodb";
import { slateEditor } from "@payloadcms/richtext-slate";
import path from "path";
import { Products } from "./payload/collections/Products/Products";
import { Models } from "./payload/collections/Models/Models";
import { Media } from "./payload/collections/Media";
import { ProductFiles } from "./payload/collections/ProductFile";
import { Rapps } from "./payload/collections/Rapps/Rapps";
import { Bounties } from "./payload/collections/Bounties";
import { Users } from "./payload/collections/Users/<USER>";
import { Blogs } from "./payload/collections/Blogs";
import { Purchases } from "./payload/collections/Purchases";
import { Prices } from "./payload/collections/Prices";
import { Orders } from "./payload/collections/Orders";
import { PaymentDetails } from "./payload/collections/PaymentDetails";
import { verifyRazorPay } from "./payload/endpoints/verify-razorpay";
import Courses from "./payload/collections/Courses";
import { handler } from "./payload/endpoints/runModel";
import Dashboard from "./payload/components/views/Dashboard";
import { Logo } from "./payload/components/graphics/Logo";
import { phonepeStatus } from "./payload/endpoints/phonepe-status";
import { Icon } from "./payload/components/graphics/Icon";
import { s3Adapter } from "@payloadcms/plugin-cloud-storage/s3";
import { cloudStorage } from "@payloadcms/plugin-cloud-storage";
import { ContactInfo } from "./payload/collections/ContactInfo";
import ForceProvider from "./payload/provider/ForceProvider";
import ForceThemeProvider from "./payload/ForceThemeProvider"
import { RappsPurchase } from "./payload/collections/RappsPurchase";
import { Wallet } from "./payload/collections/Wallet";
import { Nav } from "./payload/components/Nav";
import { Payout } from "./payload/collections/Payout";
import { runModelHandler } from "./payload/endpoints/runModelHandler";
import purchaseGenerateText from "./payload/endpoints/purchaseGenerateText";
import { Category } from "./payload/collections/Category/Category";
import { Tags } from "./payload/collections/Tags/Tags";
import { PrivateRapps } from "./payload/collections/PrivateRapps";
// import modelErrorEmailSent from "./payload/endpoints/modelErrorEmailSent;
// import modelErrorEmailSent from "./payload/endpoints/modelErrorEmailSent";

import { Notifications } from "./payload/collections/Notifications";


import { ChatRooms } from "./payload/collections/ChatRooms";
import { Messages } from "./payload/collections/Message";
import UseCases from "./payload/collections/UseCases";


const mockModulePath = path.resolve(__dirname, "./emptyModuleMock.js");

export default buildConfig({
  serverURL: process.env.NEXT_PUBLIC_SERVER_URL || "",

  collections: [
    Users,
    Products,
    Media,
    ProductFiles,
    Orders,
    Models,
    Bounties,
    Blogs,
    Rapps,
    Category,
    Tags,
    Purchases,
    RappsPurchase,
    PrivateRapps,
    Prices,
    PaymentDetails,
    ContactInfo,
    Courses,
    Wallet,
    Payout,
    Notifications,
    ChatRooms,
    Messages,
    UseCases

  ],
  routes: {
    admin: "/admin",
  },
  endpoints: [
    {
      path: "/hello",
      handler: (req, res) => {
        res.send({ ex: "hi buddy" });
      },
      method: "get",
    },
    {
      path: "/run/:id",
      handler: handler,
      method: "post",
    },
    {
      path: "/model-error-email-sent",
      method: "post",
      handler: async (req, res, next) => {
        try {
          const { modelErrorEmailSent } = await import("./payload/endpoints/modelErrorEmailSent");
          return modelErrorEmailSent(req, res, next);
        } catch (err) {
          console.error("❌ Failed to load modelErrorEmailSent endpoint:", err);
          return res.status(500).json({ error: "Internal Server Error" });
        }
      },
    },
    {
      path: "/runModel/:id",
      handler: runModelHandler,
      method:"post",
    },
    {
      path: "/purchaseGenerateText",
      handler: purchaseGenerateText,
      method:"post",
    },
    {
      path: "/verify-razorpay",
      method: "post",
      handler: verifyRazorPay,
    },
    {
      path: "/phonepe-status/:txnId",
      method: "post",
      handler: phonepeStatus,
    },
  ],
  express: {
    json: {
      limit: 50,
    },
  },
  admin: {
    user: "users",
    css: path.resolve(__dirname, "./payload/style.css"),
    bundler: webpackBundler(),
    meta: {
      titleSuffix: "- RentPrompts",
      favicon: "/favicon.ico",
      ogImage: "/favicon.ico",
    },
    components: {
      providers:[ForceThemeProvider],
      //Nav:Nav, 
      views: {
        Dashboard: Dashboard,
      },
      graphics: {
        Logo,
        Icon,
      },
    },
    webpack: (config) => {
      return {
        ...config,
        resolve: {
          ...config.resolve,
          alias: {
            ...config.resolve?.alias,
            dotenv: path.resolve(__dirname, "./dotenv"),
            '@':path.resolve(__dirname),
            //dotenv: path.resolve(__dirname, './emptyModuleMock.js'),
            [path.resolve(__dirname, './payload/endpoints/phonepe-pay')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/phonepe-status')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/purchaseBounty')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/purchaseProduct')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/purchaseRapps')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/likes')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/buy-coins')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/follow-details')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/likes-details')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/runModel')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/verify-phonepe')]: mockModulePath,
            [path.resolve(__dirname, './payload/endpoints/verify-razorpay')]: mockModulePath,
            [path.resolve(__dirname, './payload/hooks/auditlog')]: mockModulePath,
            [path.resolve(__dirname, './payload/lib/phonepe')]: mockModulePath,
            [path.resolve(__dirname, './payload/lib/razorpay')]: mockModulePath,
            [path.resolve(__dirname, './payload/utilities/calculateOrderFins')]: mockModulePath,
            [path.resolve(__dirname, './payload/utilities/generateChecksum')]: mockModulePath,
            [path.resolve(__dirname, './payload/access/admins')]: mockModulePath,
            [path.resolve(__dirname, './payload/access/adminsOrLoggedIn')]: mockModulePath,
            [path.resolve(__dirname, './payload/access/adminsOrLoggedUser')]: mockModulePath,
            [path.resolve(__dirname, './payload/access/anyone')]: mockModulePath,
            [path.resolve(__dirname, './payload/access/customError')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Blogs/access/isAdminOrHasAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Blogs/hooks/auditlog')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Bounties/hooks/afterDelete')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Bounties/hooks/beforeDelete')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Bounties/hooks/readAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Orders/access/readAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/PaymentDetails/access/readAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/PaymentDetails/access/createAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Prices/endpoints/securePriceData')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Products/endpoints/secureProductData')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Purchases/access/createAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Purchases/access/readAccess')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/adminAndUser')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/customer')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/publicProfile')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/meapi')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/ensureFirstUserIsAdmin')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/loginAfterCreate')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>/resolveDuplicatePurchases')]: mockModulePath,
            [path.resolve(__dirname, './payload/collections/Users/<USER>')]: mockModulePath,
          },
          fallback: {
            ...config.resolve?.fallback,
            querystring: require.resolve("querystring-es3"),
            timers: require.resolve("timers-browserify"),
            "fs": false,
            "url": false,
            "zlib": false,
            "net": false,
            "util": false,
            "dns": false,
            "os": false,
            "tls": false,
            "child_process": false,
            "assert": false,
            "crypto": false,
            "http": false,
            "https": false,
            
          },
        },
      };
    },
  },
 
  rateLimit: {
    max: 2000,
  },
  editor: slateEditor({}),
  db: mongooseAdapter({
    url: process.env.MONGODB_URL!,
  }),
  typescript: {
    outputFile: path.resolve(__dirname, "server/payload-types.ts"),
  },
  cors: [
    "https://checkout.stripe.com",
    "https://mercury-uat.phonepe.com/transact/simulator?token=*",
    "https://api-preprod.phonepe.com",
    "https://developer.phonepe.com",
    "https://api.phonepe.com/apis/hermes/pg/v1/pay",
    "https://api.phonepe.com/apis/hermes",
    "https://checkout.razorpay.com",
    "https://www.rentprompts.com/*",
    process.env.PAYLOAD_PUBLIC_SERVER_URL || "http://localhost:3000/*",
  ].filter(Boolean),
  csrf: [
    "https://checkout.stripe.com",
    "https://mercury-uat.phonepe.com/*",
    "https://api-preprod.phonepe.com",
    "https://developer.phonepe.com",
    "https://api.phonepe.com/apis/hermes/pg/v1/pay",
    "https://api.phonepe.com/apis/hermes",
    "https://checkout.razorpay.com/v1/checkout.js",
    "https://www.rentprompts.com/*",
    process.env.PAYLOAD_PUBLIC_SERVER_URL || "http://localhost:3000/*",
  ].filter(Boolean),
  plugins: [
    cloudStorage({
      collections: {
        media: {
          adapter: s3Adapter({
            config: {
              credentials: {
                accessKeyId: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_PUBLIC_ACCESS_KEY,
                secretAccessKey: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_PUBLIC_SECRET_KEY,
              },
              region: "auto",
              endpoint: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_ENDPOINT,
            },
            bucket: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_PUBLIC_BUCKET_NAME,
          }),
        },
        product_files: {
          adapter: s3Adapter({
            config: {
              credentials: {
                accessKeyId: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_ACCESS_KEY,
                secretAccessKey: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_SECRET_KEY,
              },
              region: "auto",
              endpoint: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_ENDPOINT,
            },
            bucket: process.env.PAYLOAD_PUBLIC_CLOUDFLARE_BUCKET_NAME,
          }),
        },
      },
    }),
  ],
  
});
